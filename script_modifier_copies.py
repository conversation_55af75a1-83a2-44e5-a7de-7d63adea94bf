#!/usr/bin/env python3
"""
MODIFICATION DES COPIES DES FICHIERS JS
=======================================

Ce script modifie les copies des fichiers JS avec notre code d'injection
de logging automatique.
"""

import shutil
from pathlib import Path
import datetime

def modifier_copies():
    """Modifie les copies des fichiers JS"""
    
    # Chemins des copies dans le dossier principal
    copies_dir = Path("C:/Users/<USER>/Desktop/Travail/Projet7")
    injection_code_file = copies_dir / "injection_code_manual.js"
    
    print(f"🔧 MODIFICATION DES COPIES DES FICHIERS JS")
    print(f"=" * 60)
    print(f"Répertoire: {copies_dir}")
    
    # Chercher les fichiers JS copiés
    js_files = list(copies_dir.glob("*.js"))
    extension_copies = [f for f in js_files if "extension" in f.name.lower()]
    
    print(f"📁 Fichiers JS trouvés: {len(js_files)}")
    for js_file in js_files:
        print(f"   - {js_file.name} ({js_file.stat().st_size} bytes)")
    
    print(f"📁 Copies d'extension trouvées: {len(extension_copies)}")
    for ext_copy in extension_copies:
        print(f"   - {ext_copy.name}")
    
    if not extension_copies:
        print(f"❌ Aucune copie d'extension trouvée")
        print(f"💡 Assurez-vous d'avoir copié extension.js dans {copies_dir}")
        return False
    
    # Vérifier que le code d'injection existe
    if not injection_code_file.exists():
        print(f"❌ Code d'injection non trouvé: {injection_code_file}")
        return False
    
    # Lire le code d'injection
    with open(injection_code_file, 'r', encoding='utf-8') as f:
        injection_code = f.read()
    
    print(f"📊 Code d'injection: {len(injection_code)} caractères")
    
    # Modifier chaque copie d'extension
    success_count = 0
    
    for ext_copy in extension_copies:
        try:
            print(f"\n🔄 Modification de {ext_copy.name}...")
            
            # Créer une sauvegarde de la copie
            backup_copy = copies_dir / f"{ext_copy.stem}_backup{ext_copy.suffix}"
            shutil.copy2(ext_copy, backup_copy)
            print(f"✅ Sauvegarde copie: {backup_copy.name}")
            
            # Lire le contenu original de la copie
            with open(ext_copy, 'r', encoding='utf-8') as f:
                original_content = f.read()
            
            print(f"📊 Contenu original: {len(original_content)} caractères")
            
            # Créer le contenu modifié
            modified_content = injection_code + "\n" + original_content
            
            print(f"📊 Contenu modifié: {len(modified_content)} caractères")
            print(f"📊 Code ajouté: {len(injection_code)} caractères")
            
            # Écrire le contenu modifié dans la copie
            with open(ext_copy, 'w', encoding='utf-8') as f:
                f.write(modified_content)
            
            # Vérifier la modification
            with open(ext_copy, 'r', encoding='utf-8') as f:
                verification_content = f.read()
            
            if "AUGMENT AUTO LOGGING SYSTEM" in verification_content:
                print(f"✅ Modification réussie: {ext_copy.name}")
                print(f"✅ Injection vérifiée dans la copie")
                success_count += 1
                
                # Afficher un aperçu du début du fichier modifié
                print(f"📝 Début du fichier modifié:")
                print(f"{'='*50}")
                print(verification_content[:200])
                print(f"{'='*50}")
                
            else:
                print(f"❌ Vérification échouée: {ext_copy.name}")
        
        except Exception as e:
            print(f"❌ Erreur modification {ext_copy.name}: {e}")
    
    print(f"\n📊 RÉSUMÉ:")
    print(f"✅ Copies modifiées avec succès: {success_count}/{len(extension_copies)}")
    
    if success_count > 0:
        print(f"\n🎉 MODIFICATION DES COPIES RÉUSSIE!")
        print(f"✅ {success_count} fichier(s) copie(s) modifié(s)")
        print(f"✅ Code d'injection ajouté au début des fichiers")
        
        print(f"\n📋 PROCHAINES ÉTAPES:")
        print(f"1. 🛑 Fermez VSCode complètement")
        print(f"2. 🗑️  Supprimez les fichiers originaux:")
        for ext_copy in extension_copies:
            original_path = "C:\\Users\\<USER>\\.vscode\\extensions\\augment.vscode-augment-0.470.1\\out\\extension.js"
            print(f"   - {original_path}")
        print(f"3. 📁 Copiez les fichiers modifiés vers leur emplacement original:")
        for ext_copy in extension_copies:
            print(f"   - {ext_copy} → extension.js")
        print(f"4. 🚀 Redémarrez VSCode")
        print(f"5. 🧪 Testez avec un nouveau projet")
        
        return True
    else:
        print(f"\n❌ ÉCHEC DE LA MODIFICATION DES COPIES")
        return False

def creer_script_remplacement():
    """Crée un script pour remplacer automatiquement les fichiers originaux"""
    
    copies_dir = Path("C:/Users/<USER>/Desktop/Travail/Projet7")
    
    # Trouver les copies modifiées
    js_files = list(copies_dir.glob("*.js"))
    extension_copies = [f for f in js_files if "extension" in f.name.lower() and "backup" not in f.name.lower()]
    
    if not extension_copies:
        print(f"❌ Aucune copie modifiée trouvée")
        return False
    
    # Créer le script de remplacement
    script_content = f'''#!/usr/bin/env python3
"""
SCRIPT DE REMPLACEMENT AUTOMATIQUE
==================================
Généré automatiquement le {datetime.datetime.now().isoformat()}
"""

import shutil
from pathlib import Path

def remplacer_fichiers():
    """Remplace les fichiers originaux par les copies modifiées"""
    
    print("🔄 REMPLACEMENT DES FICHIERS ORIGINAUX")
    print("=" * 50)
    
    # Chemins
    original_extension = Path("C:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.470.1/out/extension.js")
    copies_dir = Path("{copies_dir}")
    
    # Fichiers de remplacement
    replacements = ['''
    
    for ext_copy in extension_copies:
        script_content += f'''
        {{
            "copy": copies_dir / "{ext_copy.name}",
            "original": original_extension,
            "description": "Extension Augment principale"
        }},'''
    
    script_content += f'''
    ]
    
    success_count = 0
    
    for replacement in replacements:
        try:
            copy_file = replacement["copy"]
            original_file = replacement["original"]
            description = replacement["description"]
            
            print(f"🔄 Remplacement: {{description}}")
            print(f"   Source: {{copy_file}}")
            print(f"   Destination: {{original_file}}")
            
            if not copy_file.exists():
                print(f"❌ Fichier copie non trouvé: {{copy_file}}")
                continue
            
            # Supprimer l'original s'il existe
            if original_file.exists():
                original_file.unlink()
                print(f"✅ Fichier original supprimé")
            
            # Copier le fichier modifié
            shutil.copy2(copy_file, original_file)
            print(f"✅ Fichier remplacé avec succès")
            
            success_count += 1
            
        except Exception as e:
            print(f"❌ Erreur remplacement: {{e}}")
    
    print(f"\\n📊 RÉSUMÉ:")
    print(f"✅ Fichiers remplacés: {{success_count}}/{{len(replacements)}}")
    
    if success_count > 0:
        print(f"\\n🎉 REMPLACEMENT RÉUSSI!")
        print(f"✅ Extension Augment modifiée")
        print(f"✅ Logging automatique intégré")
        print(f"\\n💡 REDÉMARREZ VSCODE pour activer les modifications")
    else:
        print(f"\\n❌ ÉCHEC DU REMPLACEMENT")

if __name__ == "__main__":
    remplacer_fichiers()
'''
    
    script_file = copies_dir / "script_remplacement_automatique.py"
    
    with open(script_file, 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print(f"✅ Script de remplacement créé: {script_file}")
    print(f"\n💡 UTILISATION:")
    print(f"1. Fermez VSCode complètement")
    print(f"2. Exécutez: python {script_file}")
    print(f"3. Redémarrez VSCode")
    
    return True

def main():
    """Fonction principale"""
    
    if modifier_copies():
        print(f"\n🔧 Création du script de remplacement automatique...")
        creer_script_remplacement()
    else:
        print(f"\n❌ Impossible de créer le script de remplacement")

if __name__ == "__main__":
    main()
