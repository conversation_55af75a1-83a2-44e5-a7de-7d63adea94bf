import{_ as x,l as j,j as Et,al as mt,a5 as _t,d as et,I as Nt,ac as vt,D as Q,i as K,ad as At,ae as Tt,af as Lt}from"./AugmentMessage-DIzdCIMv.js";import{c as gt}from"./cytoscape.esm-B0yNE0-9.js";import{am as Ot}from"./SpinnerAugment-BJ4-L7QR.js";import"./github-C1PQK5DH.js";import"./pen-to-square-Bm4lF9Yl.js";import"./augment-logo-D_UKSkj8.js";import"./TextTooltipAugment-Bkzart3o.js";import"./BaseButton-C6Dhmpxa.js";import"./IconButtonAugment-Certjadv.js";import"./Content-Czt02SJi.js";import"./globals-D0QH3NT1.js";import"./open-in-new-window-DMlqLwqy.js";import"./types-LfaCSdmF.js";import"./chat-types-NgqNgjwU.js";import"./test_service_pb-B6vKXZrG.js";import"./file-paths-BcSg4gks.js";import"./types-a569v5Ol.js";import"./folder-BJI1Q8_7.js";import"./folder-opened-DzrGzNBt.js";import"./types-BSMhNRWH.js";import"./index-C-g0ZorP.js";import"./CardAugment-BxTO-shY.js";import"./TextAreaAugment-Cj5jK817.js";import"./diff-utils-y96qaWKK.js";import"./toggleHighContrast-CwIv4U26.js";import"./preload-helper-Dv6uf1Os.js";import"./index-MyvMQzjq.js";import"./keypress-DD1aQVr0.js";import"./await_block-CvQ_3xaW.js";import"./ButtonAugment-HnJOGilM.js";import"./expand--BB_Hn_b.js";import"./mcp-logo-B9nTLE-q.js";import"./ellipsis-BWy9xWah.js";import"./IconFilePath-C-3qORpY.js";import"./LanguageIcon-BH9BM7T7.js";import"./next-edit-types-904A5ehg.js";import"./magnifying-glass-LWYs47rB.js";import"./MaterialIcon-DIlB9c-0.js";import"./Filespan-BC4kxbfx.js";import"./chevron-down-B88L5wkj.js";import"./lodash-ChYFUhWY.js";import"./terminal-BQIj5vJ0.js";var ot,st,at,ct={exports:{}},ht={exports:{}},lt={exports:{}};function Dt(){return ot||(ot=1,v=function(){return function(A){var f={};function d(e){if(f[e])return f[e].exports;var t=f[e]={i:e,l:!1,exports:{}};return A[e].call(t.exports,t,t.exports,d),t.l=!0,t.exports}return d.m=A,d.c=f,d.i=function(e){return e},d.d=function(e,t,n){d.o(e,t)||Object.defineProperty(e,t,{configurable:!1,enumerable:!0,get:n})},d.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return d.d(t,"a",t),t},d.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},d.p="",d(d.s=26)}([function(A,f,d){function e(){}e.QUALITY=1,e.DEFAULT_CREATE_BENDS_AS_NEEDED=!1,e.DEFAULT_INCREMENTAL=!1,e.DEFAULT_ANIMATION_ON_LAYOUT=!0,e.DEFAULT_ANIMATION_DURING_LAYOUT=!1,e.DEFAULT_ANIMATION_PERIOD=50,e.DEFAULT_UNIFORM_LEAF_NODE_SIZES=!1,e.DEFAULT_GRAPH_MARGIN=15,e.NODE_DIMENSIONS_INCLUDE_LABELS=!1,e.SIMPLE_NODE_SIZE=40,e.SIMPLE_NODE_HALF_SIZE=e.SIMPLE_NODE_SIZE/2,e.EMPTY_COMPOUND_NODE_SIZE=40,e.MIN_EDGE_LENGTH=1,e.WORLD_BOUNDARY=1e6,e.INITIAL_WORLD_BOUNDARY=e.WORLD_BOUNDARY/1e3,e.WORLD_CENTER_X=1200,e.WORLD_CENTER_Y=900,A.exports=e},function(A,f,d){var e=d(2),t=d(8),n=d(9);function r(o,h,a){e.call(this,a),this.isOverlapingSourceAndTarget=!1,this.vGraphObject=a,this.bendpoints=[],this.source=o,this.target=h}for(var i in r.prototype=Object.create(e.prototype),e)r[i]=e[i];r.prototype.getSource=function(){return this.source},r.prototype.getTarget=function(){return this.target},r.prototype.isInterGraph=function(){return this.isInterGraph},r.prototype.getLength=function(){return this.length},r.prototype.isOverlapingSourceAndTarget=function(){return this.isOverlapingSourceAndTarget},r.prototype.getBendpoints=function(){return this.bendpoints},r.prototype.getLca=function(){return this.lca},r.prototype.getSourceInLca=function(){return this.sourceInLca},r.prototype.getTargetInLca=function(){return this.targetInLca},r.prototype.getOtherEnd=function(o){if(this.source===o)return this.target;if(this.target===o)return this.source;throw"Node is not incident with this edge"},r.prototype.getOtherEndInGraph=function(o,h){for(var a=this.getOtherEnd(o),l=h.getGraphManager().getRoot();;){if(a.getOwner()==h)return a;if(a.getOwner()==l)break;a=a.getOwner().getParent()}return null},r.prototype.updateLength=function(){var o=new Array(4);this.isOverlapingSourceAndTarget=t.getIntersection(this.target.getRect(),this.source.getRect(),o),this.isOverlapingSourceAndTarget||(this.lengthX=o[0]-o[2],this.lengthY=o[1]-o[3],Math.abs(this.lengthX)<1&&(this.lengthX=n.sign(this.lengthX)),Math.abs(this.lengthY)<1&&(this.lengthY=n.sign(this.lengthY)),this.length=Math.sqrt(this.lengthX*this.lengthX+this.lengthY*this.lengthY))},r.prototype.updateLengthSimple=function(){this.lengthX=this.target.getCenterX()-this.source.getCenterX(),this.lengthY=this.target.getCenterY()-this.source.getCenterY(),Math.abs(this.lengthX)<1&&(this.lengthX=n.sign(this.lengthX)),Math.abs(this.lengthY)<1&&(this.lengthY=n.sign(this.lengthY)),this.length=Math.sqrt(this.lengthX*this.lengthX+this.lengthY*this.lengthY)},A.exports=r},function(A,f,d){A.exports=function(e){this.vGraphObject=e}},function(A,f,d){var e=d(2),t=d(10),n=d(13),r=d(0),i=d(16),o=d(4);function h(l,E,m,g){m==null&&g==null&&(g=E),e.call(this,g),l.graphManager!=null&&(l=l.graphManager),this.estimatedSize=t.MIN_VALUE,this.inclusionTreeDepth=t.MAX_VALUE,this.vGraphObject=g,this.edges=[],this.graphManager=l,this.rect=m!=null&&E!=null?new n(E.x,E.y,m.width,m.height):new n}for(var a in h.prototype=Object.create(e.prototype),e)h[a]=e[a];h.prototype.getEdges=function(){return this.edges},h.prototype.getChild=function(){return this.child},h.prototype.getOwner=function(){return this.owner},h.prototype.getWidth=function(){return this.rect.width},h.prototype.setWidth=function(l){this.rect.width=l},h.prototype.getHeight=function(){return this.rect.height},h.prototype.setHeight=function(l){this.rect.height=l},h.prototype.getCenterX=function(){return this.rect.x+this.rect.width/2},h.prototype.getCenterY=function(){return this.rect.y+this.rect.height/2},h.prototype.getCenter=function(){return new o(this.rect.x+this.rect.width/2,this.rect.y+this.rect.height/2)},h.prototype.getLocation=function(){return new o(this.rect.x,this.rect.y)},h.prototype.getRect=function(){return this.rect},h.prototype.getDiagonal=function(){return Math.sqrt(this.rect.width*this.rect.width+this.rect.height*this.rect.height)},h.prototype.getHalfTheDiagonal=function(){return Math.sqrt(this.rect.height*this.rect.height+this.rect.width*this.rect.width)/2},h.prototype.setRect=function(l,E){this.rect.x=l.x,this.rect.y=l.y,this.rect.width=E.width,this.rect.height=E.height},h.prototype.setCenter=function(l,E){this.rect.x=l-this.rect.width/2,this.rect.y=E-this.rect.height/2},h.prototype.setLocation=function(l,E){this.rect.x=l,this.rect.y=E},h.prototype.moveBy=function(l,E){this.rect.x+=l,this.rect.y+=E},h.prototype.getEdgeListToNode=function(l){var E=[],m=this;return m.edges.forEach(function(g){if(g.target==l){if(g.source!=m)throw"Incorrect edge source!";E.push(g)}}),E},h.prototype.getEdgesBetween=function(l){var E=[],m=this;return m.edges.forEach(function(g){if(g.source!=m&&g.target!=m)throw"Incorrect edge source and/or target";g.target!=l&&g.source!=l||E.push(g)}),E},h.prototype.getNeighborsList=function(){var l=new Set,E=this;return E.edges.forEach(function(m){if(m.source==E)l.add(m.target);else{if(m.target!=E)throw"Incorrect incidency!";l.add(m.source)}}),l},h.prototype.withChildren=function(){var l=new Set;if(l.add(this),this.child!=null)for(var E=this.child.getNodes(),m=0;m<E.length;m++)E[m].withChildren().forEach(function(g){l.add(g)});return l},h.prototype.getNoOfChildren=function(){var l=0;if(this.child==null)l=1;else for(var E=this.child.getNodes(),m=0;m<E.length;m++)l+=E[m].getNoOfChildren();return l==0&&(l=1),l},h.prototype.getEstimatedSize=function(){if(this.estimatedSize==t.MIN_VALUE)throw"assert failed";return this.estimatedSize},h.prototype.calcEstimatedSize=function(){return this.child==null?this.estimatedSize=(this.rect.width+this.rect.height)/2:(this.estimatedSize=this.child.calcEstimatedSize(),this.rect.width=this.estimatedSize,this.rect.height=this.estimatedSize,this.estimatedSize)},h.prototype.scatter=function(){var l,E,m=-r.INITIAL_WORLD_BOUNDARY,g=r.INITIAL_WORLD_BOUNDARY;l=r.WORLD_CENTER_X+i.nextDouble()*(g-m)+m;var _=-r.INITIAL_WORLD_BOUNDARY,L=r.INITIAL_WORLD_BOUNDARY;E=r.WORLD_CENTER_Y+i.nextDouble()*(L-_)+_,this.rect.x=l,this.rect.y=E},h.prototype.updateBounds=function(){if(this.getChild()==null)throw"assert failed";if(this.getChild().getNodes().length!=0){var l=this.getChild();if(l.updateBounds(!0),this.rect.x=l.getLeft(),this.rect.y=l.getTop(),this.setWidth(l.getRight()-l.getLeft()),this.setHeight(l.getBottom()-l.getTop()),r.NODE_DIMENSIONS_INCLUDE_LABELS){var E=l.getRight()-l.getLeft(),m=l.getBottom()-l.getTop();this.labelWidth>E&&(this.rect.x-=(this.labelWidth-E)/2,this.setWidth(this.labelWidth)),this.labelHeight>m&&(this.labelPos=="center"?this.rect.y-=(this.labelHeight-m)/2:this.labelPos=="top"&&(this.rect.y-=this.labelHeight-m),this.setHeight(this.labelHeight))}}},h.prototype.getInclusionTreeDepth=function(){if(this.inclusionTreeDepth==t.MAX_VALUE)throw"assert failed";return this.inclusionTreeDepth},h.prototype.transform=function(l){var E=this.rect.x;E>r.WORLD_BOUNDARY?E=r.WORLD_BOUNDARY:E<-r.WORLD_BOUNDARY&&(E=-r.WORLD_BOUNDARY);var m=this.rect.y;m>r.WORLD_BOUNDARY?m=r.WORLD_BOUNDARY:m<-r.WORLD_BOUNDARY&&(m=-r.WORLD_BOUNDARY);var g=new o(E,m),_=l.inverseTransformPoint(g);this.setLocation(_.x,_.y)},h.prototype.getLeft=function(){return this.rect.x},h.prototype.getRight=function(){return this.rect.x+this.rect.width},h.prototype.getTop=function(){return this.rect.y},h.prototype.getBottom=function(){return this.rect.y+this.rect.height},h.prototype.getParent=function(){return this.owner==null?null:this.owner.getParent()},A.exports=h},function(A,f,d){function e(t,n){t==null&&n==null?(this.x=0,this.y=0):(this.x=t,this.y=n)}e.prototype.getX=function(){return this.x},e.prototype.getY=function(){return this.y},e.prototype.setX=function(t){this.x=t},e.prototype.setY=function(t){this.y=t},e.prototype.getDifference=function(t){return new DimensionD(this.x-t.x,this.y-t.y)},e.prototype.getCopy=function(){return new e(this.x,this.y)},e.prototype.translate=function(t){return this.x+=t.width,this.y+=t.height,this},A.exports=e},function(A,f,d){var e=d(2),t=d(10),n=d(0),r=d(6),i=d(3),o=d(1),h=d(13),a=d(12),l=d(11);function E(g,_,L){e.call(this,L),this.estimatedSize=t.MIN_VALUE,this.margin=n.DEFAULT_GRAPH_MARGIN,this.edges=[],this.nodes=[],this.isConnected=!1,this.parent=g,_!=null&&_ instanceof r?this.graphManager=_:_!=null&&_ instanceof Layout&&(this.graphManager=_.graphManager)}for(var m in E.prototype=Object.create(e.prototype),e)E[m]=e[m];E.prototype.getNodes=function(){return this.nodes},E.prototype.getEdges=function(){return this.edges},E.prototype.getGraphManager=function(){return this.graphManager},E.prototype.getParent=function(){return this.parent},E.prototype.getLeft=function(){return this.left},E.prototype.getRight=function(){return this.right},E.prototype.getTop=function(){return this.top},E.prototype.getBottom=function(){return this.bottom},E.prototype.isConnected=function(){return this.isConnected},E.prototype.add=function(g,_,L){if(_==null&&L==null){var N=g;if(this.graphManager==null)throw"Graph has no graph mgr!";if(this.getNodes().indexOf(N)>-1)throw"Node already in graph!";return N.owner=this,this.getNodes().push(N),N}var T=g;if(!(this.getNodes().indexOf(_)>-1&&this.getNodes().indexOf(L)>-1))throw"Source or target not in graph!";if(_.owner!=L.owner||_.owner!=this)throw"Both owners must be this graph!";return _.owner!=L.owner?null:(T.source=_,T.target=L,T.isInterGraph=!1,this.getEdges().push(T),_.edges.push(T),L!=_&&L.edges.push(T),T)},E.prototype.remove=function(g){var _=g;if(g instanceof i){if(_==null)throw"Node is null!";if(_.owner==null||_.owner!=this)throw"Owner graph is invalid!";if(this.graphManager==null)throw"Owner graph manager is invalid!";for(var L=_.edges.slice(),N=L.length,T=0;T<N;T++)(u=L[T]).isInterGraph?this.graphManager.remove(u):u.source.owner.remove(u);if((I=this.nodes.indexOf(_))==-1)throw"Node not in owner node list!";this.nodes.splice(I,1)}else if(g instanceof o){var u;if((u=g)==null)throw"Edge is null!";if(u.source==null||u.target==null)throw"Source and/or target is null!";if(u.source.owner==null||u.target.owner==null||u.source.owner!=this||u.target.owner!=this)throw"Source and/or target owner is invalid!";var I,s=u.source.edges.indexOf(u),c=u.target.edges.indexOf(u);if(!(s>-1&&c>-1))throw"Source and/or target doesn't know this edge!";if(u.source.edges.splice(s,1),u.target!=u.source&&u.target.edges.splice(c,1),(I=u.source.owner.getEdges().indexOf(u))==-1)throw"Not in owner's edge list!";u.source.owner.getEdges().splice(I,1)}},E.prototype.updateLeftTop=function(){for(var g,_,L,N=t.MAX_VALUE,T=t.MAX_VALUE,u=this.getNodes(),I=u.length,s=0;s<I;s++){var c=u[s];N>(g=c.getTop())&&(N=g),T>(_=c.getLeft())&&(T=_)}return N==t.MAX_VALUE?null:(L=u[0].getParent().paddingLeft!=null?u[0].getParent().paddingLeft:this.margin,this.left=T-L,this.top=N-L,new a(this.left,this.top))},E.prototype.updateBounds=function(g){for(var _,L,N,T,u,I=t.MAX_VALUE,s=-t.MAX_VALUE,c=t.MAX_VALUE,p=-t.MAX_VALUE,y=this.nodes,O=y.length,D=0;D<O;D++){var w=y[D];g&&w.child!=null&&w.updateBounds(),I>(_=w.getLeft())&&(I=_),s<(L=w.getRight())&&(s=L),c>(N=w.getTop())&&(c=N),p<(T=w.getBottom())&&(p=T)}var R=new h(I,c,s-I,p-c);I==t.MAX_VALUE&&(this.left=this.parent.getLeft(),this.right=this.parent.getRight(),this.top=this.parent.getTop(),this.bottom=this.parent.getBottom()),u=y[0].getParent().paddingLeft!=null?y[0].getParent().paddingLeft:this.margin,this.left=R.x-u,this.right=R.x+R.width+u,this.top=R.y-u,this.bottom=R.y+R.height+u},E.calculateBounds=function(g){for(var _,L,N,T,u=t.MAX_VALUE,I=-t.MAX_VALUE,s=t.MAX_VALUE,c=-t.MAX_VALUE,p=g.length,y=0;y<p;y++){var O=g[y];u>(_=O.getLeft())&&(u=_),I<(L=O.getRight())&&(I=L),s>(N=O.getTop())&&(s=N),c<(T=O.getBottom())&&(c=T)}return new h(u,s,I-u,c-s)},E.prototype.getInclusionTreeDepth=function(){return this==this.graphManager.getRoot()?1:this.parent.getInclusionTreeDepth()},E.prototype.getEstimatedSize=function(){if(this.estimatedSize==t.MIN_VALUE)throw"assert failed";return this.estimatedSize},E.prototype.calcEstimatedSize=function(){for(var g=0,_=this.nodes,L=_.length,N=0;N<L;N++)g+=_[N].calcEstimatedSize();return this.estimatedSize=g==0?n.EMPTY_COMPOUND_NODE_SIZE:g/Math.sqrt(this.nodes.length),this.estimatedSize},E.prototype.updateConnected=function(){var g=this;if(this.nodes.length!=0){var _,L,N=new l,T=new Set,u=this.nodes[0];for(u.withChildren().forEach(function(p){N.push(p),T.add(p)});N.length!==0;)for(var I=(_=(u=N.shift()).getEdges()).length,s=0;s<I;s++)(L=_[s].getOtherEndInGraph(u,this))==null||T.has(L)||L.withChildren().forEach(function(p){N.push(p),T.add(p)});if(this.isConnected=!1,T.size>=this.nodes.length){var c=0;T.forEach(function(p){p.owner==g&&c++}),c==this.nodes.length&&(this.isConnected=!0)}}else this.isConnected=!0},A.exports=E},function(A,f,d){var e,t=d(1);function n(r){e=d(5),this.layout=r,this.graphs=[],this.edges=[]}n.prototype.addRoot=function(){var r=this.layout.newGraph(),i=this.layout.newNode(null),o=this.add(r,i);return this.setRootGraph(o),this.rootGraph},n.prototype.add=function(r,i,o,h,a){if(o==null&&h==null&&a==null){if(r==null)throw"Graph is null!";if(i==null)throw"Parent node is null!";if(this.graphs.indexOf(r)>-1)throw"Graph already in this graph mgr!";if(this.graphs.push(r),r.parent!=null)throw"Already has a parent!";if(i.child!=null)throw"Already has a child!";return r.parent=i,i.child=r,r}a=o,o=r;var l=(h=i).getOwner(),E=a.getOwner();if(l==null||l.getGraphManager()!=this)throw"Source not in this graph mgr!";if(E==null||E.getGraphManager()!=this)throw"Target not in this graph mgr!";if(l==E)return o.isInterGraph=!1,l.add(o,h,a);if(o.isInterGraph=!0,o.source=h,o.target=a,this.edges.indexOf(o)>-1)throw"Edge already in inter-graph edge list!";if(this.edges.push(o),o.source==null||o.target==null)throw"Edge source and/or target is null!";if(o.source.edges.indexOf(o)!=-1||o.target.edges.indexOf(o)!=-1)throw"Edge already in source and/or target incidency list!";return o.source.edges.push(o),o.target.edges.push(o),o},n.prototype.remove=function(r){if(r instanceof e){var i=r;if(i.getGraphManager()!=this)throw"Graph not in this graph mgr";if(i!=this.rootGraph&&(i.parent==null||i.parent.graphManager!=this))throw"Invalid parent node!";for(var o,h=[],a=(h=h.concat(i.getEdges())).length,l=0;l<a;l++)o=h[l],i.remove(o);var E,m=[];for(a=(m=m.concat(i.getNodes())).length,l=0;l<a;l++)E=m[l],i.remove(E);i==this.rootGraph&&this.setRootGraph(null);var g=this.graphs.indexOf(i);this.graphs.splice(g,1),i.parent=null}else if(r instanceof t){if((o=r)==null)throw"Edge is null!";if(!o.isInterGraph)throw"Not an inter-graph edge!";if(o.source==null||o.target==null)throw"Source and/or target is null!";if(o.source.edges.indexOf(o)==-1||o.target.edges.indexOf(o)==-1)throw"Source and/or target doesn't know this edge!";if(g=o.source.edges.indexOf(o),o.source.edges.splice(g,1),g=o.target.edges.indexOf(o),o.target.edges.splice(g,1),o.source.owner==null||o.source.owner.getGraphManager()==null)throw"Edge owner graph or owner graph manager is null!";if(o.source.owner.getGraphManager().edges.indexOf(o)==-1)throw"Not in owner graph manager's edge list!";g=o.source.owner.getGraphManager().edges.indexOf(o),o.source.owner.getGraphManager().edges.splice(g,1)}},n.prototype.updateBounds=function(){this.rootGraph.updateBounds(!0)},n.prototype.getGraphs=function(){return this.graphs},n.prototype.getAllNodes=function(){if(this.allNodes==null){for(var r=[],i=this.getGraphs(),o=i.length,h=0;h<o;h++)r=r.concat(i[h].getNodes());this.allNodes=r}return this.allNodes},n.prototype.resetAllNodes=function(){this.allNodes=null},n.prototype.resetAllEdges=function(){this.allEdges=null},n.prototype.resetAllNodesToApplyGravitation=function(){this.allNodesToApplyGravitation=null},n.prototype.getAllEdges=function(){if(this.allEdges==null){var r=[],i=this.getGraphs();i.length;for(var o=0;o<i.length;o++)r=r.concat(i[o].getEdges());r=r.concat(this.edges),this.allEdges=r}return this.allEdges},n.prototype.getAllNodesToApplyGravitation=function(){return this.allNodesToApplyGravitation},n.prototype.setAllNodesToApplyGravitation=function(r){if(this.allNodesToApplyGravitation!=null)throw"assert failed";this.allNodesToApplyGravitation=r},n.prototype.getRoot=function(){return this.rootGraph},n.prototype.setRootGraph=function(r){if(r.getGraphManager()!=this)throw"Root not in this graph mgr!";this.rootGraph=r,r.parent==null&&(r.parent=this.layout.newNode("Root node"))},n.prototype.getLayout=function(){return this.layout},n.prototype.isOneAncestorOfOther=function(r,i){if(r==null||i==null)throw"assert failed";if(r==i)return!0;for(var o,h=r.getOwner();(o=h.getParent())!=null;){if(o==i)return!0;if((h=o.getOwner())==null)break}for(h=i.getOwner();(o=h.getParent())!=null;){if(o==r)return!0;if((h=o.getOwner())==null)break}return!1},n.prototype.calcLowestCommonAncestors=function(){for(var r,i,o,h,a,l=this.getAllEdges(),E=l.length,m=0;m<E;m++)if(i=(r=l[m]).source,o=r.target,r.lca=null,r.sourceInLca=i,r.targetInLca=o,i!=o){for(h=i.getOwner();r.lca==null;){for(r.targetInLca=o,a=o.getOwner();r.lca==null;){if(a==h){r.lca=a;break}if(a==this.rootGraph)break;if(r.lca!=null)throw"assert failed";r.targetInLca=a.getParent(),a=r.targetInLca.getOwner()}if(h==this.rootGraph)break;r.lca==null&&(r.sourceInLca=h.getParent(),h=r.sourceInLca.getOwner())}if(r.lca==null)throw"assert failed"}else r.lca=i.getOwner()},n.prototype.calcLowestCommonAncestor=function(r,i){if(r==i)return r.getOwner();for(var o=r.getOwner();o!=null;){for(var h=i.getOwner();h!=null;){if(h==o)return h;h=h.getParent().getOwner()}o=o.getParent().getOwner()}return o},n.prototype.calcInclusionTreeDepths=function(r,i){var o;r==null&&i==null&&(r=this.rootGraph,i=1);for(var h=r.getNodes(),a=h.length,l=0;l<a;l++)(o=h[l]).inclusionTreeDepth=i,o.child!=null&&this.calcInclusionTreeDepths(o.child,i+1)},n.prototype.includesInvalidEdge=function(){for(var r,i=this.edges.length,o=0;o<i;o++)if(r=this.edges[o],this.isOneAncestorOfOther(r.source,r.target))return!0;return!1},A.exports=n},function(A,f,d){var e=d(0);function t(){}for(var n in e)t[n]=e[n];t.MAX_ITERATIONS=2500,t.DEFAULT_EDGE_LENGTH=50,t.DEFAULT_SPRING_STRENGTH=.45,t.DEFAULT_REPULSION_STRENGTH=4500,t.DEFAULT_GRAVITY_STRENGTH=.4,t.DEFAULT_COMPOUND_GRAVITY_STRENGTH=1,t.DEFAULT_GRAVITY_RANGE_FACTOR=3.8,t.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=1.5,t.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION=!0,t.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION=!0,t.DEFAULT_COOLING_FACTOR_INCREMENTAL=.3,t.COOLING_ADAPTATION_FACTOR=.33,t.ADAPTATION_LOWER_NODE_LIMIT=1e3,t.ADAPTATION_UPPER_NODE_LIMIT=5e3,t.MAX_NODE_DISPLACEMENT_INCREMENTAL=100,t.MAX_NODE_DISPLACEMENT=3*t.MAX_NODE_DISPLACEMENT_INCREMENTAL,t.MIN_REPULSION_DIST=t.DEFAULT_EDGE_LENGTH/10,t.CONVERGENCE_CHECK_PERIOD=100,t.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=.1,t.MIN_EDGE_LENGTH=1,t.GRID_CALCULATION_CHECK_PERIOD=10,A.exports=t},function(A,f,d){var e=d(12);function t(){}t.calcSeparationAmount=function(n,r,i,o){if(!n.intersects(r))throw"assert failed";var h=new Array(2);this.decideDirectionsForOverlappingNodes(n,r,h),i[0]=Math.min(n.getRight(),r.getRight())-Math.max(n.x,r.x),i[1]=Math.min(n.getBottom(),r.getBottom())-Math.max(n.y,r.y),n.getX()<=r.getX()&&n.getRight()>=r.getRight()?i[0]+=Math.min(r.getX()-n.getX(),n.getRight()-r.getRight()):r.getX()<=n.getX()&&r.getRight()>=n.getRight()&&(i[0]+=Math.min(n.getX()-r.getX(),r.getRight()-n.getRight())),n.getY()<=r.getY()&&n.getBottom()>=r.getBottom()?i[1]+=Math.min(r.getY()-n.getY(),n.getBottom()-r.getBottom()):r.getY()<=n.getY()&&r.getBottom()>=n.getBottom()&&(i[1]+=Math.min(n.getY()-r.getY(),r.getBottom()-n.getBottom()));var a=Math.abs((r.getCenterY()-n.getCenterY())/(r.getCenterX()-n.getCenterX()));r.getCenterY()===n.getCenterY()&&r.getCenterX()===n.getCenterX()&&(a=1);var l=a*i[0],E=i[1]/a;i[0]<E?E=i[0]:l=i[1],i[0]=-1*h[0]*(E/2+o),i[1]=-1*h[1]*(l/2+o)},t.decideDirectionsForOverlappingNodes=function(n,r,i){n.getCenterX()<r.getCenterX()?i[0]=-1:i[0]=1,n.getCenterY()<r.getCenterY()?i[1]=-1:i[1]=1},t.getIntersection2=function(n,r,i){var o=n.getCenterX(),h=n.getCenterY(),a=r.getCenterX(),l=r.getCenterY();if(n.intersects(r))return i[0]=o,i[1]=h,i[2]=a,i[3]=l,!0;var E=n.getX(),m=n.getY(),g=n.getRight(),_=n.getX(),L=n.getBottom(),N=n.getRight(),T=n.getWidthHalf(),u=n.getHeightHalf(),I=r.getX(),s=r.getY(),c=r.getRight(),p=r.getX(),y=r.getBottom(),O=r.getRight(),D=r.getWidthHalf(),w=r.getHeightHalf(),R=!1,C=!1;if(o===a){if(h>l)return i[0]=o,i[1]=m,i[2]=a,i[3]=y,!1;if(h<l)return i[0]=o,i[1]=L,i[2]=a,i[3]=s,!1}else if(h===l){if(o>a)return i[0]=E,i[1]=h,i[2]=c,i[3]=l,!1;if(o<a)return i[0]=g,i[1]=h,i[2]=I,i[3]=l,!1}else{var M=n.height/n.width,S=r.height/r.width,G=(l-h)/(a-o),Y=void 0,F=void 0,P=void 0,b=void 0,k=void 0,U=void 0;if(-M===G?o>a?(i[0]=_,i[1]=L,R=!0):(i[0]=g,i[1]=m,R=!0):M===G&&(o>a?(i[0]=E,i[1]=m,R=!0):(i[0]=N,i[1]=L,R=!0)),-S===G?a>o?(i[2]=p,i[3]=y,C=!0):(i[2]=c,i[3]=s,C=!0):S===G&&(a>o?(i[2]=I,i[3]=s,C=!0):(i[2]=O,i[3]=y,C=!0)),R&&C)return!1;if(o>a?h>l?(Y=this.getCardinalDirection(M,G,4),F=this.getCardinalDirection(S,G,2)):(Y=this.getCardinalDirection(-M,G,3),F=this.getCardinalDirection(-S,G,1)):h>l?(Y=this.getCardinalDirection(-M,G,1),F=this.getCardinalDirection(-S,G,3)):(Y=this.getCardinalDirection(M,G,2),F=this.getCardinalDirection(S,G,4)),!R)switch(Y){case 1:b=m,P=o+-u/G,i[0]=P,i[1]=b;break;case 2:P=N,b=h+T*G,i[0]=P,i[1]=b;break;case 3:b=L,P=o+u/G,i[0]=P,i[1]=b;break;case 4:P=_,b=h+-T*G,i[0]=P,i[1]=b}if(!C)switch(F){case 1:U=s,k=a+-w/G,i[2]=k,i[3]=U;break;case 2:k=O,U=l+D*G,i[2]=k,i[3]=U;break;case 3:U=y,k=a+w/G,i[2]=k,i[3]=U;break;case 4:k=p,U=l+-D*G,i[2]=k,i[3]=U}}return!1},t.getCardinalDirection=function(n,r,i){return n>r?i:1+i%4},t.getIntersection=function(n,r,i,o){if(o==null)return this.getIntersection2(n,r,i);var h,a,l,E,m,g,_,L=n.x,N=n.y,T=r.x,u=r.y,I=i.x,s=i.y,c=o.x,p=o.y;return(_=(h=u-N)*(E=I-c)-(a=p-s)*(l=L-T))==0?null:new e((l*(g=c*s-I*p)-E*(m=T*N-L*u))/_,(a*m-h*g)/_)},t.angleOfVector=function(n,r,i,o){var h=void 0;return n!==i?(h=Math.atan((o-r)/(i-n)),i<n?h+=Math.PI:o<r&&(h+=this.TWO_PI)):h=o<r?this.ONE_AND_HALF_PI:this.HALF_PI,h},t.doIntersect=function(n,r,i,o){var h=n.x,a=n.y,l=r.x,E=r.y,m=i.x,g=i.y,_=o.x,L=o.y,N=(l-h)*(L-g)-(_-m)*(E-a);if(N===0)return!1;var T=((L-g)*(_-h)+(m-_)*(L-a))/N,u=((a-E)*(_-h)+(l-h)*(L-a))/N;return 0<T&&T<1&&0<u&&u<1},t.HALF_PI=.5*Math.PI,t.ONE_AND_HALF_PI=1.5*Math.PI,t.TWO_PI=2*Math.PI,t.THREE_PI=3*Math.PI,A.exports=t},function(A,f,d){function e(){}e.sign=function(t){return t>0?1:t<0?-1:0},e.floor=function(t){return t<0?Math.ceil(t):Math.floor(t)},e.ceil=function(t){return t<0?Math.floor(t):Math.ceil(t)},A.exports=e},function(A,f,d){function e(){}e.MAX_VALUE=2147483647,e.MIN_VALUE=-2147483648,A.exports=e},function(A,f,d){var e=function(){function o(h,a){for(var l=0;l<a.length;l++){var E=a[l];E.enumerable=E.enumerable||!1,E.configurable=!0,"value"in E&&(E.writable=!0),Object.defineProperty(h,E.key,E)}}return function(h,a,l){return a&&o(h.prototype,a),l&&o(h,l),h}}(),t=function(o){return{value:o,next:null,prev:null}},n=function(o,h,a,l){return o!==null?o.next=h:l.head=h,a!==null?a.prev=h:l.tail=h,h.prev=o,h.next=a,l.length++,h},r=function(o,h){var a=o.prev,l=o.next;return a!==null?a.next=l:h.head=l,l!==null?l.prev=a:h.tail=a,o.prev=o.next=null,h.length--,o},i=function(){function o(h){var a=this;(function(l,E){if(!(l instanceof E))throw new TypeError("Cannot call a class as a function")})(this,o),this.length=0,this.head=null,this.tail=null,h!=null&&h.forEach(function(l){return a.push(l)})}return e(o,[{key:"size",value:function(){return this.length}},{key:"insertBefore",value:function(h,a){return n(a.prev,t(h),a,this)}},{key:"insertAfter",value:function(h,a){return n(a,t(h),a.next,this)}},{key:"insertNodeBefore",value:function(h,a){return n(a.prev,h,a,this)}},{key:"insertNodeAfter",value:function(h,a){return n(a,h,a.next,this)}},{key:"push",value:function(h){return n(this.tail,t(h),null,this)}},{key:"unshift",value:function(h){return n(null,t(h),this.head,this)}},{key:"remove",value:function(h){return r(h,this)}},{key:"pop",value:function(){return r(this.tail,this).value}},{key:"popNode",value:function(){return r(this.tail,this)}},{key:"shift",value:function(){return r(this.head,this).value}},{key:"shiftNode",value:function(){return r(this.head,this)}},{key:"get_object_at",value:function(h){if(h<=this.length()){for(var a=1,l=this.head;a<h;)l=l.next,a++;return l.value}}},{key:"set_object_at",value:function(h,a){if(h<=this.length()){for(var l=1,E=this.head;l<h;)E=E.next,l++;E.value=a}}}]),o}();A.exports=i},function(A,f,d){function e(t,n,r){this.x=null,this.y=null,t==null&&n==null&&r==null?(this.x=0,this.y=0):typeof t=="number"&&typeof n=="number"&&r==null?(this.x=t,this.y=n):t.constructor.name=="Point"&&n==null&&r==null&&(r=t,this.x=r.x,this.y=r.y)}e.prototype.getX=function(){return this.x},e.prototype.getY=function(){return this.y},e.prototype.getLocation=function(){return new e(this.x,this.y)},e.prototype.setLocation=function(t,n,r){t.constructor.name=="Point"&&n==null&&r==null?(r=t,this.setLocation(r.x,r.y)):typeof t=="number"&&typeof n=="number"&&r==null&&(parseInt(t)==t&&parseInt(n)==n?this.move(t,n):(this.x=Math.floor(t+.5),this.y=Math.floor(n+.5)))},e.prototype.move=function(t,n){this.x=t,this.y=n},e.prototype.translate=function(t,n){this.x+=t,this.y+=n},e.prototype.equals=function(t){if(t.constructor.name=="Point"){var n=t;return this.x==n.x&&this.y==n.y}return this==t},e.prototype.toString=function(){return new e().constructor.name+"[x="+this.x+",y="+this.y+"]"},A.exports=e},function(A,f,d){function e(t,n,r,i){this.x=0,this.y=0,this.width=0,this.height=0,t!=null&&n!=null&&r!=null&&i!=null&&(this.x=t,this.y=n,this.width=r,this.height=i)}e.prototype.getX=function(){return this.x},e.prototype.setX=function(t){this.x=t},e.prototype.getY=function(){return this.y},e.prototype.setY=function(t){this.y=t},e.prototype.getWidth=function(){return this.width},e.prototype.setWidth=function(t){this.width=t},e.prototype.getHeight=function(){return this.height},e.prototype.setHeight=function(t){this.height=t},e.prototype.getRight=function(){return this.x+this.width},e.prototype.getBottom=function(){return this.y+this.height},e.prototype.intersects=function(t){return!(this.getRight()<t.x||this.getBottom()<t.y||t.getRight()<this.x||t.getBottom()<this.y)},e.prototype.getCenterX=function(){return this.x+this.width/2},e.prototype.getMinX=function(){return this.getX()},e.prototype.getMaxX=function(){return this.getX()+this.width},e.prototype.getCenterY=function(){return this.y+this.height/2},e.prototype.getMinY=function(){return this.getY()},e.prototype.getMaxY=function(){return this.getY()+this.height},e.prototype.getWidthHalf=function(){return this.width/2},e.prototype.getHeightHalf=function(){return this.height/2},A.exports=e},function(A,f,d){var e=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(n){return typeof n}:function(n){return n&&typeof Symbol=="function"&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n};function t(){}t.lastID=0,t.createID=function(n){return t.isPrimitive(n)?n:(n.uniqueID!=null||(n.uniqueID=t.getString(),t.lastID++),n.uniqueID)},t.getString=function(n){return n==null&&(n=t.lastID),"Object#"+n},t.isPrimitive=function(n){var r=n===void 0?"undefined":e(n);return n==null||r!="object"&&r!="function"},A.exports=t},function(A,f,d){function e(m){if(Array.isArray(m)){for(var g=0,_=Array(m.length);g<m.length;g++)_[g]=m[g];return _}return Array.from(m)}var t=d(0),n=d(6),r=d(3),i=d(1),o=d(5),h=d(4),a=d(17),l=d(27);function E(m){l.call(this),this.layoutQuality=t.QUALITY,this.createBendsAsNeeded=t.DEFAULT_CREATE_BENDS_AS_NEEDED,this.incremental=t.DEFAULT_INCREMENTAL,this.animationOnLayout=t.DEFAULT_ANIMATION_ON_LAYOUT,this.animationDuringLayout=t.DEFAULT_ANIMATION_DURING_LAYOUT,this.animationPeriod=t.DEFAULT_ANIMATION_PERIOD,this.uniformLeafNodeSizes=t.DEFAULT_UNIFORM_LEAF_NODE_SIZES,this.edgeToDummyNodes=new Map,this.graphManager=new n(this),this.isLayoutFinished=!1,this.isSubLayout=!1,this.isRemoteUse=!1,m!=null&&(this.isRemoteUse=m)}E.RANDOM_SEED=1,E.prototype=Object.create(l.prototype),E.prototype.getGraphManager=function(){return this.graphManager},E.prototype.getAllNodes=function(){return this.graphManager.getAllNodes()},E.prototype.getAllEdges=function(){return this.graphManager.getAllEdges()},E.prototype.getAllNodesToApplyGravitation=function(){return this.graphManager.getAllNodesToApplyGravitation()},E.prototype.newGraphManager=function(){var m=new n(this);return this.graphManager=m,m},E.prototype.newGraph=function(m){return new o(null,this.graphManager,m)},E.prototype.newNode=function(m){return new r(this.graphManager,m)},E.prototype.newEdge=function(m){return new i(null,null,m)},E.prototype.checkLayoutSuccess=function(){return this.graphManager.getRoot()==null||this.graphManager.getRoot().getNodes().length==0||this.graphManager.includesInvalidEdge()},E.prototype.runLayout=function(){var m;return this.isLayoutFinished=!1,this.tilingPreLayout&&this.tilingPreLayout(),this.initParameters(),m=!this.checkLayoutSuccess()&&this.layout(),t.ANIMATE!=="during"&&(m&&(this.isSubLayout||this.doPostLayout()),this.tilingPostLayout&&this.tilingPostLayout(),this.isLayoutFinished=!0,m)},E.prototype.doPostLayout=function(){this.incremental||this.transform(),this.update()},E.prototype.update2=function(){if(this.createBendsAsNeeded&&(this.createBendpointsFromDummyNodes(),this.graphManager.resetAllEdges()),!this.isRemoteUse){for(var m=this.graphManager.getAllEdges(),g=0;g<m.length;g++)m[g];var _=this.graphManager.getRoot().getNodes();for(g=0;g<_.length;g++)_[g];this.update(this.graphManager.getRoot())}},E.prototype.update=function(m){if(m==null)this.update2();else if(m instanceof r){var g=m;if(g.getChild()!=null)for(var _=g.getChild().getNodes(),L=0;L<_.length;L++)update(_[L]);g.vGraphObject!=null&&g.vGraphObject.update(g)}else if(m instanceof i){var N=m;N.vGraphObject!=null&&N.vGraphObject.update(N)}else if(m instanceof o){var T=m;T.vGraphObject!=null&&T.vGraphObject.update(T)}},E.prototype.initParameters=function(){this.isSubLayout||(this.layoutQuality=t.QUALITY,this.animationDuringLayout=t.DEFAULT_ANIMATION_DURING_LAYOUT,this.animationPeriod=t.DEFAULT_ANIMATION_PERIOD,this.animationOnLayout=t.DEFAULT_ANIMATION_ON_LAYOUT,this.incremental=t.DEFAULT_INCREMENTAL,this.createBendsAsNeeded=t.DEFAULT_CREATE_BENDS_AS_NEEDED,this.uniformLeafNodeSizes=t.DEFAULT_UNIFORM_LEAF_NODE_SIZES),this.animationDuringLayout&&(this.animationOnLayout=!1)},E.prototype.transform=function(m){if(m==null)this.transform(new h(0,0));else{var g=new a,_=this.graphManager.getRoot().updateLeftTop();if(_!=null){g.setWorldOrgX(m.x),g.setWorldOrgY(m.y),g.setDeviceOrgX(_.x),g.setDeviceOrgY(_.y);for(var L=this.getAllNodes(),N=0;N<L.length;N++)L[N].transform(g)}}},E.prototype.positionNodesRandomly=function(m){if(m==null)this.positionNodesRandomly(this.getGraphManager().getRoot()),this.getGraphManager().getRoot().updateBounds(!0);else for(var g,_,L=m.getNodes(),N=0;N<L.length;N++)(_=(g=L[N]).getChild())==null||_.getNodes().length==0?g.scatter():(this.positionNodesRandomly(_),g.updateBounds())},E.prototype.getFlatForest=function(){for(var m=[],g=!0,_=this.graphManager.getRoot().getNodes(),L=!0,N=0;N<_.length;N++)_[N].getChild()!=null&&(L=!1);if(!L)return m;var T=new Set,u=[],I=new Map,s=[];for(s=s.concat(_);s.length>0&&g;){for(u.push(s[0]);u.length>0&&g;){var c=u[0];u.splice(0,1),T.add(c);var p=c.getEdges();for(N=0;N<p.length;N++){var y=p[N].getOtherEnd(c);if(I.get(c)!=y){if(T.has(y)){g=!1;break}u.push(y),I.set(y,c)}}}if(g){var O=[].concat(e(T));for(m.push(O),N=0;N<O.length;N++){var D=O[N],w=s.indexOf(D);w>-1&&s.splice(w,1)}T=new Set,I=new Map}else m=[]}return m},E.prototype.createDummyNodesForBendpoints=function(m){for(var g=[],_=m.source,L=this.graphManager.calcLowestCommonAncestor(m.source,m.target),N=0;N<m.bendpoints.length;N++){var T=this.newNode(null);T.setRect(new Point(0,0),new Dimension(1,1)),L.add(T);var u=this.newEdge(null);this.graphManager.add(u,_,T),g.add(T),_=T}return u=this.newEdge(null),this.graphManager.add(u,_,m.target),this.edgeToDummyNodes.set(m,g),m.isInterGraph()?this.graphManager.remove(m):L.remove(m),g},E.prototype.createBendpointsFromDummyNodes=function(){var m=[];m=m.concat(this.graphManager.getAllEdges()),m=[].concat(e(this.edgeToDummyNodes.keys())).concat(m);for(var g=0;g<m.length;g++){var _=m[g];if(_.bendpoints.length>0){for(var L=this.edgeToDummyNodes.get(_),N=0;N<L.length;N++){var T=L[N],u=new h(T.getCenterX(),T.getCenterY()),I=_.bendpoints.get(N);I.x=u.x,I.y=u.y,T.getOwner().remove(T)}this.graphManager.add(_,_.source,_.target)}}},E.transform=function(m,g,_,L){if(_!=null&&L!=null){var N=g;return m<=50?N-=(g-g/_)/50*(50-m):N+=(g*L-g)/50*(m-50),N}var T,u;return m<=50?(T=9*g/500,u=g/10):(T=9*g/50,u=-8*g),T*m+u},E.findCenterOfTree=function(m){var g=[];g=g.concat(m);var _=[],L=new Map,N=!1,T=null;g.length!=1&&g.length!=2||(N=!0,T=g[0]);for(var u=0;u<g.length;u++){var I=(p=g[u]).getNeighborsList().size;L.set(p,p.getNeighborsList().size),I==1&&_.push(p)}var s=[];for(s=s.concat(_);!N;){var c=[];for(c=c.concat(s),s=[],u=0;u<g.length;u++){var p=g[u],y=g.indexOf(p);y>=0&&g.splice(y,1),p.getNeighborsList().forEach(function(O){if(_.indexOf(O)<0){var D=L.get(O)-1;D==1&&s.push(O),L.set(O,D)}})}_=_.concat(s),g.length!=1&&g.length!=2||(N=!0,T=g[0])}return T},E.prototype.setGraphManager=function(m){this.graphManager=m},A.exports=E},function(A,f,d){function e(){}e.seed=1,e.x=0,e.nextDouble=function(){return e.x=1e4*Math.sin(e.seed++),e.x-Math.floor(e.x)},A.exports=e},function(A,f,d){var e=d(4);function t(n,r){this.lworldOrgX=0,this.lworldOrgY=0,this.ldeviceOrgX=0,this.ldeviceOrgY=0,this.lworldExtX=1,this.lworldExtY=1,this.ldeviceExtX=1,this.ldeviceExtY=1}t.prototype.getWorldOrgX=function(){return this.lworldOrgX},t.prototype.setWorldOrgX=function(n){this.lworldOrgX=n},t.prototype.getWorldOrgY=function(){return this.lworldOrgY},t.prototype.setWorldOrgY=function(n){this.lworldOrgY=n},t.prototype.getWorldExtX=function(){return this.lworldExtX},t.prototype.setWorldExtX=function(n){this.lworldExtX=n},t.prototype.getWorldExtY=function(){return this.lworldExtY},t.prototype.setWorldExtY=function(n){this.lworldExtY=n},t.prototype.getDeviceOrgX=function(){return this.ldeviceOrgX},t.prototype.setDeviceOrgX=function(n){this.ldeviceOrgX=n},t.prototype.getDeviceOrgY=function(){return this.ldeviceOrgY},t.prototype.setDeviceOrgY=function(n){this.ldeviceOrgY=n},t.prototype.getDeviceExtX=function(){return this.ldeviceExtX},t.prototype.setDeviceExtX=function(n){this.ldeviceExtX=n},t.prototype.getDeviceExtY=function(){return this.ldeviceExtY},t.prototype.setDeviceExtY=function(n){this.ldeviceExtY=n},t.prototype.transformX=function(n){var r=0,i=this.lworldExtX;return i!=0&&(r=this.ldeviceOrgX+(n-this.lworldOrgX)*this.ldeviceExtX/i),r},t.prototype.transformY=function(n){var r=0,i=this.lworldExtY;return i!=0&&(r=this.ldeviceOrgY+(n-this.lworldOrgY)*this.ldeviceExtY/i),r},t.prototype.inverseTransformX=function(n){var r=0,i=this.ldeviceExtX;return i!=0&&(r=this.lworldOrgX+(n-this.ldeviceOrgX)*this.lworldExtX/i),r},t.prototype.inverseTransformY=function(n){var r=0,i=this.ldeviceExtY;return i!=0&&(r=this.lworldOrgY+(n-this.ldeviceOrgY)*this.lworldExtY/i),r},t.prototype.inverseTransformPoint=function(n){return new e(this.inverseTransformX(n.x),this.inverseTransformY(n.y))},A.exports=t},function(A,f,d){var e=d(15),t=d(7),n=d(0),r=d(8),i=d(9);function o(){e.call(this),this.useSmartIdealEdgeLengthCalculation=t.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION,this.idealEdgeLength=t.DEFAULT_EDGE_LENGTH,this.springConstant=t.DEFAULT_SPRING_STRENGTH,this.repulsionConstant=t.DEFAULT_REPULSION_STRENGTH,this.gravityConstant=t.DEFAULT_GRAVITY_STRENGTH,this.compoundGravityConstant=t.DEFAULT_COMPOUND_GRAVITY_STRENGTH,this.gravityRangeFactor=t.DEFAULT_GRAVITY_RANGE_FACTOR,this.compoundGravityRangeFactor=t.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR,this.displacementThresholdPerNode=3*t.DEFAULT_EDGE_LENGTH/100,this.coolingFactor=t.DEFAULT_COOLING_FACTOR_INCREMENTAL,this.initialCoolingFactor=t.DEFAULT_COOLING_FACTOR_INCREMENTAL,this.totalDisplacement=0,this.oldTotalDisplacement=0,this.maxIterations=t.MAX_ITERATIONS}for(var h in o.prototype=Object.create(e.prototype),e)o[h]=e[h];o.prototype.initParameters=function(){e.prototype.initParameters.call(this,arguments),this.totalIterations=0,this.notAnimatedIterations=0,this.useFRGridVariant=t.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION,this.grid=[]},o.prototype.calcIdealEdgeLengths=function(){for(var a,l,E,m,g,_,L=this.getGraphManager().getAllEdges(),N=0;N<L.length;N++)(a=L[N]).idealLength=this.idealEdgeLength,a.isInterGraph&&(E=a.getSource(),m=a.getTarget(),g=a.getSourceInLca().getEstimatedSize(),_=a.getTargetInLca().getEstimatedSize(),this.useSmartIdealEdgeLengthCalculation&&(a.idealLength+=g+_-2*n.SIMPLE_NODE_SIZE),l=a.getLca().getInclusionTreeDepth(),a.idealLength+=t.DEFAULT_EDGE_LENGTH*t.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR*(E.getInclusionTreeDepth()+m.getInclusionTreeDepth()-2*l))},o.prototype.initSpringEmbedder=function(){var a=this.getAllNodes().length;this.incremental?(a>t.ADAPTATION_LOWER_NODE_LIMIT&&(this.coolingFactor=Math.max(this.coolingFactor*t.COOLING_ADAPTATION_FACTOR,this.coolingFactor-(a-t.ADAPTATION_LOWER_NODE_LIMIT)/(t.ADAPTATION_UPPER_NODE_LIMIT-t.ADAPTATION_LOWER_NODE_LIMIT)*this.coolingFactor*(1-t.COOLING_ADAPTATION_FACTOR))),this.maxNodeDisplacement=t.MAX_NODE_DISPLACEMENT_INCREMENTAL):(a>t.ADAPTATION_LOWER_NODE_LIMIT?this.coolingFactor=Math.max(t.COOLING_ADAPTATION_FACTOR,1-(a-t.ADAPTATION_LOWER_NODE_LIMIT)/(t.ADAPTATION_UPPER_NODE_LIMIT-t.ADAPTATION_LOWER_NODE_LIMIT)*(1-t.COOLING_ADAPTATION_FACTOR)):this.coolingFactor=1,this.initialCoolingFactor=this.coolingFactor,this.maxNodeDisplacement=t.MAX_NODE_DISPLACEMENT),this.maxIterations=Math.max(5*this.getAllNodes().length,this.maxIterations),this.totalDisplacementThreshold=this.displacementThresholdPerNode*this.getAllNodes().length,this.repulsionRange=this.calcRepulsionRange()},o.prototype.calcSpringForces=function(){for(var a,l=this.getAllEdges(),E=0;E<l.length;E++)a=l[E],this.calcSpringForce(a,a.idealLength)},o.prototype.calcRepulsionForces=function(){var a,l,E,m,g,_=!(arguments.length>0&&arguments[0]!==void 0)||arguments[0],L=arguments.length>1&&arguments[1]!==void 0&&arguments[1],N=this.getAllNodes();if(this.useFRGridVariant)for(this.totalIterations%t.GRID_CALCULATION_CHECK_PERIOD==1&&_&&this.updateGrid(),g=new Set,a=0;a<N.length;a++)E=N[a],this.calculateRepulsionForceOfANode(E,g,_,L),g.add(E);else for(a=0;a<N.length;a++)for(E=N[a],l=a+1;l<N.length;l++)m=N[l],E.getOwner()==m.getOwner()&&this.calcRepulsionForce(E,m)},o.prototype.calcGravitationalForces=function(){for(var a,l=this.getAllNodesToApplyGravitation(),E=0;E<l.length;E++)a=l[E],this.calcGravitationalForce(a)},o.prototype.moveNodes=function(){for(var a=this.getAllNodes(),l=0;l<a.length;l++)a[l].move()},o.prototype.calcSpringForce=function(a,l){var E,m,g,_,L=a.getSource(),N=a.getTarget();if(this.uniformLeafNodeSizes&&L.getChild()==null&&N.getChild()==null)a.updateLengthSimple();else if(a.updateLength(),a.isOverlapingSourceAndTarget)return;(E=a.getLength())!=0&&(g=(m=this.springConstant*(E-l))*(a.lengthX/E),_=m*(a.lengthY/E),L.springForceX+=g,L.springForceY+=_,N.springForceX-=g,N.springForceY-=_)},o.prototype.calcRepulsionForce=function(a,l){var E,m,g,_,L,N,T,u=a.getRect(),I=l.getRect(),s=new Array(2),c=new Array(4);if(u.intersects(I)){r.calcSeparationAmount(u,I,s,t.DEFAULT_EDGE_LENGTH/2),N=2*s[0],T=2*s[1];var p=a.noOfChildren*l.noOfChildren/(a.noOfChildren+l.noOfChildren);a.repulsionForceX-=p*N,a.repulsionForceY-=p*T,l.repulsionForceX+=p*N,l.repulsionForceY+=p*T}else this.uniformLeafNodeSizes&&a.getChild()==null&&l.getChild()==null?(E=I.getCenterX()-u.getCenterX(),m=I.getCenterY()-u.getCenterY()):(r.getIntersection(u,I,c),E=c[2]-c[0],m=c[3]-c[1]),Math.abs(E)<t.MIN_REPULSION_DIST&&(E=i.sign(E)*t.MIN_REPULSION_DIST),Math.abs(m)<t.MIN_REPULSION_DIST&&(m=i.sign(m)*t.MIN_REPULSION_DIST),g=E*E+m*m,_=Math.sqrt(g),N=(L=this.repulsionConstant*a.noOfChildren*l.noOfChildren/g)*E/_,T=L*m/_,a.repulsionForceX-=N,a.repulsionForceY-=T,l.repulsionForceX+=N,l.repulsionForceY+=T},o.prototype.calcGravitationalForce=function(a){var l,E,m,g,_,L,N,T;E=((l=a.getOwner()).getRight()+l.getLeft())/2,m=(l.getTop()+l.getBottom())/2,g=a.getCenterX()-E,_=a.getCenterY()-m,L=Math.abs(g)+a.getWidth()/2,N=Math.abs(_)+a.getHeight()/2,a.getOwner()==this.graphManager.getRoot()?(L>(T=l.getEstimatedSize()*this.gravityRangeFactor)||N>T)&&(a.gravitationForceX=-this.gravityConstant*g,a.gravitationForceY=-this.gravityConstant*_):(L>(T=l.getEstimatedSize()*this.compoundGravityRangeFactor)||N>T)&&(a.gravitationForceX=-this.gravityConstant*g*this.compoundGravityConstant,a.gravitationForceY=-this.gravityConstant*_*this.compoundGravityConstant)},o.prototype.isConverged=function(){var a,l=!1;return this.totalIterations>this.maxIterations/3&&(l=Math.abs(this.totalDisplacement-this.oldTotalDisplacement)<2),a=this.totalDisplacement<this.totalDisplacementThreshold,this.oldTotalDisplacement=this.totalDisplacement,a||l},o.prototype.animate=function(){this.animationDuringLayout&&!this.isSubLayout&&(this.notAnimatedIterations==this.animationPeriod?(this.update(),this.notAnimatedIterations=0):this.notAnimatedIterations++)},o.prototype.calcNoOfChildrenForAllNodes=function(){for(var a,l=this.graphManager.getAllNodes(),E=0;E<l.length;E++)(a=l[E]).noOfChildren=a.getNoOfChildren()},o.prototype.calcGrid=function(a){var l,E;l=parseInt(Math.ceil((a.getRight()-a.getLeft())/this.repulsionRange)),E=parseInt(Math.ceil((a.getBottom()-a.getTop())/this.repulsionRange));for(var m=new Array(l),g=0;g<l;g++)m[g]=new Array(E);for(g=0;g<l;g++)for(var _=0;_<E;_++)m[g][_]=new Array;return m},o.prototype.addNodeToGrid=function(a,l,E){var m,g,_,L;m=parseInt(Math.floor((a.getRect().x-l)/this.repulsionRange)),g=parseInt(Math.floor((a.getRect().width+a.getRect().x-l)/this.repulsionRange)),_=parseInt(Math.floor((a.getRect().y-E)/this.repulsionRange)),L=parseInt(Math.floor((a.getRect().height+a.getRect().y-E)/this.repulsionRange));for(var N=m;N<=g;N++)for(var T=_;T<=L;T++)this.grid[N][T].push(a),a.setGridCoordinates(m,g,_,L)},o.prototype.updateGrid=function(){var a,l,E=this.getAllNodes();for(this.grid=this.calcGrid(this.graphManager.getRoot()),a=0;a<E.length;a++)l=E[a],this.addNodeToGrid(l,this.graphManager.getRoot().getLeft(),this.graphManager.getRoot().getTop())},o.prototype.calculateRepulsionForceOfANode=function(a,l,E,m){if(this.totalIterations%t.GRID_CALCULATION_CHECK_PERIOD==1&&E||m){var g,_=new Set;a.surrounding=new Array;for(var L=this.grid,N=a.startX-1;N<a.finishX+2;N++)for(var T=a.startY-1;T<a.finishY+2;T++)if(!(N<0||T<0||N>=L.length||T>=L[0].length)){for(var u=0;u<L[N][T].length;u++)if(g=L[N][T][u],a.getOwner()==g.getOwner()&&a!=g&&!l.has(g)&&!_.has(g)){var I=Math.abs(a.getCenterX()-g.getCenterX())-(a.getWidth()/2+g.getWidth()/2),s=Math.abs(a.getCenterY()-g.getCenterY())-(a.getHeight()/2+g.getHeight()/2);I<=this.repulsionRange&&s<=this.repulsionRange&&_.add(g)}}a.surrounding=[].concat(function(c){if(Array.isArray(c)){for(var p=0,y=Array(c.length);p<c.length;p++)y[p]=c[p];return y}return Array.from(c)}(_))}for(N=0;N<a.surrounding.length;N++)this.calcRepulsionForce(a,a.surrounding[N])},o.prototype.calcRepulsionRange=function(){return 0},A.exports=o},function(A,f,d){var e=d(1),t=d(7);function n(i,o,h){e.call(this,i,o,h),this.idealLength=t.DEFAULT_EDGE_LENGTH}for(var r in n.prototype=Object.create(e.prototype),e)n[r]=e[r];A.exports=n},function(A,f,d){var e=d(3);function t(r,i,o,h){e.call(this,r,i,o,h),this.springForceX=0,this.springForceY=0,this.repulsionForceX=0,this.repulsionForceY=0,this.gravitationForceX=0,this.gravitationForceY=0,this.displacementX=0,this.displacementY=0,this.startX=0,this.finishX=0,this.startY=0,this.finishY=0,this.surrounding=[]}for(var n in t.prototype=Object.create(e.prototype),e)t[n]=e[n];t.prototype.setGridCoordinates=function(r,i,o,h){this.startX=r,this.finishX=i,this.startY=o,this.finishY=h},A.exports=t},function(A,f,d){function e(t,n){this.width=0,this.height=0,t!==null&&n!==null&&(this.height=n,this.width=t)}e.prototype.getWidth=function(){return this.width},e.prototype.setWidth=function(t){this.width=t},e.prototype.getHeight=function(){return this.height},e.prototype.setHeight=function(t){this.height=t},A.exports=e},function(A,f,d){var e=d(14);function t(){this.map={},this.keys=[]}t.prototype.put=function(n,r){var i=e.createID(n);this.contains(i)||(this.map[i]=r,this.keys.push(n))},t.prototype.contains=function(n){return e.createID(n),this.map[n]!=null},t.prototype.get=function(n){var r=e.createID(n);return this.map[r]},t.prototype.keySet=function(){return this.keys},A.exports=t},function(A,f,d){var e=d(14);function t(){this.set={}}t.prototype.add=function(n){var r=e.createID(n);this.contains(r)||(this.set[r]=n)},t.prototype.remove=function(n){delete this.set[e.createID(n)]},t.prototype.clear=function(){this.set={}},t.prototype.contains=function(n){return this.set[e.createID(n)]==n},t.prototype.isEmpty=function(){return this.size()===0},t.prototype.size=function(){return Object.keys(this.set).length},t.prototype.addAllTo=function(n){for(var r=Object.keys(this.set),i=r.length,o=0;o<i;o++)n.push(this.set[r[o]])},t.prototype.size=function(){return Object.keys(this.set).length},t.prototype.addAll=function(n){for(var r=n.length,i=0;i<r;i++){var o=n[i];this.add(o)}},A.exports=t},function(A,f,d){var e=function(){function r(i,o){for(var h=0;h<o.length;h++){var a=o[h];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(i,a.key,a)}}return function(i,o,h){return o&&r(i.prototype,o),h&&r(i,h),i}}(),t=d(11),n=function(){function r(i,o){(function(a,l){if(!(a instanceof l))throw new TypeError("Cannot call a class as a function")})(this,r),o===null&&o===void 0||(this.compareFunction=this._defaultCompareFunction);var h=void 0;h=i instanceof t?i.size():i.length,this._quicksort(i,0,h-1)}return e(r,[{key:"_quicksort",value:function(i,o,h){if(o<h){var a=this._partition(i,o,h);this._quicksort(i,o,a),this._quicksort(i,a+1,h)}}},{key:"_partition",value:function(i,o,h){for(var a=this._get(i,o),l=o,E=h;;){for(;this.compareFunction(a,this._get(i,E));)E--;for(;this.compareFunction(this._get(i,l),a);)l++;if(!(l<E))return E;this._swap(i,l,E),l++,E--}}},{key:"_get",value:function(i,o){return i instanceof t?i.get_object_at(o):i[o]}},{key:"_set",value:function(i,o,h){i instanceof t?i.set_object_at(o,h):i[o]=h}},{key:"_swap",value:function(i,o,h){var a=this._get(i,o);this._set(i,o,this._get(i,h)),this._set(i,h,a)}},{key:"_defaultCompareFunction",value:function(i,o){return o>i}}]),r}();A.exports=n},function(A,f,d){var e=function(){function n(r,i){for(var o=0;o<i.length;o++){var h=i[o];h.enumerable=h.enumerable||!1,h.configurable=!0,"value"in h&&(h.writable=!0),Object.defineProperty(r,h.key,h)}}return function(r,i,o){return i&&n(r.prototype,i),o&&n(r,o),r}}(),t=function(){function n(r,i){var o=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1,h=arguments.length>3&&arguments[3]!==void 0?arguments[3]:-1,a=arguments.length>4&&arguments[4]!==void 0?arguments[4]:-1;(function(_,L){if(!(_ instanceof L))throw new TypeError("Cannot call a class as a function")})(this,n),this.sequence1=r,this.sequence2=i,this.match_score=o,this.mismatch_penalty=h,this.gap_penalty=a,this.iMax=r.length+1,this.jMax=i.length+1,this.grid=new Array(this.iMax);for(var l=0;l<this.iMax;l++){this.grid[l]=new Array(this.jMax);for(var E=0;E<this.jMax;E++)this.grid[l][E]=0}this.tracebackGrid=new Array(this.iMax);for(var m=0;m<this.iMax;m++){this.tracebackGrid[m]=new Array(this.jMax);for(var g=0;g<this.jMax;g++)this.tracebackGrid[m][g]=[null,null,null]}this.alignments=[],this.score=-1,this.computeGrids()}return e(n,[{key:"getScore",value:function(){return this.score}},{key:"getAlignments",value:function(){return this.alignments}},{key:"computeGrids",value:function(){for(var r=1;r<this.jMax;r++)this.grid[0][r]=this.grid[0][r-1]+this.gap_penalty,this.tracebackGrid[0][r]=[!1,!1,!0];for(var i=1;i<this.iMax;i++)this.grid[i][0]=this.grid[i-1][0]+this.gap_penalty,this.tracebackGrid[i][0]=[!1,!0,!1];for(var o=1;o<this.iMax;o++)for(var h=1;h<this.jMax;h++){var a=[this.sequence1[o-1]===this.sequence2[h-1]?this.grid[o-1][h-1]+this.match_score:this.grid[o-1][h-1]+this.mismatch_penalty,this.grid[o-1][h]+this.gap_penalty,this.grid[o][h-1]+this.gap_penalty],l=this.arrayAllMaxIndexes(a);this.grid[o][h]=a[l[0]],this.tracebackGrid[o][h]=[l.includes(0),l.includes(1),l.includes(2)]}this.score=this.grid[this.iMax-1][this.jMax-1]}},{key:"alignmentTraceback",value:function(){var r=[];for(r.push({pos:[this.sequence1.length,this.sequence2.length],seq1:"",seq2:""});r[0];){var i=r[0],o=this.tracebackGrid[i.pos[0]][i.pos[1]];o[0]&&r.push({pos:[i.pos[0]-1,i.pos[1]-1],seq1:this.sequence1[i.pos[0]-1]+i.seq1,seq2:this.sequence2[i.pos[1]-1]+i.seq2}),o[1]&&r.push({pos:[i.pos[0]-1,i.pos[1]],seq1:this.sequence1[i.pos[0]-1]+i.seq1,seq2:"-"+i.seq2}),o[2]&&r.push({pos:[i.pos[0],i.pos[1]-1],seq1:"-"+i.seq1,seq2:this.sequence2[i.pos[1]-1]+i.seq2}),i.pos[0]===0&&i.pos[1]===0&&this.alignments.push({sequence1:i.seq1,sequence2:i.seq2}),r.shift()}return this.alignments}},{key:"getAllIndexes",value:function(r,i){for(var o=[],h=-1;(h=r.indexOf(i,h+1))!==-1;)o.push(h);return o}},{key:"arrayAllMaxIndexes",value:function(r){return this.getAllIndexes(r,Math.max.apply(null,r))}}]),n}();A.exports=t},function(A,f,d){var e=function(){};e.FDLayout=d(18),e.FDLayoutConstants=d(7),e.FDLayoutEdge=d(19),e.FDLayoutNode=d(20),e.DimensionD=d(21),e.HashMap=d(22),e.HashSet=d(23),e.IGeometry=d(8),e.IMath=d(9),e.Integer=d(10),e.Point=d(12),e.PointD=d(4),e.RandomSeed=d(16),e.RectangleD=d(13),e.Transform=d(17),e.UniqueIDGeneretor=d(14),e.Quicksort=d(24),e.LinkedList=d(11),e.LGraphObject=d(2),e.LGraph=d(5),e.LEdge=d(1),e.LGraphManager=d(6),e.LNode=d(3),e.Layout=d(15),e.LayoutConstants=d(0),e.NeedlemanWunsch=d(25),A.exports=e},function(A,f,d){function e(){this.listeners=[]}var t=e.prototype;t.addListener=function(n,r){this.listeners.push({event:n,callback:r})},t.removeListener=function(n,r){for(var i=this.listeners.length;i>=0;i--){var o=this.listeners[i];o.event===n&&o.callback===r&&this.listeners.splice(i,1)}},t.emit=function(n,r){for(var i=0;i<this.listeners.length;i++){var o=this.listeners[i];n===o.event&&o.callback(r)}},A.exports=e}])},lt.exports=v()),lt.exports;var v}at=function(v){return function(A){var f={};function d(e){if(f[e])return f[e].exports;var t=f[e]={i:e,l:!1,exports:{}};return A[e].call(t.exports,t,t.exports,d),t.l=!0,t.exports}return d.m=A,d.c=f,d.i=function(e){return e},d.d=function(e,t,n){d.o(e,t)||Object.defineProperty(e,t,{configurable:!1,enumerable:!0,get:n})},d.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return d.d(t,"a",t),t},d.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},d.p="",d(d.s=1)}([function(A,f){A.exports=v},function(A,f,d){var e=d(0).layoutBase.LayoutConstants,t=d(0).layoutBase.FDLayoutConstants,n=d(0).CoSEConstants,r=d(0).CoSELayout,i=d(0).CoSENode,o=d(0).layoutBase.PointD,h=d(0).layoutBase.DimensionD,a={ready:function(){},stop:function(){},quality:"default",nodeDimensionsIncludeLabels:!1,refresh:30,fit:!0,padding:10,randomize:!0,nodeRepulsion:4500,idealEdgeLength:50,edgeElasticity:.45,nestingFactor:.1,gravity:.25,numIter:2500,tile:!0,animate:"end",animationDuration:500,tilingPaddingVertical:10,tilingPaddingHorizontal:10,gravityRangeCompound:1.5,gravityCompound:1,gravityRange:3.8,initialEnergyOnIncremental:.5};function l(g){this.options=function(_,L){var N={};for(var T in _)N[T]=_[T];for(var T in L)N[T]=L[T];return N}(a,g),E(this.options)}var E=function(g){g.nodeRepulsion!=null&&(n.DEFAULT_REPULSION_STRENGTH=t.DEFAULT_REPULSION_STRENGTH=g.nodeRepulsion),g.idealEdgeLength!=null&&(n.DEFAULT_EDGE_LENGTH=t.DEFAULT_EDGE_LENGTH=g.idealEdgeLength),g.edgeElasticity!=null&&(n.DEFAULT_SPRING_STRENGTH=t.DEFAULT_SPRING_STRENGTH=g.edgeElasticity),g.nestingFactor!=null&&(n.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=t.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=g.nestingFactor),g.gravity!=null&&(n.DEFAULT_GRAVITY_STRENGTH=t.DEFAULT_GRAVITY_STRENGTH=g.gravity),g.numIter!=null&&(n.MAX_ITERATIONS=t.MAX_ITERATIONS=g.numIter),g.gravityRange!=null&&(n.DEFAULT_GRAVITY_RANGE_FACTOR=t.DEFAULT_GRAVITY_RANGE_FACTOR=g.gravityRange),g.gravityCompound!=null&&(n.DEFAULT_COMPOUND_GRAVITY_STRENGTH=t.DEFAULT_COMPOUND_GRAVITY_STRENGTH=g.gravityCompound),g.gravityRangeCompound!=null&&(n.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=t.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=g.gravityRangeCompound),g.initialEnergyOnIncremental!=null&&(n.DEFAULT_COOLING_FACTOR_INCREMENTAL=t.DEFAULT_COOLING_FACTOR_INCREMENTAL=g.initialEnergyOnIncremental),g.quality=="draft"?e.QUALITY=0:g.quality=="proof"?e.QUALITY=2:e.QUALITY=1,n.NODE_DIMENSIONS_INCLUDE_LABELS=t.NODE_DIMENSIONS_INCLUDE_LABELS=e.NODE_DIMENSIONS_INCLUDE_LABELS=g.nodeDimensionsIncludeLabels,n.DEFAULT_INCREMENTAL=t.DEFAULT_INCREMENTAL=e.DEFAULT_INCREMENTAL=!g.randomize,n.ANIMATE=t.ANIMATE=e.ANIMATE=g.animate,n.TILE=g.tile,n.TILING_PADDING_VERTICAL=typeof g.tilingPaddingVertical=="function"?g.tilingPaddingVertical.call():g.tilingPaddingVertical,n.TILING_PADDING_HORIZONTAL=typeof g.tilingPaddingHorizontal=="function"?g.tilingPaddingHorizontal.call():g.tilingPaddingHorizontal};l.prototype.run=function(){var g,_,L=this.options;this.idToLNode={};var N=this.layout=new r,T=this;T.stopped=!1,this.cy=this.options.cy,this.cy.trigger({type:"layoutstart",layout:this});var u=N.newGraphManager();this.gm=u;var I=this.options.eles.nodes(),s=this.options.eles.edges();this.root=u.addRoot(),this.processChildrenList(this.root,this.getTopMostNodes(I),N);for(var c=0;c<s.length;c++){var p=s[c],y=this.idToLNode[p.data("source")],O=this.idToLNode[p.data("target")];y!==O&&y.getEdgesBetween(O).length==0&&(u.add(N.newEdge(),y,O).id=p.id())}var D=function(R,C){typeof R=="number"&&(R=C);var M=R.data("id"),S=T.idToLNode[M];return{x:S.getRect().getCenterX(),y:S.getRect().getCenterY()}},w=function R(){for(var C,M=function(){L.fit&&L.cy.fit(L.eles,L.padding),g||(g=!0,T.cy.one("layoutready",L.ready),T.cy.trigger({type:"layoutready",layout:T}))},S=T.options.refresh,G=0;G<S&&!C;G++)C=T.stopped||T.layout.tick();if(C)return N.checkLayoutSuccess()&&!N.isSubLayout&&N.doPostLayout(),N.tilingPostLayout&&N.tilingPostLayout(),N.isLayoutFinished=!0,T.options.eles.nodes().positions(D),M(),T.cy.one("layoutstop",T.options.stop),T.cy.trigger({type:"layoutstop",layout:T}),_&&cancelAnimationFrame(_),void(g=!1);var Y=T.layout.getPositionsData();L.eles.nodes().positions(function(F,P){if(typeof F=="number"&&(F=P),!F.isParent()){for(var b=F.id(),k=Y[b],U=F;k==null&&(k=Y[U.data("parent")]||Y["DummyCompound_"+U.data("parent")],Y[b]=k,(U=U.parent()[0])!=null););return k!=null?{x:k.x,y:k.y}:{x:F.position("x"),y:F.position("y")}}}),M(),_=requestAnimationFrame(R)};return N.addListener("layoutstarted",function(){T.options.animate==="during"&&(_=requestAnimationFrame(w))}),N.runLayout(),this.options.animate!=="during"&&(T.options.eles.nodes().not(":parent").layoutPositions(T,T.options,D),g=!1),this},l.prototype.getTopMostNodes=function(g){for(var _={},L=0;L<g.length;L++)_[g[L].id()]=!0;var N=g.filter(function(T,u){typeof T=="number"&&(T=u);for(var I=T.parent()[0];I!=null;){if(_[I.id()])return!1;I=I.parent()[0]}return!0});return N},l.prototype.processChildrenList=function(g,_,L){for(var N=_.length,T=0;T<N;T++){var u,I,s=_[T],c=s.children(),p=s.layoutDimensions({nodeDimensionsIncludeLabels:this.options.nodeDimensionsIncludeLabels});if((u=s.outerWidth()!=null&&s.outerHeight()!=null?g.add(new i(L.graphManager,new o(s.position("x")-p.w/2,s.position("y")-p.h/2),new h(parseFloat(p.w),parseFloat(p.h)))):g.add(new i(this.graphManager))).id=s.data("id"),u.paddingLeft=parseInt(s.css("padding")),u.paddingTop=parseInt(s.css("padding")),u.paddingRight=parseInt(s.css("padding")),u.paddingBottom=parseInt(s.css("padding")),this.options.nodeDimensionsIncludeLabels&&s.isParent()){var y=s.boundingBox({includeLabels:!0,includeNodes:!1}).w,O=s.boundingBox({includeLabels:!0,includeNodes:!1}).h,D=s.css("text-halign");u.labelWidth=y,u.labelHeight=O,u.labelPos=D}this.idToLNode[s.data("id")]=u,isNaN(u.rect.x)&&(u.rect.x=0),isNaN(u.rect.y)&&(u.rect.y=0),c!=null&&c.length>0&&(I=L.getGraphManager().add(L.newGraph(),u),this.processChildrenList(I,c,L))}},l.prototype.stop=function(){return this.stopped=!0,this};var m=function(g){g("layout","cose-bilkent",l)};typeof cytoscape<"u"&&m(cytoscape),A.exports=m}])},ct.exports=at(function(){return st?ht.exports:(st=1,v=function(A){return function(f){var d={};function e(t){if(d[t])return d[t].exports;var n=d[t]={i:t,l:!1,exports:{}};return f[t].call(n.exports,n,n.exports,e),n.l=!0,n.exports}return e.m=f,e.c=d,e.i=function(t){return t},e.d=function(t,n,r){e.o(t,n)||Object.defineProperty(t,n,{configurable:!1,enumerable:!0,get:r})},e.n=function(t){var n=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(n,"a",n),n},e.o=function(t,n){return Object.prototype.hasOwnProperty.call(t,n)},e.p="",e(e.s=7)}([function(f,d){f.exports=A},function(f,d,e){var t=e(0).FDLayoutConstants;function n(){}for(var r in t)n[r]=t[r];n.DEFAULT_USE_MULTI_LEVEL_SCALING=!1,n.DEFAULT_RADIAL_SEPARATION=t.DEFAULT_EDGE_LENGTH,n.DEFAULT_COMPONENT_SEPERATION=60,n.TILE=!0,n.TILING_PADDING_VERTICAL=10,n.TILING_PADDING_HORIZONTAL=10,n.TREE_REDUCTION_ON_INCREMENTAL=!1,f.exports=n},function(f,d,e){var t=e(0).FDLayoutEdge;function n(i,o,h){t.call(this,i,o,h)}for(var r in n.prototype=Object.create(t.prototype),t)n[r]=t[r];f.exports=n},function(f,d,e){var t=e(0).LGraph;function n(i,o,h){t.call(this,i,o,h)}for(var r in n.prototype=Object.create(t.prototype),t)n[r]=t[r];f.exports=n},function(f,d,e){var t=e(0).LGraphManager;function n(i){t.call(this,i)}for(var r in n.prototype=Object.create(t.prototype),t)n[r]=t[r];f.exports=n},function(f,d,e){var t=e(0).FDLayoutNode,n=e(0).IMath;function r(o,h,a,l){t.call(this,o,h,a,l)}for(var i in r.prototype=Object.create(t.prototype),t)r[i]=t[i];r.prototype.move=function(){var o=this.graphManager.getLayout();this.displacementX=o.coolingFactor*(this.springForceX+this.repulsionForceX+this.gravitationForceX)/this.noOfChildren,this.displacementY=o.coolingFactor*(this.springForceY+this.repulsionForceY+this.gravitationForceY)/this.noOfChildren,Math.abs(this.displacementX)>o.coolingFactor*o.maxNodeDisplacement&&(this.displacementX=o.coolingFactor*o.maxNodeDisplacement*n.sign(this.displacementX)),Math.abs(this.displacementY)>o.coolingFactor*o.maxNodeDisplacement&&(this.displacementY=o.coolingFactor*o.maxNodeDisplacement*n.sign(this.displacementY)),this.child==null||this.child.getNodes().length==0?this.moveBy(this.displacementX,this.displacementY):this.propogateDisplacementToChildren(this.displacementX,this.displacementY),o.totalDisplacement+=Math.abs(this.displacementX)+Math.abs(this.displacementY),this.springForceX=0,this.springForceY=0,this.repulsionForceX=0,this.repulsionForceY=0,this.gravitationForceX=0,this.gravitationForceY=0,this.displacementX=0,this.displacementY=0},r.prototype.propogateDisplacementToChildren=function(o,h){for(var a,l=this.getChild().getNodes(),E=0;E<l.length;E++)(a=l[E]).getChild()==null?(a.moveBy(o,h),a.displacementX+=o,a.displacementY+=h):a.propogateDisplacementToChildren(o,h)},r.prototype.setPred1=function(o){this.pred1=o},r.prototype.getPred1=function(){return pred1},r.prototype.getPred2=function(){return pred2},r.prototype.setNext=function(o){this.next=o},r.prototype.getNext=function(){return next},r.prototype.setProcessed=function(o){this.processed=o},r.prototype.isProcessed=function(){return processed},f.exports=r},function(f,d,e){var t=e(0).FDLayout,n=e(4),r=e(3),i=e(5),o=e(2),h=e(1),a=e(0).FDLayoutConstants,l=e(0).LayoutConstants,E=e(0).Point,m=e(0).PointD,g=e(0).Layout,_=e(0).Integer,L=e(0).IGeometry,N=e(0).LGraph,T=e(0).Transform;function u(){t.call(this),this.toBeTiled={}}for(var I in u.prototype=Object.create(t.prototype),t)u[I]=t[I];u.prototype.newGraphManager=function(){var s=new n(this);return this.graphManager=s,s},u.prototype.newGraph=function(s){return new r(null,this.graphManager,s)},u.prototype.newNode=function(s){return new i(this.graphManager,s)},u.prototype.newEdge=function(s){return new o(null,null,s)},u.prototype.initParameters=function(){t.prototype.initParameters.call(this,arguments),this.isSubLayout||(h.DEFAULT_EDGE_LENGTH<10?this.idealEdgeLength=10:this.idealEdgeLength=h.DEFAULT_EDGE_LENGTH,this.useSmartIdealEdgeLengthCalculation=h.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION,this.springConstant=a.DEFAULT_SPRING_STRENGTH,this.repulsionConstant=a.DEFAULT_REPULSION_STRENGTH,this.gravityConstant=a.DEFAULT_GRAVITY_STRENGTH,this.compoundGravityConstant=a.DEFAULT_COMPOUND_GRAVITY_STRENGTH,this.gravityRangeFactor=a.DEFAULT_GRAVITY_RANGE_FACTOR,this.compoundGravityRangeFactor=a.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR,this.prunedNodesAll=[],this.growTreeIterations=0,this.afterGrowthIterations=0,this.isTreeGrowing=!1,this.isGrowthFinished=!1,this.coolingCycle=0,this.maxCoolingCycle=this.maxIterations/a.CONVERGENCE_CHECK_PERIOD,this.finalTemperature=a.CONVERGENCE_CHECK_PERIOD/this.maxIterations,this.coolingAdjuster=1)},u.prototype.layout=function(){return l.DEFAULT_CREATE_BENDS_AS_NEEDED&&(this.createBendpoints(),this.graphManager.resetAllEdges()),this.level=0,this.classicLayout()},u.prototype.classicLayout=function(){if(this.nodesWithGravity=this.calculateNodesToApplyGravitationTo(),this.graphManager.setAllNodesToApplyGravitation(this.nodesWithGravity),this.calcNoOfChildrenForAllNodes(),this.graphManager.calcLowestCommonAncestors(),this.graphManager.calcInclusionTreeDepths(),this.graphManager.getRoot().calcEstimatedSize(),this.calcIdealEdgeLengths(),this.incremental)h.TREE_REDUCTION_ON_INCREMENTAL&&(this.reduceTrees(),this.graphManager.resetAllNodesToApplyGravitation(),c=new Set(this.getAllNodes()),p=this.nodesWithGravity.filter(function(y){return c.has(y)}),this.graphManager.setAllNodesToApplyGravitation(p));else{var s=this.getFlatForest();if(s.length>0)this.positionNodesRadially(s);else{this.reduceTrees(),this.graphManager.resetAllNodesToApplyGravitation();var c=new Set(this.getAllNodes()),p=this.nodesWithGravity.filter(function(y){return c.has(y)});this.graphManager.setAllNodesToApplyGravitation(p),this.positionNodesRandomly()}}return this.initSpringEmbedder(),this.runSpringEmbedder(),!0},u.prototype.tick=function(){if(this.totalIterations++,this.totalIterations===this.maxIterations&&!this.isTreeGrowing&&!this.isGrowthFinished){if(!(this.prunedNodesAll.length>0))return!0;this.isTreeGrowing=!0}if(this.totalIterations%a.CONVERGENCE_CHECK_PERIOD==0&&!this.isTreeGrowing&&!this.isGrowthFinished){if(this.isConverged()){if(!(this.prunedNodesAll.length>0))return!0;this.isTreeGrowing=!0}this.coolingCycle++,this.layoutQuality==0?this.coolingAdjuster=this.coolingCycle:this.layoutQuality==1&&(this.coolingAdjuster=this.coolingCycle/3),this.coolingFactor=Math.max(this.initialCoolingFactor-Math.pow(this.coolingCycle,Math.log(100*(this.initialCoolingFactor-this.finalTemperature))/Math.log(this.maxCoolingCycle))/100*this.coolingAdjuster,this.finalTemperature),this.animationPeriod=Math.ceil(this.initialAnimationPeriod*Math.sqrt(this.coolingFactor))}if(this.isTreeGrowing){if(this.growTreeIterations%10==0)if(this.prunedNodesAll.length>0){this.graphManager.updateBounds(),this.updateGrid(),this.growTree(this.prunedNodesAll),this.graphManager.resetAllNodesToApplyGravitation();var s=new Set(this.getAllNodes()),c=this.nodesWithGravity.filter(function(O){return s.has(O)});this.graphManager.setAllNodesToApplyGravitation(c),this.graphManager.updateBounds(),this.updateGrid(),this.coolingFactor=a.DEFAULT_COOLING_FACTOR_INCREMENTAL}else this.isTreeGrowing=!1,this.isGrowthFinished=!0;this.growTreeIterations++}if(this.isGrowthFinished){if(this.isConverged())return!0;this.afterGrowthIterations%10==0&&(this.graphManager.updateBounds(),this.updateGrid()),this.coolingFactor=a.DEFAULT_COOLING_FACTOR_INCREMENTAL*((100-this.afterGrowthIterations)/100),this.afterGrowthIterations++}var p=!this.isTreeGrowing&&!this.isGrowthFinished,y=this.growTreeIterations%10==1&&this.isTreeGrowing||this.afterGrowthIterations%10==1&&this.isGrowthFinished;return this.totalDisplacement=0,this.graphManager.updateBounds(),this.calcSpringForces(),this.calcRepulsionForces(p,y),this.calcGravitationalForces(),this.moveNodes(),this.animate(),!1},u.prototype.getPositionsData=function(){for(var s=this.graphManager.getAllNodes(),c={},p=0;p<s.length;p++){var y=s[p].rect,O=s[p].id;c[O]={id:O,x:y.getCenterX(),y:y.getCenterY(),w:y.width,h:y.height}}return c},u.prototype.runSpringEmbedder=function(){this.initialAnimationPeriod=25,this.animationPeriod=this.initialAnimationPeriod;var s=!1;if(a.ANIMATE==="during")this.emit("layoutstarted");else{for(;!s;)s=this.tick();this.graphManager.updateBounds()}},u.prototype.calculateNodesToApplyGravitationTo=function(){var s,c,p=[],y=this.graphManager.getGraphs(),O=y.length;for(c=0;c<O;c++)(s=y[c]).updateConnected(),s.isConnected||(p=p.concat(s.getNodes()));return p},u.prototype.createBendpoints=function(){var s=[];s=s.concat(this.graphManager.getAllEdges());var c,p=new Set;for(c=0;c<s.length;c++){var y=s[c];if(!p.has(y)){var O=y.getSource(),D=y.getTarget();if(O==D)y.getBendpoints().push(new m),y.getBendpoints().push(new m),this.createDummyNodesForBendpoints(y),p.add(y);else{var w=[];if(w=(w=w.concat(O.getEdgeListToNode(D))).concat(D.getEdgeListToNode(O)),!p.has(w[0])){var R;if(w.length>1)for(R=0;R<w.length;R++){var C=w[R];C.getBendpoints().push(new m),this.createDummyNodesForBendpoints(C)}w.forEach(function(M){p.add(M)})}}}if(p.size==s.length)break}},u.prototype.positionNodesRadially=function(s){for(var c=new E(0,0),p=Math.ceil(Math.sqrt(s.length)),y=0,O=0,D=0,w=new m(0,0),R=0;R<s.length;R++){R%p==0&&(D=0,O=y,R!=0&&(O+=h.DEFAULT_COMPONENT_SEPERATION),y=0);var C=s[R],M=g.findCenterOfTree(C);c.x=D,c.y=O,(w=u.radialLayout(C,M,c)).y>y&&(y=Math.floor(w.y)),D=Math.floor(w.x+h.DEFAULT_COMPONENT_SEPERATION)}this.transform(new m(l.WORLD_CENTER_X-w.x/2,l.WORLD_CENTER_Y-w.y/2))},u.radialLayout=function(s,c,p){var y=Math.max(this.maxDiagonalInTree(s),h.DEFAULT_RADIAL_SEPARATION);u.branchRadialLayout(c,null,0,359,0,y);var O=N.calculateBounds(s),D=new T;D.setDeviceOrgX(O.getMinX()),D.setDeviceOrgY(O.getMinY()),D.setWorldOrgX(p.x),D.setWorldOrgY(p.y);for(var w=0;w<s.length;w++)s[w].transform(D);var R=new m(O.getMaxX(),O.getMaxY());return D.inverseTransformPoint(R)},u.branchRadialLayout=function(s,c,p,y,O,D){var w=(y-p+1)/2;w<0&&(w+=180);var R=(w+p)%360*L.TWO_PI/360,C=O*Math.cos(R),M=O*Math.sin(R);s.setCenter(C,M);var S=[],G=(S=S.concat(s.getEdges())).length;c!=null&&G--;for(var Y,F=0,P=S.length,b=s.getEdgesBetween(c);b.length>1;){var k=b[0];b.splice(0,1);var U=S.indexOf(k);U>=0&&S.splice(U,1),P--,G--}Y=c!=null?(S.indexOf(b[0])+1)%P:0;for(var q=Math.abs(y-p)/G,W=Y;F!=G;W=++W%P){var B=S[W].getOtherEnd(s);if(B!=c){var Z=(p+F*q)%360,V=(Z+q)%360;u.branchRadialLayout(B,s,Z,V,O+D,D),F++}}},u.maxDiagonalInTree=function(s){for(var c=_.MIN_VALUE,p=0;p<s.length;p++){var y=s[p].getDiagonal();y>c&&(c=y)}return c},u.prototype.calcRepulsionRange=function(){return 2*(this.level+1)*this.idealEdgeLength},u.prototype.groupZeroDegreeMembers=function(){var s=this,c={};this.memberGroups={},this.idToDummyNode={};for(var p=[],y=this.graphManager.getAllNodes(),O=0;O<y.length;O++){var D=(w=y[O]).getParent();this.getNodeDegreeWithChildren(w)!==0||D.id!=null&&this.getToBeTiled(D)||p.push(w)}for(O=0;O<p.length;O++){var w,R=(w=p[O]).getParent().id;c[R]===void 0&&(c[R]=[]),c[R]=c[R].concat(w)}Object.keys(c).forEach(function(C){if(c[C].length>1){var M="DummyCompound_"+C;s.memberGroups[M]=c[C];var S=c[C][0].getParent(),G=new i(s.graphManager);G.id=M,G.paddingLeft=S.paddingLeft||0,G.paddingRight=S.paddingRight||0,G.paddingBottom=S.paddingBottom||0,G.paddingTop=S.paddingTop||0,s.idToDummyNode[M]=G;var Y=s.getGraphManager().add(s.newGraph(),G),F=S.getChild();F.add(G);for(var P=0;P<c[C].length;P++){var b=c[C][P];F.remove(b),Y.add(b)}}})},u.prototype.clearCompounds=function(){var s={},c={};this.performDFSOnCompounds();for(var p=0;p<this.compoundOrder.length;p++)c[this.compoundOrder[p].id]=this.compoundOrder[p],s[this.compoundOrder[p].id]=[].concat(this.compoundOrder[p].getChild().getNodes()),this.graphManager.remove(this.compoundOrder[p].getChild()),this.compoundOrder[p].child=null;this.graphManager.resetAllNodes(),this.tileCompoundMembers(s,c)},u.prototype.clearZeroDegreeMembers=function(){var s=this,c=this.tiledZeroDegreePack=[];Object.keys(this.memberGroups).forEach(function(p){var y=s.idToDummyNode[p];c[p]=s.tileNodes(s.memberGroups[p],y.paddingLeft+y.paddingRight),y.rect.width=c[p].width,y.rect.height=c[p].height})},u.prototype.repopulateCompounds=function(){for(var s=this.compoundOrder.length-1;s>=0;s--){var c=this.compoundOrder[s],p=c.id,y=c.paddingLeft,O=c.paddingTop;this.adjustLocations(this.tiledMemberPack[p],c.rect.x,c.rect.y,y,O)}},u.prototype.repopulateZeroDegreeMembers=function(){var s=this,c=this.tiledZeroDegreePack;Object.keys(c).forEach(function(p){var y=s.idToDummyNode[p],O=y.paddingLeft,D=y.paddingTop;s.adjustLocations(c[p],y.rect.x,y.rect.y,O,D)})},u.prototype.getToBeTiled=function(s){var c=s.id;if(this.toBeTiled[c]!=null)return this.toBeTiled[c];var p=s.getChild();if(p==null)return this.toBeTiled[c]=!1,!1;for(var y=p.getNodes(),O=0;O<y.length;O++){var D=y[O];if(this.getNodeDegree(D)>0)return this.toBeTiled[c]=!1,!1;if(D.getChild()!=null){if(!this.getToBeTiled(D))return this.toBeTiled[c]=!1,!1}else this.toBeTiled[D.id]=!1}return this.toBeTiled[c]=!0,!0},u.prototype.getNodeDegree=function(s){s.id;for(var c=s.getEdges(),p=0,y=0;y<c.length;y++){var O=c[y];O.getSource().id!==O.getTarget().id&&(p+=1)}return p},u.prototype.getNodeDegreeWithChildren=function(s){var c=this.getNodeDegree(s);if(s.getChild()==null)return c;for(var p=s.getChild().getNodes(),y=0;y<p.length;y++){var O=p[y];c+=this.getNodeDegreeWithChildren(O)}return c},u.prototype.performDFSOnCompounds=function(){this.compoundOrder=[],this.fillCompexOrderByDFS(this.graphManager.getRoot().getNodes())},u.prototype.fillCompexOrderByDFS=function(s){for(var c=0;c<s.length;c++){var p=s[c];p.getChild()!=null&&this.fillCompexOrderByDFS(p.getChild().getNodes()),this.getToBeTiled(p)&&this.compoundOrder.push(p)}},u.prototype.adjustLocations=function(s,c,p,y,O){p+=O;for(var D=c+=y,w=0;w<s.rows.length;w++){var R=s.rows[w];c=D;for(var C=0,M=0;M<R.length;M++){var S=R[M];S.rect.x=c,S.rect.y=p,c+=S.rect.width+s.horizontalPadding,S.rect.height>C&&(C=S.rect.height)}p+=C+s.verticalPadding}},u.prototype.tileCompoundMembers=function(s,c){var p=this;this.tiledMemberPack=[],Object.keys(s).forEach(function(y){var O=c[y];p.tiledMemberPack[y]=p.tileNodes(s[y],O.paddingLeft+O.paddingRight),O.rect.width=p.tiledMemberPack[y].width,O.rect.height=p.tiledMemberPack[y].height})},u.prototype.tileNodes=function(s,c){var p={rows:[],rowWidth:[],rowHeight:[],width:0,height:c,verticalPadding:h.TILING_PADDING_VERTICAL,horizontalPadding:h.TILING_PADDING_HORIZONTAL};s.sort(function(D,w){return D.rect.width*D.rect.height>w.rect.width*w.rect.height?-1:D.rect.width*D.rect.height<w.rect.width*w.rect.height?1:0});for(var y=0;y<s.length;y++){var O=s[y];p.rows.length==0?this.insertNodeToRow(p,O,0,c):this.canAddHorizontal(p,O.rect.width,O.rect.height)?this.insertNodeToRow(p,O,this.getShortestRowIndex(p),c):this.insertNodeToRow(p,O,p.rows.length,c),this.shiftToLastRow(p)}return p},u.prototype.insertNodeToRow=function(s,c,p,y){var O=y;p==s.rows.length&&(s.rows.push([]),s.rowWidth.push(O),s.rowHeight.push(0));var D=s.rowWidth[p]+c.rect.width;s.rows[p].length>0&&(D+=s.horizontalPadding),s.rowWidth[p]=D,s.width<D&&(s.width=D);var w=c.rect.height;p>0&&(w+=s.verticalPadding);var R=0;w>s.rowHeight[p]&&(R=s.rowHeight[p],s.rowHeight[p]=w,R=s.rowHeight[p]-R),s.height+=R,s.rows[p].push(c)},u.prototype.getShortestRowIndex=function(s){for(var c=-1,p=Number.MAX_VALUE,y=0;y<s.rows.length;y++)s.rowWidth[y]<p&&(c=y,p=s.rowWidth[y]);return c},u.prototype.getLongestRowIndex=function(s){for(var c=-1,p=Number.MIN_VALUE,y=0;y<s.rows.length;y++)s.rowWidth[y]>p&&(c=y,p=s.rowWidth[y]);return c},u.prototype.canAddHorizontal=function(s,c,p){var y=this.getShortestRowIndex(s);if(y<0)return!0;var O=s.rowWidth[y];if(O+s.horizontalPadding+c<=s.width)return!0;var D,w,R=0;return s.rowHeight[y]<p&&y>0&&(R=p+s.verticalPadding-s.rowHeight[y]),D=s.width-O>=c+s.horizontalPadding?(s.height+R)/(O+c+s.horizontalPadding):(s.height+R)/s.width,R=p+s.verticalPadding,(w=s.width<c?(s.height+R)/c:(s.height+R)/s.width)<1&&(w=1/w),D<1&&(D=1/D),D<w},u.prototype.shiftToLastRow=function(s){var c=this.getLongestRowIndex(s),p=s.rowWidth.length-1,y=s.rows[c],O=y[y.length-1],D=O.width+s.horizontalPadding;if(s.width-s.rowWidth[p]>D&&c!=p){y.splice(-1,1),s.rows[p].push(O),s.rowWidth[c]=s.rowWidth[c]-D,s.rowWidth[p]=s.rowWidth[p]+D,s.width=s.rowWidth[instance.getLongestRowIndex(s)];for(var w=Number.MIN_VALUE,R=0;R<y.length;R++)y[R].height>w&&(w=y[R].height);c>0&&(w+=s.verticalPadding);var C=s.rowHeight[c]+s.rowHeight[p];s.rowHeight[c]=w,s.rowHeight[p]<O.height+s.verticalPadding&&(s.rowHeight[p]=O.height+s.verticalPadding);var M=s.rowHeight[c]+s.rowHeight[p];s.height+=M-C,this.shiftToLastRow(s)}},u.prototype.tilingPreLayout=function(){h.TILE&&(this.groupZeroDegreeMembers(),this.clearCompounds(),this.clearZeroDegreeMembers())},u.prototype.tilingPostLayout=function(){h.TILE&&(this.repopulateZeroDegreeMembers(),this.repopulateCompounds())},u.prototype.reduceTrees=function(){for(var s,c=[],p=!0;p;){var y=this.graphManager.getAllNodes(),O=[];p=!1;for(var D=0;D<y.length;D++)(s=y[D]).getEdges().length!=1||s.getEdges()[0].isInterGraph||s.getChild()!=null||(O.push([s,s.getEdges()[0],s.getOwner()]),p=!0);if(p==1){for(var w=[],R=0;R<O.length;R++)O[R][0].getEdges().length==1&&(w.push(O[R]),O[R][0].getOwner().remove(O[R][0]));c.push(w),this.graphManager.resetAllNodes(),this.graphManager.resetAllEdges()}}this.prunedNodesAll=c},u.prototype.growTree=function(s){for(var c,p=s[s.length-1],y=0;y<p.length;y++)c=p[y],this.findPlaceforPrunedNode(c),c[2].add(c[0]),c[2].add(c[1],c[1].source,c[1].target);s.splice(s.length-1,1),this.graphManager.resetAllNodes(),this.graphManager.resetAllEdges()},u.prototype.findPlaceforPrunedNode=function(s){var c,p,y=s[0],O=(p=y==s[1].source?s[1].target:s[1].source).startX,D=p.finishX,w=p.startY,R=p.finishY,C=[0,0,0,0];if(w>0)for(var M=O;M<=D;M++)C[0]+=this.grid[M][w-1].length+this.grid[M][w].length-1;if(D<this.grid.length-1)for(M=w;M<=R;M++)C[1]+=this.grid[D+1][M].length+this.grid[D][M].length-1;if(R<this.grid[0].length-1)for(M=O;M<=D;M++)C[2]+=this.grid[M][R+1].length+this.grid[M][R].length-1;if(O>0)for(M=w;M<=R;M++)C[3]+=this.grid[O-1][M].length+this.grid[O][M].length-1;for(var S,G,Y=_.MAX_VALUE,F=0;F<C.length;F++)C[F]<Y?(Y=C[F],S=1,G=F):C[F]==Y&&S++;if(S==3&&Y==0)C[0]==0&&C[1]==0&&C[2]==0?c=1:C[0]==0&&C[1]==0&&C[3]==0?c=0:C[0]==0&&C[2]==0&&C[3]==0?c=3:C[1]==0&&C[2]==0&&C[3]==0&&(c=2);else if(S==2&&Y==0){var P=Math.floor(2*Math.random());c=C[0]==0&&C[1]==0?P==0?0:1:C[0]==0&&C[2]==0?P==0?0:2:C[0]==0&&C[3]==0?P==0?0:3:C[1]==0&&C[2]==0?P==0?1:2:C[1]==0&&C[3]==0?P==0?1:3:P==0?2:3}else c=S==4&&Y==0?P=Math.floor(4*Math.random()):G;c==0?y.setCenter(p.getCenterX(),p.getCenterY()-p.getHeight()/2-a.DEFAULT_EDGE_LENGTH-y.getHeight()/2):c==1?y.setCenter(p.getCenterX()+p.getWidth()/2+a.DEFAULT_EDGE_LENGTH+y.getWidth()/2,p.getCenterY()):c==2?y.setCenter(p.getCenterX(),p.getCenterY()+p.getHeight()/2+a.DEFAULT_EDGE_LENGTH+y.getHeight()/2):y.setCenter(p.getCenterX()-p.getWidth()/2-a.DEFAULT_EDGE_LENGTH-y.getWidth()/2,p.getCenterY())},f.exports=u},function(f,d,e){var t={};t.layoutBase=e(0),t.CoSEConstants=e(1),t.CoSEEdge=e(2),t.CoSEGraph=e(3),t.CoSEGraphManager=e(4),t.CoSELayout=e(6),t.CoSENode=e(5),f.exports=t}])},ht.exports=v(Dt()));var v}());const It=Ot(ct.exports);var J=function(){var v=x(function(u,I,s,c){for(s=s||{},c=u.length;c--;s[u[c]]=I);return s},"o"),A=[1,4],f=[1,13],d=[1,12],e=[1,15],t=[1,16],n=[1,20],r=[1,19],i=[6,7,8],o=[1,26],h=[1,24],a=[1,25],l=[6,7,11],E=[1,6,13,15,16,19,22],m=[1,33],g=[1,34],_=[1,6,7,11,13,15,16,19,22],L={trace:x(function(){},"trace"),yy:{},symbols_:{error:2,start:3,mindMap:4,spaceLines:5,SPACELINE:6,NL:7,MINDMAP:8,document:9,stop:10,EOF:11,statement:12,SPACELIST:13,node:14,ICON:15,CLASS:16,nodeWithId:17,nodeWithoutId:18,NODE_DSTART:19,NODE_DESCR:20,NODE_DEND:21,NODE_ID:22,$accept:0,$end:1},terminals_:{2:"error",6:"SPACELINE",7:"NL",8:"MINDMAP",11:"EOF",13:"SPACELIST",15:"ICON",16:"CLASS",19:"NODE_DSTART",20:"NODE_DESCR",21:"NODE_DEND",22:"NODE_ID"},productions_:[0,[3,1],[3,2],[5,1],[5,2],[5,2],[4,2],[4,3],[10,1],[10,1],[10,1],[10,2],[10,2],[9,3],[9,2],[12,2],[12,2],[12,2],[12,1],[12,1],[12,1],[12,1],[12,1],[14,1],[14,1],[18,3],[17,1],[17,4]],performAction:x(function(u,I,s,c,p,y,O){var D=y.length-1;switch(p){case 6:case 7:return c;case 8:c.getLogger().trace("Stop NL ");break;case 9:c.getLogger().trace("Stop EOF ");break;case 11:c.getLogger().trace("Stop NL2 ");break;case 12:c.getLogger().trace("Stop EOF2 ");break;case 15:c.getLogger().info("Node: ",y[D].id),c.addNode(y[D-1].length,y[D].id,y[D].descr,y[D].type);break;case 16:c.getLogger().trace("Icon: ",y[D]),c.decorateNode({icon:y[D]});break;case 17:case 21:c.decorateNode({class:y[D]});break;case 18:c.getLogger().trace("SPACELIST");break;case 19:c.getLogger().trace("Node: ",y[D].id),c.addNode(0,y[D].id,y[D].descr,y[D].type);break;case 20:c.decorateNode({icon:y[D]});break;case 25:c.getLogger().trace("node found ..",y[D-2]),this.$={id:y[D-1],descr:y[D-1],type:c.getType(y[D-2],y[D])};break;case 26:this.$={id:y[D],descr:y[D],type:c.nodeType.DEFAULT};break;case 27:c.getLogger().trace("node found ..",y[D-3]),this.$={id:y[D-3],descr:y[D-1],type:c.getType(y[D-2],y[D])}}},"anonymous"),table:[{3:1,4:2,5:3,6:[1,5],8:A},{1:[3]},{1:[2,1]},{4:6,6:[1,7],7:[1,8],8:A},{6:f,7:[1,10],9:9,12:11,13:d,14:14,15:e,16:t,17:17,18:18,19:n,22:r},v(i,[2,3]),{1:[2,2]},v(i,[2,4]),v(i,[2,5]),{1:[2,6],6:f,12:21,13:d,14:14,15:e,16:t,17:17,18:18,19:n,22:r},{6:f,9:22,12:11,13:d,14:14,15:e,16:t,17:17,18:18,19:n,22:r},{6:o,7:h,10:23,11:a},v(l,[2,22],{17:17,18:18,14:27,15:[1,28],16:[1,29],19:n,22:r}),v(l,[2,18]),v(l,[2,19]),v(l,[2,20]),v(l,[2,21]),v(l,[2,23]),v(l,[2,24]),v(l,[2,26],{19:[1,30]}),{20:[1,31]},{6:o,7:h,10:32,11:a},{1:[2,7],6:f,12:21,13:d,14:14,15:e,16:t,17:17,18:18,19:n,22:r},v(E,[2,14],{7:m,11:g}),v(_,[2,8]),v(_,[2,9]),v(_,[2,10]),v(l,[2,15]),v(l,[2,16]),v(l,[2,17]),{20:[1,35]},{21:[1,36]},v(E,[2,13],{7:m,11:g}),v(_,[2,11]),v(_,[2,12]),{21:[1,37]},v(l,[2,25]),v(l,[2,27])],defaultActions:{2:[2,1],6:[2,2]},parseError:x(function(u,I){if(!I.recoverable){var s=new Error(u);throw s.hash=I,s}this.trace(u)},"parseError"),parse:x(function(u){var I=this,s=[0],c=[],p=[null],y=[],O=this.table,D="",w=0,R=0,C=y.slice.call(arguments,1),M=Object.create(this.lexer),S={yy:{}};for(var G in this.yy)Object.prototype.hasOwnProperty.call(this.yy,G)&&(S.yy[G]=this.yy[G]);M.setInput(u,S.yy),S.yy.lexer=M,S.yy.parser=this,M.yylloc===void 0&&(M.yylloc={});var Y=M.yylloc;y.push(Y);var F=M.options&&M.options.ranges;function P(){var $;return typeof($=c.pop()||M.lex()||1)!="number"&&($ instanceof Array&&($=(c=$).pop()),$=I.symbols_[$]||$),$}typeof S.yy.parseError=="function"?this.parseError=S.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError,x(function($){s.length=s.length-2*$,p.length=p.length-$,y.length=y.length-$},"popStack"),x(P,"lex");for(var b,k,U,q,W,B,Z,V,z={};;){if(k=s[s.length-1],this.defaultActions[k]?U=this.defaultActions[k]:(b==null&&(b=P()),U=O[k]&&O[k][b]),U===void 0||!U.length||!U[0]){var it="";for(W in V=[],O[k])this.terminals_[W]&&W>2&&V.push("'"+this.terminals_[W]+"'");it=M.showPosition?"Parse error on line "+(w+1)+`:
`+M.showPosition()+`
Expecting `+V.join(", ")+", got '"+(this.terminals_[b]||b)+"'":"Parse error on line "+(w+1)+": Unexpected "+(b==1?"end of input":"'"+(this.terminals_[b]||b)+"'"),this.parseError(it,{text:M.match,token:this.terminals_[b]||b,line:M.yylineno,loc:Y,expected:V})}if(U[0]instanceof Array&&U.length>1)throw new Error("Parse Error: multiple actions possible at state: "+k+", token: "+b);switch(U[0]){case 1:s.push(b),p.push(M.yytext),y.push(M.yylloc),s.push(U[1]),b=null,R=M.yyleng,D=M.yytext,w=M.yylineno,Y=M.yylloc;break;case 2:if(B=this.productions_[U[1]][1],z.$=p[p.length-B],z._$={first_line:y[y.length-(B||1)].first_line,last_line:y[y.length-1].last_line,first_column:y[y.length-(B||1)].first_column,last_column:y[y.length-1].last_column},F&&(z._$.range=[y[y.length-(B||1)].range[0],y[y.length-1].range[1]]),(q=this.performAction.apply(z,[D,R,w,S.yy,U[1],p,y].concat(C)))!==void 0)return q;B&&(s=s.slice(0,-1*B*2),p=p.slice(0,-1*B),y=y.slice(0,-1*B)),s.push(this.productions_[U[1]][0]),p.push(z.$),y.push(z._$),Z=O[s[s.length-2]][s[s.length-1]],s.push(Z);break;case 3:return!0}}return!0},"parse")},N=function(){return{EOF:1,parseError:x(function(u,I){if(!this.yy.parser)throw new Error(u);this.yy.parser.parseError(u,I)},"parseError"),setInput:x(function(u,I){return this.yy=I||this.yy||{},this._input=u,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:x(function(){var u=this._input[0];return this.yytext+=u,this.yyleng++,this.offset++,this.match+=u,this.matched+=u,u.match(/(?:\r\n?|\n).*/g)?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),u},"input"),unput:x(function(u){var I=u.length,s=u.split(/(?:\r\n?|\n)/g);this._input=u+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-I),this.offset-=I;var c=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),s.length-1&&(this.yylineno-=s.length-1);var p=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:s?(s.length===c.length?this.yylloc.first_column:0)+c[c.length-s.length].length-s[0].length:this.yylloc.first_column-I},this.options.ranges&&(this.yylloc.range=[p[0],p[0]+this.yyleng-I]),this.yyleng=this.yytext.length,this},"unput"),more:x(function(){return this._more=!0,this},"more"),reject:x(function(){return this.options.backtrack_lexer?(this._backtrack=!0,this):this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"reject"),less:x(function(u){this.unput(this.match.slice(u))},"less"),pastInput:x(function(){var u=this.matched.substr(0,this.matched.length-this.match.length);return(u.length>20?"...":"")+u.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:x(function(){var u=this.match;return u.length<20&&(u+=this._input.substr(0,20-u.length)),(u.substr(0,20)+(u.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:x(function(){var u=this.pastInput(),I=new Array(u.length+1).join("-");return u+this.upcomingInput()+`
`+I+"^"},"showPosition"),test_match:x(function(u,I){var s,c,p;if(this.options.backtrack_lexer&&(p={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(p.yylloc.range=this.yylloc.range.slice(0))),(c=u[0].match(/(?:\r\n?|\n).*/g))&&(this.yylineno+=c.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:c?c[c.length-1].length-c[c.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+u[0].length},this.yytext+=u[0],this.match+=u[0],this.matches=u,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(u[0].length),this.matched+=u[0],s=this.performAction.call(this,this.yy,this,I,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),s)return s;if(this._backtrack){for(var y in p)this[y]=p[y];return!1}return!1},"test_match"),next:x(function(){if(this.done)return this.EOF;var u,I,s,c;this._input||(this.done=!0),this._more||(this.yytext="",this.match="");for(var p=this._currentRules(),y=0;y<p.length;y++)if((s=this._input.match(this.rules[p[y]]))&&(!I||s[0].length>I[0].length)){if(I=s,c=y,this.options.backtrack_lexer){if((u=this.test_match(s,p[y]))!==!1)return u;if(this._backtrack){I=!1;continue}return!1}if(!this.options.flex)break}return I?(u=this.test_match(I,p[c]))!==!1&&u:this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:x(function(){var u=this.next();return u||this.lex()},"lex"),begin:x(function(u){this.conditionStack.push(u)},"begin"),popState:x(function(){return this.conditionStack.length-1>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:x(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:x(function(u){return(u=this.conditionStack.length-1-Math.abs(u||0))>=0?this.conditionStack[u]:"INITIAL"},"topState"),pushState:x(function(u){this.begin(u)},"pushState"),stateStackSize:x(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:x(function(u,I,s,c){switch(s){case 0:return u.getLogger().trace("Found comment",I.yytext),6;case 1:return 8;case 2:this.begin("CLASS");break;case 3:return this.popState(),16;case 4:case 23:case 26:this.popState();break;case 5:u.getLogger().trace("Begin icon"),this.begin("ICON");break;case 6:return u.getLogger().trace("SPACELINE"),6;case 7:return 7;case 8:return 15;case 9:u.getLogger().trace("end icon"),this.popState();break;case 10:return u.getLogger().trace("Exploding node"),this.begin("NODE"),19;case 11:return u.getLogger().trace("Cloud"),this.begin("NODE"),19;case 12:return u.getLogger().trace("Explosion Bang"),this.begin("NODE"),19;case 13:return u.getLogger().trace("Cloud Bang"),this.begin("NODE"),19;case 14:case 15:case 16:case 17:return this.begin("NODE"),19;case 18:return 13;case 19:return 22;case 20:return 11;case 21:this.begin("NSTR2");break;case 22:return"NODE_DESCR";case 24:u.getLogger().trace("Starting NSTR"),this.begin("NSTR");break;case 25:return u.getLogger().trace("description:",I.yytext),"NODE_DESCR";case 27:return this.popState(),u.getLogger().trace("node end ))"),"NODE_DEND";case 28:return this.popState(),u.getLogger().trace("node end )"),"NODE_DEND";case 29:return this.popState(),u.getLogger().trace("node end ...",I.yytext),"NODE_DEND";case 30:case 33:case 34:return this.popState(),u.getLogger().trace("node end (("),"NODE_DEND";case 31:case 32:return this.popState(),u.getLogger().trace("node end (-"),"NODE_DEND";case 35:case 36:return u.getLogger().trace("Long description:",I.yytext),20}},"anonymous"),rules:[/^(?:\s*%%.*)/i,/^(?:mindmap\b)/i,/^(?::::)/i,/^(?:.+)/i,/^(?:\n)/i,/^(?:::icon\()/i,/^(?:[\s]+[\n])/i,/^(?:[\n]+)/i,/^(?:[^\)]+)/i,/^(?:\))/i,/^(?:-\))/i,/^(?:\(-)/i,/^(?:\)\))/i,/^(?:\))/i,/^(?:\(\()/i,/^(?:\{\{)/i,/^(?:\()/i,/^(?:\[)/i,/^(?:[\s]+)/i,/^(?:[^\(\[\n\)\{\}]+)/i,/^(?:$)/i,/^(?:["][`])/i,/^(?:[^`"]+)/i,/^(?:[`]["])/i,/^(?:["])/i,/^(?:[^"]+)/i,/^(?:["])/i,/^(?:[\)]\))/i,/^(?:[\)])/i,/^(?:[\]])/i,/^(?:\}\})/i,/^(?:\(-)/i,/^(?:-\))/i,/^(?:\(\()/i,/^(?:\()/i,/^(?:[^\)\]\(\}]+)/i,/^(?:.+(?!\(\())/i],conditions:{CLASS:{rules:[3,4],inclusive:!1},ICON:{rules:[8,9],inclusive:!1},NSTR2:{rules:[22,23],inclusive:!1},NSTR:{rules:[25,26],inclusive:!1},NODE:{rules:[21,24,27,28,29,30,31,32,33,34,35,36],inclusive:!1},INITIAL:{rules:[0,1,2,5,6,7,10,11,12,13,14,15,16,17,18,19,20],inclusive:!0}}}}();function T(){this.yy={}}return L.lexer=N,x(T,"Parser"),T.prototype=L,L.Parser=T,new T}();J.parser=J;var wt=J,H=[],ut=0,tt={},Rt=x(()=>{H=[],ut=0,tt={}},"clear"),Ct=x(function(v){for(let A=H.length-1;A>=0;A--)if(H[A].level<v)return H[A];return null},"getParent"),Mt=x(()=>H.length>0?H[0]:null,"getMindmap"),xt=x((v,A,f,d)=>{var i,o;j.info("addNode",v,A,f,d);const e=et();let t=((i=e.mindmap)==null?void 0:i.padding)??Q.mindmap.padding;switch(d){case X.ROUNDED_RECT:case X.RECT:case X.HEXAGON:t*=2}const n={id:ut++,nodeId:K(A,e),level:v,descr:K(f,e),type:d,children:[],width:((o=e.mindmap)==null?void 0:o.maxNodeWidth)??Q.mindmap.maxNodeWidth,padding:t},r=Ct(v);if(r)r.children.push(n),H.push(n);else{if(H.length!==0)throw new Error('There can be only one root. No parent could be found for ("'+n.descr+'")');H.push(n)}},"addNode"),X={DEFAULT:0,NO_BORDER:0,ROUNDED_RECT:1,RECT:2,CIRCLE:3,CLOUD:4,BANG:5,HEXAGON:6},St={clear:Rt,addNode:xt,getMindmap:Mt,nodeType:X,getType:x((v,A)=>{switch(j.debug("In get type",v,A),v){case"[":return X.RECT;case"(":return A===")"?X.ROUNDED_RECT:X.CLOUD;case"((":return X.CIRCLE;case")":return X.CLOUD;case"))":return X.BANG;case"{{":return X.HEXAGON;default:return X.DEFAULT}},"getType"),setElementForId:x((v,A)=>{tt[v]=A},"setElementForId"),decorateNode:x(v=>{if(!v)return;const A=et(),f=H[H.length-1];v.icon&&(f.icon=K(v.icon,A)),v.class&&(f.class=K(v.class,A))},"decorateNode"),type2Str:x(v=>{switch(v){case X.DEFAULT:return"no-border";case X.RECT:return"rect";case X.ROUNDED_RECT:return"rounded-rect";case X.CIRCLE:return"circle";case X.CLOUD:return"cloud";case X.BANG:return"bang";case X.HEXAGON:return"hexgon";default:return"no-border"}},"type2Str"),getLogger:x(()=>j,"getLogger"),getElementById:x(v=>tt[v],"getElementById")},Gt=x(function(v,A,f,d){A.append("path").attr("id","node-"+f.id).attr("class","node-bkg node-"+v.type2Str(f.type)).attr("d",`M0 ${f.height-5} v${10-f.height} q0,-5 5,-5 h${f.width-10} q5,0 5,5 v${f.height-5} H0 Z`),A.append("line").attr("class","node-line-"+d).attr("x1",0).attr("y1",f.height).attr("x2",f.width).attr("y2",f.height)},"defaultBkg"),bt=x(function(v,A,f){A.append("rect").attr("id","node-"+f.id).attr("class","node-bkg node-"+v.type2Str(f.type)).attr("height",f.height).attr("width",f.width)},"rectBkg"),Ft=x(function(v,A,f){const d=f.width,e=f.height,t=.15*d,n=.25*d,r=.35*d,i=.2*d;A.append("path").attr("id","node-"+f.id).attr("class","node-bkg node-"+v.type2Str(f.type)).attr("d",`M0 0 a${t},${t} 0 0,1 ${.25*d},${-1*d*.1}
      a${r},${r} 1 0,1 ${.4*d},${-1*d*.1}
      a${n},${n} 1 0,1 ${.35*d},${1*d*.2}

      a${t},${t} 1 0,1 ${.15*d},${1*e*.35}
      a${i},${i} 1 0,1 ${-1*d*.15},${1*e*.65}

      a${n},${t} 1 0,1 ${-1*d*.25},${.15*d}
      a${r},${r} 1 0,1 ${-1*d*.5},0
      a${t},${t} 1 0,1 ${-1*d*.25},${-1*d*.15}

      a${t},${t} 1 0,1 ${-1*d*.1},${-1*e*.35}
      a${i},${i} 1 0,1 ${.1*d},${-1*e*.65}

    H0 V0 Z`)},"cloudBkg"),Pt=x(function(v,A,f){const d=f.width,e=f.height,t=.15*d;A.append("path").attr("id","node-"+f.id).attr("class","node-bkg node-"+v.type2Str(f.type)).attr("d",`M0 0 a${t},${t} 1 0,0 ${.25*d},${-1*e*.1}
      a${t},${t} 1 0,0 ${.25*d},0
      a${t},${t} 1 0,0 ${.25*d},0
      a${t},${t} 1 0,0 ${.25*d},${1*e*.1}

      a${t},${t} 1 0,0 ${.15*d},${1*e*.33}
      a${.8*t},${.8*t} 1 0,0 0,${1*e*.34}
      a${t},${t} 1 0,0 ${-1*d*.15},${1*e*.33}

      a${t},${t} 1 0,0 ${-1*d*.25},${.15*e}
      a${t},${t} 1 0,0 ${-1*d*.25},0
      a${t},${t} 1 0,0 ${-1*d*.25},0
      a${t},${t} 1 0,0 ${-1*d*.25},${-1*e*.15}

      a${t},${t} 1 0,0 ${-1*d*.1},${-1*e*.33}
      a${.8*t},${.8*t} 1 0,0 0,${-1*e*.34}
      a${t},${t} 1 0,0 ${.1*d},${-1*e*.33}

    H0 V0 Z`)},"bangBkg"),Ut=x(function(v,A,f){A.append("circle").attr("id","node-"+f.id).attr("class","node-bkg node-"+v.type2Str(f.type)).attr("r",f.width/2)},"circleBkg");function pt(v,A,f,d,e){return v.insert("polygon",":first-child").attr("points",d.map(function(t){return t.x+","+t.y}).join(" ")).attr("transform","translate("+(e.width-A)/2+", "+f+")")}x(pt,"insertPolygonShape");var Yt=x(function(v,A,f){const d=f.height,e=d/4,t=f.width-f.padding+2*e;pt(A,t,d,[{x:e,y:0},{x:t-e,y:0},{x:t,y:-d/2},{x:t-e,y:-d},{x:e,y:-d},{x:0,y:-d/2}],f)},"hexagonBkg"),kt=x(function(v,A,f){A.append("rect").attr("id","node-"+f.id).attr("class","node-bkg node-"+v.type2Str(f.type)).attr("height",f.height).attr("rx",f.padding).attr("ry",f.padding).attr("width",f.width)},"roundedRectBkg"),Xt=x(async function(v,A,f,d,e){const t=e.htmlLabels,n=d%11,r=A.append("g");f.section=n;let i="section-"+n;n<0&&(i+=" section-root"),r.attr("class",(f.class?f.class+" ":"")+"mindmap-node "+i);const o=r.append("g"),h=r.append("g"),a=f.descr.replace(/(<br\/*>)/g,`
`);await mt(h,a,{useHtmlLabels:t,width:f.width,classes:"mindmap-node-label"},e),t||h.attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","middle").attr("text-anchor","middle");const l=h.node().getBBox(),[E]=_t(e.fontSize);if(f.height=l.height+1.1*E*.5+f.padding,f.width=l.width+2*f.padding,f.icon)if(f.type===v.nodeType.CIRCLE)f.height+=50,f.width+=50,r.append("foreignObject").attr("height","50px").attr("width",f.width).attr("style","text-align: center;").append("div").attr("class","icon-container").append("i").attr("class","node-icon-"+n+" "+f.icon),h.attr("transform","translate("+f.width/2+", "+(f.height/2-1.5*f.padding)+")");else{f.width+=50;const m=f.height;f.height=Math.max(m,60);const g=Math.abs(f.height-m);r.append("foreignObject").attr("width","60px").attr("height",f.height).attr("style","text-align: center;margin-top:"+g/2+"px;").append("div").attr("class","icon-container").append("i").attr("class","node-icon-"+n+" "+f.icon),h.attr("transform","translate("+(25+f.width/2)+", "+(g/2+f.padding/2)+")")}else if(t){const m=(f.width-l.width)/2,g=(f.height-l.height)/2;h.attr("transform","translate("+m+", "+g+")")}else{const m=f.width/2,g=f.padding/2;h.attr("transform","translate("+m+", "+g+")")}switch(f.type){case v.nodeType.DEFAULT:Gt(v,o,f,n);break;case v.nodeType.ROUNDED_RECT:kt(v,o,f,n);break;case v.nodeType.RECT:bt(v,o,f,n);break;case v.nodeType.CIRCLE:o.attr("transform","translate("+f.width/2+", "+ +f.height/2+")"),Ut(v,o,f,n);break;case v.nodeType.CLOUD:Ft(v,o,f,n);break;case v.nodeType.BANG:Pt(v,o,f,n);break;case v.nodeType.HEXAGON:Yt(v,o,f,n)}return v.setElementForId(f.id,r),f.height},"drawNode"),Bt=x(function(v,A){const f=v.getElementById(A.id),d=A.x||0,e=A.y||0;f.attr("transform","translate("+d+","+e+")")},"positionNode");async function nt(v,A,f,d,e){await Xt(v,A,f,d,e),f.children&&await Promise.all(f.children.map((t,n)=>nt(v,A,t,d<0?n:d,e)))}function dt(v,A){A.edges().map((f,d)=>{const e=f.data();if(f[0]._private.bodyBounds){const t=f[0]._private.rscratch;j.trace("Edge: ",d,e),v.insert("path").attr("d",`M ${t.startX},${t.startY} L ${t.midX},${t.midY} L${t.endX},${t.endY} `).attr("class","edge section-edge-"+e.section+" edge-depth-"+e.depth)}})}function rt(v,A,f,d){A.add({group:"nodes",data:{id:v.id.toString(),labelText:v.descr,height:v.height,width:v.width,level:d,nodeId:v.id,padding:v.padding,type:v.type},position:{x:v.x,y:v.y}}),v.children&&v.children.forEach(e=>{rt(e,A,f,d+1),A.add({group:"edges",data:{id:`${v.id}_${e.id}`,source:v.id,target:e.id,depth:d,section:e.section}})})}function ft(v,A){return new Promise(f=>{const d=Et("body").append("div").attr("id","cy").attr("style","display:none"),e=gt({container:document.getElementById("cy"),style:[{selector:"edge",style:{"curve-style":"bezier"}}]});d.remove(),rt(v,e,A,0),e.nodes().forEach(function(t){t.layoutDimensions=()=>{const n=t.data();return{w:n.width,h:n.height}}}),e.layout({name:"cose-bilkent",quality:"proof",styleEnabled:!1,animate:!1}).run(),e.ready(t=>{j.info("Ready",t),f(e)})})}function yt(v,A){A.nodes().map((f,d)=>{const e=f.data();e.x=f.position().x,e.y=f.position().y,Bt(v,e);const t=v.getElementById(e.nodeId);j.info("Id:",d,"Position: (",f.position().x,", ",f.position().y,")",e),t.attr("transform",`translate(${f.position().x-e.width/2}, ${f.position().y-e.height/2})`),t.attr("attr",`apa-${d})`)})}gt.use(It),x(nt,"drawNodes"),x(dt,"drawEdges"),x(rt,"addNodes"),x(ft,"layoutMindmap"),x(yt,"positionNodes");var $t={draw:x(async(v,A,f,d)=>{var a,l;j.debug(`Rendering mindmap diagram
`+v);const e=d.db,t=e.getMindmap();if(!t)return;const n=et();n.htmlLabels=!1;const r=Nt(A),i=r.append("g");i.attr("class","mindmap-edges");const o=r.append("g");o.attr("class","mindmap-nodes"),await nt(e,o,t,-1,n);const h=await ft(t,n);dt(i,h),yt(e,h),vt(void 0,r,((a=n.mindmap)==null?void 0:a.padding)??Q.mindmap.padding,((l=n.mindmap)==null?void 0:l.useMaxWidth)??Q.mindmap.useMaxWidth)},"draw")},Ht=x(v=>{let A="";for(let f=0;f<v.THEME_COLOR_LIMIT;f++)v["lineColor"+f]=v["lineColor"+f]||v["cScaleInv"+f],At(v["lineColor"+f])?v["lineColor"+f]=Tt(v["lineColor"+f],20):v["lineColor"+f]=Lt(v["lineColor"+f],20);for(let f=0;f<v.THEME_COLOR_LIMIT;f++){const d=""+(17-3*f);A+=`
    .section-${f-1} rect, .section-${f-1} path, .section-${f-1} circle, .section-${f-1} polygon, .section-${f-1} path  {
      fill: ${v["cScale"+f]};
    }
    .section-${f-1} text {
     fill: ${v["cScaleLabel"+f]};
    }
    .node-icon-${f-1} {
      font-size: 40px;
      color: ${v["cScaleLabel"+f]};
    }
    .section-edge-${f-1}{
      stroke: ${v["cScale"+f]};
    }
    .edge-depth-${f-1}{
      stroke-width: ${d};
    }
    .section-${f-1} line {
      stroke: ${v["cScaleInv"+f]} ;
      stroke-width: 3;
    }

    .disabled, .disabled circle, .disabled text {
      fill: lightgray;
    }
    .disabled text {
      fill: #efefef;
    }
    `}return A},"genSections"),Ge={db:St,renderer:$t,parser:wt,styles:x(v=>`
  .edge {
    stroke-width: 3;
  }
  ${Ht(v)}
  .section-root rect, .section-root path, .section-root circle, .section-root polygon  {
    fill: ${v.git0};
  }
  .section-root text {
    fill: ${v.gitBranchLabel0};
  }
  .icon-container {
    height:100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .edge {
    fill: none;
  }
  .mindmap-node-label {
    dy: 1em;
    alignment-baseline: middle;
    text-anchor: middle;
    dominant-baseline: middle;
    text-align: center;
  }
`,"getStyles")};export{Ge as diagram};
