#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour chercher la vraie conversation (pas les scripts Python)
"""

import os
import json
import re
from pathlib import Path
from datetime import datetime

def search_real_conversations():
    """Cherche les vraies conversations (pas les scripts)"""
    
    base_path = Path("C:/Users/<USER>/AppData/Roaming/Code/User/workspaceStorage")
    
    print("🔍 RECHERCHE DES VRAIES CONVERSATIONS")
    print("=" * 60)
    
    found_conversations = []
    
    # Explorer tous les workspaces
    for workspace_dir in base_path.iterdir():
        if workspace_dir.is_dir():
            conversations_path = workspace_dir / "Augment.vscode-augment" / "augment-user-assets" / "checkpoint-documents"
            
            if conversations_path.exists():
                for conversation_dir in conversations_path.iterdir():
                    if conversation_dir.is_dir():
                        for file_path in conversation_dir.iterdir():
                            if file_path.is_file() and file_path.name.startswith("document-c__"):
                                
                                # EXCLURE les fichiers .py dans le nom
                                if ".py-" not in file_path.name:
                                    
                                    try:
                                        with open(file_path, 'r', encoding='utf-8') as f:
                                            content = f.read()
                                        
                                        # Parser JSON
                                        try:
                                            data = json.loads(content)
                                            original_code = data.get('originalCode', '')
                                            modified_code = data.get('modifiedCode', '')
                                            full_content = original_code + "\n" + modified_code
                                            
                                            # Chercher des indicateurs de conversation
                                            conversation_indicators = [
                                                "Human:",
                                                "Assistant:",
                                                "User:",
                                                "<<HUMAN_CONVERSATION_START>>",
                                                "tu avais raison",
                                                "finalement"
                                            ]
                                            
                                            found_indicators = []
                                            for indicator in conversation_indicators:
                                                if indicator.lower() in full_content.lower():
                                                    count = full_content.lower().count(indicator.lower())
                                                    found_indicators.append(f"{indicator} ({count}x)")
                                            
                                            if found_indicators:
                                                timestamp_match = re.search(r'-(\d+)-', file_path.name)
                                                timestamp = None
                                                if timestamp_match:
                                                    timestamp_ms = int(timestamp_match.group(1))
                                                    timestamp = datetime.fromtimestamp(timestamp_ms / 1000)
                                                
                                                found_conversations.append({
                                                    'workspace_id': workspace_dir.name,
                                                    'conversation_id': conversation_dir.name,
                                                    'file_name': file_path.name,
                                                    'file_path': str(file_path),
                                                    'timestamp': timestamp,
                                                    'indicators': found_indicators,
                                                    'content_size': len(full_content),
                                                    'preview': full_content[:300]
                                                })
                                                
                                                print(f"\n✅ CONVERSATION TROUVÉE!")
                                                print(f"📅 Date: {timestamp}")
                                                print(f"📄 Fichier: {file_path.name}")
                                                print(f"🔑 Indicateurs: {', '.join(found_indicators)}")
                                                print(f"📏 Taille: {len(full_content)} caractères")
                                                print(f"📝 Aperçu: {full_content[:200]}...")
                                                
                                        except json.JSONDecodeError:
                                            pass
                                            
                                    except Exception as e:
                                        pass
    
    # Trier par timestamp (plus récent en premier)
    found_conversations.sort(key=lambda x: x['timestamp'] if x['timestamp'] else datetime.min, reverse=True)
    
    print(f"\n📊 RÉSUMÉ:")
    print(f"💬 Vraies conversations trouvées: {len(found_conversations)}")
    
    if found_conversations:
        print(f"\n🎯 CONVERSATIONS TRIÉES PAR DATE:")
        for i, conv in enumerate(found_conversations, 1):
            print(f"\n--- CONVERSATION {i} ---")
            print(f"📅 Date: {conv['timestamp']}")
            print(f"📄 Fichier: {conv['file_name']}")
            print(f"🔑 Indicateurs: {', '.join(conv['indicators'])}")
            print(f"📏 Taille: {conv['content_size']} caractères")
            
            # Chercher spécifiquement "tu avais raison finalement"
            if "tu avais raison" in conv['preview'].lower() and "finalement" in conv['preview'].lower():
                print(f"🎉 CONTIENT 'TU AVAIS RAISON FINALEMENT'!")
                return conv
    
    return found_conversations[0] if found_conversations else None

if __name__ == "__main__":
    print("🚀 RECHERCHE DES VRAIES CONVERSATIONS")
    print("=" * 60)
    
    result = search_real_conversations()
    
    if result:
        print(f"\n🎉 VRAIE CONVERSATION TROUVÉE!")
        print(f"📁 Chemin: {result['file_path']}")
    else:
        print(f"\n❌ AUCUNE VRAIE CONVERSATION TROUVÉE")
        print("🤔 Les conversations pourraient être dans un autre format")
