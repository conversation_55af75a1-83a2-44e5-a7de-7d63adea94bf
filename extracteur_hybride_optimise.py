#!/usr/bin/env python3
"""
EXTRACTEUR HYBRIDE OPTIMISÉ - MEILLEUR DES DEUX MONDES
Combine la détection parfaite par index + extraction robuste du contenu
"""

import os
import time
import threading
from datetime import datetime
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import json
import sqlite3

class ExtracteurHybrideOptimise(FileSystemEventHandler):
    def __init__(self):
        self.workspace_path = r"C:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\a35ba43ef26792e61fe787a234121806"
        self.db_path = os.path.join(self.workspace_path, "state.vscdb")
        self.wal_path = self.db_path + "-wal"
        self.output_file = "messages_hybrides_optimises.txt"
        
        self.running = True
        self.observer = None
        self.lock = threading.Lock()
        self.derniere_taille_wal = 0
        self.dernier_nombre_messages = 0
        self.numero_message_global = 1
        
        self.init_output_file()
        self.init_wal_monitoring()
        self.charger_etat_initial()
    
    def init_output_file(self):
        """Initialise le fichier de sortie hybride"""
        with open(self.output_file, 'w', encoding='utf-8') as f:
            f.write("EXTRACTEUR HYBRIDE OPTIMISÉ - TEMPS RÉEL\n")
            f.write(f"Démarré: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("=" * 80 + "\n")
            f.write("🎯 Détection: Par index (fiable)\n")
            f.write("📝 Extraction: Robuste avec retry intelligent\n")
            f.write("🔢 Format: [TIMESTAMP] MESSAGE #N - ROLE\n")
            f.write("=" * 80 + "\n\n")
    
    def init_wal_monitoring(self):
        """Initialise la surveillance WAL"""
        try:
            if os.path.exists(self.wal_path):
                self.derniere_taille_wal = os.path.getsize(self.wal_path)
                print(f"📊 WAL initial: {self.derniere_taille_wal} bytes")
            else:
                print("⚠️ Fichier WAL non trouvé")
                self.derniere_taille_wal = 0
        except Exception as e:
            print(f"❌ Erreur init WAL: {e}")
            self.derniere_taille_wal = 0
    
    def charger_etat_initial(self):
        """Charge l'état initial avec extraction robuste"""
        try:
            messages_existants = self.extraire_messages_robuste()
            self.dernier_nombre_messages = len(messages_existants)
            self.numero_message_global = self.dernier_nombre_messages + 1
            
            print(f"📊 État initial: {self.dernier_nombre_messages} messages existants")
            print(f"🔢 Prochain numéro global: {self.numero_message_global}")
            
        except Exception as e:
            print(f"❌ Erreur chargement état: {e}")
            self.dernier_nombre_messages = 0
            self.numero_message_global = 1
    
    def extraire_messages_robuste(self):
        """Extraction robuste inspirée d'intercepteur_conversation_propre.py"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=3.0)
            conn.execute("PRAGMA journal_mode=WAL")
            conn.execute("PRAGMA synchronous=NORMAL")
            cursor = conn.cursor()
            
            # Requête robuste pour la clé Augment
            cursor.execute("SELECT key, value FROM ItemTable WHERE key LIKE '%augment-chat%'")
            rows = cursor.fetchall()
            
            for key, value in rows:
                if 'webviewView.augment-chat' in key:
                    try:
                        if isinstance(value, bytes):
                            value_str = value.decode('utf-8')
                        else:
                            value_str = str(value)
                        
                        main_data = json.loads(value_str)
                        webview_data = json.loads(main_data['webviewState'])
                        
                        conversations = webview_data.get('conversations', {})
                        current_conv_id = webview_data.get('currentConversationId')
                        
                        if current_conv_id and current_conv_id in conversations:
                            chat_history = conversations[current_conv_id].get('chatHistory', [])
                            conn.close()
                            return chat_history
                            
                    except Exception as e:
                        print(f"   ⚠️ Erreur parsing JSON: {e}")
                        continue
            
            conn.close()
            return []
            
        except Exception as e:
            print(f"❌ Erreur extraction robuste: {e}")
            return []
    
    def on_modified(self, event):
        """Détection instantanée WAL + extraction hybride"""
        if event.is_directory:
            return
        
        if os.path.basename(event.src_path) == "state.vscdb-wal":
            timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
            print(f"⚡ [{timestamp}] WAL MODIFIÉ - EXTRACTION HYBRIDE...")
            threading.Thread(target=self.extraction_hybride_intelligente, args=(timestamp,)).start()
    
    def extraction_hybride_intelligente(self, timestamp_detection):
        """Extraction hybride avec retry intelligent"""
        max_attempts = 5
        delay_ms = [50, 100, 150, 200, 300]  # Délais progressifs
        
        for attempt in range(max_attempts):
            try:
                # Délai adaptatif
                if attempt > 0:
                    time.sleep(delay_ms[min(attempt-1, len(delay_ms)-1)] / 1000.0)
                
                # Extraction robuste
                messages_actuels = self.extraire_messages_robuste()
                nombre_actuel = len(messages_actuels)
                
                print(f"   📊 Tentative {attempt + 1}: {self.dernier_nombre_messages} → {nombre_actuel} messages")
                
                if nombre_actuel > self.dernier_nombre_messages:
                    # Vérifier que les nouveaux messages ont du contenu
                    nouveaux_messages = messages_actuels[self.dernier_nombre_messages:]
                    
                    # Vérifier la qualité des données
                    messages_valides = []
                    for msg in nouveaux_messages:
                        if self.valider_message(msg):
                            messages_valides.append(msg)
                    
                    if messages_valides or attempt == max_attempts - 1:
                        # SUCCESS ou dernier essai
                        timestamp_extraction = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                        delai = (datetime.strptime(timestamp_extraction, '%H:%M:%S.%f') - 
                                datetime.strptime(timestamp_detection, '%H:%M:%S.%f')).total_seconds() * 1000
                        
                        nouveaux_count = nombre_actuel - self.dernier_nombre_messages
                        print(f"🔥 [{timestamp_extraction}] {nouveaux_count} MESSAGES EXTRAITS (tentative {attempt + 1}) !")
                        print(f"   ⏱️ Délai total: {delai:.1f}ms")
                        print(f"   ✅ Messages valides: {len(messages_valides)}/{nouveaux_count}")
                        
                        self.ecrire_messages_hybrides(nouveaux_messages, timestamp_extraction, delai, attempt + 1)
                        self.dernier_nombre_messages = nombre_actuel
                        return
                    else:
                        print(f"   🔄 Tentative {attempt + 1}: Messages sans contenu, retry...")
                
                elif nombre_actuel == self.dernier_nombre_messages:
                    print(f"   📝 Aucun nouveau message (stable à {nombre_actuel})")
                    return
                
            except Exception as e:
                print(f"   ❌ Erreur tentative {attempt + 1}: {e}")
        
        print(f"   ⚠️ Extraction incomplète après {max_attempts} tentatives")
    
    def valider_message(self, message):
        """Valide qu'un message a du contenu utilisable"""
        if not isinstance(message, dict):
            return False
        
        role = message.get('role', '')
        content = message.get('content', '')
        
        # Un message valide a au moins un rôle ou du contenu
        return bool(role) or bool(content.strip())
    
    def ecrire_messages_hybrides(self, nouveaux_messages, timestamp, delai, tentatives):
        """Écriture optimisée des messages hybrides"""
        with self.lock:
            try:
                with open(self.output_file, 'a', encoding='utf-8') as f:
                    for message in nouveaux_messages:
                        role = message.get('role', 'unknown')
                        content = message.get('content', '')
                        
                        # Formatage amélioré du rôle
                        if role == 'user':
                            role_display = '👤 UTILISATEUR'
                        elif role == 'assistant':
                            role_display = '🤖 ASSISTANT'
                        elif role == 'tool':
                            role_display = '🔧 OUTIL'
                        else:
                            role_display = f'❓ {role.upper()}'
                        
                        # En-tête du message avec métadonnées
                        f.write(f"[{timestamp}] MESSAGE #{self.numero_message_global} - {role_display}\n")
                        f.write(f"Délai: {delai:.1f}ms | Tentatives: {tentatives}\n")
                        f.write("-" * 60 + "\n")
                        
                        # Contenu du message
                        if content and content.strip():
                            content_clean = self.nettoyer_contenu_avance(content)
                            f.write(f"{content_clean}\n")
                        else:
                            f.write("[Message vide ou en cours de génération]\n")
                        
                        f.write("=" * 80 + "\n\n")
                        
                        # Log console avec aperçu du contenu
                        content_preview = content[:100].replace('\n', ' ') if content else '[vide]'
                        print(f"   ✅ MESSAGE #{self.numero_message_global} ({role}): {content_preview}...")
                        self.numero_message_global += 1
                    
                    f.flush()
                    os.fsync(f.fileno())
                    
            except Exception as e:
                print(f"   ❌ Erreur écriture hybride: {e}")
    
    def nettoyer_contenu_avance(self, content):
        """Nettoyage avancé du contenu"""
        if not content:
            return "[Contenu vide]"
        
        import re
        
        # Supprimer les caractères de contrôle
        content = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', '', content)
        
        # Normaliser les espaces
        content = re.sub(r'\s+', ' ', content)
        
        # Limiter la longueur si très long
        if len(content) > 15000:
            content = content[:15000] + "\n[... contenu tronqué ...]"
        
        return content.strip()
    
    def demarrer_surveillance(self):
        """Démarre la surveillance hybride optimisée"""
        print("🚀 EXTRACTEUR HYBRIDE OPTIMISÉ DÉMARRÉ")
        print("=" * 60)
        print(f"📁 Workspace: a35ba43ef26792e6...")
        print(f"📝 WAL: {self.wal_path}")
        print(f"📄 Sortie: {self.output_file}")
        print(f"🔢 Messages existants: {self.dernier_nombre_messages}")
        print(f"🔢 Prochain numéro: #{self.numero_message_global}")
        print("🎯 Détection: Index + Extraction: Robuste")
        print("💬 Écrivez dans Augment pour voir l'extraction hybride...")
        print("⏹️  Ctrl+C pour arrêter")
        print("=" * 60)
        
        # Surveillance du répertoire workspace
        self.observer = Observer()
        self.observer.schedule(self, self.workspace_path, recursive=False)
        self.observer.start()
        
        try:
            while self.running:
                threading.Event().wait(1)
                
        except KeyboardInterrupt:
            print("\n🛑 Arrêt de l'extracteur hybride...")
            self.running = False
            if self.observer:
                self.observer.stop()
                self.observer.join()

if __name__ == "__main__":
    extracteur = ExtracteurHybrideOptimise()
    extracteur.demarrer_surveillance()
