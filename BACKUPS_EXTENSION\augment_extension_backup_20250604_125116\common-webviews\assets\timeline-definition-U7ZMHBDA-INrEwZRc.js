import{_ as s,aa as yt,ab as gt,x as mt,j as V,d as ft,l as $,ac as xt,ad as bt,ae as _t,af as kt}from"./AugmentMessage-DIzdCIMv.js";import{d as K}from"./arc-B_nq00OU.js";import"./SpinnerAugment-BJ4-L7QR.js";import"./github-C1PQK5DH.js";import"./pen-to-square-Bm4lF9Yl.js";import"./augment-logo-D_UKSkj8.js";import"./TextTooltipAugment-Bkzart3o.js";import"./BaseButton-C6Dhmpxa.js";import"./IconButtonAugment-Certjadv.js";import"./Content-Czt02SJi.js";import"./globals-D0QH3NT1.js";import"./open-in-new-window-DMlqLwqy.js";import"./types-LfaCSdmF.js";import"./chat-types-NgqNgjwU.js";import"./test_service_pb-B6vKXZrG.js";import"./file-paths-BcSg4gks.js";import"./types-a569v5Ol.js";import"./folder-BJI1Q8_7.js";import"./folder-opened-DzrGzNBt.js";import"./types-BSMhNRWH.js";import"./index-C-g0ZorP.js";import"./CardAugment-BxTO-shY.js";import"./TextAreaAugment-Cj5jK817.js";import"./diff-utils-y96qaWKK.js";import"./toggleHighContrast-CwIv4U26.js";import"./preload-helper-Dv6uf1Os.js";import"./index-MyvMQzjq.js";import"./keypress-DD1aQVr0.js";import"./await_block-CvQ_3xaW.js";import"./ButtonAugment-HnJOGilM.js";import"./expand--BB_Hn_b.js";import"./mcp-logo-B9nTLE-q.js";import"./ellipsis-BWy9xWah.js";import"./IconFilePath-C-3qORpY.js";import"./LanguageIcon-BH9BM7T7.js";import"./next-edit-types-904A5ehg.js";import"./magnifying-glass-LWYs47rB.js";import"./MaterialIcon-DIlB9c-0.js";import"./Filespan-BC4kxbfx.js";import"./chevron-down-B88L5wkj.js";import"./lodash-ChYFUhWY.js";import"./terminal-BQIj5vJ0.js";var G=function(){var n=s(function(r,h,l,u){for(l=l||{},u=r.length;u--;l[r[u]]=h);return l},"o"),t=[6,8,10,11,12,14,16,17,20,21],e=[1,9],o=[1,10],i=[1,11],a=[1,12],p=[1,13],m=[1,16],d=[1,17],g={trace:s(function(){},"trace"),yy:{},symbols_:{error:2,start:3,timeline:4,document:5,EOF:6,line:7,SPACE:8,statement:9,NEWLINE:10,title:11,acc_title:12,acc_title_value:13,acc_descr:14,acc_descr_value:15,acc_descr_multiline_value:16,section:17,period_statement:18,event_statement:19,period:20,event:21,$accept:0,$end:1},terminals_:{2:"error",4:"timeline",6:"EOF",8:"SPACE",10:"NEWLINE",11:"title",12:"acc_title",13:"acc_title_value",14:"acc_descr",15:"acc_descr_value",16:"acc_descr_multiline_value",17:"section",20:"period",21:"event"},productions_:[0,[3,3],[5,0],[5,2],[7,2],[7,1],[7,1],[7,1],[9,1],[9,2],[9,2],[9,1],[9,1],[9,1],[9,1],[18,1],[19,1]],performAction:s(function(r,h,l,u,y,c,k){var x=c.length-1;switch(y){case 1:return c[x-1];case 2:case 6:case 7:this.$=[];break;case 3:c[x-1].push(c[x]),this.$=c[x-1];break;case 4:case 5:this.$=c[x];break;case 8:u.getCommonDb().setDiagramTitle(c[x].substr(6)),this.$=c[x].substr(6);break;case 9:this.$=c[x].trim(),u.getCommonDb().setAccTitle(this.$);break;case 10:case 11:this.$=c[x].trim(),u.getCommonDb().setAccDescription(this.$);break;case 12:u.addSection(c[x].substr(8)),this.$=c[x].substr(8);break;case 15:u.addTask(c[x],0,""),this.$=c[x];break;case 16:u.addEvent(c[x].substr(2)),this.$=c[x]}},"anonymous"),table:[{3:1,4:[1,2]},{1:[3]},n(t,[2,2],{5:3}),{6:[1,4],7:5,8:[1,6],9:7,10:[1,8],11:e,12:o,14:i,16:a,17:p,18:14,19:15,20:m,21:d},n(t,[2,7],{1:[2,1]}),n(t,[2,3]),{9:18,11:e,12:o,14:i,16:a,17:p,18:14,19:15,20:m,21:d},n(t,[2,5]),n(t,[2,6]),n(t,[2,8]),{13:[1,19]},{15:[1,20]},n(t,[2,11]),n(t,[2,12]),n(t,[2,13]),n(t,[2,14]),n(t,[2,15]),n(t,[2,16]),n(t,[2,4]),n(t,[2,9]),n(t,[2,10])],defaultActions:{},parseError:s(function(r,h){if(!h.recoverable){var l=new Error(r);throw l.hash=h,l}this.trace(r)},"parseError"),parse:s(function(r){var h=this,l=[0],u=[],y=[null],c=[],k=this.table,x="",N=0,P=0,D=c.slice.call(arguments,1),b=Object.create(this.lexer),I={yy:{}};for(var M in this.yy)Object.prototype.hasOwnProperty.call(this.yy,M)&&(I.yy[M]=this.yy[M]);b.setInput(r,I.yy),I.yy.lexer=b,I.yy.parser=this,b.yylloc===void 0&&(b.yylloc={});var v=b.yylloc;c.push(v);var A=b.options&&b.options.ranges;function L(){var E;return typeof(E=u.pop()||b.lex()||1)!="number"&&(E instanceof Array&&(E=(u=E).pop()),E=h.symbols_[E]||E),E}typeof I.yy.parseError=="function"?this.parseError=I.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError,s(function(E){l.length=l.length-2*E,y.length=y.length-E,c.length=c.length-E},"popStack"),s(L,"lex");for(var w,T,S,Z,z,C,X,B,O={};;){if(T=l[l.length-1],this.defaultActions[T]?S=this.defaultActions[T]:(w==null&&(w=L()),S=k[T]&&k[T][w]),S===void 0||!S.length||!S[0]){var J="";for(z in B=[],k[T])this.terminals_[z]&&z>2&&B.push("'"+this.terminals_[z]+"'");J=b.showPosition?"Parse error on line "+(N+1)+`:
`+b.showPosition()+`
Expecting `+B.join(", ")+", got '"+(this.terminals_[w]||w)+"'":"Parse error on line "+(N+1)+": Unexpected "+(w==1?"end of input":"'"+(this.terminals_[w]||w)+"'"),this.parseError(J,{text:b.match,token:this.terminals_[w]||w,line:b.yylineno,loc:v,expected:B})}if(S[0]instanceof Array&&S.length>1)throw new Error("Parse Error: multiple actions possible at state: "+T+", token: "+w);switch(S[0]){case 1:l.push(w),y.push(b.yytext),c.push(b.yylloc),l.push(S[1]),w=null,P=b.yyleng,x=b.yytext,N=b.yylineno,v=b.yylloc;break;case 2:if(C=this.productions_[S[1]][1],O.$=y[y.length-C],O._$={first_line:c[c.length-(C||1)].first_line,last_line:c[c.length-1].last_line,first_column:c[c.length-(C||1)].first_column,last_column:c[c.length-1].last_column},A&&(O._$.range=[c[c.length-(C||1)].range[0],c[c.length-1].range[1]]),(Z=this.performAction.apply(O,[x,P,N,I.yy,S[1],y,c].concat(D)))!==void 0)return Z;C&&(l=l.slice(0,-1*C*2),y=y.slice(0,-1*C),c=c.slice(0,-1*C)),l.push(this.productions_[S[1]][0]),y.push(O.$),c.push(O._$),X=k[l[l.length-2]][l[l.length-1]],l.push(X);break;case 3:return!0}}return!0},"parse")},_=function(){return{EOF:1,parseError:s(function(r,h){if(!this.yy.parser)throw new Error(r);this.yy.parser.parseError(r,h)},"parseError"),setInput:s(function(r,h){return this.yy=h||this.yy||{},this._input=r,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:s(function(){var r=this._input[0];return this.yytext+=r,this.yyleng++,this.offset++,this.match+=r,this.matched+=r,r.match(/(?:\r\n?|\n).*/g)?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),r},"input"),unput:s(function(r){var h=r.length,l=r.split(/(?:\r\n?|\n)/g);this._input=r+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-h),this.offset-=h;var u=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),l.length-1&&(this.yylineno-=l.length-1);var y=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:l?(l.length===u.length?this.yylloc.first_column:0)+u[u.length-l.length].length-l[0].length:this.yylloc.first_column-h},this.options.ranges&&(this.yylloc.range=[y[0],y[0]+this.yyleng-h]),this.yyleng=this.yytext.length,this},"unput"),more:s(function(){return this._more=!0,this},"more"),reject:s(function(){return this.options.backtrack_lexer?(this._backtrack=!0,this):this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"reject"),less:s(function(r){this.unput(this.match.slice(r))},"less"),pastInput:s(function(){var r=this.matched.substr(0,this.matched.length-this.match.length);return(r.length>20?"...":"")+r.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:s(function(){var r=this.match;return r.length<20&&(r+=this._input.substr(0,20-r.length)),(r.substr(0,20)+(r.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:s(function(){var r=this.pastInput(),h=new Array(r.length+1).join("-");return r+this.upcomingInput()+`
`+h+"^"},"showPosition"),test_match:s(function(r,h){var l,u,y;if(this.options.backtrack_lexer&&(y={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(y.yylloc.range=this.yylloc.range.slice(0))),(u=r[0].match(/(?:\r\n?|\n).*/g))&&(this.yylineno+=u.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:u?u[u.length-1].length-u[u.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+r[0].length},this.yytext+=r[0],this.match+=r[0],this.matches=r,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(r[0].length),this.matched+=r[0],l=this.performAction.call(this,this.yy,this,h,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),l)return l;if(this._backtrack){for(var c in y)this[c]=y[c];return!1}return!1},"test_match"),next:s(function(){if(this.done)return this.EOF;var r,h,l,u;this._input||(this.done=!0),this._more||(this.yytext="",this.match="");for(var y=this._currentRules(),c=0;c<y.length;c++)if((l=this._input.match(this.rules[y[c]]))&&(!h||l[0].length>h[0].length)){if(h=l,u=c,this.options.backtrack_lexer){if((r=this.test_match(l,y[c]))!==!1)return r;if(this._backtrack){h=!1;continue}return!1}if(!this.options.flex)break}return h?(r=this.test_match(h,y[u]))!==!1&&r:this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:s(function(){var r=this.next();return r||this.lex()},"lex"),begin:s(function(r){this.conditionStack.push(r)},"begin"),popState:s(function(){return this.conditionStack.length-1>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:s(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:s(function(r){return(r=this.conditionStack.length-1-Math.abs(r||0))>=0?this.conditionStack[r]:"INITIAL"},"topState"),pushState:s(function(r){this.begin(r)},"pushState"),stateStackSize:s(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:s(function(r,h,l,u){switch(l){case 0:case 1:case 3:case 4:break;case 2:return 10;case 5:return 4;case 6:return 11;case 7:return this.begin("acc_title"),12;case 8:return this.popState(),"acc_title_value";case 9:return this.begin("acc_descr"),14;case 10:return this.popState(),"acc_descr_value";case 11:this.begin("acc_descr_multiline");break;case 12:this.popState();break;case 13:return"acc_descr_multiline_value";case 14:return 17;case 15:return 21;case 16:return 20;case 17:return 6;case 18:return"INVALID"}},"anonymous"),rules:[/^(?:%(?!\{)[^\n]*)/i,/^(?:[^\}]%%[^\n]*)/i,/^(?:[\n]+)/i,/^(?:\s+)/i,/^(?:#[^\n]*)/i,/^(?:timeline\b)/i,/^(?:title\s[^\n]+)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?:section\s[^:\n]+)/i,/^(?::\s[^:\n]+)/i,/^(?:[^#:\n]+)/i,/^(?:$)/i,/^(?:.)/i],conditions:{acc_descr_multiline:{rules:[12,13],inclusive:!1},acc_descr:{rules:[10],inclusive:!1},acc_title:{rules:[8],inclusive:!1},INITIAL:{rules:[0,1,2,3,4,5,6,7,9,11,14,15,16,17,18],inclusive:!0}}}}();function f(){this.yy={}}return g.lexer=_,s(f,"Parser"),f.prototype=g,g.Parser=f,new f}();G.parser=G;var wt=G,nt={};yt(nt,{addEvent:()=>ht,addSection:()=>at,addTask:()=>lt,addTaskOrg:()=>dt,clear:()=>st,default:()=>vt,getCommonDb:()=>rt,getSections:()=>ot,getTasks:()=>ct});var j="",it=0,q=[],F=[],R=[],rt=s(()=>gt,"getCommonDb"),st=s(function(){q.length=0,F.length=0,j="",R.length=0,mt()},"clear"),at=s(function(n){j=n,q.push(n)},"addSection"),ot=s(function(){return q},"getSections"),ct=s(function(){let n=Q(),t=0;for(;!n&&t<100;)n=Q(),t++;return F.push(...R),F},"getTasks"),lt=s(function(n,t,e){const o={id:it++,section:j,type:j,task:n,score:t||0,events:e?[e]:[]};R.push(o)},"addTask"),ht=s(function(n){R.find(t=>t.id===it-1).events.push(n)},"addEvent"),dt=s(function(n){const t={section:j,type:j,description:n,task:n,classes:[]};F.push(t)},"addTaskOrg"),Q=s(function(){const n=s(function(e){return R[e].processed},"compileTask");let t=!0;for(const[e,o]of R.entries())n(e),t=t&&o.processed;return t},"compileTasks"),vt={clear:st,getCommonDb:rt,addSection:at,getSections:ot,getTasks:ct,addTask:lt,addTaskOrg:dt,addEvent:ht},W=s(function(n,t){const e=n.append("rect");return e.attr("x",t.x),e.attr("y",t.y),e.attr("fill",t.fill),e.attr("stroke",t.stroke),e.attr("width",t.width),e.attr("height",t.height),e.attr("rx",t.rx),e.attr("ry",t.ry),t.class!==void 0&&e.attr("class",t.class),e},"drawRect"),St=s(function(n,t){const o=n.append("circle").attr("cx",t.cx).attr("cy",t.cy).attr("class","face").attr("r",15).attr("stroke-width",2).attr("overflow","visible"),i=n.append("g");function a(d){const g=K().startAngle(Math.PI/2).endAngle(Math.PI/2*3).innerRadius(7.5).outerRadius(6.8181818181818175);d.append("path").attr("class","mouth").attr("d",g).attr("transform","translate("+t.cx+","+(t.cy+2)+")")}function p(d){const g=K().startAngle(3*Math.PI/2).endAngle(Math.PI/2*5).innerRadius(7.5).outerRadius(6.8181818181818175);d.append("path").attr("class","mouth").attr("d",g).attr("transform","translate("+t.cx+","+(t.cy+7)+")")}function m(d){d.append("line").attr("class","mouth").attr("stroke",2).attr("x1",t.cx-5).attr("y1",t.cy+7).attr("x2",t.cx+5).attr("y2",t.cy+7).attr("class","mouth").attr("stroke-width","1px").attr("stroke","#666")}return i.append("circle").attr("cx",t.cx-5).attr("cy",t.cy-5).attr("r",1.5).attr("stroke-width",2).attr("fill","#666").attr("stroke","#666"),i.append("circle").attr("cx",t.cx+5).attr("cy",t.cy-5).attr("r",1.5).attr("stroke-width",2).attr("fill","#666").attr("stroke","#666"),s(a,"smile"),s(p,"sad"),s(m,"ambivalent"),t.score>3?a(i):t.score<3?p(i):m(i),o},"drawFace"),$t=s(function(n,t){const e=n.append("circle");return e.attr("cx",t.cx),e.attr("cy",t.cy),e.attr("class","actor-"+t.pos),e.attr("fill",t.fill),e.attr("stroke",t.stroke),e.attr("r",t.r),e.class!==void 0&&e.attr("class",e.class),t.title!==void 0&&e.append("title").text(t.title),e},"drawCircle"),pt=s(function(n,t){const e=t.text.replace(/<br\s*\/?>/gi," "),o=n.append("text");o.attr("x",t.x),o.attr("y",t.y),o.attr("class","legend"),o.style("text-anchor",t.anchor),t.class!==void 0&&o.attr("class",t.class);const i=o.append("tspan");return i.attr("x",t.x+2*t.textMargin),i.text(e),o},"drawText"),Et=s(function(n,t){function e(i,a,p,m,d){return i+","+a+" "+(i+p)+","+a+" "+(i+p)+","+(a+m-d)+" "+(i+p-1.2*d)+","+(a+m)+" "+i+","+(a+m)}s(e,"genPoints");const o=n.append("polygon");o.attr("points",e(t.x,t.y,50,20,7)),o.attr("class","labelBox"),t.y=t.y+t.labelMargin,t.x=t.x+.5*t.labelMargin,pt(n,t)},"drawLabel"),Tt=s(function(n,t,e){const o=n.append("g"),i=U();i.x=t.x,i.y=t.y,i.fill=t.fill,i.width=e.width,i.height=e.height,i.class="journey-section section-type-"+t.num,i.rx=3,i.ry=3,W(o,i),ut(e)(t.text,o,i.x,i.y,i.width,i.height,{class:"journey-section section-type-"+t.num},e,t.colour)},"drawSection"),tt=-1,It=s(function(n,t,e){const o=t.x+e.width/2,i=n.append("g");tt++,i.append("line").attr("id","task"+tt).attr("x1",o).attr("y1",t.y).attr("x2",o).attr("y2",450).attr("class","task-line").attr("stroke-width","1px").attr("stroke-dasharray","4 2").attr("stroke","#666"),St(i,{cx:o,cy:300+30*(5-t.score),score:t.score});const a=U();a.x=t.x,a.y=t.y,a.fill=t.fill,a.width=e.width,a.height=e.height,a.class="task task-type-"+t.num,a.rx=3,a.ry=3,W(i,a),ut(e)(t.task,i,a.x,a.y,a.width,a.height,{class:"task"},e,t.colour)},"drawTask"),Mt=s(function(n,t){W(n,{x:t.startx,y:t.starty,width:t.stopx-t.startx,height:t.stopy-t.starty,fill:t.fill,class:"rect"}).lower()},"drawBackgroundRect"),Nt=s(function(){return{x:0,y:0,fill:void 0,"text-anchor":"start",width:100,height:100,textMargin:0,rx:0,ry:0}},"getTextObj"),U=s(function(){return{x:0,y:0,width:100,anchor:"start",height:100,rx:0,ry:0}},"getNoteRect"),ut=function(){function n(i,a,p,m,d,g,_,f){o(a.append("text").attr("x",p+d/2).attr("y",m+g/2+5).style("font-color",f).style("text-anchor","middle").text(i),_)}function t(i,a,p,m,d,g,_,f,r){const{taskFontSize:h,taskFontFamily:l}=f,u=i.split(/<br\s*\/?>/gi);for(let y=0;y<u.length;y++){const c=y*h-h*(u.length-1)/2,k=a.append("text").attr("x",p+d/2).attr("y",m).attr("fill",r).style("text-anchor","middle").style("font-size",h).style("font-family",l);k.append("tspan").attr("x",p+d/2).attr("dy",c).text(u[y]),k.attr("y",m+g/2).attr("dominant-baseline","central").attr("alignment-baseline","central"),o(k,_)}}function e(i,a,p,m,d,g,_,f){const r=a.append("switch"),h=r.append("foreignObject").attr("x",p).attr("y",m).attr("width",d).attr("height",g).attr("position","fixed").append("xhtml:div").style("display","table").style("height","100%").style("width","100%");h.append("div").attr("class","label").style("display","table-cell").style("text-align","center").style("vertical-align","middle").text(i),t(i,r,p,m,d,g,_,f),o(h,_)}function o(i,a){for(const p in a)p in a&&i.attr(p,a[p])}return s(n,"byText"),s(t,"byTspan"),s(e,"byFo"),s(o,"_setTextAttrs"),function(i){return i.textPlacement==="fo"?e:i.textPlacement==="old"?n:t}}(),At=s(function(n){n.append("defs").append("marker").attr("id","arrowhead").attr("refX",5).attr("refY",2).attr("markerWidth",6).attr("markerHeight",4).attr("orient","auto").append("path").attr("d","M 0,0 V 4 L6,2 Z")},"initGraphics");function Y(n,t){n.each(function(){var e,o=V(this),i=o.text().split(/(\s+|<br>)/).reverse(),a=[],p=o.attr("y"),m=parseFloat(o.attr("dy")),d=o.text(null).append("tspan").attr("x",0).attr("y",p).attr("dy",m+"em");for(let g=0;g<i.length;g++)e=i[i.length-1-g],a.push(e),d.text(a.join(" ").trim()),(d.node().getComputedTextLength()>t||e==="<br>")&&(a.pop(),d.text(a.join(" ").trim()),a=e==="<br>"?[""]:[e],d=o.append("tspan").attr("x",0).attr("y",p).attr("dy","1.1em").text(e))})}s(Y,"wrap");var Ct=s(function(n,t,e,o){var _;const i=e%12-1,a=n.append("g");t.section=i,a.attr("class",(t.class?t.class+" ":"")+"timeline-node section-"+i);const p=a.append("g"),m=a.append("g"),d=m.append("text").text(t.descr).attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","middle").attr("text-anchor","middle").call(Y,t.width).node().getBBox(),g=(_=o.fontSize)!=null&&_.replace?o.fontSize.replace("px",""):o.fontSize;return t.height=d.height+1.1*g*.5+t.padding,t.height=Math.max(t.height,t.maxHeight),t.width=t.width+2*t.padding,m.attr("transform","translate("+t.width/2+", "+t.padding/2+")"),Pt(p,t,i,o),t},"drawNode"),Lt=s(function(n,t,e){var p;const o=n.append("g"),i=o.append("text").text(t.descr).attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","middle").attr("text-anchor","middle").call(Y,t.width).node().getBBox(),a=(p=e.fontSize)!=null&&p.replace?e.fontSize.replace("px",""):e.fontSize;return o.remove(),i.height+1.1*a*.5+t.padding},"getVirtualNodeHeight"),Pt=s(function(n,t,e){n.append("path").attr("id","node-"+t.id).attr("class","node-bkg node-"+t.type).attr("d",`M0 ${t.height-5} v${10-t.height} q0,-5 5,-5 h${t.width-10} q5,0 5,5 v${t.height-5} H0 Z`),n.append("line").attr("class","node-line-"+e).attr("x1",0).attr("y1",t.height).attr("x2",t.width).attr("y2",t.height)},"defaultBkg"),H={drawRect:W,drawCircle:$t,drawSection:Tt,drawText:pt,drawLabel:Et,drawTask:It,drawBackgroundRect:Mt,getTextObj:Nt,getNoteRect:U,initGraphics:At,drawNode:Ct,getVirtualNodeHeight:Lt},Ht=s(function(n,t,e,o){var b,I;const i=ft(),a=i.leftMargin??50;$.debug("timeline",o.db);const p=i.securityLevel;let m;p==="sandbox"&&(m=V("#i"+t));const d=V(p==="sandbox"?m.nodes()[0].contentDocument.body:"body").select("#"+t);d.append("g");const g=o.db.getTasks(),_=o.db.getCommonDb().getDiagramTitle();$.debug("task",g),H.initGraphics(d);const f=o.db.getSections();$.debug("sections",f);let r=0,h=0,l=0,u=0,y=50+a,c=50;u=50;let k=0,x=!0;f.forEach(function(M){const v={number:k,descr:M,section:k,width:150,padding:20,maxHeight:r},A=H.getVirtualNodeHeight(d,v,i);$.debug("sectionHeight before draw",A),r=Math.max(r,A+20)});let N=0,P=0;$.debug("tasks.length",g.length);for(const[M,v]of g.entries()){const A={number:M,descr:v,section:v.section,width:150,padding:20,maxHeight:h},L=H.getVirtualNodeHeight(d,A,i);$.debug("taskHeight before draw",L),h=Math.max(h,L+20),N=Math.max(N,v.events.length);let w=0;for(const T of v.events){const S={descr:T,section:v.section,number:v.section,width:150,padding:20,maxHeight:50};w+=H.getVirtualNodeHeight(d,S,i)}P=Math.max(P,w)}$.debug("maxSectionHeight before draw",r),$.debug("maxTaskHeight before draw",h),f&&f.length>0?f.forEach(M=>{const v=g.filter(T=>T.section===M),A={number:k,descr:M,section:k,width:200*Math.max(v.length,1)-50,padding:20,maxHeight:r};$.debug("sectionNode",A);const L=d.append("g"),w=H.drawNode(L,A,k,i);$.debug("sectionNode output",w),L.attr("transform",`translate(${y}, 50)`),c+=r+50,v.length>0&&et(d,v,k,y,c,h,i,N,P,r,!1),y+=200*Math.max(v.length,1),c=50,k++}):(x=!1,et(d,g,k,y,c,h,i,N,P,r,!0));const D=d.node().getBBox();$.debug("bounds",D),_&&d.append("text").text(_).attr("x",D.width/2-a).attr("font-size","4ex").attr("font-weight","bold").attr("y",20),l=x?r+h+150:h+100,d.append("g").attr("class","lineWrapper").append("line").attr("x1",a).attr("y1",l).attr("x2",D.width+3*a).attr("y2",l).attr("stroke-width",4).attr("stroke","black").attr("marker-end","url(#arrowhead)"),xt(void 0,d,((b=i.timeline)==null?void 0:b.padding)??50,((I=i.timeline)==null?void 0:I.useMaxWidth)??!1)},"draw"),et=s(function(n,t,e,o,i,a,p,m,d,g,_){var f;for(const r of t){const h={descr:r.task,section:e,number:e,width:150,padding:20,maxHeight:a};$.debug("taskNode",h);const l=n.append("g").attr("class","taskWrapper"),u=H.drawNode(l,h,e,p).height;if($.debug("taskHeight after draw",u),l.attr("transform",`translate(${o}, ${i})`),a=Math.max(a,u),r.events){const y=n.append("g").attr("class","lineWrapper");let c=a;i+=100,c+=Ot(n,r.events,e,o,i,p),i-=100,y.append("line").attr("x1",o+95).attr("y1",i+a).attr("x2",o+95).attr("y2",i+a+(_?a:g)+d+120).attr("stroke-width",2).attr("stroke","black").attr("marker-end","url(#arrowhead)").attr("stroke-dasharray","5,5")}o+=200,_&&!((f=p.timeline)!=null&&f.disableMulticolor)&&e++}i-=10},"drawTasks"),Ot=s(function(n,t,e,o,i,a){let p=0;const m=i;i+=100;for(const d of t){const g={descr:d,section:e,number:e,width:150,padding:20,maxHeight:50};$.debug("eventNode",g);const _=n.append("g").attr("class","eventWrapper"),f=H.drawNode(_,g,e,a).height;p+=f,_.attr("transform",`translate(${o}, ${i})`),i=i+10+f}return i=m,p},"drawEvents"),jt={setConf:s(()=>{},"setConf"),draw:Ht},Rt=s(n=>{let t="";for(let e=0;e<n.THEME_COLOR_LIMIT;e++)n["lineColor"+e]=n["lineColor"+e]||n["cScaleInv"+e],bt(n["lineColor"+e])?n["lineColor"+e]=_t(n["lineColor"+e],20):n["lineColor"+e]=kt(n["lineColor"+e],20);for(let e=0;e<n.THEME_COLOR_LIMIT;e++){const o=""+(17-3*e);t+=`
    .section-${e-1} rect, .section-${e-1} path, .section-${e-1} circle, .section-${e-1} path  {
      fill: ${n["cScale"+e]};
    }
    .section-${e-1} text {
     fill: ${n["cScaleLabel"+e]};
    }
    .node-icon-${e-1} {
      font-size: 40px;
      color: ${n["cScaleLabel"+e]};
    }
    .section-edge-${e-1}{
      stroke: ${n["cScale"+e]};
    }
    .edge-depth-${e-1}{
      stroke-width: ${o};
    }
    .section-${e-1} line {
      stroke: ${n["cScaleInv"+e]} ;
      stroke-width: 3;
    }

    .lineWrapper line{
      stroke: ${n["cScaleLabel"+e]} ;
    }

    .disabled, .disabled circle, .disabled text {
      fill: lightgray;
    }
    .disabled text {
      fill: #efefef;
    }
    `}return t},"genSections"),Te={db:nt,renderer:jt,parser:wt,styles:s(n=>`
  .edge {
    stroke-width: 3;
  }
  ${Rt(n)}
  .section-root rect, .section-root path, .section-root circle  {
    fill: ${n.git0};
  }
  .section-root text {
    fill: ${n.gitBranchLabel0};
  }
  .icon-container {
    height:100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .edge {
    fill: none;
  }
  .eventWrapper  {
   filter: brightness(120%);
  }
`,"getStyles")};export{Te as diagram};
