#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour rechercher dans TOUS les workspaces Augment
"""

import os
from pathlib import Path

def search_all_workspaces():
    """Recherche dans tous les workspaces Augment"""
    
    base_path = Path("C:/Users/<USER>/AppData/Roaming/Code/User/workspaceStorage")
    
    print("🔍 RECHERCHE DANS TOUS LES WORKSPACES AUGMENT")
    print("=" * 60)
    
    workspaces = [
        "0274dc7166a66c69494b9273936046e7",
        "03b88cc79c2765585d13da3ff9102e4b", 
        "335aeb916a669e9574c4b8bade2d3093",
        "371af59b792cf24049be576a51211c6b",
        "464cd6b9a395b786276aa704dc30d7e0",
        "591be8b7f6bd4169258c0affc2eaa1fc",
        "678c16ca99a78e469650c2f7dd29d444",
        "85957ecfc79cd3f9a290bec200e7168c",
        "a9c5224a5a418a4afb4a799139c72fc6",
        "ae986251edd6627681670aed67ef0194",
        "b3731b3b91ebfdea7f6e41c078489edc",
        "bc549fb525f77702a89a738082a3659d",
        "c8c1bbe410954258c6a1a7c74e885a9f",
        "ee2ad90ad27bac9066a855246beb217c",
        "f1b95ebcbba3cf8f700cb9ccd0a04fcf",
        "f98f0e85632fd85543deea3dd2906b3c"
    ]
    
    found_conversations = []
    
    for workspace_id in workspaces:
        print(f"\n📁 Workspace: {workspace_id}")
        workspace_path = base_path / workspace_id / "Augment.vscode-augment" / "augment-user-assets"
        
        if not workspace_path.exists():
            print("  ❌ Pas de répertoire Augment")
            continue
        
        # Chercher dans task-storage
        task_storage = workspace_path / "task-storage" / "tasks"
        if task_storage.exists():
            print(f"  📋 Task-storage trouvé")
            
            for task_dir in task_storage.iterdir():
                if task_dir.is_dir():
                    # Chercher les fichiers dans chaque tâche
                    for file_path in task_dir.iterdir():
                        if file_path.is_file():
                            try:
                                with open(file_path, 'r', encoding='utf-8') as f:
                                    content = f.read()
                                
                                if "tu avais raison" in content.lower():
                                    print(f"  🎉 TROUVÉ dans task: {task_dir.name}")
                                    print(f"  📄 Fichier: {file_path.name}")
                                    
                                    # Extraire contexte
                                    content_lower = content.lower()
                                    start_pos = content_lower.find("tu avais raison")
                                    context_start = max(0, start_pos - 200)
                                    context_end = min(len(content), start_pos + 400)
                                    context = content[context_start:context_end]
                                    
                                    found_conversations.append({
                                        'workspace': workspace_id,
                                        'task': task_dir.name,
                                        'file': file_path.name,
                                        'context': context,
                                        'full_content': content
                                    })
                                    
                                    print(f"  📝 Contexte:")
                                    print("  " + "-" * 50)
                                    print("  " + context.replace('\n', '\n  '))
                                    print("  " + "-" * 50)
                                    
                            except Exception as e:
                                pass  # Ignorer erreurs de lecture
        
        # Chercher dans checkpoint-documents
        checkpoint_docs = workspace_path / "checkpoint-documents"
        if checkpoint_docs.exists():
            print(f"  📋 Checkpoint-documents trouvé")
            
            for conv_dir in checkpoint_docs.iterdir():
                if conv_dir.is_dir():
                    for file_path in conv_dir.iterdir():
                        if file_path.is_file():
                            try:
                                with open(file_path, 'r', encoding='utf-8') as f:
                                    content = f.read()
                                
                                if "tu avais raison" in content.lower():
                                    print(f"  🎉 TROUVÉ dans checkpoint: {conv_dir.name}")
                                    print(f"  📄 Fichier: {file_path.name}")
                                    
                                    # Extraire contexte
                                    content_lower = content.lower()
                                    start_pos = content_lower.find("tu avais raison")
                                    context_start = max(0, start_pos - 200)
                                    context_end = min(len(content), start_pos + 400)
                                    context = content[context_start:context_end]
                                    
                                    found_conversations.append({
                                        'workspace': workspace_id,
                                        'checkpoint': conv_dir.name,
                                        'file': file_path.name,
                                        'context': context,
                                        'full_content': content
                                    })
                                    
                                    print(f"  📝 Contexte:")
                                    print("  " + "-" * 50)
                                    print("  " + context.replace('\n', '\n  '))
                                    print("  " + "-" * 50)
                                    
                            except Exception as e:
                                pass  # Ignorer erreurs de lecture
    
    print(f"\n🎯 RÉSULTATS FINAUX:")
    print(f"💬 Total conversations trouvées: {len(found_conversations)}")
    
    if found_conversations:
        print(f"\n✅ CONVERSATIONS AVEC 'TU AVAIS RAISON':")
        for i, conv in enumerate(found_conversations, 1):
            print(f"\n--- CONVERSATION {i} ---")
            print(f"📁 Workspace: {conv['workspace']}")
            if 'task' in conv:
                print(f"📋 Task: {conv['task']}")
            if 'checkpoint' in conv:
                print(f"📋 Checkpoint: {conv['checkpoint']}")
            print(f"📄 Fichier: {conv['file']}")
            print(f"📝 Contexte complet:")
            print("-" * 60)
            print(conv['context'])
            print("-" * 60)
    else:
        print("❌ Aucune conversation trouvée avec 'tu avais raison'")
    
    return found_conversations

if __name__ == "__main__":
    print("🚀 RECHERCHE EXHAUSTIVE DANS TOUS LES WORKSPACES")
    print("=" * 60)
    
    results = search_all_workspaces()
    
    if results:
        print(f"\n🎉 MISSION ACCOMPLIE ! {len(results)} CONVERSATIONS TROUVÉES !")
    else:
        print(f"\n❌ AUCUNE CONVERSATION TROUVÉE")
        print("🤔 La conversation pourrait être dans un autre emplacement...")
