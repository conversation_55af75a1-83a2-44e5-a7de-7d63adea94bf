# Project Goal and Structure
- Create a new Baccarat predictor that corrects inconsistencies in the existing one, organized in 7 domains: training, optimization, parameters, interface, calculations, resources, and chargementsauvegarde.
- The predictor should predict the next round (0 for Player, 1 for <PERSON><PERSON>) with maximum precision, without TIE prediction.
- Follow two-step methodology: first establish optimal architecture, then build simple and efficient program.
- Create index files (INDEX_RECOLTE_*.txt) in each category folder to systematically harvest information from old programs.
- Organize prediction models (LSTM, LGBM, Markov) in a dedicated models/ folder with a single models.py file.
- Maintain the gui_isolated folder and its content in the project structure.

# Parameter Organization
- Centralize all parameters in parameters.py with no hard-coded values in methods.
- Organize parameters by categories (training, mathematical formulas, prediction models) and subcategories (fixed vs. optimizable).
- Select only the simplest and essential parameters, excluding all WAIT and NON-WAIT related parameters.
- Ensure model parameters are consistent between fixed and optimizable values.
- Parameters must be properly calibrated at game start for optimal performance during 60-round games.

# AZR Model Requirements
- Adapt AZR (Absolute Zero Reasoner) for Baccarat prediction with real-time adaptive pattern recognition without pre-training.
- AZR should self-predict round N+1, then learn from real outcomes when Player/Banker buttons are pressed.
- Explore alternative mathematical approaches beyond patterns: geometry, chaos theory, fractals.
- AZR must use ALL previous rounds (1 to N-1) to predict round N, not just the immediate previous round.
- Consider only rounds 31-60 for predictions, with first 30 rounds as warm-up for auto-calibration.
- Implement real-time auto-optimization using 28GB RAM and all CPU cores.
- Reset counter on reset button and auto-optimize at each round for maximum efficiency.

# AZR Implementation Details
- AZR requires: unified model as proposer/solver, verifiable environment, self-play training with rewards, three reasoning modes, task-relative REINFORCE++, code executor, buffer management, lambda parameter.
- Critical issue: rewards must include negative values to penalize bad predictions and avoid overconfidence.
- Balance between sparse/dense rewards and positive/negative feedback is needed for optimal performance.
- Prediction recommendations should be based on probabilities rather than mechanical repetitive patterns.

# Prediction Models and Calculations
- Research confirms ensemble methods combining LSTM, LGBM and Markov chains significantly improve time series prediction accuracy.
- Stacking ensemble with RNN/LSTM meta-learner shows superior performance; LSTM-LGBM combinations reduce variance and increase robustness.
- Ensemble methods produce fewer relatively bad predictions than individual models.
- Implement a single unified formula for calculating uncertainty and confidence across all prediction models.
- Confidence levels should reach higher values (like 80%) with mathematical analysis methods actually utilized.
- All confidence calculation formulas must be implemented in a single Python file.
- Unified confidence/uncertainty calculation for ensembles requires decomposing variance into epistemic (model uncertainty) and aleatoric (data uncertainty) components.
- Mathematical formulas: total variance = epistemic variance + aleatoric variance.
- Ensembles enable more precise uncertainty quantification via uncertainty propagation between base models and the meta-learner.
- Identify and remove obsolete mathematical formulas that generate noise and outdated models from the program for better code quality.

# Graphical Interface
- Clean up UI buttons but keep reset button for new game initialization.
- Reset button should reinitialize current game with all parameters reset to initial state.
- Do not display the AZR trend graph in the interface.
- Generate predictions automatically after entering a result rather than requiring a separate button click.

# Agent Performance Principles
- Complete tasks in one session without interruptions or intermediate status reports.
- Focus on complete task execution with fluid progression between elements.
- "Plus tu estimes, moins tu produis. Plus tu comptes, moins tu avances."
- Never estimate during action phases or provide unrequested intermediate reports.
- Always complete 100% before communicating and focus on accomplishment, not measurement.
- Use previous work as reference for future tasks and maintain comprehensive overview.