#!/usr/bin/env python3
"""
SCRIPT DE VÉRIFICATION DES DOUBLONS ENTRE 74 MÉTHODES ET 31 UNIVERSELLES
========================================================================

Objectif : Vérifier s'il y a des doublons entre les 74 méthodes Rollout 1 
cluster 0 et les 31 méthodes universelles, et éliminer les doublons.
"""

def get_31_universal_methods():
    """Liste des 31 méthodes universelles"""
    return {
        # 31 méthodes universelles centralisées
        '_rollout_analyzer',
        '_analyze_impair_consecutive_bias',
        '_analyze_pair_priority_2_autonomous',
        '_analyze_sync_alternation_bias',
        '_correlate_impair_with_sync',
        '_correlate_impair_with_combined',
        '_correlate_impair_with_pb',
        '_correlate_impair_with_so',
        '_correlate_bias_to_pb_variations',
        '_correlate_bias_to_so_variations',
        '_analyze_combined_structural_bias',
        '_generate_priority_based_synthesis_autonomous',
        '_generate_bias_signals_summary',
        '_generate_bias_generation_guidance',
        '_generate_bias_quick_access',
        '_rollout_generator',
        '_define_optimized_generation_space',
        '_generate_sequences_from_signals',
        '_generate_fallback_sequences',
        '_enrich_sequences_with_complete_indexes',
        '_rollout_predictor',
        '_evaluate_sequence_quality',
        '_evaluate_signal_alignment',
        '_evaluate_fallback_alignment',
        '_analyze_sequence_consistency',
        '_assess_risk_reward_ratio',
        '_validate_sequence_logic',
        '_calculate_sequence_score',
        '_select_best_sequence',
        '_calculate_cluster_confidence_azr_calibrated',
        '_convert_pb_sequence_to_so'
    }

def get_74_rollout1_methods():
    """Liste des 74 méthodes Rollout 1 cluster 0"""
    return [
        '_generate_all_possible_sequences',
        '_generate_fallback_sequences',
        '_generate_so_based_sequence',
        '_classify_confidence_level',
        '_convert_pb_sequence_to_so_with_history',
        '_calculate_sequence_probability',
        '_calculate_sequence_quality_metrics',
        '_generate_pair_sync_sequence',
        '_generate_impair_sync_sequence',
        '_generate_pb_sequence',
        '_generate_generic_signal_sequence',
        '_analyze_complete_cross_impacts',
        '_analyze_impair_pair_to_so_impact',
        '_analyze_desync_sync_to_pbt_impact',
        '_identify_desync_periods',
        '_analyze_desync_sync_to_so_impact',
        '_analyze_combined_to_pbt_impact',
        '_analyze_combined_to_so_impact',
        '_analyze_tri_dimensional_impacts',
        '_analyze_variations_impact_on_outcomes',
        '_analyze_consecutive_length_impact',
        '_find_consecutive_sequences_with_positions',
        '_calculate_asymmetric_impair_alert_level',
        '_find_consecutive_sequences',
        '_calculate_asymmetric_pair_alert_level',
        '_calculate_impair_rarity_score',
        '_calculate_pair_commonality_score',
        '_calculate_asymmetric_significance',
        '_identify_dominant_desync_sync_so_pattern',
        '_calculate_combined_so_impact_strength',
        '_calculate_combined_pbt_impact_strength',
        '_identify_dominant_impair_pair_so_pattern',
        '_calculate_overall_impact_strength',
        '_analyze_transition_moments_impact',
        '_calculate_distribution',
        '_analyze_desync_periods_impact',
        '_analyze_combined_state_changes_impact',
        '_analyze_temporal_correlation_evolution',
        '_calculate_phase_impair_pair_pb_correlation',
        '_calculate_phase_impair_pair_so_correlation',
        '_calculate_phase_sync_desync_pb_correlation',
        '_calculate_phase_sync_desync_so_correlation',
        '_calculate_phase_correlation_strength',
        '_analyze_correlation_trend',
        '_calculate_correlation_stability',
        '_calculate_variance',
        '_generate_temporal_recommendation',
        '_calculate_evolution_strength',
        '_calculate_temporal_consistency',
        '_calculate_temporal_predictability',
        '_calculate_variation_strength_analysis',
        '_extract_consecutive_length_strength',
        '_extract_transition_moments_strength',
        '_extract_desync_periods_strength',
        '_extract_combined_state_changes_strength',
        '_extract_temporal_evolution_strength',
        '_calculate_confidence_level',
        '_generate_exploitation_recommendation',
        '_identify_best_prediction_context',
        '_calculate_strength_distribution',
        '_calculate_variation_consistency',
        '_assess_sample_size_adequacy',
        '_calculate_statistical_significance',
        '_calculate_pattern_stability',
        '_assess_overall_quality',
        '_identify_enhanced_dominant_correlations',
        '_identify_enhanced_high_confidence_zones',
        '_generate_impair_pair_optimized_sequence',
        '_generate_sync_based_sequence',
        '_generate_combined_index_sequence',
        '_generate_so_pattern_sequence',
        '_enrich_sequences_with_complete_indexes',
        '_classify_combined_transition_type',
        '_count_consecutive_pattern'
    ]

def find_duplicates_and_clean():
    """Trouve les doublons et nettoie la liste"""
    
    universal_methods = get_31_universal_methods()
    rollout1_methods = get_74_rollout1_methods()
    
    print("🔍 VÉRIFICATION DES DOUBLONS ENTRE 74 ET 31 MÉTHODES")
    print("=" * 55)
    
    # Recherche des doublons
    duplicates = []
    unique_rollout1_methods = []
    
    for method in rollout1_methods:
        if method in universal_methods:
            duplicates.append(method)
        else:
            unique_rollout1_methods.append(method)
    
    print(f"Méthodes universelles : {len(universal_methods)}")
    print(f"Méthodes Rollout 1 : {len(rollout1_methods)}")
    print(f"Doublons trouvés : {len(duplicates)}")
    print(f"Méthodes Rollout 1 uniques : {len(unique_rollout1_methods)}")
    
    return {
        'universal_methods': universal_methods,
        'rollout1_methods': rollout1_methods,
        'duplicates': duplicates,
        'unique_rollout1_methods': unique_rollout1_methods,
        'total_unique': len(unique_rollout1_methods)
    }

def generate_cleaned_report(results):
    """Génère un rapport avec la liste nettoyée"""
    
    duplicates = results['duplicates']
    unique_methods = results['unique_rollout1_methods']
    
    report = f"""VÉRIFICATION DOUBLONS 74 MÉTHODES ROLLOUT 1 vs 31 UNIVERSELLES
================================================================

RÉSULTATS DE LA VÉRIFICATION :
==============================
Méthodes universelles : {len(results['universal_methods'])}
Méthodes Rollout 1 initiales : {len(results['rollout1_methods'])}
Doublons trouvés : {len(duplicates)}
Méthodes Rollout 1 uniques : {len(unique_methods)}

"""
    
    if duplicates:
        report += f"DOUBLONS DÉTECTÉS ({len(duplicates)}) :\n"
        report += "=" * 35 + "\n\n"
        
        for i, duplicate in enumerate(duplicates, 1):
            report += f"{i:2d}. {duplicate}\n"
            report += "    ❌ ÉLIMINÉ - Déjà présent dans les 31 méthodes universelles\n\n"
    else:
        report += "✅ AUCUN DOUBLON DÉTECTÉ\n"
        report += "Toutes les 74 méthodes sont uniques par rapport aux 31 universelles\n\n"
    
    report += f"LISTE NETTOYÉE DES MÉTHODES ROLLOUT 1 CLUSTER 0 ({len(unique_methods)}) :\n"
    report += "=" * 65 + "\n\n"
    
    for i, method in enumerate(unique_methods, 1):
        report += f"{i:2d}. {method}\n"
    
    report += f"""

CLASSIFICATION FONCTIONNELLE DES {len(unique_methods)} MÉTHODES UNIQUES :
{'=' * 60}

🔥 ANALYSE DES BIAIS STRUCTURELS :
"""
    
    # Classification des méthodes uniques
    analysis_methods = [m for m in unique_methods if '_analyze_' in m and any(keyword in m for keyword in ['impact', 'cross', 'tri_dimensional', 'variations', 'consecutive', 'transition', 'desync', 'combined', 'temporal', 'correlation'])]
    
    calculation_methods = [m for m in unique_methods if '_calculate_' in m and any(keyword in m for keyword in ['phase', 'correlation', 'strength', 'asymmetric', 'rarity', 'significance', 'distribution', 'variance', 'evolution', 'consistency', 'predictability', 'variation', 'confidence', 'statistical', 'pattern'])]
    
    identification_methods = [m for m in unique_methods if '_identify_' in m or '_find_' in m or '_extract_' in m]
    
    generation_methods = [m for m in unique_methods if '_generate_' in m]
    
    other_methods = [m for m in unique_methods if m not in analysis_methods + calculation_methods + identification_methods + generation_methods]
    
    for method in analysis_methods:
        report += f"- {method}\n"
    
    report += f"\n📊 CALCULS DE CORRÉLATIONS ET MÉTRIQUES :\n"
    for method in calculation_methods:
        report += f"- {method}\n"
    
    report += f"\n🔍 IDENTIFICATION ET EXTRACTION :\n"
    for method in identification_methods:
        report += f"- {method}\n"
    
    report += f"\n🎲 GÉNÉRATION ANALYTIQUE :\n"
    for method in generation_methods:
        report += f"- {method}\n"
    
    if other_methods:
        report += f"\n🔧 AUTRES MÉTHODES :\n"
        for method in other_methods:
            report += f"- {method}\n"
    
    report += f"""

IMPACT DE LA DÉDUPLICATION :
===========================
Méthodes Rollout 1 initiales : {len(results['rollout1_methods'])}
Doublons éliminés : {len(duplicates)}
Méthodes uniques restantes : {len(unique_methods)}
Réduction : {len(duplicates)} méthodes ({len(duplicates)/len(results['rollout1_methods'])*100:.1f}%)

CONCLUSION :
============
"""
    
    if duplicates:
        report += f"✅ {len(duplicates)} doublons détectés et éliminés avec succès\n"
        report += f"✅ {len(unique_methods)} méthodes Rollout 1 uniques identifiées\n"
        report += "✅ Aucun conflit avec les méthodes universelles existantes\n"
    else:
        report += "✅ Aucun doublon détecté - Toutes les méthodes sont uniques\n"
        report += "✅ Les 74 méthodes Rollout 1 sont complémentaires aux 31 universelles\n"
    
    report += f"✅ Prêt pour intégration des {len(unique_methods)} méthodes dans l'architecture universelle\n"
    
    return report

def main():
    """Fonction principale"""
    print("🔍 VÉRIFICATION DES DOUBLONS 74 vs 31 MÉTHODES")
    
    results = find_duplicates_and_clean()
    
    # Génération du rapport
    report = generate_cleaned_report(results)
    
    # Sauvegarde
    output_file = 'centralisation_methodes/liste_rollout1_cluster0_sans_doublons.txt'
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"\n📋 Rapport de déduplication sauvé dans {output_file}")
    
    if results['duplicates']:
        print(f"⚠️ {len(results['duplicates'])} doublons trouvés et éliminés")
        print(f"✅ {results['total_unique']} méthodes Rollout 1 uniques restantes")
    else:
        print("✅ Aucun doublon détecté - Toutes les 74 méthodes sont uniques")

if __name__ == "__main__":
    main()
