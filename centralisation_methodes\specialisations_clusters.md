# 🎯 SPÉCIALISATIONS DÉTAILLÉES DES 8 CLUSTERS AZR

## 📋 **CLUSTER PAR DÉFAUT (C0-C1) - RÉFÉRENCE**

### **ROLLOUT 1 ANALYSEUR**
- **Logique :** Analyse complète des 5 index avec corrélations 1,2,3 → 4,5
- **Fenêtre récente :** 3 manches (standard)
- **Spécialisation :** Au<PERSON>ne (logique de référence pure)
- **Priorités :** IMPAIR > PAIR > SYNC > COMBINÉ > Corrélations

### **ROLLOUT 2 GÉNÉRATEUR**
- **Logique :** Génération 4 séquences candidates basées sur analyse
- **Stratégies :** Exploitation biais, Anti-pattern, Équilibre, Sécurité
- **Fenêtre génération :** Standard selon analyse

### **ROLLOUT 3 PRÉDICTEUR**
- **Logique :** Sélection optimale avec critères S/O prioritaires
- **Critères :** <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Performance

---

## 🎯 **CLUSTER C2 - PATTERNS COURTS (2-3 manches)**

### **ROLLOUT 1 ANALYSEUR**
- **Logique de base :** IDENTIQUE C0 (5 index + corrélations 1,2,3 → 4,5)
- **Spécialisation EN PLUS :**
  - **Fenêtre récente :** 2 manches (réactivité maximale)
  - **Focus patterns courts :** Séquences ≤ 3 manches
  - **Bonus réactivité :** Détection micro-changements
  - **Seuils sensibles :** Réaction rapide aux variations
- **Paramètres spécialisés :**
  - `c2_short_pattern_min_length: 2`
  - `c2_short_pattern_max_length: 3`
  - `c2_reactivity_threshold: 0.8`
  - `c2_micro_change_bonus: 0.1`

### **ROLLOUT 2 GÉNÉRATEUR**
- **Logique de base :** IDENTIQUE C0
- **Spécialisation EN PLUS :**
  - **Génération réactive :** Séquences courtes privilégiées
  - **Adaptation rapide :** Changement stratégie selon micro-signaux
  - **Fenêtre génération :** 2-3 manches maximum

### **ROLLOUT 3 PRÉDICTEUR**
- **Logique de base :** IDENTIQUE C0
- **Spécialisation EN PLUS :**
  - **Sélection réactive :** Privilégie prédictions immédiates
  - **Confiance ajustée :** Bonus pour patterns courts détectés

---

## 🎯 **CLUSTER C3 - PATTERNS MOYENS (4-6 manches)**

### **ROLLOUT 1 ANALYSEUR**
- **Logique de base :** IDENTIQUE C0 (5 index + corrélations 1,2,3 → 4,5)
- **Spécialisation EN PLUS :**
  - **Fenêtre récente :** 4 manches (vision élargie)
  - **Focus patterns moyens :** Séquences 4-6 manches
  - **Équilibre réactivité/continuité :** Vision plus large
  - **Détection début patterns :** Anticipation patterns moyens
- **Paramètres spécialisés :**
  - `c3_medium_pattern_min_length: 4`
  - `c3_medium_pattern_max_length: 6`
  - `c3_vision_threshold: 0.6`
  - `c3_balance_bonus: 0.15`

### **ROLLOUT 2 GÉNÉRATEUR**
- **Logique de base :** IDENTIQUE C0
- **Spécialisation EN PLUS :**
  - **Génération équilibrée :** Séquences moyennes privilégiées
  - **Vision élargie :** Considère contexte 4-6 manches
  - **Stratégies moyennes :** Équilibre entre réactivité et continuité

### **ROLLOUT 3 PRÉDICTEUR**
- **Logique de base :** IDENTIQUE C0
- **Spécialisation EN PLUS :**
  - **Sélection équilibrée :** Privilégie patterns moyens
  - **Confiance progressive :** Bonus pour continuité détectée

---

## 🎯 **CLUSTER C4 - PATTERNS LONGS (7+ manches)**

### **ROLLOUT 1 ANALYSEUR**
- **Logique de base :** IDENTIQUE C0 (5 index + corrélations 1,2,3 → 4,5)
- **Spécialisation EN PLUS :**
  - **Fenêtre récente :** 6 manches (mémoire étendue)
  - **Focus patterns longs :** Séquences ≥ 7 manches
  - **Mémoire étendue :** Analyse historique approfondie
  - **Continuité privilégiée :** Détection tendances longues
- **Paramètres spécialisés :**
  - `c4_long_pattern_min_length: 7`
  - `c4_long_pattern_max_length: 15`
  - `c4_continuity_threshold: 0.7`
  - `c4_memory_bonus: 0.2`

### **ROLLOUT 2 GÉNÉRATEUR**
- **Logique de base :** IDENTIQUE C0
- **Spécialisation EN PLUS :**
  - **Génération continue :** Séquences longues privilégiées
  - **Mémoire étendue :** Utilise historique approfondi
  - **Stratégies longues :** Continuité et persistance

### **ROLLOUT 3 PRÉDICTEUR**
- **Logique de base :** IDENTIQUE C0
- **Spécialisation EN PLUS :**
  - **Sélection continue :** Privilégie patterns longs
  - **Confiance cumulative :** Bonus pour persistance détectée

---

## 🎯 **CLUSTER C5 - CORRÉLATIONS IMPAIR/PAIR**

### **ROLLOUT 1 ANALYSEUR**
- **Logique de base :** IDENTIQUE C0 (5 index + corrélations 1,2,3 → 4,5)
- **Spécialisation EN PLUS :**
  - **Fenêtre récente :** 3 manches (standard)
  - **Focus corrélations :** IMPAIR/PAIR avec tous les index
  - **Analyse multi-dimensionnelle :** Corrélations croisées
  - **Détection patterns cachés :** Relations non-évidentes
- **Paramètres spécialisés :**
  - `c5_correlation_threshold: 0.65`
  - `c5_impair_pair_bonus: 0.12`
  - `c5_multi_dimensional_bonus: 0.18`

### **ROLLOUT 2 GÉNÉRATEUR**
- **Logique de base :** IDENTIQUE C0
- **Spécialisation EN PLUS :**
  - **Génération corrélée :** Exploite corrélations IMPAIR/PAIR
  - **Stratégies multi-dimensionnelles :** Combine plusieurs corrélations
  - **Patterns cachés :** Génère selon relations non-évidentes

### **ROLLOUT 3 PRÉDICTEUR**
- **Logique de base :** IDENTIQUE C0
- **Spécialisation EN PLUS :**
  - **Sélection corrélée :** Privilégie corrélations fortes
  - **Confiance multi-dimensionnelle :** Bonus pour corrélations multiples

---

## 🎯 **CLUSTER C6 - ÉTATS SYNC/DESYNC**

### **ROLLOUT 1 ANALYSEUR**
- **Logique de base :** IDENTIQUE C0 (5 index + corrélations 1,2,3 → 4,5)
- **Spécialisation EN PLUS :**
  - **Fenêtre récente :** 3 manches (standard)
  - **Focus SYNC/DESYNC :** États de synchronisation privilégiés
  - **Détection ruptures :** Changements d'états SYNC/DESYNC
  - **Alternance optimisée :** Patterns d'alternance
- **Paramètres spécialisés :**
  - `c6_sync_desync_threshold: 0.55`
  - `c6_alternation_bonus: 0.14`
  - `c6_rupture_detection_bonus: 0.16`

### **ROLLOUT 2 GÉNÉRATEUR**
- **Logique de base :** IDENTIQUE C0
- **Spécialisation EN PLUS :**
  - **Génération synchronisée :** Exploite états SYNC/DESYNC
  - **Stratégies d'alternance :** Patterns SYNC/DESYNC
  - **Détection ruptures :** Anticipation changements d'états

### **ROLLOUT 3 PRÉDICTEUR**
- **Logique de base :** IDENTIQUE C0
- **Spécialisation EN PLUS :**
  - **Sélection synchronisée :** Privilégie cohérence SYNC/DESYNC
  - **Confiance d'alternance :** Bonus pour patterns SYNC/DESYNC

---

## 🎯 **CLUSTER C7 - ADAPTATIF CONTEXTUEL**

### **ROLLOUT 1 ANALYSEUR**
- **Logique de base :** IDENTIQUE C0 (5 index + corrélations 1,2,3 → 4,5)
- **Spécialisation EN PLUS :**
  - **Fenêtre récente :** 5 manches (adaptative)
  - **Adaptation contextuelle :** Ajuste selon contexte de jeu
  - **Apprentissage continu :** Améliore selon performance
  - **Variabilité gérée :** S'adapte aux changements
- **Paramètres spécialisés :**
  - `c7_adaptation_threshold: 0.5`
  - `c7_context_bonus: 0.25`
  - `c7_learning_rate: 0.1`
  - `c7_variability_bonus: 0.2`

### **ROLLOUT 2 GÉNÉRATEUR**
- **Logique de base :** IDENTIQUE C0
- **Spécialisation EN PLUS :**
  - **Génération adaptative :** Ajuste selon contexte
  - **Stratégies variables :** Change selon performance
  - **Apprentissage intégré :** Améliore génération

### **ROLLOUT 3 PRÉDICTEUR**
- **Logique de base :** IDENTIQUE C0
- **Spécialisation EN PLUS :**
  - **Sélection adaptative :** Ajuste selon contexte
  - **Confiance variable :** Adapte selon performance

---

## 🏗️ **ARCHITECTURE COMMUNE À TOUS LES CLUSTERS**

### **STRUCTURE IDENTIQUE (LOGIQUE DE RÉFÉRENCE)**
1. **Extraction systématique des 5 index** (IDENTIQUE tous)
2. **Analyse des corrélations 1,2,3 → 4,5** (IDENTIQUE tous)
3. **Application spécialisation EN PLUS** de la logique de base
4. **Utilisation fenêtres récentes DANS** le cadre de cette structure

### **PARAMÈTRES CENTRALISÉS DANS AZRConfig**
- **Fenêtres récentes spécialisées** par cluster
- **Seuils et bonus spécialisés** par cluster
- **Multiplicateurs de confiance** par cluster
- **Paramètres de spécialisation** par cluster

### **MÉTHODES CENTRALISÉES À CRÉER**
- **Une méthode universelle** pour Rollout 1 Analyseur
- **Une méthode universelle** pour Rollout 2 Générateur
- **Une méthode universelle** pour Rollout 3 Prédicteur
- **Adaptation automatique** selon cluster_id et paramètres AZRConfig

---

## 🎯 **OBJECTIF FINAL**

**UNE SEULE IMPLÉMENTATION** qui s'adapte automatiquement selon :
- **cluster_id** (2, 3, 4, 5, 6, 7)
- **Paramètres centralisés** dans AZRConfig
- **Spécialisations appliquées EN PLUS** de la logique de base
- **Fenêtres récentes intégrées DANS** la structure commune

**RÉSULTAT :** 8 comportements différents avec 3 méthodes universelles.
