#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour lire le fichier de conversation trouvé
"""

import json
from pathlib import Path

def read_conversation_file():
    """Lit le fichier de conversation spécifique trouvé"""
    
    file_path = Path("C:/Users/<USER>/AppData/Roaming/Code/User/workspaceStorage/464cd6b9a395b786276aa704dc30d7e0/Augment.vscode-augment/augment-user-assets/checkpoint-documents/cf038300-3893-4ac1-9163-30802a5c5580/document-c__Users_Administrateur_Desktop_azr_MEO_conversation_log.txt-1748423380391-e0ad7a71-c20e-4009-83ce-cdf7fbf997c4.json")
    
    print("🔍 LECTURE DU FICHIER DE CONVERSATION TROUVÉ")
    print("=" * 60)
    print(f"📁 Fichier: {file_path.name}")
    print(f"📂 Répertoire: {file_path.parent}")
    
    if not file_path.exists():
        print("❌ Fichier non trouvé")
        return None
    
    try:
        # Lire le fichier
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"📏 Taille du fichier: {len(content)} caractères")
        
        # Essayer de parser en JSON
        try:
            data = json.loads(content)
            print("✅ JSON valide détecté")
            
            # Afficher la structure
            if isinstance(data, dict):
                print(f"🔑 Clés principales: {list(data.keys())}")
                
                # Afficher le contenu complet formaté
                print(f"\n📝 CONTENU COMPLET DU FICHIER:")
                print("=" * 80)
                print(json.dumps(data, indent=2, ensure_ascii=False))
                print("=" * 80)
                
                # Chercher spécifiquement "tu avais raison"
                content_lower = content.lower()
                if "tu avais raison" in content_lower:
                    print(f"\n🎉 PHRASE 'TU AVAIS RAISON' TROUVÉE!")
                    
                    # Extraire le contexte
                    start_pos = content_lower.find("tu avais raison")
                    context_start = max(0, start_pos - 300)
                    context_end = min(len(content), start_pos + 500)
                    context = content[context_start:context_end]
                    
                    print(f"\n📝 CONTEXTE AUTOUR DE 'TU AVAIS RAISON':")
                    print("-" * 60)
                    print(context)
                    print("-" * 60)
                
                if "finalement" in content_lower:
                    print(f"\n🎉 MOT 'FINALEMENT' TROUVÉ!")
                    
                    # Extraire le contexte
                    start_pos = content_lower.find("finalement")
                    context_start = max(0, start_pos - 300)
                    context_end = min(len(content), start_pos + 500)
                    context = content[context_start:context_end]
                    
                    print(f"\n📝 CONTEXTE AUTOUR DE 'FINALEMENT':")
                    print("-" * 60)
                    print(context)
                    print("-" * 60)
            
            return data
            
        except json.JSONDecodeError:
            print("⚠️ Pas du JSON valide, affichage du contenu brut")
            print(f"\n📝 CONTENU BRUT:")
            print("=" * 80)
            print(content)
            print("=" * 80)
            return content
            
    except Exception as e:
        print(f"❌ Erreur lors de la lecture: {e}")
        return None

if __name__ == "__main__":
    print("🚀 LECTURE DU FICHIER DE CONVERSATION TROUVÉ")
    print("=" * 60)
    
    result = read_conversation_file()
    
    if result:
        print(f"\n✅ FICHIER LU AVEC SUCCÈS!")
    else:
        print(f"\n❌ Erreur lors de la lecture du fichier")
