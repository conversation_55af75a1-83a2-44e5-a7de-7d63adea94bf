MÉTHODES ROLLOUT 1 CLUSTER 0 (PAR DÉFAUT) - IDENTIFICATION PRÉCISE
====================================================================

MÉTHODOLOGIE :
=============
1. Analyse de _rollout_analyzer (Rollout 1 cluster 0/1 standard)
2. Extraction récursive des méthodes appelées
3. Vérification analytique (pas de génération)
4. Confirmation par analyse de contenu

RÉSULTATS :
==========
Méthodes directement appelées : 10
Total méthodes trouvées : 117
Méthodes analytiques vérifiées : 74
Méthodes exclues : 43

MÉTHODES DIRECTEMENT APPELÉES PAR _ROLLOUT_ANALYZER :
===================================================
 1. _generate_bias_generation_guidance
 2. _analyze_combined_structural_bias
 3. _correlate_bias_to_so_variations
 4. _generate_bias_quick_access
 5. _generate_bias_signals_summary
 6. _analyze_pair_priority_2_autonomous
 7. _correlate_bias_to_pb_variations
 8. _analyze_impair_consecutive_bias
 9. _analyze_sync_alternation_bias
10. _generate_priority_based_synthesis_autonomous

MÉTHODES ROLLOUT 1 CLUSTER 0 VÉRIFIÉES (74) :
=======================================================

 1. _generate_all_possible_sequences
    Score analytique : 159
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 6194

 2. _generate_fallback_sequences
    Score analytique : 159
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 6300

 3. _generate_so_based_sequence
    Score analytique : 159
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 6235

 4. _classify_confidence_level
    Score analytique : 159
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 6254

 5. _convert_pb_sequence_to_so_with_history
    Score analytique : 155
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 6121

 6. _calculate_sequence_probability
    Score analytique : 155
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 6093

 7. _calculate_sequence_quality_metrics
    Score analytique : 152
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 6001

 8. _generate_pair_sync_sequence
    Score analytique : 151
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 5907

 9. _generate_impair_sync_sequence
    Score analytique : 151
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 5865

10. _generate_pb_sequence
    Score analytique : 151
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 5954

11. _generate_generic_signal_sequence
    Score analytique : 151
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 5820

12. _analyze_complete_cross_impacts
    Score analytique : 134
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 5447

13. _analyze_impair_pair_to_so_impact
    Score analytique : 125
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 5362

14. _analyze_desync_sync_to_pbt_impact
    Score analytique : 123
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 5334

15. _identify_desync_periods
    Score analytique : 122
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 5300

16. _analyze_desync_sync_to_so_impact
    Score analytique : 121
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 5276

17. _analyze_combined_to_pbt_impact
    Score analytique : 119
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 5248

18. _analyze_combined_to_so_impact
    Score analytique : 117
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 5204

19. _analyze_tri_dimensional_impacts
    Score analytique : 115
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 5168

20. _analyze_variations_impact_on_outcomes
    Score analytique : 110
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 5126

21. _analyze_consecutive_length_impact
    Score analytique : 102
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 5061

22. _find_consecutive_sequences_with_positions
    Score analytique : 101
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 4989

23. _calculate_asymmetric_impair_alert_level
    Score analytique : 101
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 4931

24. _find_consecutive_sequences
    Score analytique : 101
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 4958

25. _calculate_asymmetric_pair_alert_level
    Score analytique : 100
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 4913

26. _calculate_impair_rarity_score
    Score analytique : 99
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 4895

27. _calculate_pair_commonality_score
    Score analytique : 98
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 4876

28. _calculate_asymmetric_significance
    Score analytique : 97
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 4859

29. _identify_dominant_desync_sync_so_pattern
    Score analytique : 94
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 4831

30. _calculate_combined_so_impact_strength
    Score analytique : 93
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 4775

31. _calculate_combined_pbt_impact_strength
    Score analytique : 92
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 4711

32. _identify_dominant_impair_pair_so_pattern
    Score analytique : 91
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 4640

33. _calculate_overall_impact_strength
    Score analytique : 90
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 4584

34. _analyze_transition_moments_impact
    Score analytique : 89
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 4487

35. _calculate_distribution
    Score analytique : 88
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 4322

36. _analyze_desync_periods_impact
    Score analytique : 87
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 4270

37. _analyze_combined_state_changes_impact
    Score analytique : 86
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 4014

38. _analyze_temporal_correlation_evolution
    Score analytique : 85
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 3731

39. _calculate_phase_impair_pair_pb_correlation
    Score analytique : 70
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 3530

40. _calculate_phase_impair_pair_so_correlation
    Score analytique : 69
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 3473

41. _calculate_phase_sync_desync_pb_correlation
    Score analytique : 68
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 3419

42. _calculate_phase_sync_desync_so_correlation
    Score analytique : 67
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 3362

43. _calculate_phase_correlation_strength
    Score analytique : 66
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 3308

44. _analyze_correlation_trend
    Score analytique : 65
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 3282

45. _calculate_correlation_stability
    Score analytique : 64
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 3247

46. _calculate_variance
    Score analytique : 62
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 3234

47. _generate_temporal_recommendation
    Score analytique : 61
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 3223

48. _calculate_evolution_strength
    Score analytique : 61
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 3206

49. _calculate_temporal_consistency
    Score analytique : 60
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 3190

50. _calculate_temporal_predictability
    Score analytique : 58
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 3173

51. _calculate_variation_strength_analysis
    Score analytique : 57
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 3152

52. _extract_consecutive_length_strength
    Score analytique : 41
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 2980

53. _extract_transition_moments_strength
    Score analytique : 40
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 2943

54. _extract_desync_periods_strength
    Score analytique : 39
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 2912

55. _extract_combined_state_changes_strength
    Score analytique : 38
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 2882

56. _extract_temporal_evolution_strength
    Score analytique : 37
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 2861

57. _calculate_confidence_level
    Score analytique : 36
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 2843

58. _generate_exploitation_recommendation
    Score analytique : 35
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 2829

59. _identify_best_prediction_context
    Score analytique : 35
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 2811

60. _calculate_strength_distribution
    Score analytique : 34
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 2784

61. _calculate_variation_consistency
    Score analytique : 33
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 2767

62. _assess_sample_size_adequacy
    Score analytique : 32
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 2749

63. _calculate_statistical_significance
    Score analytique : 31
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 2725

64. _calculate_pattern_stability
    Score analytique : 29
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 2715

65. _assess_overall_quality
    Score analytique : 27
    Mots-clés trouvés : analyze, calculate, assess, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 2703

66. _identify_enhanced_dominant_correlations
    Score analytique : 26
    Mots-clés trouvés : analyze, calculate, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 2686

67. _identify_enhanced_high_confidence_zones
    Score analytique : 25
    Mots-clés trouvés : analyze, calculate, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 2388

68. _generate_impair_pair_optimized_sequence
    Score analytique : 24
    Mots-clés trouvés : analyze, calculate, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 1787

69. _generate_sync_based_sequence
    Score analytique : 23
    Mots-clés trouvés : analyze, calculate, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 1608

70. _generate_combined_index_sequence
    Score analytique : 22
    Mots-clés trouvés : analyze, calculate, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 1404

71. _generate_so_pattern_sequence
    Score analytique : 21
    Mots-clés trouvés : analyze, calculate, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 1167

72. _enrich_sequences_with_complete_indexes
    Score analytique : 20
    Mots-clés trouvés : analyze, calculate, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 920

73. _classify_combined_transition_type
    Score analytique : 19
    Mots-clés trouvés : analyze, calculate, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 663

74. _count_consecutive_pattern
    Score analytique : 18
    Mots-clés trouvés : analyze, calculate, evaluate, extract, identify
    Retourne analyse : OUI
    Lignes de code : 233


MÉTHODES EXCLUES (43) :
==============================

❌ _correlate_bias_to_so_variations - Contient génération: generate_sequence
❌ _convert_pb_sequence_to_so - Contient génération: generate_sequence
❌ _calculate_global_strength_metrics - Contient génération: generate_sequence
❌ _generate_bias_quick_access - Contient génération: generate_sequence
❌ _generate_sequence_from_signal - Contient génération: generate_sequence
❌ _get_cluster_specialization_params - Contient génération: generate_sequence
❌ _assess_risk_reward_ratio - Contient génération: generate_sequence
❌ _define_optimized_generation_space - Contient génération: generate_sequence
❌ _calculate_rollout_consensus - Contient génération: generate_sequence
❌ _generate_sequences_from_signals - Contient génération: generate_sequence
❌ _calculate_sequence_score - Contient génération: generate_sequence
❌ _apply_veto_system - Méthode non trouvée
❌ _calculate_confidence_risk_factors - Contient génération: generate_sequence
❌ _analyze_combined_structural_bias - Contient génération: generate_sequence
❌ _validate_sequence_logic - Contient génération: generate_sequence
❌ _generate_bias_generation_guidance - Contient génération: generate_sequence
❌ _analyze_sync_alternation_bias - Contient génération: generate_sequence
❌ _evaluate_signal_alignment - Contient génération: generate_sequence
❌ _calculate_cluster_confidence_azr_calibrated - Contient génération: generate_sequence
❌ _correlate_bias_to_pb_variations - Contient génération: generate_sequence
❌ _analyze_sync_bias_specialized - Contient génération: generate_sequence
❌ _get_last_historical_pb_result - Contient génération: generate_sequence
❌ _generate_priority_based_synthesis_autonomous - Contient génération: generate_sequence
❌ _apply_c2_short_patterns_specialization - Contient génération: generate_sequence
❌ _demonstrate_anti_blindness_system - Méthode non trouvée
❌ _apply_cluster_specialization - Contient génération: generate_sequence
❌ _analyze_impair_consecutive_bias - Contient génération: generate_sequence
❌ _correlate_impair_with_pb - Contient génération: generate_sequence
❌ _analyze_impair_bias_specialized - Contient génération: generate_sequence
❌ _correlate_impair_with_so - Contient génération: generate_sequence
❌ _analyze_pair_priority_2_autonomous - Contient génération: generate_sequence
❌ _analyze_sequence_consistency - Contient génération: generate_sequence
❌ _analyze_sync_alternation_bias_c2_specialized - Contient génération: generate_sequence
❌ _evaluate_sequence_quality - Contient génération: generate_sequence
❌ _calculate_epistemic_uncertainty - Contient génération: generate_sequence
❌ _generate_bias_signals_summary - Contient génération: generate_sequence
❌ _analyze_impair_consecutive_bias_c2_specialized - Contient génération: generate_sequence
❌ _select_best_sequence - Contient génération: generate_sequence
❌ _correlate_impair_with_sync - Contient génération: generate_sequence
❌ _evaluate_fallback_alignment - Contient génération: generate_sequence
❌ _calculate_variations_impact - Contient génération: generate_sequence
❌ _calculate_cross_index_impacts - Contient génération: generate_sequence
❌ _correlate_impair_with_combined - Contient génération: generate_sequence

VALIDATION :
===========
✅ Ces méthodes appartiennent CERTAINEMENT au Rollout 1 cluster 0
✅ Elles sont appelées directement ou indirectement par _rollout_analyzer
✅ Elles sont purement analytiques (pas de génération)
✅ Elles analysent les indices 1,2,3 et leur impact sur 4,5

FONCTIONNALITÉS ROLLOUT 1 CLUSTER 0 :
====================================
- Analyse des biais IMPAIR consécutifs (priorité 1)
- Analyse des alternances SYNC/DESYNC (priorité 2)  
- Analyse des biais COMBINED (priorité 3)
- Corrélations croisées indices → P/B/T et S/O
- Mesure des impacts structurels
- Évaluation des déviations statistiques

CONCLUSION :
============
Ces 74 méthodes constituent le cœur analytique du 
Rollout 1 pour le cluster par défaut (C0-C1 standard).

Elles représentent les fonctionnalités manquantes à intégrer dans 
les méthodes universelles pour compléter l'architecture.
