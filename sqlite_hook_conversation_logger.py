#!/usr/bin/env python3
"""
HOOK SQLITE POUR LOGGING AUTOMATIQUE DES CONVERSATIONS AUGMENT
==============================================================

Ce script crée un hook sur la base de données SQLite pour intercepter
automatiquement les écritures et créer un log parallèle en temps réel.

Stratégie:
1. Surveiller les modifications du fichier state.vscdb
2. Intercepter les changements dans la clé conversation
3. Écrire automatiquement dans un fichier parallèle
4. Fonctionnement transparent et automatique
"""

import sqlite3
import json
import time
import datetime
import threading
import os
from pathlib import Path
import hashlib

class SQLiteConversationHook:
    def __init__(self):
        self.state_file = Path("C:/Users/<USER>/AppData/Roaming/Code/User/workspaceStorage/a35ba43ef26792e61fe787a234121806/state.vscdb")
        self.log_file = Path("C:/Users/<USER>/Desktop/Travail/Projet7/conversation_augment_auto.txt")
        self.json_log = Path("C:/Users/<USER>/Desktop/Travail/Projet7/conversation_augment_auto.json")
        
        self.last_hash = ""
        self.last_message_count = 0
        self.running = False
        self.thread = None
        
        # Initialiser les fichiers de log
        self.init_log_files()
        
    def init_log_files(self):
        """Initialise les fichiers de log"""
        # Fichier texte
        with open(self.log_file, 'w', encoding='utf-8') as f:
            f.write("CONVERSATION AUGMENT - LOG AUTOMATIQUE\n")
            f.write(f"Démarré: {datetime.datetime.now().isoformat()}\n")
            f.write("=" * 80 + "\n\n")
        
        # Fichier JSON
        initial_data = {
            "started_at": datetime.datetime.now().isoformat(),
            "source_file": str(self.state_file),
            "messages": []
        }
        with open(self.json_log, 'w', encoding='utf-8') as f:
            json.dump(initial_data, f, indent=2, ensure_ascii=False)
    
    def get_conversation_hash(self, conversation_data):
        """Calcule un hash des données de conversation pour détecter les changements"""
        if not conversation_data:
            return ""
        
        # Convertir en string JSON stable
        json_str = json.dumps(conversation_data, sort_keys=True)
        return hashlib.md5(json_str.encode()).hexdigest()
    
    def extract_conversation_data(self):
        """Extrait les données de conversation"""
        try:
            if not self.state_file.exists():
                return None
            
            conn = sqlite3.connect(str(self.state_file))
            cursor = conn.cursor()
            
            cursor.execute("SELECT value FROM ItemTable WHERE key = 'memento/webviewView.augment-chat';")
            result = cursor.fetchone()
            
            if not result:
                conn.close()
                return None
            
            value = result[0]
            if isinstance(value, bytes):
                value_str = value.decode('utf-8')
            else:
                value_str = str(value)
            
            main_data = json.loads(value_str)
            webview_data = json.loads(main_data['webviewState'])
            
            conn.close()
            return webview_data
            
        except Exception as e:
            return None
    
    def format_new_message(self, message_data, index):
        """Formate un nouveau message pour le log"""
        timestamp = datetime.datetime.now().isoformat()
        
        # Extraire le contenu
        user_content = message_data.get('request_message', '')
        assistant_content = ""
        tools_used = []
        
        # Chercher la réponse de l'assistant
        if 'structured_output_nodes' in message_data:
            for node in message_data['structured_output_nodes']:
                if node.get('type') == 0 and node.get('content'):
                    assistant_content = node['content']
                elif node.get('type') == 5 and node.get('tool_use'):
                    tool_info = node['tool_use']
                    tools_used.append({
                        'name': tool_info.get('tool_name', 'unknown'),
                        'input': tool_info.get('input_json', '{}')
                    })
        
        return {
            'index': index,
            'timestamp': timestamp,
            'user_message': user_content,
            'assistant_response': assistant_content,
            'tools_used': tools_used,
            'request_id': message_data.get('request_id', ''),
            'raw_timestamp': message_data.get('timestamp', '')
        }
    
    def append_to_logs(self, new_messages):
        """Ajoute les nouveaux messages aux logs"""
        if not new_messages:
            return
        
        # Log texte
        with open(self.log_file, 'a', encoding='utf-8') as f:
            for msg in new_messages:
                f.write(f"[{msg['timestamp']}] MESSAGE #{msg['index']}\n")
                f.write("-" * 60 + "\n")
                
                if msg['user_message']:
                    f.write(f"👤 UTILISATEUR:\n{msg['user_message']}\n\n")
                
                if msg['assistant_response']:
                    f.write(f"🤖 ASSISTANT:\n{msg['assistant_response']}\n\n")
                
                if msg['tools_used']:
                    f.write(f"🔧 OUTILS: {', '.join([t['name'] for t in msg['tools_used']])}\n\n")
                
                f.write("=" * 80 + "\n\n")
        
        # Log JSON
        try:
            with open(self.json_log, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            data['messages'].extend(new_messages)
            data['last_update'] = datetime.datetime.now().isoformat()
            data['total_messages'] = len(data['messages'])
            
            with open(self.json_log, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            pass
    
    def monitor_changes(self):
        """Surveille les changements en continu"""
        print(f"🔍 Hook SQLite démarré - Surveillance: {self.state_file.name}")
        print(f"📝 Log automatique: {self.log_file.name}")
        
        while self.running:
            try:
                conversation_data = self.extract_conversation_data()
                
                if conversation_data:
                    current_hash = self.get_conversation_hash(conversation_data)
                    
                    if current_hash != self.last_hash:
                        conversations = conversation_data.get('conversations', {})
                        current_conv_id = conversation_data.get('currentConversationId')
                        
                        if current_conv_id and current_conv_id in conversations:
                            chat_history = conversations[current_conv_id].get('chatHistory', [])
                            
                            if len(chat_history) > self.last_message_count:
                                # Nouveaux messages détectés
                                new_messages_raw = chat_history[self.last_message_count:]
                                new_messages = []
                                
                                for i, msg_data in enumerate(new_messages_raw):
                                    formatted_msg = self.format_new_message(
                                        msg_data, 
                                        self.last_message_count + i + 1
                                    )
                                    new_messages.append(formatted_msg)
                                
                                # Écrire automatiquement dans les logs
                                self.append_to_logs(new_messages)
                                
                                self.last_message_count = len(chat_history)
                                
                                print(f"✅ {len(new_messages)} nouveaux messages loggés automatiquement")
                        
                        self.last_hash = current_hash
                
                time.sleep(0.5)  # Vérification très fréquente
                
            except Exception as e:
                time.sleep(2)
    
    def start_background_monitoring(self):
        """Démarre la surveillance en arrière-plan"""
        if self.running:
            return
        
        self.running = True
        self.thread = threading.Thread(target=self.monitor_changes, daemon=True)
        self.thread.start()
        
        print(f"🚀 Hook SQLite démarré en arrière-plan")
        print(f"📁 Fichiers de log créés:")
        print(f"   📝 Texte: {self.log_file}")
        print(f"   📊 JSON: {self.json_log}")
    
    def stop(self):
        """Arrête la surveillance"""
        self.running = False
        if self.thread:
            self.thread.join()

# Instance globale pour utilisation
conversation_hook = SQLiteConversationHook()

def start_auto_logging():
    """Démarre le logging automatique"""
    conversation_hook.start_background_monitoring()
    return conversation_hook

def stop_auto_logging():
    """Arrête le logging automatique"""
    conversation_hook.stop()

if __name__ == "__main__":
    # Test du système
    hook = start_auto_logging()
    
    try:
        print("🔄 Système de logging automatique actif...")
        print("💬 Tapez quelque chose dans Augment pour voir le logging en action")
        print("⏹️  Ctrl+C pour arrêter")
        
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n🛑 Arrêt du logging automatique")
        stop_auto_logging()
