import{S as k,i as j,s as H,a as J,b as $t,I as ut,J as dt,K as mt,L as pt,h as b,d as Q,M as ft,g as gt,n as M,j as V,E as ot,e as R,u as m,q as G,t as f,r as A,af as D,a0 as at,w as C,x as E,y as x,z as w,A as I,B as y,C as ht,ah as B,D as T,F as vt,G as O,H as rt,T as it,Q as S,c as L,f as N,a4 as xt,_ as wt}from"./SpinnerAugment-BJ4-L7QR.js";import"./design-system-init-DA68MSAy.js";import{h as q,W as _,e as X}from"./BaseButton-C6Dhmpxa.js";import{S as yt,O as bt}from"./OpenFileButton-DWw6TNYm.js";import{C as Rt,E as Lt}from"./chat-flags-model-IiDhbRsI.js";import{M as ct}from"./TextTooltipAugment-Bkzart3o.js";import{M as Mt}from"./MarkdownEditor-DWj1HgDp.js";import{A as U,M as W}from"./types-BSMhNRWH.js";import{D as z}from"./index-C-g0ZorP.js";import{B as St}from"./ButtonAugment-HnJOGilM.js";import{C as Ft}from"./chevron-down-B88L5wkj.js";import{F as Ct}from"./Filespan-BC4kxbfx.js";import"./open-in-new-window-DMlqLwqy.js";import"./types-LfaCSdmF.js";import"./chat-types-NgqNgjwU.js";import"./test_service_pb-B6vKXZrG.js";import"./file-paths-BcSg4gks.js";import"./types-a569v5Ol.js";import"./Content-Czt02SJi.js";import"./globals-D0QH3NT1.js";import"./IconButtonAugment-Certjadv.js";import"./TextAreaAugment-Cj5jK817.js";import"./lodash-ChYFUhWY.js";import"./CardAugment-BxTO-shY.js";function Et(a){let t,n,e=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},a[0]],o={};for(let s=0;s<e.length;s+=1)o=J(o,e[s]);return{c(){t=$t("svg"),n=new ut(!0),this.h()},l(s){t=dt(s,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var c=mt(t);n=pt(c,!0),c.forEach(b),this.h()},h(){n.a=null,Q(t,o)},m(s,c){ft(s,t,c),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M440.6 273.4c4.7-4.5 7.4-10.8 7.4-17.4s-2.7-12.8-7.4-17.4l-176-168c-9.6-9.2-24.8-8.8-33.9.8s-8.8 24.8.8 33.9L364.1 232H24c-13.3 0-24 10.7-24 24s10.7 24 24 24h340.1L231.4 406.6c-9.6 9.2-9.9 24.3-.8 33.9s24.3 9.9 33.9.8l176-168z"/>',t)},p(s,[c]){Q(t,o=gt(e,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},1&c&&s[0]]))},i:M,o:M,d(s){s&&b(t)}}}function It(a,t,n){return a.$$set=e=>{n(0,t=J(J({},t),V(e)))},[t=V(t)]}class lt extends k{constructor(t){super(),j(this,t,It,Et,H,{})}}function Y(a,t,n){const e=a.slice();return e[16]=t[n],e[18]=n,e}function Z(a){let t,n,e,o;function s(i){a[13](i)}function c(i){a[14](i)}let $={$$slots:{default:[Ot]},$$scope:{ctx:a}};return a[2]!==void 0&&($.requestClose=a[2]),a[1]!==void 0&&($.focusedIndex=a[1]),t=new z.Root({props:$}),C.push(()=>E(t,"requestClose",s)),C.push(()=>E(t,"focusedIndex",c)),{c(){x(t.$$.fragment)},m(i,l){w(t,i,l),o=!0},p(i,l){const r={};524401&l&&(r.$$scope={dirty:l,ctx:i}),!n&&4&l&&(n=!0,r.requestClose=i[2],I(()=>n=!1)),!e&&2&l&&(e=!0,r.focusedIndex=i[1],I(()=>e=!1)),t.$set(r)},i(i){o||(m(t.$$.fragment,i),o=!0)},o(i){f(t.$$.fragment,i),o=!1},d(i){y(t,i)}}}function Dt(a){let t,n=(a[5]?a[5].path:"Rules")+"";return{c(){t=O(n)},m(e,o){R(e,t,o)},p(e,o){32&o&&n!==(n=(e[5]?e[5].path:"Rules")+"")&&rt(t,n)},d(e){e&&b(t)}}}function zt(a){let t,n;return t=new lt({props:{slot:"iconLeft"}}),{c(){x(t.$$.fragment)},m(e,o){w(t,e,o),n=!0},p:M,i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){y(t,e)}}}function Nt(a){let t,n;return t=new Ft({props:{slot:"iconRight"}}),{c(){x(t.$$.fragment)},m(e,o){w(t,e,o),n=!0},p:M,i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){y(t,e)}}}function Bt(a){let t,n;return t=new St({props:{color:"neutral",variant:"soft",size:1,disabled:a[0],$$slots:{iconRight:[Nt],iconLeft:[zt],default:[Dt]},$$scope:{ctx:a}}}),{c(){x(t.$$.fragment)},m(e,o){w(t,e,o),n=!0},p(e,o){const s={};1&o&&(s.disabled=e[0]),524320&o&&(s.$$scope={dirty:o,ctx:e}),t.$set(s)},i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){y(t,e)}}}function Gt(a){let t,n;return t=new Ct({props:{filepath:a[16].path}}),{c(){x(t.$$.fragment)},m(e,o){w(t,e,o),n=!0},p(e,o){const s={};16&o&&(s.filepath=e[16].path),t.$set(s)},i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){y(t,e)}}}function tt(a){let t,n;function e(){return a[12](a[16])}return t=new z.Item({props:{onSelect:e,highlight:a[6]===a[18],$$slots:{default:[Gt]},$$scope:{ctx:a}}}),{c(){x(t.$$.fragment)},m(o,s){w(t,o,s),n=!0},p(o,s){a=o;const c={};16&s&&(c.onSelect=e),64&s&&(c.highlight=a[6]===a[18]),524304&s&&(c.$$scope={dirty:s,ctx:a}),t.$set(c)},i(o){n||(m(t.$$.fragment,o),n=!0)},o(o){f(t.$$.fragment,o),n=!1},d(o){y(t,o)}}}function et(a){let t,n,e,o;return t=new z.Separator({}),e=new z.Label({props:{$$slots:{default:[Tt]},$$scope:{ctx:a}}}),{c(){x(t.$$.fragment),n=T(),x(e.$$.fragment)},m(s,c){w(t,s,c),R(s,n,c),w(e,s,c),o=!0},p(s,c){const $={};524368&c&&($.$$scope={dirty:c,ctx:s}),e.$set($)},i(s){o||(m(t.$$.fragment,s),m(e.$$.fragment,s),o=!0)},o(s){f(t.$$.fragment,s),f(e.$$.fragment,s),o=!1},d(s){s&&b(n),y(t,s),y(e,s)}}}function At(a){let t,n=nt(a[4][a[6]])+"";return{c(){t=O(n)},m(e,o){R(e,t,o)},p(e,o){80&o&&n!==(n=nt(e[4][e[6]])+"")&&rt(t,n)},d(e){e&&b(t)}}}function Tt(a){let t,n;return t=new it({props:{size:1,color:"neutral",$$slots:{default:[At]},$$scope:{ctx:a}}}),{c(){x(t.$$.fragment)},m(e,o){w(t,e,o),n=!0},p(e,o){const s={};524368&o&&(s.$$scope={dirty:o,ctx:e}),t.$set(s)},i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){y(t,e)}}}function qt(a){let t,n,e,o=X(a[4]),s=[];for(let i=0;i<o.length;i+=1)s[i]=tt(Y(a,o,i));const c=i=>f(s[i],1,1,()=>{s[i]=null});let $=a[6]!==void 0&&a[4][a[6]]&&et(a);return{c(){for(let i=0;i<s.length;i+=1)s[i].c();t=T(),$&&$.c(),n=ot()},m(i,l){for(let r=0;r<s.length;r+=1)s[r]&&s[r].m(i,l);R(i,t,l),$&&$.m(i,l),R(i,n,l),e=!0},p(i,l){if(1104&l){let r;for(o=X(i[4]),r=0;r<o.length;r+=1){const d=Y(i,o,r);s[r]?(s[r].p(d,l),m(s[r],1)):(s[r]=tt(d),s[r].c(),m(s[r],1),s[r].m(t.parentNode,t))}for(G(),r=o.length;r<s.length;r+=1)c(r);A()}i[6]!==void 0&&i[4][i[6]]?$?($.p(i,l),80&l&&m($,1)):($=et(i),$.c(),m($,1),$.m(n.parentNode,n)):$&&(G(),f($,1,1,()=>{$=null}),A())},i(i){if(!e){for(let l=0;l<o.length;l+=1)m(s[l]);m($),e=!0}},o(i){s=s.filter(Boolean);for(let l=0;l<s.length;l+=1)f(s[l]);f($),e=!1},d(i){i&&(b(t),b(n)),vt(s,i),$&&$.d(i)}}}function Ot(a){let t,n,e,o;return t=new z.Trigger({props:{$$slots:{default:[Bt]},$$scope:{ctx:a}}}),e=new z.Content({props:{side:"bottom",align:"start",$$slots:{default:[qt]},$$scope:{ctx:a}}}),{c(){x(t.$$.fragment),n=T(),x(e.$$.fragment)},m(s,c){w(t,s,c),R(s,n,c),w(e,s,c),o=!0},p(s,c){const $={};524321&c&&($.$$scope={dirty:c,ctx:s}),t.$set($);const i={};524368&c&&(i.$$scope={dirty:c,ctx:s}),e.$set(i)},i(s){o||(m(t.$$.fragment,s),m(e.$$.fragment,s),o=!0)},o(s){f(t.$$.fragment,s),f(e.$$.fragment,s),o=!1},d(s){s&&b(n),y(t,s),y(e,s)}}}function _t(a){let t,n,e=!a[3]&&a[4].length>0&&Z(a);return{c(){e&&e.c(),t=ot()},m(o,s){e&&e.m(o,s),R(o,t,s),n=!0},p(o,[s]){!o[3]&&o[4].length>0?e?(e.p(o,s),24&s&&m(e,1)):(e=Z(o),e.c(),m(e,1),e.m(t.parentNode,t)):e&&(G(),f(e,1,1,()=>{e=null}),A())},i(o){n||(m(e),n=!0)},o(o){f(e),n=!1},d(o){o&&b(t),e&&e.d(o)}}}function nt(a){return`Move to ${a.path}`}function kt(a,t,n){let e,o,s,c,$=M,i=()=>($(),$=ht(v,g=>n(6,c=g)),v);a.$$.on_destroy.push(()=>$());let{onRuleSelected:l}=t,{disabled:r=!1}=t;const d=B([]);D(a,d,g=>n(4,o=g));const u=B(!0);D(a,u,g=>n(3,e=g));const h=B(void 0);let v;D(a,h,g=>n(5,s=g)),i();let p=()=>{};function F(g){h.set(g),l(g),p()}return at(()=>{u.set(!0),q.postMessage({type:_.getRulesListRequest,data:{query:"",maxResults:100}});const g=K=>{var P;((P=K.data)==null?void 0:P.type)===_.getRulesListResponse&&(d.set(K.data.data||[]),u.set(!1))};return window.addEventListener("message",g),()=>{window.removeEventListener("message",g)}}),a.$$set=g=>{"onRuleSelected"in g&&n(11,l=g.onRuleSelected),"disabled"in g&&n(0,r=g.disabled)},[r,v,p,e,o,s,c,d,u,h,F,l,g=>F(g),function(g){p=g,n(2,p)},function(g){v=g,i(n(1,v))}]}class jt extends k{constructor(t){super(),j(this,t,kt,_t,H,{onRuleSelected:11,disabled:0})}}function Ht(a){let t;return{c(){t=O("User Guidelines")},m(n,e){R(n,t,e)},d(n){n&&b(t)}}}function Ut(a){let t,n,e;return n=new lt({}),{c(){t=S("div"),x(n.$$.fragment),L(t,"slot","iconLeft"),L(t,"class","c-move-text-btn__left_icon svelte-1yddhs6")},m(o,s){R(o,t,s),w(n,t,null),e=!0},p:M,i(o){e||(m(n.$$.fragment,o),e=!0)},o(o){f(n.$$.fragment,o),e=!1},d(o){o&&b(t),y(n)}}}function st(a){let t,n,e;return n=new jt({props:{onRuleSelected:a[10],disabled:!a[2]}}),{c(){t=S("div"),x(n.$$.fragment),L(t,"class","c-move-text-btn svelte-1yddhs6")},m(o,s){R(o,t,s),w(n,t,null),e=!0},p(o,s){const c={};4&s&&(c.disabled=!o[2]),n.$set(c)},i(o){e||(m(n.$$.fragment,o),e=!0)},o(o){f(n.$$.fragment,o),e=!1},d(o){o&&b(t),y(n)}}}function Wt(a){let t;return{c(){t=O("Open Memories")},m(n,e){R(n,t,e)},d(n){n&&b(t)}}}function Jt(a){let t,n;return t=new it({props:{slot:"text",size:1,$$slots:{default:[Wt]},$$scope:{ctx:a}}}),{c(){x(t.$$.fragment)},m(e,o){w(t,e,o),n=!0},p(e,o){const s={};1048576&o&&(s.$$scope={dirty:o,ctx:e}),t.$set(s)},i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){y(t,e)}}}function Kt(a){let t,n,e,o,s,c,$,i,l;o=new yt({props:{tooltip:{neutral:"Move highlighted text to user guidelines",success:"Text moved to user guidelines"},stateVariant:{success:"solid",neutral:"soft"},defaultColor:"neutral",onClick:a[12],disabled:!a[2],stickyColor:!1,persistOnTooltipClose:!0,replaceIconOnSuccess:!0,size:1,$$slots:{iconLeft:[Ut],default:[Ht]},$$scope:{ctx:a}}});let r=a[5]&&st(a);return i=new bt({props:{size:1,path:a[1],variant:"soft",onOpenLocalFile:a[13],$$slots:{text:[Jt]},$$scope:{ctx:a}}}),{c(){t=S("div"),n=S("div"),e=S("div"),x(o.$$.fragment),s=T(),r&&r.c(),c=T(),$=S("div"),x(i.$$.fragment),L(e,"class","c-move-text-btn svelte-1yddhs6"),L(n,"class","l-file-controls-left svelte-1yddhs6"),L($,"class","l-file-controls-right svelte-1yddhs6"),L(t,"class","l-file-controls svelte-1yddhs6"),L(t,"slot","header")},m(d,u){R(d,t,u),N(t,n),N(n,e),w(o,e,null),N(n,s),r&&r.m(n,null),N(t,c),N(t,$),w(i,$,null),l=!0},p(d,u){const h={};4&u&&(h.disabled=!d[2]),1048576&u&&(h.$$scope={dirty:u,ctx:d}),o.$set(h),d[5]?r?(r.p(d,u),32&u&&m(r,1)):(r=st(d),r.c(),m(r,1),r.m(n,null)):r&&(G(),f(r,1,1,()=>{r=null}),A());const v={};2&u&&(v.path=d[1]),2&u&&(v.onOpenLocalFile=d[13]),1048576&u&&(v.$$scope={dirty:u,ctx:d}),i.$set(v)},i(d){l||(m(o.$$.fragment,d),m(r),m(i.$$.fragment,d),l=!0)},o(d){f(o.$$.fragment,d),f(r),f(i.$$.fragment,d),l=!1},d(d){d&&b(t),y(o),r&&r.d(),y(i)}}}function Pt(a){let t,n,e,o,s,c;function $(u){a[14](u)}function i(u){a[15](u)}function l(u){a[16](u)}function r(u){a[17](u)}let d={saveFunction:a[8],variant:"surface",size:2,resize:"vertical",class:"markdown-editor",$$slots:{header:[Kt]},$$scope:{ctx:a}};return a[2]!==void 0&&(d.selectedText=a[2]),a[3]!==void 0&&(d.selectionStart=a[3]),a[4]!==void 0&&(d.selectionEnd=a[4]),a[0]!==void 0&&(d.value=a[0]),t=new Mt({props:d}),C.push(()=>E(t,"selectedText",$)),C.push(()=>E(t,"selectionStart",i)),C.push(()=>E(t,"selectionEnd",l)),C.push(()=>E(t,"value",r)),{c(){x(t.$$.fragment)},m(u,h){w(t,u,h),c=!0},p(u,[h]){const v={};1048614&h&&(v.$$scope={dirty:h,ctx:u}),!n&&4&h&&(n=!0,v.selectedText=u[2],I(()=>n=!1)),!e&&8&h&&(e=!0,v.selectionStart=u[3],I(()=>e=!1)),!o&&16&h&&(o=!0,v.selectionEnd=u[4],I(()=>o=!1)),!s&&1&h&&(s=!0,v.value=u[0],I(()=>s=!1)),t.$set(v)},i(u){c||(m(t.$$.fragment,u),c=!0)},o(u){f(t.$$.fragment,u),c=!1},d(u){y(t,u)}}}function Qt(a,t,n){let e,o,{text:s}=t,{path:c}=t;const $=new ct(q),i=new Rt;D(a,i,p=>n(11,o=p));const l=new Lt(q,$,i);(async function(){try{const p=await l.getChatInitData();i.update({enableRules:p.enableRules??!1,enableDebugFeatures:p.enableDebugFeatures??!1})}catch(p){console.error("Failed to initialize flags:",p)}})();let r="",d=0,u=0;const h=async()=>{c&&l.saveFile({repoRoot:"",pathName:c,content:s})};async function v(p){if(r){const F=s.substring(0,d)+s.substring(u);return n(0,s=F),p==="userGuidelines"?(l.updateUserGuidelines(r),l.reportAgentSessionEvent({eventName:U.memoriesMove,conversationId:"",eventData:{memoriesMoveData:{target:W.userGuidelines}}})):(l.updateWorkspaceGuidelines(r),l.reportAgentSessionEvent({eventName:U.memoriesMove,conversationId:"",eventData:{memoriesMoveData:{target:W.augmentGuidelines}}})),await h(),"success"}}return a.$$set=p=>{"text"in p&&n(0,s=p.text),"path"in p&&n(1,c=p.path)},a.$$.update=()=>{2048&a.$$.dirty&&n(5,e=o.enableRules)},[s,c,r,d,u,e,i,l,h,v,async function(p){if(r){l.updateRuleFile(p.path,r);const F=s.substring(0,d)+s.substring(u);n(0,s=F),await h(),l.reportAgentSessionEvent({eventName:U.memoriesMove,conversationId:"",eventData:{memoriesMoveData:{target:W.rules}}})}},o,()=>v("userGuidelines"),async()=>(l.openFile({repoRoot:"",pathName:c}),"success"),function(p){r=p,n(2,r)},function(p){d=p,n(3,d)},function(p){u=p,n(4,u)},function(p){s=p,n(0,s)}]}class Vt extends k{constructor(t){super(),j(this,t,Qt,Pt,H,{text:0,path:1})}}function Xt(a){let t;return{c(){t=O("Loading memories...")},m(n,e){R(n,t,e)},p:M,i:M,o:M,d(n){n&&b(t)}}}function Yt(a){let t,n;return t=new Vt({props:{text:a[0],path:a[1]}}),{c(){x(t.$$.fragment)},m(e,o){w(t,e,o),n=!0},p(e,o){const s={};1&o&&(s.text=e[0]),2&o&&(s.path=e[1]),t.$set(s)},i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){y(t,e)}}}function Zt(a){let t,n,e,o,s,c;const $=[Yt,Xt],i=[];function l(r,d){return r[0]!==null&&r[1]!==null?0:1}return n=l(a),e=i[n]=$[n](a),{c(){t=S("div"),e.c(),L(t,"class","c-memories-container svelte-1vchs21")},m(r,d){R(r,t,d),i[n].m(t,null),o=!0,s||(c=xt(window,"message",a[2].onMessageFromExtension),s=!0)},p(r,[d]){let u=n;n=l(r),n===u?i[n].p(r,d):(G(),f(i[u],1,1,()=>{i[u]=null}),A(),e=i[n],e?e.p(r,d):(e=i[n]=$[n](r),e.c()),m(e,1),e.m(t,null))},i(r){o||(m(e),o=!0)},o(r){f(e),o=!1},d(r){r&&b(t),i[n].d(),s=!1,c()}}}function te(a,t,n){let e,o;const s=new ct(q),c=B(null);D(a,c,l=>n(0,e=l));const $=B(null);D(a,$,l=>n(1,o=l));const i={handleMessageFromExtension(l){const r=l.data;if(r&&r.type===_.loadFile){if(r.data.content!==void 0){const d=r.data.content.replace(/^\n+/,"");c.set(d)}r.data.pathName&&$.set(r.data.pathName)}return!0}};return at(()=>{s.registerConsumer(i),q.postMessage({type:_.memoriesLoaded})}),wt(()=>{s.dispose()}),[e,o,s,c,$]}new class extends k{constructor(a){super(),j(this,a,te,Zt,H,{})}}({target:document.getElementById("app")});
