#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour lire la conversation spécifique avec l'ID donné
"""

import os
import json
from pathlib import Path

def find_conversation_file(conversation_id):
    """Trouve le fichier de conversation avec l'ID spécifique"""
    
    base_path = Path("C:/Users/<USER>/AppData/Roaming/Code")
    
    print(f"🔍 RECHERCHE DE LA CONVERSATION: {conversation_id}")
    print("=" * 60)
    
    # Rechercher récursivement le fichier avec cet ID
    for file_path in base_path.rglob("*"):
        if file_path.name == conversation_id:
            print(f"✅ Fichier trouvé: {file_path}")
            return file_path
    
    print(f"❌ Fichier non trouvé pour l'ID: {conversation_id}")
    return None

def read_conversation_content(file_path):
    """Lit le contenu de la conversation"""
    
    try:
        print(f"\n📖 LECTURE DU FICHIER: {file_path}")
        print("=" * 60)
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"📏 Taille du fichier: {len(content)} caractères")
        
        # Essayer de parser en JSON
        try:
            data = json.loads(content)
            print("✅ JSON valide détecté")
            
            # Afficher la structure
            if isinstance(data, dict):
                print(f"🔑 Clés principales: {list(data.keys())}")
                
                # Chercher des messages ou conversations
                if 'messages' in data:
                    print(f"💬 {len(data['messages'])} messages trouvés")
                elif 'conversation' in data:
                    print("💬 Données de conversation trouvées")
                
                # Chercher le texte "tu avais raison finalement"
                content_lower = content.lower()
                if "tu avais raison" in content_lower:
                    print("🎉 PHRASE 'TU AVAIS RAISON' TROUVÉE!")
                    
                    # Extraire le contexte autour de cette phrase
                    start_pos = content_lower.find("tu avais raison")
                    context_start = max(0, start_pos - 200)
                    context_end = min(len(content), start_pos + 300)
                    context = content[context_start:context_end]
                    
                    print(f"\n📝 CONTEXTE AUTOUR DE 'TU AVAIS RAISON':")
                    print("-" * 50)
                    print(context)
                    print("-" * 50)
                
                if "finalement" in content_lower:
                    print("🎉 MOT 'FINALEMENT' TROUVÉ!")
                    
                    # Extraire le contexte autour de cette phrase
                    start_pos = content_lower.find("finalement")
                    context_start = max(0, start_pos - 200)
                    context_end = min(len(content), start_pos + 300)
                    context = content[context_start:context_end]
                    
                    print(f"\n📝 CONTEXTE AUTOUR DE 'FINALEMENT':")
                    print("-" * 50)
                    print(context)
                    print("-" * 50)
            
            return data
            
        except json.JSONDecodeError:
            print("⚠️ Pas du JSON valide, affichage du contenu brut")
            print(f"\n📝 CONTENU BRUT (premiers 1000 caractères):")
            print("-" * 50)
            print(content[:1000])
            print("-" * 50)
            return content
            
    except Exception as e:
        print(f"❌ Erreur lors de la lecture: {e}")
        return None

def search_all_conversations():
    """Recherche toutes les conversations contenant 'tu avais raison'"""
    
    print(f"\n🔍 RECHERCHE GLOBALE DE 'TU AVAIS RAISON'")
    print("=" * 60)
    
    base_path = Path("C:/Users/<USER>/AppData/Roaming/Code")
    found_files = []
    
    # Rechercher dans tous les fichiers
    for file_path in base_path.rglob("*"):
        if file_path.is_file():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if "tu avais raison" in content.lower():
                    found_files.append({
                        'path': str(file_path),
                        'size': len(content),
                        'content': content
                    })
                    print(f"✅ Trouvé dans: {file_path}")
                    
            except Exception:
                pass  # Ignorer les erreurs de lecture
    
    return found_files

if __name__ == "__main__":
    conversation_id = "2bcca0ab-285d-410b-b63e-7d5f054030d9"
    
    print("🚀 LECTURE DE LA CONVERSATION SPÉCIFIQUE")
    print("=" * 60)
    
    # Trouver le fichier
    file_path = find_conversation_file(conversation_id)
    
    if file_path:
        # Lire le contenu
        data = read_conversation_content(file_path)
        
        if data:
            print(f"\n✅ CONVERSATION LUE AVEC SUCCÈS!")
        else:
            print(f"\n❌ Erreur lors de la lecture")
    else:
        print(f"\n❌ Fichier de conversation non trouvé")
    
    # Recherche globale en backup
    print(f"\n🔍 RECHERCHE GLOBALE EN BACKUP...")
    all_conversations = search_all_conversations()
    
    if all_conversations:
        print(f"\n🎉 {len(all_conversations)} FICHIERS CONTENANT 'TU AVAIS RAISON' TROUVÉS!")
        for i, conv in enumerate(all_conversations, 1):
            print(f"\n--- FICHIER {i} ---")
            print(f"📁 Chemin: {conv['path']}")
            print(f"📏 Taille: {conv['size']} caractères")
            
            # Extraire le contexte
            content_lower = conv['content'].lower()
            start_pos = content_lower.find("tu avais raison")
            if start_pos != -1:
                context_start = max(0, start_pos - 100)
                context_end = min(len(conv['content']), start_pos + 200)
                context = conv['content'][context_start:context_end]
                print(f"📝 Contexte: {context}")
    else:
        print(f"\n❌ Aucun fichier contenant 'tu avais raison' trouvé")
