# 📋 MÉTHODES DU CLUSTER PAR DÉFAUT (C0-C1) - ROLLOUTS 1, 2, 3

"""
Ce fichier contient TOUTES les méthodes du cluster par défaut qui servent de base
pour la centralisation. Ces méthodes seront adaptées pour fonctionner avec
les paramètres centralisés de tous les clusters.

STRUCTURE :
1. ROLLOUT 1 ANALYSEUR - Méthode principale + méthodes support
2. ROLLOUT 2 GÉNÉRATEUR - Méthode principale + méthodes support  
3. ROLLOUT 3 PRÉDICTEUR - Méthode principale + méthodes support
4. MÉTHODES UTILITAIRES COMMUNES
"""

# ============================================================================
# 🎯 ROLLOUT 1 ANALYSEUR - MÉTHODE PRINCIPALE
# ============================================================================

def _rollout_analyzer(self, standardized_sequence: Dict) -> Dict:
    """
    Rollout 1 Analyseur de Biais - Exploitation des contraintes structurelles du baccarat

    NOUVELLE LOGIQUE ANTI-MOYENNES :
    - ÉLIMINE toutes les moyennes (piège mortel au baccarat)
    - UTILISE les écart-types pour mesurer les déviations structurelles
    - PRIORISE les impairs consécutifs (30× plus rares que les pairs)
    - EXPLOITE l'alternance sync/desync (3ème carte distribuée)
    - CORRÈLE les biais structurels avec les variations P/B → S/O

    Hiérarchie d'analyse des biais :
    1. PRIORITÉ 1 : Impairs consécutifs (rareté extrême)
    2. PRIORITÉ 2 : Alternance sync/desync (3ème carte)
    3. PRIORITÉ 3 : Combinaisons rares (impair+desync)
    4. CORRÉLATION : Impact sur P/B → S/O

    Args:
        standardized_sequence: Séquence complète depuis brûlage

    Returns:
        Dict: Analyse des biais structurels exploitables
    """
    try:
        analysis_start = time.time()

        # Extraction données complètes depuis brûlage
        hands_data = standardized_sequence.get('hands_history', [])
        if not hands_data:
            return {'error': 'Aucune donnée historique disponible'}

        # ================================================================
        # NOUVEAU SYSTÈME DE PRIORITÉS SANS SEUILS LIMITANTS
        # ================================================================

        # PRIORITÉ 1 : ANALYSE COMPLÈTE DES IMPAIRS (isolés + séquences)
        impair_bias_analysis = self._analyze_impair_consecutive_bias(hands_data)

        # PRIORITÉ 2 : ANALYSE PAIRS EN CONTEXTE DES IMPAIRS (AUTONOME)
        pair_bias_analysis = self._analyze_pair_priority_2_autonomous(hands_data, impair_bias_analysis)

        # PRIORITÉ 3 : ANALYSE SYNC/DESYNC (3ème carte)
        sync_bias_analysis = self._analyze_sync_alternation_bias(hands_data)

        # PRIORITÉ 4 : ANALYSE BIAIS COMBINÉS (tous indices)
        combined_bias_analysis = self._analyze_combined_structural_bias(
            impair_bias_analysis, sync_bias_analysis, hands_data
        )

        # ================================================================
        # CORRÉLATION : IMPACT DES BIAIS SUR P/B → S/O
        # ================================================================

        pb_correlation_analysis = self._correlate_bias_to_pb_variations(
            impair_bias_analysis, sync_bias_analysis, combined_bias_analysis, hands_data
        )

        so_correlation_analysis = self._correlate_bias_to_so_variations(
            pb_correlation_analysis, hands_data
        )

        # ================================================================
        # SYNTHÈSE FINALE BASÉE SUR LES PRIORITÉS
        # ================================================================

        # Synthèse autonome des biais (ROLLOUT 1 INDÉPENDANT)
        bias_synthesis = self._generate_priority_based_synthesis_autonomous({
            'priority_1_impair_bias': impair_bias_analysis,
            'priority_2_pair_bias': pair_bias_analysis,
            'priority_3_sync_bias': sync_bias_analysis,
            'priority_4_combined_bias': combined_bias_analysis,
            'pb_correlation': pb_correlation_analysis,
            'so_correlation': so_correlation_analysis
        }, hands_data)

        # NOUVEAU : Génération des signaux de biais pour Rollout 2
        bias_signals_summary = self._generate_bias_signals_summary(bias_synthesis)
        bias_generation_guidance = self._generate_bias_generation_guidance(bias_synthesis)
        bias_quick_access = self._generate_bias_quick_access(bias_synthesis)

        # Rapport final OPTIMISÉ pour exploitation de biais
        analyzer_report = {
            # NOUVEAU : Signaux de biais exploitables (priorité absolue)
            'bias_signals_summary': bias_signals_summary,
            'bias_generation_guidance': bias_generation_guidance,
            'bias_quick_access': bias_quick_access,

            # ANALYSE DÉTAILLÉE DES BIAIS STRUCTURELS
            'structural_bias_analysis': {
                'impair_consecutive_bias': impair_bias_analysis,
                'sync_alternation_bias': sync_bias_analysis,
                'combined_structural_bias': combined_bias_analysis,
                'pb_correlation_bias': pb_correlation_analysis,
                'so_correlation_bias': so_correlation_analysis
            },
            'bias_synthesis': bias_synthesis,
            'exploitation_metadata': {
                'total_hands_analyzed': len(hands_data),
                'bias_exploitation_quality': bias_synthesis.get('exploitation_quality', self.config.zero_value),
                'strongest_bias_detected': bias_synthesis.get('strongest_bias', {}),
                'exploitation_confidence': bias_synthesis.get('exploitation_confidence', self.config.zero_value),
                'bias_persistence_score': bias_synthesis.get('bias_persistence', self.config.zero_value)
            },
            'execution_time_ms': (time.time() - analysis_start) * 1000,
            'cluster_id': self.cluster_id,
            'analysis_type': 'structural_bias_exploitation'
        }

        return analyzer_report

    except Exception as e:
        logger.error(f"Erreur rollout analyzer cluster {self.cluster_id}: {e}")
        return {'error': str(e)}

# ============================================================================
# 🎯 ROLLOUT 2 GÉNÉRATEUR - MÉTHODE PRINCIPALE
# ============================================================================

def _rollout_generator(self, analyzer_report: Dict) -> Dict:
    """
    Rollout 2 Générateur - Génération séquences candidates basées sur analyse complète

    Exploite l'analyse complète des 5 indices pour génération optimale.
    Génère 4 séquences candidates avec stratégies distinctes.

    Args:
        analyzer_report: Rapport complet du rollout analyseur avec 5 indices

    Returns:
        Dict: Résultat structuré contenant séquences candidates et métadonnées
            - sequences: List[Dict] - Séquences candidates enrichies
            - generation_metadata: Dict - Métadonnées de génération
            - signals_used: Dict - Signaux utilisés pour la génération
            - generation_stats: Dict - Statistiques de génération
    """
    try:
        if 'error' in analyzer_report:
            return {
                'sequences': [],
                'generation_metadata': {
                    'total_sequences_generated': 0,
                    'generation_strategy': 'analyzer_error',
                    'cluster_id': self.cluster_id,
                    'generation_timestamp': time.time()
                },
                'error': 'Analyzer report contains error',
                'analyzer_error': analyzer_report.get('error')
            }

        # ================================================================
        # NOUVEAU : UTILISATION DES SECTIONS OPTIMISÉES DU ROLLOUT 1
        # ================================================================

        # Extraction des sections optimisées pour le générateur
        signals_summary = analyzer_report.get('signals_summary', {})
        generation_guidance = analyzer_report.get('generation_guidance', {})
        quick_access = analyzer_report.get('quick_access', {})

        # Extraction analyse complète des 5 indices (fallback)
        indices_analysis = analyzer_report.get('indices_analysis', {})
        synthesis = analyzer_report.get('synthesis', {})
        sequence_metadata = analyzer_report.get('sequence_metadata', {})

        # Définition espace de génération basé sur les nouvelles sections optimisées
        generation_space = self._define_optimized_generation_space(
            signals_summary, generation_guidance, quick_access,
            indices_analysis, synthesis, sequence_metadata
        )

        # ================================================================
        # GÉNÉRATION OPTIMISÉE BASÉE SUR LES SIGNAUX DU ROLLOUT 1
        # ================================================================

        # Génération de séquences candidates basées sur les signaux optimisés
        candidates = self._generate_sequences_from_signals(
            signals_summary, generation_guidance, quick_access, generation_space
        )

        # Fallback : Génération classique si pas de signaux optimisés
        if not candidates:
            candidates = self._generate_fallback_sequences(generation_space)

        # Enrichissement séquences avec tous les indices
        enriched_candidates = self._enrich_sequences_with_complete_indexes(candidates, indices_analysis, synthesis)

        # NOUVEAU : Retourner un dictionnaire structuré au lieu d'une liste
        generator_result = {
            'sequences': enriched_candidates,
            'generation_metadata': {
                'total_sequences_generated': len(enriched_candidates),
                'generation_strategy': 'signals_based',
                'cluster_id': self.cluster_id,
                'generation_timestamp': time.time()
            },
            'signals_used': {
                'signals_summary': signals_summary,
                'generation_guidance': generation_guidance,
                'quick_access': quick_access
            },
            'generation_stats': {
                'fallback_used': len(candidates) == 0,
                'enrichment_applied': True,
                'avg_sequence_length': sum(len(seq) for seq in enriched_candidates) / len(enriched_candidates) if enriched_candidates else 0
            }
        }

        return generator_result

    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        logger.error(f"Erreur rollout generator cluster {self.cluster_id}: {type(e).__name__}: {e}")
        logger.error(f"Détails erreur cluster {self.cluster_id}: {error_details}")
        return {
            'sequences': [],
            'generation_metadata': {
                'total_sequences_generated': 0,
                'generation_strategy': 'error_fallback',
                'cluster_id': self.cluster_id,
                'generation_timestamp': time.time()
            },
            'error': str(e),
            'error_details': error_details
        }

# ============================================================================
# 🎯 ROLLOUT 3 PRÉDICTEUR - MÉTHODE PRINCIPALE  
# ============================================================================

def _rollout_predictor(self, generator_result: Dict, analyzer_report: Dict) -> Dict:
    """
    Rollout 3 Prédicteur - Sélection séquence optimale finale

    Évalue et sélectionne la meilleure séquence parmi les candidates.
    Utilise critères S/O prioritaires et intelligence de sélection.

    Args:
        generator_result: Résultat structuré du générateur (dictionnaire)
        analyzer_report: Rapport original de l'analyseur

    Returns:
        Dict: Prédiction finale avec confiance et métadonnées
    """
    # [MÉTHODE COMPLÈTE À EXTRAIRE DU CODE SOURCE]

# ============================================================================
# 🔧 MÉTHODES SUPPORT ROLLOUT 1 - ANALYSE DE BIAIS
# ============================================================================

def _analyze_impair_consecutive_bias(self, hands_data: List) -> Dict:
    """
    PRIORITÉ 1 : Analyse COMPLÈTE des IMPAIRS (isolés + séquences)

    NOUVELLE LOGIQUE SANS SEUILS LIMITANTS :
    - Analyse TOUS les IMPAIRS (même isolés)
    - Attention progressive selon longueur séquence
    - Corrèle avec TOUS les autres indices (2, 3, 4, 5)
    - Génère TOUJOURS des signaux exploitables
    """
    # [MÉTHODE COMPLÈTE À EXTRAIRE DU CODE SOURCE]

def _analyze_pair_priority_2_autonomous(self, hands_data: List, impair_bias_analysis: Dict) -> Dict:
    """
    PRIORITÉ 2 : Analyse PAIRS en contexte des IMPAIRS (AUTONOME)
    
    LOGIQUE ANTI-CONTAMINATION :
    - Analyse PAIRS indépendamment des découvertes IMPAIRS
    - Évite la contamination des signaux
    - Génère ses propres patterns PAIRS
    """
    # [MÉTHODE COMPLÈTE À EXTRAIRE DU CODE SOURCE]

def _analyze_sync_alternation_bias(self, hands_data: List) -> Dict:
    """
    PRIORITÉ 3 : Analyse SYNC/DESYNC (3ème carte distribuée)
    
    LOGIQUE ALTERNANCE :
    - Détecte ruptures d'alternance SYNC/DESYNC
    - Mesure persistance des états
    - Corrèle avec variations P/B
    """
    # [MÉTHODE COMPLÈTE À EXTRAIRE DU CODE SOURCE]

def _analyze_combined_structural_bias(self, impair_bias: Dict, sync_bias: Dict, hands_data: List) -> Dict:
    """
    PRIORITÉ 4 : Analyse des biais combinés (impair+desync)
    
    LOGIQUE ANTI-MOYENNES :
    - Combine Index 1 (IMPAIR) + Index 2 (DESYNC)
    - Détecte combinaisons rares IMPAIR_DESYNC
    - Mesure impact sur Index 4 (P/B) et Index 5 (S/O)
    """
    # [MÉTHODE COMPLÈTE À EXTRAIRE DU CODE SOURCE]

# ============================================================================
# 🔗 MÉTHODES DE CORRÉLATION (INDEX 1,2,3 → 4,5)
# ============================================================================

def _correlate_bias_to_pb_variations(self, impair_bias: Dict, sync_bias: Dict, combined_bias: Dict, hands_data: List) -> Dict:
    """Corrèle les biais structurels avec les variations P/B"""
    # [MÉTHODE COMPLÈTE À EXTRAIRE DU CODE SOURCE]

def _correlate_bias_to_so_variations(self, pb_correlation: Dict, hands_data: List) -> Dict:
    """Corrèle les biais avec les variations S/O"""
    # [MÉTHODE COMPLÈTE À EXTRAIRE DU CODE SOURCE]

def _correlate_impair_with_sync(self, isolated_impairs: List, consecutive_sequences: List, sync_states: List) -> Dict:
    """Corrèle les IMPAIRS avec les états SYNC/DESYNC"""
    # [MÉTHODE COMPLÈTE À EXTRAIRE DU CODE SOURCE]

def _correlate_impair_with_combined(self, isolated_impairs: List, consecutive_sequences: List, combined_states: List) -> Dict:
    """Corrèle les IMPAIRS avec les états combinés"""
    # [MÉTHODE COMPLÈTE À EXTRAIRE DU CODE SOURCE]

def _correlate_impair_with_pb(self, isolated_impairs: List, consecutive_sequences: List, pb_outcomes: List, total_hands: int) -> Dict:
    """Corrèle les IMPAIRS avec les résultats P/B"""
    # [MÉTHODE COMPLÈTE À EXTRAIRE DU CODE SOURCE]

def _correlate_impair_with_so(self, isolated_impairs: List, consecutive_sequences: List, so_outcomes: List, total_hands: int) -> Dict:
    """Corrèle les IMPAIRS avec les conversions S/O"""
    # [MÉTHODE COMPLÈTE À EXTRAIRE DU CODE SOURCE]

# ============================================================================
# 🎯 MÉTHODES DE SYNTHÈSE ET GÉNÉRATION DE SIGNAUX
# ============================================================================

def _generate_priority_based_synthesis_autonomous(self, all_analyses: Dict, hands_data: List) -> Dict:
    """
    Synthèse autonome des biais basée sur les priorités
    
    ROLLOUT 1 INDÉPENDANT :
    - Synthèse sans influence externe
    - Priorités respectées (IMPAIR > SYNC > COMBINÉ)
    - Signaux exploitables garantis
    """
    # [MÉTHODE COMPLÈTE À EXTRAIRE DU CODE SOURCE]

def _generate_bias_signals_summary(self, bias_synthesis: Dict) -> Dict:
    """Génère le résumé des signaux de biais pour Rollout 2"""
    # [MÉTHODE COMPLÈTE À EXTRAIRE DU CODE SOURCE]

def _generate_bias_generation_guidance(self, bias_synthesis: Dict) -> Dict:
    """Génère les directives de génération pour Rollout 2"""
    # [MÉTHODE COMPLÈTE À EXTRAIRE DU CODE SOURCE]

def _generate_bias_quick_access(self, bias_synthesis: Dict) -> Dict:
    """Génère l'accès rapide aux biais pour Rollout 2"""
    # [MÉTHODE COMPLÈTE À EXTRAIRE DU CODE SOURCE]

# ============================================================================
# 📝 NOTES POUR LA CENTRALISATION
# ============================================================================

"""
MÉTHODES À PARAMÉTRER POUR LA CENTRALISATION :

1. FENÊTRES RÉCENTES :
   - Utiliser config.get_cluster_recent_window_size(cluster_id)
   - Adapter toutes les analyses aux fenêtres spécialisées

2. SEUILS ET BONUS :
   - Remplacer valeurs codées par paramètres AZRConfig
   - Utiliser les paramètres cluster-spécifiques (c2_*, c3_*, etc.)

3. SPÉCIALISATIONS :
   - Ajouter logique conditionnelle selon cluster_id
   - Appliquer bonus spécialisés EN PLUS de la logique de base

4. STRUCTURE COMMUNE :
   - Maintenir les 5 phases : extraction index, analyses, corrélations, spécialisation, synthèse
   - Adapter chaque phase selon paramètres cluster

5. SIGNAUX DE SORTIE :
   - Adapter les signaux selon spécialisation cluster
   - Maintenir structure commune pour Rollout 2 et 3
"""
