var on=Object.defineProperty;var rn=(o,e,n)=>e in o?on(o,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):o[e]=n;var ne=(o,e,n)=>rn(o,typeof e!="symbol"?e+"":e,n);import{n as Ht,g as Ze,a as cn,h as an,b as ln,c as un,d as dn,e as $n,f as mn,j as De,k as je,m as Ot,A as Ee,T as He,E as $e,R as Fe,o as We,S as pn,p as Me,O as gn}from"./open-in-new-window-DMlqLwqy.js";import{a as fn,b as de,R as Te}from"./types-LfaCSdmF.js";import{S as Z,i as j,s as W,E as ee,e as x,u as $,q as D,t as p,r as U,h,ae as re,af as ae,a as Ie,a8 as Ke,Q as M,y,D as B,G as F,c as v,a1 as Ye,a2 as O,z as C,f as R,a4 as Q,g as Ue,Y as hn,H as K,B as _,a6 as ze,P as he,aa as Xe,V as ve,W as xe,X as we,ax as vn,n as V,C as fe,ak as Dt,a7 as xn,w as oe,_ as Ut,x as qe,A as Be,aE as ye,R as Qe,a5 as Vt,ab as ge,a0 as Ce,al as _e,T as te,b as pe,aq as wn,F as Jt,aA as et,I as yn,J as Cn,K as _n,L as kn,d as tt,M as bn,j as nt}from"./SpinnerAugment-BJ4-L7QR.js";import{M as In}from"./MaterialIcon-DIlB9c-0.js";import{o as Gt}from"./keypress-DD1aQVr0.js";import{A as st,a as Y}from"./autofix-state-d-ymFdyn.js";import{bb as Sn,bc as Tn,bd as Ge,be as Rn,aq as En,bf as Mn,bg as Zt,bh as Ln,bi as An,bj as Fn,bk as Oe,bl as zn}from"./AugmentMessage-DIzdCIMv.js";import{T as Pe}from"./Content-Czt02SJi.js";import{E as qn,a as jt,b as Bn}from"./folder-BJI1Q8_7.js";import{e as Ne,g as Pn,h as Nn,f as Hn,E as On,i as Dn,T as Wt}from"./Keybindings-4L2d2tRE.js";import{c as Kt,I as Un,R as Vn,D as Jn,d as Gn,e as Yt,f as Zn,h as Le,i as jn}from"./main-panel-CLAFkah5.js";import{B as Xt}from"./ButtonAugment-HnJOGilM.js";import{E as Wn}from"./expand--BB_Hn_b.js";import{P as Kn}from"./pen-to-square-Bm4lF9Yl.js";import{C as Yn}from"./folder-opened-DzrGzNBt.js";import{T as Se}from"./TextTooltipAugment-Bkzart3o.js";import{I as ke}from"./IconButtonAugment-Certjadv.js";import{C as Xn,a as Qn,M as es,b as Qt,c as en}from"./diff-utils-y96qaWKK.js";import{B as tn}from"./layer-group-CZFSGU8L.js";import{e as Ae}from"./BaseButton-C6Dhmpxa.js";import{C as ts}from"./CardAugment-BxTO-shY.js";import{M as ns,S as ss}from"./index-MyvMQzjq.js";import{C as os}from"./github-C1PQK5DH.js";import{s as rs}from"./types-a569v5Ol.js";const cs=(o,e,n,t)=>{const s={retryMessage:void 0,showGeneratingResponse:!1,showResumingRemoteAgent:!1,showAwaitingUserInput:!1,showRunningSpacer:!1,showStopped:!1,remoteAgentErrorConfig:void 0};if(e===Ee.running){const r=o==null?void 0:o.lastExchange;if(r!=null&&r.isRetriable&&(r!=null&&r.display_error_message))s.retryMessage=r.display_error_message;else if(n||t.isActive){const c=t.isActive?t.getLastToolUseState():o.getLastToolUseState();if(t.isActive){const i=t.currentAgent;(i==null?void 0:i.workspace_status)===fn.workspaceResuming?s.showResumingRemoteAgent=!0:c.phase!==He.running?s.showGeneratingResponse=!0:s.showRunningSpacer=!0}else c.phase!==He.running?s.showGeneratingResponse=!0:s.showRunningSpacer=!0}else s.showGeneratingResponse=!0}else e===Ee.awaitingUserAction?(s.showAwaitingUserInput=!0,s.showRunningSpacer=!0):((r,c)=>{var d;const i=(d=r==null?void 0:r.lastExchange)==null?void 0:d.status,a=i===$e.cancelled,l=r==null?void 0:r.getLastToolUseState().phase,u=l===He.cancelled;return!c.isActive&&(a||u)})(o,t)&&(s.showStopped=!0);return s},mc=(o,e,n,t)=>{const s=o.currentConversationModel,r=((d,m)=>m.isActive?m.getCurrentChatHistory():d.chatHistory.filter(g=>Ze(g)||cn(g)||an(g)||ln(g)||un(g)||dn(g)||$n(g)||mn(g)||De(g)||je(g)))(s,t),c=(d=>d.reduce((m,g,k)=>(Ze(g)&&Ot(g)&&m.length>0||je(g)&&m.length>0?m[m.length-1].push({turn:g,idx:k}):m.push([{turn:g,idx:k}]),m),[]))(r),i=((d,m)=>m.isActive?m.isCurrentAgentRunning?Ee.running:Ee.notRunning:d)(e,t),a=cs(s,i,n,t),l=!t.isActive,u=!!t.isActive;if(t.isActive){if(t.sendMessageError&&t.currentAgentId){const d=t.currentAgentId,m=t.sendMessageError;a.remoteAgentErrorConfig={error:m,onRetry:m.canRetry&&m.failedExchangeId?()=>t.retryFailedMessage(d,m.failedExchangeId):void 0,onDelete:m.type===Ht.agentFailed?()=>t.deleteAgent(d):void 0}}else if(t.agentChatHistoryError&&t.currentAgentId){const d=t.currentAgentId;a.remoteAgentErrorConfig={error:t.agentChatHistoryError,onRetry:()=>t.refreshAgentChatHistory(d)}}}return{chatHistory:r,groupedChatHistory:c,lastGroupConfig:a,doShowFloatingButtons:l,doShowAgentSetupLogs:u}};function ot(o){let e,n,t,s,r,c,i,a;const l=[o[4][o[1]]];let u={};for(let g=0;g<l.length;g+=1)u=Ie(u,l[g]);n=new In({props:u});let d=[{class:"stage-container"},o[1]?Ke(o[3][o[1]]):{},{role:"button"},{tabindex:"0"}],m={};for(let g=0;g<d.length;g+=1)m=Ie(m,d[g]);return{c(){e=M("div"),y(n.$$.fragment),t=B(),s=M("div"),r=F(o[1]),v(s,"class","message svelte-1etsput"),Ye(e,m),O(e,"active",o[0]),O(e,"svelte-1etsput",!0)},m(g,k){x(g,e,k),C(n,e,null),R(e,t),R(e,s),R(s,r),c=!0,i||(a=[Q(e,"click",o[5]),Q(e,"keydown",Gt("Enter",o[5]))],i=!0)},p(g,k){const L=18&k?Ue(l,[hn(g[4][g[1]])]):{};n.$set(L),(!c||2&k)&&K(r,g[1]),Ye(e,m=Ue(d,[{class:"stage-container"},2&k&&(g[1]?Ke(g[3][g[1]]):{}),{role:"button"},{tabindex:"0"}])),O(e,"active",g[0]),O(e,"svelte-1etsput",!0)},i(g){c||($(n.$$.fragment,g),c=!0)},o(g){p(n.$$.fragment,g),c=!1},d(g){g&&h(e),_(n),i=!1,ze(a)}}}function is(o){let e,n,t=o[1]&&ot(o);return{c(){t&&t.c(),e=ee()},m(s,r){t&&t.m(s,r),x(s,e,r),n=!0},p(s,[r]){s[1]?t?(t.p(s,r),2&r&&$(t,1)):(t=ot(s),t.c(),$(t,1),t.m(e.parentNode,e)):t&&(D(),p(t,1,1,()=>{t=null}),U())},i(s){n||($(t),n=!0)},o(s){p(t),n=!1},d(s){s&&h(e),t&&t.d(s)}}}function as(o,e,n){let t,s,r,c,{stage:i}=e,{iterationId:a}=e,{stageCount:l}=e;const u=re("autofixConversationModel");ae(o,u,g=>n(10,c=g));const d={[Y.retesting]:"info",[Y.testRunning]:"info",[Y.testFailed]:"error",[Y.testPassed]:"success",[Y.generatingSolutions]:"info",[Y.suggestedSolutions]:"warning",[Y.selectedSolutions]:"success"},m={[Y.retesting]:{iconName:"cached",color:"#FFFFFF"},[Y.testRunning]:{iconName:"cached",color:"#FFFFFF"},[Y.testFailed]:{iconName:"error",color:"#DB3B4B"},[Y.testPassed]:{iconName:"check_circle",color:"#388A34"},[Y.generatingSolutions]:{iconName:"cached",color:"#FFFFFF"},[Y.suggestedSolutions]:{iconName:"edit",color:"#FFFFFF"},[Y.selectedSolutions]:{iconName:"edit",color:"#FFFFFF"}};return o.$$set=g=>{"stage"in g&&n(6,i=g.stage),"iterationId"in g&&n(7,a=g.iterationId),"stageCount"in g&&n(8,l=g.stageCount)},o.$$.update=()=>{var g,k,L;1152&o.$$.dirty&&n(9,t=c==null?void 0:c.getAutofixIteration(a)),1600&o.$$.dirty&&n(0,s=t&&((L=(k=(g=c.extraData)==null?void 0:g.autofixIterations)==null?void 0:k.at(-1))==null?void 0:L.id)===t.id&&t.currentStage===i),833&o.$$.dirty&&n(1,r=function(f,I,A,P){var E;return f?I===st.runTest?f.commandFailed===void 0&&P?f.isFirstIteration?Y.testRunning:Y.retesting:f.commandFailed===!0?Y.testFailed:Y.testPassed:I===st.applyFix?A===(((E=f.suggestedSolutions)==null?void 0:E.length)||0)?f.selectedSolutions?Y.selectedSolutions:Y.generatingSolutions:Y.suggestedSolutions:null:null}(t,i,l,s))},[s,r,u,d,m,()=>{r!==Y.generatingSolutions&&u.launchAutofixPanel(a,i)},i,a,l,t,c]}class pc extends Z{constructor(e){super(),j(this,e,as,is,W,{stage:6,iterationId:7,stageCount:8})}}function gc(o,e){const n=Math.abs(o);let t=200,s=500;typeof e=="number"?t=e:e&&(t=e.baseThreshold??200,s=e.predictTime??500);const r=n*s/1e3;return Math.max(t,r)}function fc(o,e=10){const n=Math.abs(o);return n>1e3?2*e:n>500?1.5*e:n>200?e:.5*e}function rt(o){const{scrollTop:e,clientHeight:n,scrollHeight:t}=o;return t-e-n}function hc(o,e={}){let n=e,t={scrollTop:0,scrollBottom:0,scrollHeight:0,scrolledIntoBottom:!0,scrolledAwayFromBottom:!0};const s=()=>{var f,I,A;const{scrollTop:r,scrollHeight:c,offsetHeight:i}=o,a=rt(o),l=r>t.scrollTop+1,u=c-t.scrollHeight,d=!(u<0&&t.scrollBottom<-u)&&r<t.scrollTop-1&&a>t.scrollBottom+1,m=c>i,g=((P,E=40)=>rt(P)<=E)(o),k=g&&m&&l,L=d||!m;k&&!t.scrolledIntoBottom?(f=n.onScrollIntoBottom)==null||f.call(n):L&&!t.scrolledAwayFromBottom&&((I=n.onScrollAwayFromBottom)==null||I.call(n)),t={scrollTop:r,scrollBottom:a,scrolledIntoBottom:k,scrolledAwayFromBottom:L,scrollHeight:c},(A=n.onScroll)==null||A.call(n,r)};return o.addEventListener("scroll",s),{update(r){n=r},destroy(){o.removeEventListener("scroll",s)}}}function ls(o){let e,n,t;const s=o[4].default,r=he(s,o,o[3],null);return{c(){e=M("div"),r&&r.c(),v(e,"class",n="c-gradient-mask "+o[2]+" svelte-say8yn"),Xe(e,"--fade-size",o[1]+"px"),O(e,"is-horizontal",o[0]==="horizontal")},m(c,i){x(c,e,i),r&&r.m(e,null),t=!0},p(c,[i]){r&&r.p&&(!t||8&i)&&ve(r,s,c,c[3],t?we(s,c[3],i,null):xe(c[3]),null),(!t||4&i&&n!==(n="c-gradient-mask "+c[2]+" svelte-say8yn"))&&v(e,"class",n),(!t||2&i)&&Xe(e,"--fade-size",c[1]+"px"),(!t||5&i)&&O(e,"is-horizontal",c[0]==="horizontal")},i(c){t||($(r,c),t=!0)},o(c){p(r,c),t=!1},d(c){c&&h(e),r&&r.d(c)}}}function us(o,e,n){let{$$slots:t={},$$scope:s}=e,{direction:r="vertical"}=e,{fadeSize:c=Sn}=e,{class:i=""}=e;return o.$$set=a=>{"direction"in a&&n(0,r=a.direction),"fadeSize"in a&&n(1,c=a.fadeSize),"class"in a&&n(2,i=a.class),"$$scope"in a&&n(3,s=a.$$scope)},[r,c,i,s,t]}class vc extends Z{constructor(e){super(),j(this,e,us,ls,W,{direction:0,fadeSize:1,class:2})}}function xc(o,e){var r;let n=o.offsetHeight,t=e;const s=new ResizeObserver(c=>{var a;const i=c[0].contentRect.height;c.length===1?i!==n&&((a=t.onHeightChange)==null||a.call(t,i),n=i):console.warn("Unexpected number of resize entries: ",c)});return s.observe(o),(r=t==null?void 0:t.onHeightChange)==null||r.call(t,n),{update(c){t=c},destroy:()=>s.disconnect()}}function wc(o,e){let n=e;const t=Tn(()=>{n.onSeen()},1e3,{leading:!0,trailing:!0}),s=new IntersectionObserver(c=>{c.length===1?c[0].isIntersecting&&t():console.warn("Unexpected number of intersection entries: ",c)},{threshold:.5}),r=()=>{s.disconnect(),n.track&&s.observe(o)};return r(),{update(c){const i=n;n=c,n.track!==i.track&&r()},destroy:()=>{s.disconnect(),n.onSeen()}}}function ct(o){let e,n,t,s=o[6]&&it();return n=new Vn({props:{changeImageMode:o[32],saveImage:o[9].saveImage,deleteImage:o[9].deleteImage,renderImage:o[9].renderImage,isEditable:o[33]}}),{c(){s&&s.c(),e=B(),y(n.$$.fragment)},m(r,c){s&&s.m(r,c),x(r,e,c),C(n,r,c),t=!0},p(r,c){r[6]?s?64&c[0]&&$(s,1):(s=it(),s.c(),$(s,1),s.m(e.parentNode,e)):s&&(D(),p(s,1,1,()=>{s=null}),U());const i={};258&c[0]&&(i.changeImageMode=r[32]),512&c[0]&&(i.saveImage=r[9].saveImage),512&c[0]&&(i.deleteImage=r[9].deleteImage),512&c[0]&&(i.renderImage=r[9].renderImage),64&c[0]&&(i.isEditable=r[33]),n.$set(i)},i(r){t||($(s),$(n.$$.fragment,r),t=!0)},o(r){p(s),p(n.$$.fragment,r),t=!1},d(r){r&&h(e),s&&s.d(r),_(n,r)}}}function it(o){let e,n;return e=new Jn({}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function ds(o){var L;let e,n,t,s,r,c,i,a,l,u,d=o[9].flags.enableChatMultimodal&&ct(o);n=new Pn({props:{shortcuts:o[13]}});let m={requestEditorFocus:o[22],onMentionItemsUpdated:o[21]};s=new Kt({props:m}),o[34](s),c=new Nn({props:{placeholder:o[2]}}),a=new Ne.Content({props:{content:((L=o[7])==null?void 0:L.richTextJsonRepr)??o[3],onContentChanged:o[20]}});const g=o[29].default,k=he(g,o,o[37],null);return{c(){d&&d.c(),e=B(),y(n.$$.fragment),t=B(),y(s.$$.fragment),r=B(),y(c.$$.fragment),i=B(),y(a.$$.fragment),l=B(),k&&k.c()},m(f,I){d&&d.m(f,I),x(f,e,I),C(n,f,I),x(f,t,I),C(s,f,I),x(f,r,I),C(c,f,I),x(f,i,I),C(a,f,I),x(f,l,I),k&&k.m(f,I),u=!0},p(f,I){var J;f[9].flags.enableChatMultimodal?d?(d.p(f,I),512&I[0]&&$(d,1)):(d=ct(f),d.c(),$(d,1),d.m(e.parentNode,e)):d&&(D(),p(d,1,1,()=>{d=null}),U());const A={};8192&I[0]&&(A.shortcuts=f[13]),n.$set(A),s.$set({});const P={};4&I[0]&&(P.placeholder=f[2]),c.$set(P);const E={};136&I[0]&&(E.content=((J=f[7])==null?void 0:J.richTextJsonRepr)??f[3]),a.$set(E),k&&k.p&&(!u||64&I[1])&&ve(k,g,f,f[37],u?we(g,f[37],I,null):xe(f[37]),null)},i(f){u||($(d),$(n.$$.fragment,f),$(s.$$.fragment,f),$(c.$$.fragment,f),$(a.$$.fragment,f),$(k,f),u=!0)},o(f){p(d),p(n.$$.fragment,f),p(s.$$.fragment,f),p(c.$$.fragment,f),p(a.$$.fragment,f),p(k,f),u=!1},d(f){f&&(h(e),h(t),h(r),h(i),h(l)),d&&d.d(f),_(n,f),o[34](null),_(s,f),_(c,f),_(a,f),k&&k.d(f)}}}function at(o){let e,n;return e=new Gn({props:{chatModel:o[16]}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p:V,i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function $s(o){let e,n,t=o[6]&&at(o);return{c(){t&&t.c(),e=ee()},m(s,r){t&&t.m(s,r),x(s,e,r),n=!0},p(s,r){s[6]?t?(t.p(s,r),64&r[0]&&$(t,1)):(t=at(s),t.c(),$(t,1),t.m(e.parentNode,e)):t&&(D(),p(t,1,1,()=>{t=null}),U())},i(s){n||($(t),n=!0)},o(s){p(t),n=!1},d(s){s&&h(e),t&&t.d(s)}}}function lt(o){let e,n;return e=new Yt.Root({props:{$$slots:{rightAlign:[gs],leftAlign:[ms]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p(t,s){const r={};17408&s[0]|64&s[1]&&(r.$$scope={dirty:s,ctx:t}),e.$set(r)},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function ms(o){var t;let e,n;return e=new Yt.ContextMenu({props:{slot:"leftAlign",onCloseDropdown:o[22],onInsertMentionable:(t=o[10])==null?void 0:t.insertMentionNode}}),{c(){y(e.$$.fragment)},m(s,r){C(e,s,r),n=!0},p(s,r){var i;const c={};1024&r[0]&&(c.onInsertMentionable=(i=s[10])==null?void 0:i.insertMentionNode),e.$set(c)},i(s){n||($(e.$$.fragment,s),n=!0)},o(s){p(e.$$.fragment,s),n=!1},d(s){_(e,s)}}}function ps(o){let e,n;return e=new Hn({}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function gs(o){let e,n;return e=new Xt({props:{size:1,variant:"solid",disabled:!o[14],$$slots:{default:[ps]},$$scope:{ctx:o}}}),e.$on("click",o[18]),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p(t,s){const r={};16384&s[0]&&(r.disabled=!t[14]),64&s[1]&&(r.$$scope={dirty:s,ctx:t}),e.$set(r)},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function ut(o){let e,n,t;return n=new Xt({props:{variant:"solid",color:"neutral",size:1,$$slots:{iconLeft:[hs],default:[fs]},$$scope:{ctx:o}}}),n.$on("click",o[31]),{c(){e=M("div"),y(n.$$.fragment),v(e,"class","c-user-msg__collapse-button svelte-9vyoe0")},m(s,r){x(s,e,r),C(n,e,null),t=!0},p(s,r){const c={};64&r[1]&&(c.$$scope={dirty:r,ctx:s}),n.$set(c)},i(s){t||($(n.$$.fragment,s),t=!0)},o(s){p(n.$$.fragment,s),t=!1},d(s){s&&h(e),_(n)}}}function fs(o){let e;return{c(){e=M("span"),e.textContent="Expand"},m(n,t){x(n,e,t)},p:V,d(n){n&&h(e)}}}function hs(o){let e,n;return e=new Wn({props:{slot:"iconLeft"}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p:V,i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function vs(o){let e,n,t,s=o[6]&&lt(o),r=o[4]&&ut(o);return{c(){s&&s.c(),e=B(),r&&r.c(),n=ee()},m(c,i){s&&s.m(c,i),x(c,e,i),r&&r.m(c,i),x(c,n,i),t=!0},p(c,i){c[6]?s?(s.p(c,i),64&i[0]&&$(s,1)):(s=lt(c),s.c(),$(s,1),s.m(e.parentNode,e)):s&&(D(),p(s,1,1,()=>{s=null}),U()),c[4]?r?(r.p(c,i),16&i[0]&&$(r,1)):(r=ut(c),r.c(),$(r,1),r.m(n.parentNode,n)):r&&(D(),p(r,1,1,()=>{r=null}),U())},i(c){t||($(s),$(r),t=!0)},o(c){p(s),p(r),t=!1},d(c){c&&(h(e),h(n)),s&&s.d(c),r&&r.d(c)}}}function xs(o){let e,n,t,s,r,c={editable:o[6],$$slots:{footer:[vs],header:[$s],default:[ds]},$$scope:{ctx:o}};return n=new Ne.Root({props:c}),o[35](n),n.$on("click",ws),n.$on("dblclick",o[17]),{c(){e=M("div"),y(n.$$.fragment),v(e,"class","c-chat-input svelte-9vyoe0"),v(e,"role","button"),v(e,"tabindex","-1"),O(e,"is-collapsed",o[4]),O(e,"is-editing",o[6])},m(i,a){x(i,e,a),C(n,e,null),o[36](e),t=!0,s||(r=[Q(window,"mousedown",o[19]),Q(e,"mousedown",vn(o[30]))],s=!0)},p(i,a){const l={};64&a[0]&&(l.editable=i[6]),26623&a[0]|64&a[1]&&(l.$$scope={dirty:a,ctx:i}),n.$set(l),(!t||16&a[0])&&O(e,"is-collapsed",i[4]),(!t||64&a[0])&&O(e,"is-editing",i[6])},i(i){t||($(n.$$.fragment,i),t=!0)},o(i){p(n.$$.fragment,i),t=!1},d(i){i&&h(e),o[35](null),_(n),o[36](null),s=!1,ze(r)}}}const ws=o=>o.stopPropagation();function ys(o,e,n){let t,s,r,c,i,a,l,u=V;o.$$.on_destroy.push(()=>u());let{$$slots:d={},$$scope:m}=e;const g=re("chatModel");ae(o,g,b=>n(9,l=b));const k=re(Fe.key);let{requestId:L}=e,{placeholder:f="Edit your message..."}=e,{content:I}=e,{collapsed:A=!1}=e,{onSubmitEdit:P}=e,{onCancelEdit:E}=e,{setIsCollapsed:J}=e,{userExpanded:T}=e,S,N,z,w=!1,X=[];async function q(){r&&(n(0,T=!0),A&&J(!1),n(6,w=!0),await Dt(),me())}function G(){return!(!c||!S)&&(P(S,X),!0)}function H(){return n(0,T=!1),n(6,w=!1),n(7,S=void 0),E(),!0}const me=()=>z==null?void 0:z.forceFocus();let se;return o.$$set=b=>{"requestId"in b&&n(1,L=b.requestId),"placeholder"in b&&n(2,f=b.placeholder),"content"in b&&n(3,I=b.content),"collapsed"in b&&n(4,A=b.collapsed),"onSubmitEdit"in b&&n(23,P=b.onSubmitEdit),"onCancelEdit"in b&&n(24,E=b.onCancelEdit),"setIsCollapsed"in b&&n(5,J=b.setIsCollapsed),"userExpanded"in b&&n(0,T=b.userExpanded),"$$scope"in b&&n(37,m=b.$$scope)},o.$$.update=()=>{512&o.$$.dirty[0]&&(n(15,t=l.currentConversationModel),u(),u=fe(t,b=>n(8,a=b))),268435968&o.$$.dirty[0]&&n(27,r=l.flags.enableEditableHistory&&!s),134218184&o.$$.dirty[0]&&n(14,c=w&&r&&S!==void 0&&S.rawText.trim()!==""&&S.rawText!==I&&S.richTextJsonRepr!==I&&!a.awaitingReply&&!Un.hasLoadingImages(S.richTextJsonRepr))},n(28,s=!!(k!=null&&k.isActive)),n(13,i={Enter:G,Escape:H}),[T,L,f,I,A,J,w,S,a,l,N,z,se,i,c,t,g,q,G,H,function(b){b!==S&&n(7,S=b)},function(b){X=b.current},()=>z==null?void 0:z.requestFocus(),P,E,()=>{q()},function(){return se},r,s,d,function(b){xn.call(this,o,b)},()=>{n(0,T=!0),J(!1)},b=>{L&&b&&a.updateChatItem(L,{rich_text_json_repr:b})},()=>w,function(b){oe[b?"unshift":"push"](()=>{N=b,n(10,N)})},function(b){oe[b?"unshift":"push"](()=>{z=b,n(11,z)})},function(b){oe[b?"unshift":"push"](()=>{se=b,n(12,se)})},m]}class Cs extends Z{constructor(e){super(),j(this,e,ys,xs,W,{requestId:1,placeholder:2,content:3,collapsed:4,onSubmitEdit:23,onCancelEdit:24,setIsCollapsed:5,userExpanded:0,requestStartEdit:25,getEditorContainer:26},null,[-1,-1])}get requestStartEdit(){return this.$$.ctx[25]}get getEditorContainer(){return this.$$.ctx[26]}}const ie=class ie{constructor(e){ne(this,"_tipTapExtension");ne(this,"_resizeObserver");ne(this,"_checkHeight",e=>{var t,s;const n=e.getBoundingClientRect().height;(s=(t=this._options).onResize)==null||s.call(t,n)});ne(this,"_setResizeObserver",e=>{var t;const n=(t=e.view)==null?void 0:t.dom;n&&(this._resizeObserver=new ResizeObserver(s=>{for(const r of s)this._checkHeight(r.target)}),this._resizeObserver.observe(n),this._checkHeight(n))});ne(this,"_clearResizeObserver",()=>{var e;(e=this._resizeObserver)==null||e.disconnect(),this._resizeObserver=void 0});ne(this,"updateOptions",e=>{this._options={...this._options,...e}});this._options=e;const n=ie._getNextPluginId(),t=this._setResizeObserver,s=this._clearResizeObserver,r=this._checkHeight;this._tipTapExtension=On.create({name:n,onCreate:function(){var i;((i=this.editor.view)==null?void 0:i.dom)&&(t(this.editor),this.editor.on("destroy",s))},onUpdate:function(){var i;const c=(i=this.editor.view)==null?void 0:i.dom;c&&r(c)},onDestroy:()=>{var c;(c=this._resizeObserver)==null||c.disconnect(),this._resizeObserver=void 0}})}get tipTapExtension(){return this._tipTapExtension}};ne(ie,"_sequenceId",0),ne(ie,"RESIZE_OBSERVER_PLUGIN_KEY_BASE","augment-resize-observer-plugin-{}"),ne(ie,"_getSequenceId",()=>ie._sequenceId++),ne(ie,"_getNextPluginId",()=>{const e=ie._getSequenceId().toString();return ie.RESIZE_OBSERVER_PLUGIN_KEY_BASE.replace("{}",e)});let Ve=ie;function _s(o,e,n){let{height:t=0}=e;const s=a=>{n(0,t=a)},r=re(Dn.CONTEXT_KEY),c=new Ve({onResize:s}),i=r.pluginManager.registerPlugin(c);return Ut(i),o.$$set=a=>{"height"in a&&n(0,t=a.height)},c.updateOptions({onResize:s}),[t]}let ks=class extends Z{constructor(o){super(),j(this,o,_s,null,W,{height:0})}};function bs(o){let e,n,t;function s(c){o[21](c)}let r={};return o[6]!==void 0&&(r.height=o[6]),e=new ks({props:r}),oe.push(()=>qe(e,"height",s)),{c(){y(e.$$.fragment)},m(c,i){C(e,c,i),t=!0},p(c,i){const a={};!n&&64&i&&(n=!0,a.height=c[6],Be(()=>n=!1)),e.$set(a)},i(c){t||($(e.$$.fragment,c),t=!0)},o(c){p(e.$$.fragment,c),t=!1},d(c){_(e,c)}}}function Is(o){let e,n,t;function s(c){o[23](c)}let r={collapsed:o[7],content:o[3]??o[1],requestId:o[2],onSubmitEdit:o[13],onCancelEdit:o[5],setIsCollapsed:o[11],$$slots:{default:[bs]},$$scope:{ctx:o}};return o[8]!==void 0&&(r.userExpanded=o[8]),e=new Cs({props:r}),o[22](e),oe.push(()=>qe(e,"userExpanded",s)),{c(){y(e.$$.fragment)},m(c,i){C(e,c,i),t=!0},p(c,i){const a={};128&i&&(a.collapsed=c[7]),10&i&&(a.content=c[3]??c[1]),4&i&&(a.requestId=c[2]),32&i&&(a.onCancelEdit=c[5]),134217792&i&&(a.$$scope={dirty:i,ctx:c}),!n&&256&i&&(n=!0,a.userExpanded=c[8],Be(()=>n=!1)),e.$set(a)},i(c){t||($(e.$$.fragment,c),t=!0)},o(c){p(e.$$.fragment,c),t=!1},d(c){o[22](null),_(e,c)}}}function Ss(o){let e,n,t;return n=new Rn({props:{items:o[10]}}),{c(){e=M("div"),y(n.$$.fragment),v(e,"slot","edit"),v(e,"class","c-user-msg__actions svelte-ln0veu")},m(s,r){x(s,e,r),C(n,e,null),t=!0},p(s,r){const c={};1024&r&&(c.items=s[10]),n.$set(c)},i(s){t||($(n.$$.fragment,s),t=!0)},o(s){p(n.$$.fragment,s),t=!1},d(s){s&&h(e),_(n)}}}function Ts(o){let e,n,t,s;return e=new Ge({props:{timestamp:o[4],$$slots:{edit:[Ss],content:[Is]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(r,c){C(e,r,c),n=!0,t||(s=[Q(window,"keydown",Gt("Escape",o[12])),Q(window,"mousedown",o[12])],t=!0)},p(r,[c]){const i={};16&c&&(i.timestamp=r[4]),134219758&c&&(i.$$scope={dirty:c,ctx:r}),e.$set(i)},i(r){n||($(e.$$.fragment,r),n=!0)},o(r){p(e.$$.fragment,r),n=!1},d(r){_(e,r),t=!1,ze(s)}}}function Rs(o,e,n){let t,s,r,c,i,a,l,u=V,d=()=>(u(),u=fe(m,w=>n(20,l=w)),m);o.$$.on_destroy.push(()=>u());let{chatModel:m}=e;d();let{msg:g}=e,{requestId:k}=e,{richTextJsonRepr:L}=e,{timestamp:f}=e,{onStartEdit:I=()=>{}}=e,{onAcceptEdit:A=()=>{}}=e,{onCancelEdit:P=()=>{}}=e;const E=re(Fe.key);let J=!1,T=!1;async function S(w){await Dt(),n(7,J=w&&r&&!T)}const N=w=>{i&&k&&(S(!1),z==null||z.requestStartEdit(),I(),w.stopPropagation())};let z;return o.$$set=w=>{"chatModel"in w&&d(n(0,m=w.chatModel)),"msg"in w&&n(1,g=w.msg),"requestId"in w&&n(2,k=w.requestId),"richTextJsonRepr"in w&&n(3,L=w.richTextJsonRepr),"timestamp"in w&&n(4,f=w.timestamp),"onStartEdit"in w&&n(14,I=w.onStartEdit),"onAcceptEdit"in w&&n(15,A=w.onAcceptEdit),"onCancelEdit"in w&&n(5,P=w.onCancelEdit)},o.$$.update=()=>{var w,X;1048580&o.$$.dirty&&n(19,s=k===void 0||k===((X=(w=l==null?void 0:l.currentConversationModel)==null?void 0:w.lastExchange)==null?void 0:X.request_id)),524288&o.$$.dirty&&n(16,r=!s&&!0),1310724&o.$$.dirty&&n(17,i=k!==void 0&&l.flags.fullFeatured&&l.flags.enableEditableHistory&&!t),131072&o.$$.dirty&&n(10,a=[...i?[{label:"Edit message",action:N,id:"edit-message",disabled:!1,icon:Kn}]:[]]),65600&o.$$.dirty&&c&&r&&(z!=null&&z.getEditorContainer())&&c&&r&&S(!(J&&c<120)&&c>120)},n(18,t=!!(E!=null&&E.isActive)),n(6,c=0),[m,g,k,L,f,P,c,J,T,z,a,S,()=>{},function(w,X){if(!k)return;m.currentConversationModel.clearHistoryFrom(k);const q=l.flags.enableChatMultimodal&&w.richTextJsonRepr?m.currentConversationModel.createStructuredRequestNodes(w.richTextJsonRepr):void 0;m.currentConversationModel.sendExchange({request_message:w.rawText,rich_text_json_repr:w.richTextJsonRepr,status:$e.draft,mentioned_items:X,structured_request_nodes:q}),A()},I,A,r,i,t,s,l,function(w){c=w,n(6,c)},function(w){oe[w?"unshift":"push"](()=>{z=w,n(9,z)})},function(w){T=w,n(8,T)}]}class Es extends Z{constructor(e){super(),j(this,e,Rs,Ts,W,{chatModel:0,msg:1,requestId:2,richTextJsonRepr:3,timestamp:4,onStartEdit:14,onAcceptEdit:15,onCancelEdit:5})}}function dt(o){let e,n;return e=new Es({props:{msg:o[1].request_message??"",richTextJsonRepr:o[11],chatModel:o[0],requestId:o[9],timestamp:o[1].timestamp}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p(t,s){const r={};2&s&&(r.msg=t[1].request_message??""),2048&s&&(r.richTextJsonRepr=t[11]),1&s&&(r.chatModel=t[0]),512&s&&(r.requestId=t[9]),2&s&&(r.timestamp=t[1].timestamp),e.$set(r)},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function $t(o){let e,n,t;function s(a,l){return a[1].display_error_message?As:a[1].response_text&&a[1].response_text.length>0?Ls:Ms}let r=s(o),c=r(o),i=o[9]&&mt(o);return{c(){e=M("div"),c.c(),n=B(),i&&i.c(),v(e,"class","c-msg-list__turn-response-failure svelte-1d1manc")},m(a,l){x(a,e,l),c.m(e,null),R(e,n),i&&i.m(e,null),t=!0},p(a,l){r===(r=s(a))&&c?c.p(a,l):(c.d(1),c=r(a),c&&(c.c(),c.m(e,n))),a[9]?i?(i.p(a,l),512&l&&$(i,1)):(i=mt(a),i.c(),$(i,1),i.m(e,null)):i&&(D(),p(i,1,1,()=>{i=null}),U())},i(a){t||($(i),t=!0)},o(a){p(i),t=!1},d(a){a&&h(e),c.d(),i&&i.d()}}}function Ms(o){let e,n,t,s;return{c(){e=F(`We encountered an issue sending your message. Please
        `),n=M("button"),n.textContent="try again",v(n,"class","svelte-1d1manc")},m(r,c){x(r,e,c),x(r,n,c),t||(s=Q(n,"click",ye(o[15])),t=!0)},p:V,d(r){r&&(h(e),h(n)),t=!1,s()}}}function Ls(o){let e,n,t,s,r;return{c(){e=F(`Connection lost. Please
        `),n=M("button"),n.textContent="try again",t=F(`
        to restart the conversation!`),v(n,"class","svelte-1d1manc")},m(c,i){x(c,e,i),x(c,n,i),x(c,t,i),s||(r=Q(n,"click",ye(o[15])),s=!0)},p:V,d(c){c&&(h(e),h(n),h(t)),s=!1,r()}}}function As(o){let e,n=o[1].display_error_message+"";return{c(){e=F(n)},m(t,s){x(t,e,s)},p(t,s){2&s&&n!==(n=t[1].display_error_message+"")&&K(e,n)},d(t){t&&h(e)}}}function mt(o){let e,n,t,s,r,c,i,a;function l(d){o[21](d)}let u={onOpenChange:o[16],content:o[7],triggerOn:[Pe.Hover],$$slots:{default:[zs]},$$scope:{ctx:o}};return o[8]!==void 0&&(u.requestClose=o[8]),c=new Se({props:u}),oe.push(()=>qe(c,"requestClose",l)),{c(){e=M("div"),n=M("span"),t=F("Request ID: "),s=F(o[9]),r=B(),y(c.$$.fragment),v(e,"class","c-msg-list__request-id svelte-1d1manc")},m(d,m){x(d,e,m),R(e,n),R(n,t),R(n,s),R(e,r),C(c,e,null),a=!0},p(d,m){(!a||512&m)&&K(s,d[9]);const g={};128&m&&(g.content=d[7]),16777216&m&&(g.$$scope={dirty:m,ctx:d}),!i&&256&m&&(i=!0,g.requestClose=d[8],Be(()=>i=!1)),c.$set(g)},i(d){a||($(c.$$.fragment,d),a=!0)},o(d){p(c.$$.fragment,d),a=!1},d(d){d&&h(e),_(c)}}}function Fs(o){let e,n;return e=new Yn({}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function zs(o){let e,n;return e=new ke({props:{variant:"ghost",color:"neutral",size:1,$$slots:{default:[Fs]},$$scope:{ctx:o}}}),e.$on("click",o[17]),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p(t,s){const r={};16777216&s&&(r.$$scope={dirty:s,ctx:t}),e.$set(r)},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function qs(o){let e,n,t,s,r,c=!o[10]&&!De(o[1])&&!o[12],i=c&&dt(o);t=new En({props:{chatModel:o[6],turn:o[1],turnIndex:o[3],requestId:o[9],isLastTurn:o[2],showName:!o[10],group:o[5],showFooter:!We(o[1]),markdown:o[1].response_text??"",timestamp:o[1].timestamp,messageListContainer:o[4]}});let a=o[1].status===$e.failed&&$t(o);return{c(){e=M("div"),i&&i.c(),n=B(),y(t.$$.fragment),s=B(),a&&a.c(),v(e,"class","c-msg-list__turn svelte-1d1manc")},m(l,u){x(l,e,u),i&&i.m(e,null),R(e,n),C(t,e,null),R(e,s),a&&a.m(e,null),r=!0},p(l,[u]){5122&u&&(c=!l[10]&&!De(l[1])&&!l[12]),c?i?(i.p(l,u),5122&u&&$(i,1)):(i=dt(l),i.c(),$(i,1),i.m(e,n)):i&&(D(),p(i,1,1,()=>{i=null}),U());const d={};64&u&&(d.chatModel=l[6]),2&u&&(d.turn=l[1]),8&u&&(d.turnIndex=l[3]),512&u&&(d.requestId=l[9]),4&u&&(d.isLastTurn=l[2]),1024&u&&(d.showName=!l[10]),32&u&&(d.group=l[5]),2&u&&(d.showFooter=!We(l[1])),2&u&&(d.markdown=l[1].response_text??""),2&u&&(d.timestamp=l[1].timestamp),16&u&&(d.messageListContainer=l[4]),t.$set(d),l[1].status===$e.failed?a?(a.p(l,u),2&u&&$(a,1)):(a=$t(l),a.c(),$(a,1),a.m(e,null)):a&&(D(),p(a,1,1,()=>{a=null}),U())},i(l){r||($(i),$(t.$$.fragment,l),$(a),r=!0)},o(l){p(i),p(t.$$.fragment,l),p(a),r=!1},d(l){l&&h(e),i&&i.d(),_(t),a&&a.d()}}}function Bs(o,e,n){let t,s,r,c,i,a,l,u,d,m,g=V,k=()=>(g(),g=fe(f,w=>n(6,u=w)),f),L=V;o.$$.on_destroy.push(()=>g()),o.$$.on_destroy.push(()=>L());let{chatModel:f}=e;k();let{turn:I}=e,{isLastTurn:A=!1}=e,{turnIndex:P=0}=e,{messageListContainer:E}=e,{group:J}=e;const T=re(Fe.key);ae(o,T,w=>n(20,m=w));let S,N="Copy request ID",z=()=>{};return o.$$set=w=>{"chatModel"in w&&k(n(0,f=w.chatModel)),"turn"in w&&n(1,I=w.turn),"isLastTurn"in w&&n(2,A=w.isLastTurn),"turnIndex"in w&&n(3,P=w.turnIndex),"messageListContainer"in w&&n(4,E=w.messageListContainer),"group"in w&&n(5,J=w.group)},o.$$.update=()=>{var w;2&o.$$.dirty&&n(9,t=I.request_id),64&o.$$.dirty&&(n(13,s=u==null?void 0:u.currentConversationModel),L(),L=fe(s,X=>n(23,d=X))),1048576&o.$$.dirty&&n(19,r=(m==null?void 0:m.isActive)&&((w=m==null?void 0:m.currentAgent)==null?void 0:w.is_setup_script_agent)===!0),8&o.$$.dirty&&n(18,c=P===0),786432&o.$$.dirty&&n(12,i=r&&c),66&o.$$.dirty&&n(11,a=u.flags.enableRichTextHistory?I.rich_text_json_repr:void 0),2&o.$$.dirty&&n(10,l=Ot(I))},[f,I,A,P,E,J,u,N,z,t,l,a,i,s,T,()=>{d.resendTurn(I)},function(w){w||(clearTimeout(S),S=void 0,n(7,N="Copy request ID"))},async function(){t&&(await navigator.clipboard.writeText(t),n(7,N="Copied!"),clearTimeout(S),S=setTimeout(z,1500))},c,r,m,function(w){z=w,n(8,z)}]}class yc extends Z{constructor(e){super(),j(this,e,Bs,qs,W,{chatModel:0,turn:1,isLastTurn:2,turnIndex:3,messageListContainer:4,group:5})}}function Ps(o){let e,n,t,s,r,c,i;const a=o[15].default,l=he(a,o,o[14],null);return{c(){e=M("div"),l&&l.c(),v(e,"class",n=Qe(`c-msg-list__item ${o[5]}`)+" svelte-1s0uz2w"),v(e,"style",t=`min-height: calc(${o[4]}px - (var(--msg-list-item-spacing) * 2));`),v(e,"data-request-id",o[6])},m(u,d){x(u,e,d),l&&l.m(e,null),o[16](e),r=!0,c||(i=Vt(s=Mn.call(null,e,{follow:!o[2]&&o[1],scrollContainer:o[3],disableScrollUp:!0,smooth:!0,bottom:!0})),c=!0)},p(u,[d]){l&&l.p&&(!r||16384&d)&&ve(l,a,u,u[14],r?we(a,u[14],d,null):xe(u[14]),null),(!r||32&d&&n!==(n=Qe(`c-msg-list__item ${u[5]}`)+" svelte-1s0uz2w"))&&v(e,"class",n),(!r||16&d&&t!==(t=`min-height: calc(${u[4]}px - (var(--msg-list-item-spacing) * 2));`))&&v(e,"style",t),(!r||64&d)&&v(e,"data-request-id",u[6]),s&&ge(s.update)&&14&d&&s.update.call(null,{follow:!u[2]&&u[1],scrollContainer:u[3],disableScrollUp:!0,smooth:!0,bottom:!0})},i(u){r||($(l,u),r=!0)},o(u){p(l,u),r=!1},d(u){u&&h(e),l&&l.d(u),o[16](null),c=!1,i()}}}function Ns(o,e,n){let t,s,r,c,i=V,a=V,l=()=>(a(),a=fe(g,T=>n(13,c=T)),g);o.$$.on_destroy.push(()=>i()),o.$$.on_destroy.push(()=>a());let{$$slots:u={},$$scope:d}=e,{requestId:m}=e,{chatModel:g}=e;l();let k,{isLastItem:L=!1}=e,{userControlsScroll:f=!1}=e,{releaseScroll:I=()=>{}}=e,{messageListContainer:A}=e,{minHeight:P}=e,{class:E=""}=e,{dataRequestId:J}=e;return Ce(()=>{A&&L&&Zt(A,{smooth:!0,onScrollFinish:I})}),o.$$set=T=>{"requestId"in T&&n(9,m=T.requestId),"chatModel"in T&&l(n(0,g=T.chatModel)),"isLastItem"in T&&n(1,L=T.isLastItem),"userControlsScroll"in T&&n(2,f=T.userControlsScroll),"releaseScroll"in T&&n(10,I=T.releaseScroll),"messageListContainer"in T&&n(3,A=T.messageListContainer),"minHeight"in T&&n(4,P=T.minHeight),"class"in T&&n(5,E=T.class),"dataRequestId"in T&&n(6,J=T.dataRequestId),"$$scope"in T&&n(14,d=T.$$scope)},o.$$.update=()=>{var T,S;8192&o.$$.dirty&&(n(8,t=(T=c==null?void 0:c.currentConversationModel)==null?void 0:T.focusModel),i(),i=fe(t,N=>n(12,r=N))),4608&o.$$.dirty&&n(11,s=((S=r.focusedItem)==null?void 0:S.request_id)===m),2048&o.$$.dirty&&s&&A&&k&&Ln(A,k,{topBuffer:0,smooth:!0,scrollDuration:100,onScrollFinish:I})},[g,L,f,A,P,E,J,k,t,m,I,s,r,c,d,u,function(T){oe[T?"unshift":"push"](()=>{k=T,n(7,k)})}]}class Cc extends Z{constructor(e){super(),j(this,e,Ns,Ps,W,{requestId:9,chatModel:0,isLastItem:1,userControlsScroll:2,releaseScroll:10,messageListContainer:3,minHeight:4,class:5,dataRequestId:6})}}function Hs(o){let e;return{c(){e=F("Generating response...")},m(n,t){x(n,e,t)},d(n){n&&h(e)}}}function pt(o){let e,n;return e=new tn.Root({props:{color:"neutral",size:1,$$slots:{default:[Ds]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p(t,s){const r={};1029&s&&(r.$$scope={dirty:s,ctx:t}),e.$set(r)},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function Os(o){let e,n;return{c(){e=M("span"),n=F(o[2]),v(e,"class","c-gen-response__timer svelte-148snxl"),O(e,"is_minutes",o[0]>=60)},m(t,s){x(t,e,s),R(e,n)},p(t,s){4&s&&K(n,t[2]),1&s&&O(e,"is_minutes",t[0]>=60)},d(t){t&&h(e)}}}function Ds(o){let e,n;return e=new te({props:{type:"monospace",size:1,weight:"light",color:"secondary",$$slots:{default:[Os]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p(t,s){const r={};1029&s&&(r.$$scope={dirty:s,ctx:t}),e.$set(r)},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function Us(o){let e,n,t,s,r,c,i;t=new _e({props:{size:1}}),r=new te({props:{size:1,weight:"light",color:"secondary",$$slots:{default:[Hs]},$$scope:{ctx:o}}});let a=o[1]&&pt(o);return{c(){e=M("div"),n=M("span"),y(t.$$.fragment),s=B(),y(r.$$.fragment),c=B(),a&&a.c(),v(n,"class","c-gen-response__text svelte-148snxl"),v(e,"class","c-gen-response svelte-148snxl")},m(l,u){x(l,e,u),R(e,n),C(t,n,null),R(n,s),C(r,n,null),R(e,c),a&&a.m(e,null),i=!0},p(l,[u]){const d={};1024&u&&(d.$$scope={dirty:u,ctx:l}),r.$set(d),l[1]?a?(a.p(l,u),2&u&&$(a,1)):(a=pt(l),a.c(),$(a,1),a.m(e,null)):a&&(D(),p(a,1,1,()=>{a=null}),U())},i(l){i||($(t.$$.fragment,l),$(r.$$.fragment,l),$(a),i=!0)},o(l){p(t.$$.fragment,l),p(r.$$.fragment,l),p(a),i=!1},d(l){l&&h(e),_(t),_(r),a&&a.d()}}}function Vs(o,e,n){let t,s,r,{timeToTimerMs:c=5e3}=e,i=0,a=Date.now(),l=!1;function u(){n(0,i=Math.floor((Date.now()-a)/1e3))}function d(){n(1,l=!0),u(),r=setInterval(u,1e3)}return Ce(function(){return s=setTimeout(d,c),a=Date.now(),()=>{n(0,i=0),n(1,l=!1),clearTimeout(s),clearInterval(r)}}),o.$$set=m=>{"timeToTimerMs"in m&&n(3,c=m.timeToTimerMs)},o.$$.update=()=>{1&o.$$.dirty&&n(2,t=function(m){return m>=60?`${Math.floor(m/60)}:${String(m%60).padStart(2,"0")}`:`0:${String(m).padStart(2,"0")}`}(i))},[i,l,t,c]}class Js extends Z{constructor(e){super(),j(this,e,Vs,Us,W,{timeToTimerMs:3})}}class ue{constructor(e){ne(this,"type","plainText");this.text=e}}class Re{constructor(e){ne(this,"type","specialBlock");this.text=e}}function Je(o){return o.map(e=>e.text).join("")}function Gs(o){let e,n,t,s,r=(!o[0].status||o[0].status===$e.success)&&o[4]===Je(o[3]);e=new es({props:{renderers:o[5],markdown:o[1]+o[4]}});let c=r&&gt(o);return{c(){y(e.$$.fragment),n=B(),c&&c.c(),t=ee()},m(i,a){C(e,i,a),x(i,n,a),c&&c.m(i,a),x(i,t,a),s=!0},p(i,a){const l={};18&a&&(l.markdown=i[1]+i[4]),e.$set(l),25&a&&(r=(!i[0].status||i[0].status===$e.success)&&i[4]===Je(i[3])),r?c?(c.p(i,a),25&a&&$(c,1)):(c=gt(i),c.c(),$(c,1),c.m(t.parentNode,t)):c&&(D(),p(c,1,1,()=>{c=null}),U())},i(i){s||($(e.$$.fragment,i),$(c),s=!0)},o(i){p(e.$$.fragment,i),p(c),s=!1},d(i){i&&(h(n),h(t)),_(e,i),c&&c.d(i)}}}function Zs(o){let e;function n(r,c){return r[0].display_error_message?Ys:r[0].response_text&&r[0].response_text.length>0?Ks:Ws}let t=n(o),s=t(o);return{c(){e=M("div"),s.c(),v(e,"class","c-msg-failure svelte-9a9fi8")},m(r,c){x(r,e,c),s.m(e,null)},p(r,c){t===(t=n(r))&&s?s.p(r,c):(s.d(1),s=t(r),s&&(s.c(),s.m(e,null)))},i:V,o:V,d(r){r&&h(e),s.d()}}}function js(o){let e,n;return e=new Js({}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p:V,i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function gt(o){let e;const n=o[7].default,t=he(n,o,o[8],null);return{c(){t&&t.c()},m(s,r){t&&t.m(s,r),e=!0},p(s,r){t&&t.p&&(!e||256&r)&&ve(t,n,s,s[8],e?we(n,s[8],r,null):xe(s[8]),null)},i(s){e||($(t,s),e=!0)},o(s){p(t,s),e=!1},d(s){t&&t.d(s)}}}function Ws(o){let e,n,t=o[2]&&ft(o);return{c(){e=F("We encountered an issue sending your message."),t&&t.c(),n=F(".")},m(s,r){x(s,e,r),t&&t.m(s,r),x(s,n,r)},p(s,r){s[2]?t?t.p(s,r):(t=ft(s),t.c(),t.m(n.parentNode,n)):t&&(t.d(1),t=null)},d(s){s&&(h(e),h(n)),t&&t.d(s)}}}function Ks(o){let e,n,t=o[2]&&ht(o);return{c(){e=F("Connection lost."),t&&t.c(),n=ee()},m(s,r){x(s,e,r),t&&t.m(s,r),x(s,n,r)},p(s,r){s[2]?t?t.p(s,r):(t=ht(s),t.c(),t.m(n.parentNode,n)):t&&(t.d(1),t=null)},d(s){s&&(h(e),h(n)),t&&t.d(s)}}}function Ys(o){let e,n=o[0].display_error_message+"";return{c(){e=F(n)},m(t,s){x(t,e,s)},p(t,s){1&s&&n!==(n=t[0].display_error_message+"")&&K(e,n)},d(t){t&&h(e)}}}function ft(o){let e,n,t,s;return{c(){e=F(`Please
            `),n=M("button"),n.textContent="try again",v(n,"class","svelte-9a9fi8")},m(r,c){x(r,e,c),x(r,n,c),t||(s=Q(n,"click",ye(function(){ge(o[2])&&o[2].apply(this,arguments)})),t=!0)},p(r,c){o=r},d(r){r&&(h(e),h(n)),t=!1,s()}}}function ht(o){let e,n,t,s,r;return{c(){e=F(`Please
            `),n=M("button"),n.textContent="try again",t=F("."),v(n,"class","svelte-9a9fi8")},m(c,i){x(c,e,i),x(c,n,i),x(c,t,i),s||(r=Q(n,"click",ye(function(){ge(o[2])&&o[2].apply(this,arguments)})),s=!0)},p(c,i){o=c},d(c){c&&(h(e),h(n),h(t)),s=!1,r()}}}function Xs(o){let e,n,t,s;const r=[js,Zs,Gs],c=[];function i(a,l){return(!a[0].status||a[0].status===$e.sent)&&a[4].length<=0?0:a[0].status===$e.failed?1:2}return e=i(o),n=c[e]=r[e](o),{c(){n.c(),t=ee()},m(a,l){c[e].m(a,l),x(a,t,l),s=!0},p(a,l){let u=e;e=i(a),e===u?c[e].p(a,l):(D(),p(c[u],1,1,()=>{c[u]=null}),U(),n=c[e],n?n.p(a,l):(n=c[e]=r[e](a),n.c()),$(n,1),n.m(t.parentNode,t))},i(a){s||($(n),s=!0)},o(a){p(n),s=!1},d(a){a&&h(t),c[e].d(a)}}}function Qs(o){let e,n;return e=new Ge({props:{isAugment:!0,$$slots:{content:[Xs]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p(t,[s]){const r={};287&s&&(r.$$scope={dirty:s,ctx:t}),e.$set(r)},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function eo(o,e,n){let t,s,{$$slots:r={},$$scope:c}=e,{turn:i}=e,{preamble:a=""}=e,{resendTurn:l}=e,{markdownBlocks:u=[]}=e,d={code:Xn,codespan:Qn,link:An};return Ce(()=>{if(i.seen_state===pn.seen)return void n(0,i.response_text=Je(t),i);let m=Date.now();const g=function*(f){for(const I of f)if(I.type==="specialBlock")yield I.text;else for(const A of I.text)yield A}(t);let k=g.next();const L=()=>{let f=Date.now();const I=Math.round((f-m)/8);let A="";for(let P=0;P<I&&!k.done;P++)A+=k.value,k=g.next();n(0,i.response_text+=A,i),m=f,k.done||requestAnimationFrame(L)};L()}),o.$$set=m=>{"turn"in m&&n(0,i=m.turn),"preamble"in m&&n(1,a=m.preamble),"resendTurn"in m&&n(2,l=m.resendTurn),"markdownBlocks"in m&&n(6,u=m.markdownBlocks),"$$scope"in m&&n(8,c=m.$$scope)},o.$$.update=()=>{1&o.$$.dirty&&n(0,i.response_text=i.response_text??"",i),65&o.$$.dirty&&n(3,t=i.response_text?[new ue(i.response_text)]:u),1&o.$$.dirty&&n(4,s=i.response_text??"")},[i,a,l,t,s,d,u,r,c]}class to extends Z{constructor(e){super(),j(this,e,eo,Qs,W,{turn:0,preamble:1,resendTurn:2,markdownBlocks:6})}}function no(o){let e,n,t,s;return e=new Ne.Content({props:{content:o[2]}}),t=new Kt({props:{requestEditorFocus:o[4]}}),{c(){y(e.$$.fragment),n=B(),y(t.$$.fragment)},m(r,c){C(e,r,c),x(r,n,c),C(t,r,c),s=!0},p:V,i(r){s||($(e.$$.fragment,r),$(t.$$.fragment,r),s=!0)},o(r){p(e.$$.fragment,r),p(t.$$.fragment,r),s=!1},d(r){r&&h(n),_(e,r),_(t,r)}}}function so(o){let e,n,t={slot:"content",$$slots:{default:[no]},$$scope:{ctx:o}};return e=new Ne.Root({props:t}),o[7](e),{c(){y(e.$$.fragment)},m(s,r){C(e,s,r),n=!0},p(s,r){const c={};512&r&&(c.$$scope={dirty:r,ctx:s}),e.$set(c)},i(s){n||($(e.$$.fragment,s),n=!0)},o(s){p(e.$$.fragment,s),n=!1},d(s){o[7](null),_(e,s)}}}function oo(o){let e,n,t,s;return e=new Ge({props:{$$slots:{content:[so]},$$scope:{ctx:o}}}),t=new to({props:{turn:o[1],markdownBlocks:o[3]}}),{c(){y(e.$$.fragment),n=B(),y(t.$$.fragment)},m(r,c){C(e,r,c),x(r,n,c),C(t,r,c),s=!0},p(r,[c]){const i={};513&c&&(i.$$scope={dirty:c,ctx:r}),e.$set(i)},i(r){s||($(e.$$.fragment,r),$(t.$$.fragment,r),s=!0)},o(r){p(e.$$.fragment,r),p(t.$$.fragment,r),s=!1},d(r){r&&h(n),_(e,r),_(t,r)}}}function ro(o,e,n){let{flagsModel:t}=e,{turn:s}=e;const r={seen_state:s.seen_state,status:$e.success},c=[[new Re("[**Chat**](https://docs.augmentcode.com/using-augment/chat)"),new ue(": Explore your codebase, get up to speed on unfamiliar code, and work through technical problems using natural language.")],[new Re("[**Code Completions**](https://docs.augmentcode.com/using-augment/completions)"),new ue(": Receive intelligent code suggestions that take your entire codebase into account as you type.")],[new Re("[**Instructions**](https://docs.augmentcode.com/using-augment/instructions)"),new ue(": Use natural language prompts to write or modify code, applied as a diff for your review.")]];t.suggestedEditsAvailable&&c.push([new Re("[**Suggested Edits**](https://docs.augmentcode.com/using-augment/suggested-edits)"),new ue(": Take your completions beyond the cursor and across your workspace.")]);let i,a=[new ue(`Welcome to Augment!

Augment can help you understand code, debug issues, and ship faster with its deep understanding of your codebase. Here is what Augment can do for you:

`),...c.flatMap((l,u)=>[new ue(`${u+1}. `),...l,new ue(`

`)]),new ue("Ask questions to learn more! Just remember to tag **@Augment** when asking about Augment's capabilities.")];return o.$$set=l=>{"flagsModel"in l&&n(5,t=l.flagsModel),"turn"in l&&n(6,s=l.turn)},[i,r,{type:"doc",content:[{type:"paragraph",content:[{type:"text",text:"What can "},{type:"mention",attrs:{id:"Augment",label:"Augment",data:{id:"Augment",label:"Augment"}}},{type:"text",text:" do?"}]}]},a,function(){i==null||i.requestFocus()},t,s,function(l){oe[l?"unshift":"push"](()=>{i=l,n(0,i)})}]}class _c extends Z{constructor(e){super(),j(this,e,ro,oo,W,{flagsModel:5,turn:6})}}function co(o){let e,n;return{c(){e=pe("svg"),n=pe("path"),v(n,"d","M6.04995 2.74998C6.04995 2.44623 5.80371 2.19998 5.49995 2.19998C5.19619 2.19998 4.94995 2.44623 4.94995 2.74998V12.25C4.94995 12.5537 5.19619 12.8 5.49995 12.8C5.80371 12.8 6.04995 12.5537 6.04995 12.25V2.74998ZM10.05 2.74998C10.05 2.44623 9.80371 2.19998 9.49995 2.19998C9.19619 2.19998 8.94995 2.44623 8.94995 2.74998V12.25C8.94995 12.5537 9.19619 12.8 9.49995 12.8C9.80371 12.8 10.05 12.5537 10.05 12.25V2.74998Z"),v(n,"fill","currentColor"),v(n,"fill-rule","evenodd"),v(n,"clip-rule","evenodd"),v(e,"width","15"),v(e,"height","15"),v(e,"viewBox","0 0 15 15"),v(e,"fill","none"),v(e,"xmlns","http://www.w3.org/2000/svg")},m(t,s){x(t,e,s),R(e,n)},p:V,i:V,o:V,d(t){t&&h(e)}}}class io extends Z{constructor(e){super(),j(this,e,null,co,W,{})}}function ao(o){let e,n,t,s,r;return n=new io({}),{c(){e=M("span"),y(n.$$.fragment),t=F(`
  Waiting for user input`),s=F(o[0]),v(e,"class","c-gen-response svelte-5is5us")},m(c,i){x(c,e,i),C(n,e,null),R(e,t),R(e,s),r=!0},p(c,[i]){(!r||1&i)&&K(s,c[0])},i(c){r||($(n.$$.fragment,c),r=!0)},o(c){p(n.$$.fragment,c),r=!1},d(c){c&&h(e),_(n)}}}function lo(o,e,n){let t=".";return Ce(()=>{const s=setInterval(()=>{n(0,t=t.length>=3?".":t+".")},500);return()=>clearInterval(s)}),[t]}class kc extends Z{constructor(e){super(),j(this,e,lo,ao,W,{})}}function uo(o){let e;return{c(){e=F("Resuming remote agent...")},m(n,t){x(n,e,t)},d(n){n&&h(e)}}}function vt(o){let e,n;return e=new tn.Root({props:{color:"neutral",size:1,$$slots:{default:[mo]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p(t,s){const r={};1029&s&&(r.$$scope={dirty:s,ctx:t}),e.$set(r)},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function $o(o){let e,n;return{c(){e=M("span"),n=F(o[2]),v(e,"class","c-resuming-agent__timer svelte-16pyinb"),O(e,"is_minutes",o[0]>=60)},m(t,s){x(t,e,s),R(e,n)},p(t,s){4&s&&K(n,t[2]),1&s&&O(e,"is_minutes",t[0]>=60)},d(t){t&&h(e)}}}function mo(o){let e,n;return e=new te({props:{type:"monospace",size:1,weight:"light",color:"secondary",$$slots:{default:[$o]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p(t,s){const r={};1029&s&&(r.$$scope={dirty:s,ctx:t}),e.$set(r)},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function po(o){let e,n,t,s,r,c,i;t=new _e({props:{size:1}}),r=new te({props:{size:1,weight:"light",color:"secondary",$$slots:{default:[uo]},$$scope:{ctx:o}}});let a=o[1]&&vt(o);return{c(){e=M("div"),n=M("span"),y(t.$$.fragment),s=B(),y(r.$$.fragment),c=B(),a&&a.c(),v(n,"class","c-resuming-agent__text svelte-16pyinb"),v(e,"class","c-resuming-agent svelte-16pyinb")},m(l,u){x(l,e,u),R(e,n),C(t,n,null),R(n,s),C(r,n,null),R(e,c),a&&a.m(e,null),i=!0},p(l,[u]){const d={};1024&u&&(d.$$scope={dirty:u,ctx:l}),r.$set(d),l[1]?a?(a.p(l,u),2&u&&$(a,1)):(a=vt(l),a.c(),$(a,1),a.m(e,null)):a&&(D(),p(a,1,1,()=>{a=null}),U())},i(l){i||($(t.$$.fragment,l),$(r.$$.fragment,l),$(a),i=!0)},o(l){p(t.$$.fragment,l),p(r.$$.fragment,l),p(a),i=!1},d(l){l&&h(e),_(t),_(r),a&&a.d()}}}function go(o,e,n){let t,s,r,{timeToTimerMs:c=5e3}=e,i=0,a=Date.now(),l=!1;function u(){n(0,i=Math.floor((Date.now()-a)/1e3))}function d(){n(1,l=!0),u(),r=setInterval(u,1e3)}return Ce(function(){return s=setTimeout(d,c),a=Date.now(),()=>{n(0,i=0),n(1,l=!1),clearTimeout(s),clearInterval(r)}}),o.$$set=m=>{"timeToTimerMs"in m&&n(3,c=m.timeToTimerMs)},o.$$.update=()=>{1&o.$$.dirty&&n(2,t=function(m){return m>=60?`${Math.floor(m/60)}:${String(m%60).padStart(2,"0")}`:`0:${String(m).padStart(2,"0")}`}(i))},[i,l,t,c]}class bc extends Z{constructor(e){super(),j(this,e,go,po,W,{timeToTimerMs:3})}}function fo(o){let e,n,t,s,r;return n=new _e({props:{size:1}}),{c(){e=M("span"),y(n.$$.fragment),t=B(),s=F(o[0]),v(e,"class","c-retry-response svelte-1lxm8qk")},m(c,i){x(c,e,i),C(n,e,null),R(e,t),R(e,s),r=!0},p(c,[i]){(!r||1&i)&&K(s,c[0])},i(c){r||($(n.$$.fragment,c),r=!0)},o(c){p(n.$$.fragment,c),r=!1},d(c){c&&h(e),_(n)}}}function ho(o,e,n){let{message:t="Retrying..."}=e;return o.$$set=s=>{"message"in s&&n(0,t=s.message)},[t]}class Ic extends Z{constructor(e){super(),j(this,e,ho,fo,W,{message:0})}}function vo(o){let e,n,t;return e=new Zn({}),{c(){y(e.$$.fragment),n=F(`
    Stopped`)},m(s,r){C(e,s,r),x(s,n,r),t=!0},i(s){t||($(e.$$.fragment,s),t=!0)},o(s){p(e.$$.fragment,s),t=!1},d(s){s&&h(n),_(e,s)}}}function xo(o){let e,n,t;return n=new te({props:{size:1,$$slots:{default:[vo]},$$scope:{ctx:o}}}),{c(){e=M("span"),y(n.$$.fragment),v(e,"class","c-stopped svelte-lv19x6")},m(s,r){x(s,e,r),C(n,e,null),t=!0},p(s,[r]){const c={};1&r&&(c.$$scope={dirty:r,ctx:s}),n.$set(c)},i(s){t||($(n.$$.fragment,s),t=!0)},o(s){p(n.$$.fragment,s),t=!1},d(s){s&&h(e),_(n)}}}class Sc extends Z{constructor(e){super(),j(this,e,null,xo,W,{})}}function wo(o){let e,n;return{c(){e=pe("svg"),n=pe("path"),v(n,"fill-rule","evenodd"),v(n,"clip-rule","evenodd"),v(n,"d","M4.85355 2.14645C5.04882 2.34171 5.04882 2.65829 4.85355 2.85355L3.70711 4H9C11.4853 4 13.5 6.01472 13.5 8.5C13.5 10.9853 11.4853 13 9 13H5C4.72386 13 4.5 12.7761 4.5 12.5C4.5 12.2239 4.72386 12 5 12H9C10.933 12 12.5 10.433 12.5 8.5C12.5 6.567 10.933 5 9 5H3.70711L4.85355 6.14645C5.04882 6.34171 5.04882 6.65829 4.85355 6.85355C4.65829 7.04882 4.34171 7.04882 4.14645 6.85355L2.14645 4.85355C1.95118 4.65829 1.95118 4.34171 2.14645 4.14645L4.14645 2.14645C4.34171 1.95118 4.65829 1.95118 4.85355 2.14645Z"),v(n,"fill","currentColor"),v(e,"width","15"),v(e,"height","15"),v(e,"viewBox","0 0 15 15"),v(e,"fill","none"),v(e,"xmlns","http://www.w3.org/2000/svg")},m(t,s){x(t,e,s),R(e,n)},p:V,i:V,o:V,d(t){t&&h(e)}}}class nn extends Z{constructor(e){super(),j(this,e,null,wo,W,{})}}function yo(o){let e,n,t;return{c(){e=pe("svg"),n=pe("path"),t=pe("path"),v(n,"fill-rule","evenodd"),v(n,"clip-rule","evenodd"),v(n,"d","M3.1784 5.56111C3.17842 5.85569 3.41722 6.09449 3.71173 6.09444L9.92275 6.09447C10.0585 6.09447 10.1929 6.06857 10.3189 6.01818L13.9947 4.54786C14.1973 4.46681 14.33 4.27071 14.3301 4.05261C14.33 3.83458 14.1973 3.63846 13.9948 3.55744L10.3189 2.08711C10.1929 2.0367 10.0584 2.01083 9.92278 2.01079L3.71173 2.01079C3.41722 2.01084 3.17844 2.24962 3.1784 2.54412L3.1784 5.56111ZM9.92275 5.0278L4.2451 5.02781L4.24509 3.07749L9.92278 3.07745L11.5339 3.72196L12.2527 4.05263C12.2527 4.05263 11.8167 4.25864 11.534 4.38331C10.9139 4.65675 9.92275 5.0278 9.92275 5.0278Z"),v(n,"fill","currentColor"),v(t,"fill-rule","evenodd"),v(t,"clip-rule","evenodd"),v(t,"d","M8.53346 1.59998C8.53346 1.30543 8.29468 1.06665 8.00013 1.06665C7.70558 1.06665 7.4668 1.30543 7.4668 1.59998V3.07746L8.53346 3.07745V1.59998ZM8.53346 5.0278L7.4668 5.0278V14.4C7.4668 14.6945 7.70558 14.9333 8.00013 14.9333C8.29468 14.9333 8.53346 14.6945 8.53346 14.4V5.0278Z"),v(t,"fill","currentColor"),v(e,"width","15"),v(e,"height","15"),v(e,"viewBox","0 0 15 15"),v(e,"fill","none"),v(e,"xmlns","http://www.w3.org/2000/svg")},m(s,r){x(s,e,r),R(e,n),R(e,t)},p:V,i:V,o:V,d(s){s&&h(e)}}}class sn extends Z{constructor(e){super(),j(this,e,null,yo,W,{})}}function xt(o){let e,n,t,s,r;return n=new ts({props:{size:1,insetContent:!0,variant:"ghost",class:"c-checkpoint-tag","data-testid":"checkpoint-version-tag",$$slots:{default:[So]},$$scope:{ctx:o}}}),n.$on("click",o[17]),s=new ke({props:{variant:o[6]?"soft":"ghost-block",color:"neutral",size:1,disabled:o[6]||o[4],class:"c-revert-button","data-testid":"revert-button",$$slots:{default:[Ro]},$$scope:{ctx:o}}}),s.$on("click",o[12]),{c(){e=M("div"),y(n.$$.fragment),t=B(),y(s.$$.fragment),v(e,"class","c-checkpoint-container svelte-q20gs5"),v(e,"data-checkpoint-number",o[0]),O(e,"c-checkpoint-container--target-checkpoint",o[6]),O(e,"c-checkpoint-container--dimmed-marker",o[5])},m(c,i){x(c,e,i),C(n,e,null),R(e,t),C(s,e,null),r=!0},p(c,i){const a={};1048778&i&&(a.$$scope={dirty:i,ctx:c}),n.$set(a);const l={};64&i&&(l.variant=c[6]?"soft":"ghost-block"),80&i&&(l.disabled=c[6]||c[4]),1048656&i&&(l.$$scope={dirty:i,ctx:c}),s.$set(l),(!r||1&i)&&v(e,"data-checkpoint-number",c[0]),(!r||64&i)&&O(e,"c-checkpoint-container--target-checkpoint",c[6]),(!r||32&i)&&O(e,"c-checkpoint-container--dimmed-marker",c[5])},i(c){r||($(n.$$.fragment,c),$(s.$$.fragment,c),r=!0)},o(c){p(n.$$.fragment,c),p(s.$$.fragment,c),r=!1},d(c){c&&h(e),_(n),_(s)}}}function Co(o){let e,n;return e=new sn({props:{slot:"leftIcon"}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p:V,i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function _o(o){let e,n;return{c(){e=F("Checkpoint "),n=F(o[3])},m(t,s){x(t,e,s),x(t,n,s)},p(t,s){8&s&&K(n,t[3])},d(t){t&&(h(e),h(n))}}}function ko(o){let e,n=Le(o[7])+"";return{c(){e=F(n)},m(t,s){x(t,e,s)},p(t,s){128&s&&n!==(n=Le(t[7])+"")&&K(e,n)},d(t){t&&h(e)}}}function bo(o){let e;return{c(){e=F(o[1])},m(n,t){x(n,e,t)},p(n,t){2&t&&K(e,n[1])},d(n){n&&h(e)}}}function Io(o){let e;function n(r,c){return r[1]?bo:r[6]?ko:void 0}let t=n(o),s=t&&t(o);return{c(){s&&s.c(),e=ee()},m(r,c){s&&s.m(r,c),x(r,e,c)},p(r,c){t===(t=n(r))&&s?s.p(r,c):(s&&s.d(1),s=t&&t(r),s&&(s.c(),s.m(e.parentNode,e)))},d(r){r&&h(e),s&&s.d(r)}}}function So(o){let e,n;return e=new Wt({props:{size:1,shrink:!0,align:"left",$$slots:{grayText:[Io],text:[_o],leftIcon:[Co]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p(t,s){const r={};1048778&s&&(r.$$scope={dirty:s,ctx:t}),e.$set(r)},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function To(o){let e,n;return e=new nn({}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function Ro(o){let e,n;return e=new Se({props:{triggerOn:[Pe.Hover],content:o[6]||o[4]?"Cannot revert to current version":"Revert to this version",$$slots:{default:[To]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p(t,s){const r={};80&s&&(r.content=t[6]||t[4]?"Cannot revert to current version":"Revert to this version"),1048576&s&&(r.$$scope={dirty:s,ctx:t}),e.$set(r)},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function Eo(o){let e,n,t=(!o[4]||o[2])&&xt(o);return{c(){t&&t.c(),e=ee()},m(s,r){t&&t.m(s,r),x(s,e,r),n=!0},p(s,[r]){!s[4]||s[2]?t?(t.p(s,r),20&r&&$(t,1)):(t=xt(s),t.c(),$(t,1),t.m(e.parentNode,e)):t&&(D(),p(t,1,1,()=>{t=null}),U())},i(s){n||($(t),n=!0)},o(s){p(t),n=!1},d(s){s&&h(e),t&&t.d(s)}}}function Mo(o,e,n){let t,s,r,c,i,a,l,u,d,m,g,{turn:k}=e;const L=re("checkpointStore"),{targetCheckpointIdx:f,totalCheckpointCount:I,uuidToIdx:A}=L;function P(E){wn(f,m=E,m)}return ae(o,f,E=>n(15,m=E)),ae(o,I,E=>n(14,d=E)),ae(o,A,E=>n(16,g=E)),o.$$set=E=>{"turn"in E&&n(13,k=E.turn)},o.$$.update=()=>{var E,J,T;73728&o.$$.dirty&&n(0,t=g.get(k.uuid)??-1),8192&o.$$.dirty&&n(7,s=k.toTimestamp),49153&o.$$.dirty&&n(6,(J=d,r=(E=t)===(T=m)||T===void 0&&E===J-1)),49153&o.$$.dirty&&n(5,c=function(S,N,z){return S===z&&z!==void 0&&z<N-1}(t,d,m)),16385&o.$$.dirty&&n(4,i=t===d-1),1&o.$$.dirty&&n(3,a=t+1),8192&o.$$.dirty&&n(2,l=Me(k)),8192&o.$$.dirty&&n(1,u=Me(k)?function(S){var N,z;if((N=S.revertTarget)!=null&&N.uuid){const w=g.get(S.revertTarget.uuid);return w===void 0?void 0:`Reverted to Checkpoint ${w+1}`}return(z=S.revertTarget)!=null&&z.filePath?`Undid changes to ${S.revertTarget.filePath.relPath}`:void 0}(k):void 0)},[t,u,l,a,i,c,r,s,f,I,A,P,async function(){await L.revertToCheckpoint(k.uuid)},k,d,m,g,()=>P(t)]}class Tc extends Z{constructor(e){super(),j(this,e,Mo,Eo,W,{turn:13})}}function Lo(o){let e,n;return e=new sn({props:{slot:"leftIcon"}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p:V,i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function wt(o){let e,n,t,s,r,c,i=o[1]===1?"":"s";return{c(){e=M("span"),n=F("("),t=F(o[1]),s=F(" file"),r=F(i),c=F(")"),v(e,"class","c-checkpoint-files-count")},m(a,l){x(a,e,l),R(e,n),R(e,t),R(e,s),R(e,r),R(e,c)},p(a,l){2&l&&K(t,a[1]),2&l&&i!==(i=a[1]===1?"":"s")&&K(r,i)},d(a){a&&h(e)}}}function Ao(o){let e,n,t,s,r=o[1]>0&&wt(o);return{c(){e=F("Checkpoint "),n=F(o[0]),t=B(),r&&r.c(),s=ee()},m(c,i){x(c,e,i),x(c,n,i),x(c,t,i),r&&r.m(c,i),x(c,s,i)},p(c,i){1&i&&K(n,c[0]),c[1]>0?r?r.p(c,i):(r=wt(c),r.c(),r.m(s.parentNode,s)):r&&(r.d(1),r=null)},d(c){c&&(h(e),h(n),h(t),h(s)),r&&r.d(c)}}}function Fo(o){let e;return{c(){e=F(o[2])},m(n,t){x(n,e,t)},p(n,t){4&t&&K(e,n[2])},d(n){n&&h(e)}}}function zo(o){let e;return{c(){e=F(o[3])},m(n,t){x(n,e,t)},p(n,t){8&t&&K(e,n[3])},d(n){n&&h(e)}}}function qo(o){let e;function n(r,c){return r[3]?zo:r[2]?Fo:void 0}let t=n(o),s=t&&t(o);return{c(){s&&s.c(),e=ee()},m(r,c){s&&s.m(r,c),x(r,e,c)},p(r,c){t===(t=n(r))&&s?s.p(r,c):(s&&s.d(1),s=t&&t(r),s&&(s.c(),s.m(e.parentNode,e)))},d(r){r&&h(e),s&&s.d(r)}}}function yt(o){let e,n,t;return n=new qn({props:{totalAddedLines:o[4].totalAddedLines,totalRemovedLines:o[4].totalRemovedLines}}),{c(){e=M("div"),y(n.$$.fragment),v(e,"class","c-checkpoint-summary")},m(s,r){x(s,e,r),C(n,e,null),t=!0},p(s,r){const c={};16&r&&(c.totalAddedLines=s[4].totalAddedLines),16&r&&(c.totalRemovedLines=s[4].totalRemovedLines),n.$set(c)},i(s){t||($(n.$$.fragment,s),t=!0)},o(s){p(n.$$.fragment,s),t=!1},d(s){s&&h(e),_(n)}}}function Ct(o){let e,n;return e=new ke({props:{variant:"ghost-block",color:"neutral",size:1,class:"c-revert-button","data-testid":"revert-button",$$slots:{default:[Po]},$$scope:{ctx:o}}}),e.$on("click",function(){ge(o[7])&&o[7].apply(this,arguments)}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p(t,s){o=t;const r={};256&s&&(r.$$scope={dirty:s,ctx:o}),e.$set(r)},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function Bo(o){let e,n;return e=new nn({}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function Po(o){let e,n;return e=new Se({props:{triggerOn:[Pe.Hover],content:"Revert to this Checkpoint",$$slots:{default:[Bo]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p(t,s){const r={};256&s&&(r.$$scope={dirty:s,ctx:t}),e.$set(r)},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function No(o){let e,n,t,s,r,c,i,a;t=new Qt({}),r=new Wt({props:{size:1,shrink:!0,align:"left",$$slots:{grayText:[qo],text:[Ao],leftIcon:[Lo]},$$scope:{ctx:o}}});let l=o[5]&&yt(o),u=!o[6]&&Ct(o);return{c(){e=M("div"),n=M("div"),y(t.$$.fragment),s=B(),y(r.$$.fragment),c=B(),l&&l.c(),i=B(),u&&u.c(),v(n,"class","c-checkpoint-tag"),v(e,"class","c-checkpoint-header svelte-htx8xt")},m(d,m){x(d,e,m),R(e,n),C(t,n,null),R(n,s),C(r,n,null),R(e,c),l&&l.m(e,null),R(e,i),u&&u.m(e,null),a=!0},p(d,[m]){const g={};271&m&&(g.$$scope={dirty:m,ctx:d}),r.$set(g),d[5]?l?(l.p(d,m),32&m&&$(l,1)):(l=yt(d),l.c(),$(l,1),l.m(e,i)):l&&(D(),p(l,1,1,()=>{l=null}),U()),d[6]?u&&(D(),p(u,1,1,()=>{u=null}),U()):u?(u.p(d,m),64&m&&$(u,1)):(u=Ct(d),u.c(),$(u,1),u.m(e,null))},i(d){a||($(t.$$.fragment,d),$(r.$$.fragment,d),$(l),$(u),a=!0)},o(d){p(t.$$.fragment,d),p(r.$$.fragment,d),p(l),p(u),a=!1},d(d){d&&h(e),_(t),_(r),l&&l.d(),u&&u.d()}}}function Ho(o,e,n){let{displayCheckpointIdx:t}=e,{filesCount:s=0}=e,{timestamp:r=""}=e,{revertMessage:c}=e,{diffSummary:i={totalAddedLines:0,totalRemovedLines:0}}=e,{hasChanges:a=!1}=e,{isTarget:l=!1}=e,{onRevertClick:u}=e;return o.$$set=d=>{"displayCheckpointIdx"in d&&n(0,t=d.displayCheckpointIdx),"filesCount"in d&&n(1,s=d.filesCount),"timestamp"in d&&n(2,r=d.timestamp),"revertMessage"in d&&n(3,c=d.revertMessage),"diffSummary"in d&&n(4,i=d.diffSummary),"hasChanges"in d&&n(5,a=d.hasChanges),"isTarget"in d&&n(6,l=d.isTarget),"onRevertClick"in d&&n(7,u=d.onRevertClick)},[t,s,r,c,i,a,l,u]}class Oo extends Z{constructor(e){super(),j(this,e,Ho,No,W,{displayCheckpointIdx:0,filesCount:1,timestamp:2,revertMessage:3,diffSummary:4,hasChanges:5,isTarget:6,onRevertClick:7})}}function _t(o,e,n){const t=o.slice();return t[33]=e[n],t}function kt(o){let e,n,t,s,r,c,i;function a(u){o[27](u)}let l={class:"c-checkpoint-collapsible",stickyHeader:!0,$$slots:{header:[Go],default:[Jo]},$$scope:{ctx:o}};return o[3]!==void 0&&(l.collapsed=o[3]),n=new en({props:l}),oe.push(()=>qe(n,"collapsed",a)),{c(){e=M("div"),y(n.$$.fragment),v(e,"class","c-checkpoint-container svelte-mxd32u"),v(e,"data-checkpoint-number",o[2]),O(e,"c-checkpoint-container--target-checkpoint",o[11]),O(e,"c-checkpoint-container--dimmed-marker",o[10])},m(u,d){x(u,e,d),C(n,e,null),r=!0,c||(i=Vt(s=Fn.call(null,e,{onVisible:o[28],scrollTarget:document.body})),c=!0)},p(u,d){const m={};6522&d[0]|32&d[1]&&(m.$$scope={dirty:d,ctx:u}),!t&&8&d[0]&&(t=!0,m.collapsed=u[3],Be(()=>t=!1)),n.$set(m),(!r||4&d[0])&&v(e,"data-checkpoint-number",u[2]),s&&ge(s.update)&&1&d[0]&&s.update.call(null,{onVisible:u[28],scrollTarget:document.body}),(!r||2048&d[0])&&O(e,"c-checkpoint-container--target-checkpoint",u[11]),(!r||1024&d[0])&&O(e,"c-checkpoint-container--dimmed-marker",u[10])},i(u){r||($(n.$$.fragment,u),r=!0)},o(u){p(n.$$.fragment,u),r=!1},d(u){u&&h(e),_(n),c=!1,i()}}}function Do(o){let e,n,t;return n=new te({props:{size:1,color:"neutral",$$slots:{default:[Vo]},$$scope:{ctx:o}}}),{c(){e=M("div"),y(n.$$.fragment),v(e,"class","c-edits-list c-edits-list--empty svelte-mxd32u")},m(s,r){x(s,e,r),C(n,e,null),t=!0},p(s,r){const c={};32&r[1]&&(c.$$scope={dirty:r,ctx:s}),n.$set(c)},i(s){t||($(n.$$.fragment,s),t=!0)},o(s){p(n.$$.fragment,s),t=!1},d(s){s&&h(e),_(n)}}}function Uo(o){let e,n,t=Ae(o[1]),s=[];for(let c=0;c<t.length;c+=1)s[c]=bt(_t(o,t,c));const r=c=>p(s[c],1,1,()=>{s[c]=null});return{c(){e=M("div");for(let c=0;c<s.length;c+=1)s[c].c();v(e,"class","c-edits-list svelte-mxd32u")},m(c,i){x(c,e,i);for(let a=0;a<s.length;a+=1)s[a]&&s[a].m(e,null);n=!0},p(c,i){if(196610&i[0]){let a;for(t=Ae(c[1]),a=0;a<t.length;a+=1){const l=_t(c,t,a);s[a]?(s[a].p(l,i),$(s[a],1)):(s[a]=bt(l),s[a].c(),$(s[a],1),s[a].m(e,null))}for(D(),a=t.length;a<s.length;a+=1)r(a);U()}},i(c){if(!n){for(let i=0;i<t.length;i+=1)$(s[i]);n=!0}},o(c){s=s.filter(Boolean);for(let i=0;i<s.length;i+=1)p(s[i]);n=!1},d(c){c&&h(e),Jt(s,c)}}}function Vo(o){let e;return{c(){e=F("No changes to show")},m(n,t){x(n,e,t)},d(n){n&&h(e)}}}function bt(o){let e,n;function t(){return o[25](o[33])}function s(){return o[26](o[33])}return e=new jn({props:{qualifiedPathName:o[33].qualifiedPathName,lineChanges:o[33].changesSummary,onClickFile:t,onClickReview:s}}),{c(){y(e.$$.fragment)},m(r,c){C(e,r,c),n=!0},p(r,c){o=r;const i={};2&c[0]&&(i.qualifiedPathName=o[33].qualifiedPathName),2&c[0]&&(i.lineChanges=o[33].changesSummary),2&c[0]&&(i.onClickFile=t),2&c[0]&&(i.onClickReview=s),e.$set(i)},i(r){n||($(e.$$.fragment,r),n=!0)},o(r){p(e.$$.fragment,r),n=!1},d(r){_(e,r)}}}function Jo(o){let e,n,t,s;const r=[Uo,Do],c=[];function i(a,l){return a[4]?0:a[3]?-1:1}return~(e=i(o))&&(n=c[e]=r[e](o)),{c(){n&&n.c(),t=ee()},m(a,l){~e&&c[e].m(a,l),x(a,t,l),s=!0},p(a,l){let u=e;e=i(a),e===u?~e&&c[e].p(a,l):(n&&(D(),p(c[u],1,1,()=>{c[u]=null}),U()),~e?(n=c[e],n?n.p(a,l):(n=c[e]=r[e](a),n.c()),$(n,1),n.m(t.parentNode,t)):n=null)},i(a){s||($(n),s=!0)},o(a){p(n),s=!1},d(a){a&&h(t),~e&&c[e].d(a)}}}function Go(o){let e,n;return e=new Oo({props:{slot:"header",displayCheckpointIdx:o[8],filesCount:o[1].length,timestamp:Le(o[12]),revertMessage:o[6],diffSummary:o[5],hasChanges:o[4],isTarget:o[11],onRevertClick:o[18]}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p(t,s){const r={};256&s[0]&&(r.displayCheckpointIdx=t[8]),2&s[0]&&(r.filesCount=t[1].length),4096&s[0]&&(r.timestamp=Le(t[12])),64&s[0]&&(r.revertMessage=t[6]),32&s[0]&&(r.diffSummary=t[5]),16&s[0]&&(r.hasChanges=t[4]),2048&s[0]&&(r.isTarget=t[11]),e.$set(r)},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function Zo(o){let e,n,t=(!o[9]||o[7])&&kt(o);return{c(){t&&t.c(),e=ee()},m(s,r){t&&t.m(s,r),x(s,e,r),n=!0},p(s,r){!s[9]||s[7]?t?(t.p(s,r),640&r[0]&&$(t,1)):(t=kt(s),t.c(),$(t,1),t.m(e.parentNode,e)):t&&(D(),p(t,1,1,()=>{t=null}),U())},i(s){n||($(t),n=!0)},o(s){p(t),n=!1},d(s){s&&h(e),t&&t.d(s)}}}function jo(o,e,n){let t,s,r,c,i,a,l,u,d,m,g,k,L,f,I,{turn:A}=e;const P=re("checkpointStore"),E=re("chatModel"),{targetCheckpointIdx:J,totalCheckpointCount:T,uuidToIdx:S}=P;ae(o,J,H=>n(23,f=H)),ae(o,T,H=>n(22,L=H)),ae(o,S,H=>n(24,I=H));let N=!0;function z(H){E==null||E.extensionClient.openFile({repoRoot:H.rootPath,pathName:H.relPath,allowOutOfWorkspace:!0})}function w(H){E==null||E.extensionClient.showAgentReview(H,r,s,!1)}let X=[],q=!1,G=!1;return o.$$set=H=>{"turn"in H&&n(19,A=H.turn)},o.$$.update=()=>{var H,me,se;17301504&o.$$.dirty[0]&&n(2,t=I.get(A.uuid)??-1),524288&o.$$.dirty[0]&&n(12,s=A.toTimestamp),524288&o.$$.dirty[0]&&(r=A.fromTimestamp),12582916&o.$$.dirty[0]&&n(11,(me=L,c=(H=t)===(se=f)||se===void 0&&H===me-1)),12582916&o.$$.dirty[0]&&n(10,i=function(b,ce,le){return b===le&&le!==void 0&&le<ce-1}(t,L,f)),4194308&o.$$.dirty[0]&&n(9,a=t===L-1),4&o.$$.dirty[0]&&n(8,l=t+1),524288&o.$$.dirty[0]&&n(7,u=Me(A)),524288&o.$$.dirty[0]&&n(6,d=Me(A)?function(b){var ce,le;if((ce=b.revertTarget)!=null&&ce.uuid){const be=I.get(b.revertTarget.uuid);return be===void 0?void 0:`Reverted to Checkpoint ${be+1}`}return(le=b.revertTarget)!=null&&le.filePath?`Undid changes to ${b.revertTarget.filePath.relPath}`:void 0}(A):void 0),2621441&o.$$.dirty[0]&&q&&A&&!G&&P.getCheckpointSummary(A).then(b=>{n(20,X=b),n(21,G=!0)}),1048576&o.$$.dirty[0]&&n(1,m=X.filter(b=>b.changesSummary&&(b.changesSummary.totalAddedLines>0||b.changesSummary.totalRemovedLines>0))),2&o.$$.dirty[0]&&n(5,g=m.reduce((b,ce)=>{var le,be;return b.totalAddedLines+=((le=ce.changesSummary)==null?void 0:le.totalAddedLines)??0,b.totalRemovedLines+=((be=ce.changesSummary)==null?void 0:be.totalRemovedLines)??0,b},{totalAddedLines:0,totalRemovedLines:0})),2&o.$$.dirty[0]&&n(4,k=m.length>0)},[q,m,t,N,k,g,d,u,l,a,i,c,s,J,T,S,z,w,function(){P.revertToCheckpoint(A.uuid)},A,X,G,L,f,I,H=>z(H.qualifiedPathName),H=>w(H.qualifiedPathName),function(H){N=H,n(3,N)},()=>n(0,q=!0)]}class Rc extends Z{constructor(e){super(),j(this,e,jo,Zo,W,{turn:19},null,[-1,-1])}}const It={[Oe.SUCCESS]:"success",[Oe.FAILED]:"error",[Oe.SKIPPED]:"skipped"},St={[de.success]:"success",[de.failure]:"error",[de.running]:null,[de.unknown]:"unknown",[de.skipped]:"skipped"};function Tt(o){return o in It?It[o]:o in St?St[o]:null}function Rt(o){switch(o){case"success":return"Success";case"error":return"Failed";case"skipped":return"Skipped";case"unknown":return"Unknown";case null:return"Running"}}function Et(o){let e,n,t;return n=new te({props:{size:1,type:"monospace",$$slots:{default:[Wo]},$$scope:{ctx:o}}}),{c(){e=M("div"),y(n.$$.fragment),v(e,"class","c-command-output__code-block svelte-1kepo6x")},m(s,r){x(s,e,r),C(n,e,null),t=!0},p(s,r){const c={};16386&r&&(c.$$scope={dirty:r,ctx:s}),n.$set(c)},i(s){t||($(n.$$.fragment,s),t=!0)},o(s){p(n.$$.fragment,s),t=!1},d(s){s&&h(e),_(n)}}}function Wo(o){let e;return{c(){e=F(o[1])},m(n,t){x(n,e,t)},p(n,t){2&t&&K(e,n[1])},d(n){n&&h(e)}}}function Mt(o){let e,n,t,s;const r=[Yo,Ko],c=[];function i(a,l){return a[5]?0:1}return n=i(o),t=c[n]=r[n](o),{c(){e=M("div"),t.c(),v(e,"class","c-command-output__code-block c-command-output__code-block--output svelte-1kepo6x")},m(a,l){x(a,e,l),c[n].m(e,null),s=!0},p(a,l){let u=n;n=i(a),n===u?c[n].p(a,l):(D(),p(c[u],1,1,()=>{c[u]=null}),U(),t=c[n],t?t.p(a,l):(t=c[n]=r[n](a),t.c()),$(t,1),t.m(e,null))},i(a){s||($(t),s=!0)},o(a){p(t),s=!1},d(a){a&&h(e),c[n].d()}}}function Ko(o){let e,n;return e=new te({props:{size:1,type:"monospace",$$slots:{default:[Xo]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p(t,s){const r={};16388&s&&(r.$$scope={dirty:s,ctx:t}),e.$set(r)},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function Yo(o){let e,n;return e=new ns.Root({props:{$$slots:{default:[Qo]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p(t,s){const r={};16452&s&&(r.$$scope={dirty:s,ctx:t}),e.$set(r)},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function Xo(o){let e;return{c(){e=F(o[2])},m(n,t){x(n,e,t)},p(n,t){4&t&&K(e,n[2])},d(n){n&&h(e)}}}function Qo(o){let e,n;return e=new ss({props:{text:o[2],lang:o[6]}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p(t,s){const r={};4&s&&(r.text=t[2]),64&s&&(r.lang=t[6]),e.$set(r)},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function er(o){let e,n,t,s,r=o[1]&&!o[0]&&Et(o),c=o[2]&&(!o[3]||o[3]!=="skipped")&&Mt(o);const i=o[12].default,a=he(i,o,o[14],null);return{c(){e=M("div"),r&&r.c(),n=B(),c&&c.c(),t=B(),a&&a.c(),v(e,"class","c-command-output__command-details")},m(l,u){x(l,e,u),r&&r.m(e,null),R(e,n),c&&c.m(e,null),R(e,t),a&&a.m(e,null),s=!0},p(l,u){l[1]&&!l[0]?r?(r.p(l,u),3&u&&$(r,1)):(r=Et(l),r.c(),$(r,1),r.m(e,n)):r&&(D(),p(r,1,1,()=>{r=null}),U()),!l[2]||l[3]&&l[3]==="skipped"?c&&(D(),p(c,1,1,()=>{c=null}),U()):c?(c.p(l,u),12&u&&$(c,1)):(c=Mt(l),c.c(),$(c,1),c.m(e,t)),a&&a.p&&(!s||16384&u)&&ve(a,i,l,l[14],s?we(i,l[14],u,null):xe(l[14]),null)},i(l){s||($(r),$(c),$(a,l),s=!0)},o(l){p(r),p(c),p(a,l),s=!1},d(l){l&&h(e),r&&r.d(),c&&c.d(),a&&a.d(l)}}}function tr(o){let e;return{c(){e=M("div"),v(e,"class","c-command-output__collapsible-header__spacer svelte-1kepo6x")},m(n,t){x(n,e,t)},i:V,o:V,d(n){n&&h(e)}}}function nr(o){let e,n;return e=new Qt({}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function sr(o){let e,n;return e=new te({props:{size:1,type:"monospace",$$slots:{default:[rr]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p(t,s){const r={};16386&s&&(r.$$scope={dirty:s,ctx:t}),e.$set(r)},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function or(o){let e,n;return e=new te({props:{size:1,weight:"medium",$$slots:{default:[cr]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p(t,s){const r={};16385&s&&(r.$$scope={dirty:s,ctx:t}),e.$set(r)},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function rr(o){let e;return{c(){e=F(o[1])},m(n,t){x(n,e,t)},p(n,t){2&t&&K(e,n[1])},d(n){n&&h(e)}}}function cr(o){let e;return{c(){e=F(o[0])},m(n,t){x(n,e,t)},p(n,t){1&t&&K(e,n[0])},d(n){n&&h(e)}}}function ir(o){let e,n,t,s=o[3]==="skipped"&&Lt(o);return n=new Se({props:{content:Rt(o[3]),triggerOn:[Pe.Hover],$$slots:{default:[ur]},$$scope:{ctx:o}}}),{c(){s&&s.c(),e=B(),y(n.$$.fragment)},m(r,c){s&&s.m(r,c),x(r,e,c),C(n,r,c),t=!0},p(r,c){r[3]==="skipped"?s?8&c&&$(s,1):(s=Lt(r),s.c(),$(s,1),s.m(e.parentNode,e)):s&&(D(),p(s,1,1,()=>{s=null}),U());const i={};8&c&&(i.content=Rt(r[3])),16392&c&&(i.$$scope={dirty:c,ctx:r}),n.$set(i)},i(r){t||($(s),$(n.$$.fragment,r),t=!0)},o(r){p(s),p(n.$$.fragment,r),t=!1},d(r){r&&h(e),s&&s.d(r),_(n,r)}}}function ar(o){let e,n,t;return n=new _e({props:{size:1}}),{c(){e=M("div"),y(n.$$.fragment),v(e,"class","c-command-output__status-icon c-command-output__status-icon--loading svelte-1kepo6x")},m(s,r){x(s,e,r),C(n,e,null),t=!0},p:V,i(s){t||($(n.$$.fragment,s),t=!0)},o(s){p(n.$$.fragment,s),t=!1},d(s){s&&h(e),_(n)}}}function Lt(o){let e,n;return e=new te({props:{size:1,$$slots:{default:[lr]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function lr(o){let e;return{c(){e=F("Skipped")},m(n,t){x(n,e,t)},d(n){n&&h(e)}}}function ur(o){let e,n,t;var s=o[11](o[3]);return s&&(n=et(s,{})),{c(){e=M("div"),n&&y(n.$$.fragment),v(e,"class","c-command-output__status-icon svelte-1kepo6x"),O(e,"c-command-output__status-icon--success",o[3]==="success"),O(e,"c-command-output__status-icon--error",o[3]==="error"),O(e,"c-command-output__status-icon--warning",o[3]==="skipped")},m(r,c){x(r,e,c),n&&C(n,e,null),t=!0},p(r,c){if(8&c&&s!==(s=r[11](r[3]))){if(n){D();const i=n;p(i.$$.fragment,1,0,()=>{_(i,1)}),U()}s?(n=et(s,{}),y(n.$$.fragment),$(n.$$.fragment,1),C(n,e,null)):n=null}(!t||8&c)&&O(e,"c-command-output__status-icon--success",r[3]==="success"),(!t||8&c)&&O(e,"c-command-output__status-icon--error",r[3]==="error"),(!t||8&c)&&O(e,"c-command-output__status-icon--warning",r[3]==="skipped")},i(r){t||(n&&$(n.$$.fragment,r),t=!0)},o(r){n&&p(n.$$.fragment,r),t=!1},d(r){r&&h(e),n&&_(n)}}}function At(o){let e,n;return e=new Se({props:{content:o[7],align:"end",$$slots:{default:[$r]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p(t,s){const r={};128&s&&(r.content=t[7]),17412&s&&(r.$$scope={dirty:s,ctx:t}),e.$set(r)},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function dr(o){let e,n;return e=new gn({}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function $r(o){let e,n;return e=new ke({props:{variant:"ghost-block",color:"neutral",size:1,$$slots:{default:[dr]},$$scope:{ctx:o}}}),e.$on("click",o[13]),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p(t,s){const r={};16384&s&&(r.$$scope={dirty:s,ctx:t}),e.$set(r)},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function mr(o){let e,n,t,s,r,c,i,a,l,u,d,m;const g=[nr,tr],k=[];function L(S,N){return S[8]?0:1}n=L(o),t=k[n]=g[n](o);const f=[or,sr],I=[];function A(S,N){return S[0]?0:1}r=A(o),c=I[r]=f[r](o);const P=[ar,ir],E=[];function J(S,N){return S[9]?0:S[3]!==null?1:-1}~(l=J(o))&&(u=E[l]=P[l](o));let T=o[2]&&(!o[3]||o[3]!=="skipped")&&!o[9]&&At(o);return{c(){e=M("div"),t.c(),s=B(),c.c(),i=B(),a=M("div"),u&&u.c(),d=B(),T&&T.c(),v(a,"class","c-command-output__status-indicator svelte-1kepo6x"),v(e,"slot","header"),v(e,"class","c-command-output__collapsible-header svelte-1kepo6x")},m(S,N){x(S,e,N),k[n].m(e,null),R(e,s),I[r].m(e,null),R(e,i),R(e,a),~l&&E[l].m(a,null),R(a,d),T&&T.m(a,null),m=!0},p(S,N){let z=n;n=L(S),n!==z&&(D(),p(k[z],1,1,()=>{k[z]=null}),U(),t=k[n],t||(t=k[n]=g[n](S),t.c()),$(t,1),t.m(e,s));let w=r;r=A(S),r===w?I[r].p(S,N):(D(),p(I[w],1,1,()=>{I[w]=null}),U(),c=I[r],c?c.p(S,N):(c=I[r]=f[r](S),c.c()),$(c,1),c.m(e,i));let X=l;l=J(S),l===X?~l&&E[l].p(S,N):(u&&(D(),p(E[X],1,1,()=>{E[X]=null}),U()),~l?(u=E[l],u?u.p(S,N):(u=E[l]=P[l](S),u.c()),$(u,1),u.m(a,d)):u=null),!S[2]||S[3]&&S[3]==="skipped"||S[9]?T&&(D(),p(T,1,1,()=>{T=null}),U()):T?(T.p(S,N),524&N&&$(T,1)):(T=At(S),T.c(),$(T,1),T.m(a,null))},i(S){m||($(t),$(c),$(u),$(T),m=!0)},o(S){p(t),p(c),p(u),p(T),m=!1},d(S){S&&h(e),k[n].d(),I[r].d(),~l&&E[l].d(),T&&T.d()}}}function pr(o){let e,n,t;return n=new en({props:{collapsed:o[4],$$slots:{header:[mr],default:[er]},$$scope:{ctx:o}}}),{c(){e=M("div"),y(n.$$.fragment),v(e,"class","c-command-output__container svelte-1kepo6x")},m(s,r){x(s,e,r),C(n,e,null),t=!0},p(s,[r]){const c={};16&r&&(c.collapsed=s[4]),18415&r&&(c.$$scope={dirty:r,ctx:s}),n.$set(c)},i(s){t||($(n.$$.fragment,s),t=!0)},o(s){p(n.$$.fragment,s),t=!1},d(s){s&&h(e),_(n)}}}function gr(o,e,n){let{$$slots:t={},$$scope:s}=e,{title:r=""}=e,{command:c=""}=e,{output:i=null}=e,{status:a=null}=e,{collapsed:l=!1}=e,{useMonaco:u=!1}=e,{monacoLang:d="bash"}=e,{viewButtonTooltip:m="View full output in editor"}=e,{showCollapseButton:g=!0}=e,{isLoading:k=!1}=e,{onViewOutput:L}=e;return o.$$set=f=>{"title"in f&&n(0,r=f.title),"command"in f&&n(1,c=f.command),"output"in f&&n(2,i=f.output),"status"in f&&n(3,a=f.status),"collapsed"in f&&n(4,l=f.collapsed),"useMonaco"in f&&n(5,u=f.useMonaco),"monacoLang"in f&&n(6,d=f.monacoLang),"viewButtonTooltip"in f&&n(7,m=f.viewButtonTooltip),"showCollapseButton"in f&&n(8,g=f.showCollapseButton),"isLoading"in f&&n(9,k=f.isLoading),"onViewOutput"in f&&n(10,L=f.onViewOutput),"$$scope"in f&&n(14,s=f.$$scope)},[r,c,i,a,l,u,d,m,g,k,L,function(f){return f==="success"||f==="skipped"?jt:Bn},t,f=>L(f,i),s]}class fr extends Z{constructor(e){super(),j(this,e,gr,pr,W,{title:0,command:1,output:2,status:3,collapsed:4,useMonaco:5,monacoLang:6,viewButtonTooltip:7,showCollapseButton:8,isLoading:9,onViewOutput:10})}}function Ft(o,e,n){const t=o.slice();return t[14]=e[n],t}function hr(o){let e,n,t,s,r;return n=new te({props:{size:1,color:"secondary",$$slots:{default:[xr]},$$scope:{ctx:o}}}),s=new _e({props:{size:1}}),{c(){e=M("div"),y(n.$$.fragment),t=B(),y(s.$$.fragment),v(e,"class","c-agent-no-setup-logs svelte-12293rd")},m(c,i){x(c,e,i),C(n,e,null),R(e,t),C(s,e,null),r=!0},p(c,i){const a={};131072&i&&(a.$$scope={dirty:i,ctx:c}),n.$set(a)},i(c){r||($(n.$$.fragment,c),$(s.$$.fragment,c),r=!0)},o(c){p(n.$$.fragment,c),p(s.$$.fragment,c),r=!1},d(c){c&&h(e),_(n),_(s)}}}function vr(o){let e,n,t,s,r,c,i,a,l,u,d,m,g,k,L,f,I,A;r=new ke({props:{variant:"ghost",color:"neutral",size:1,class:"c-agent-setup-logs-toggle-button "+(o[2]?"c-agent-setup-logs-toggle-button--expanded":""),$$slots:{default:[wr]},$$scope:{ctx:o}}}),r.$on("click",o[7]);const P=[Cr,yr],E=[];function J(q,G){return q[0]?0:1}i=J(o),a=E[i]=P[i](o);const T=[Ir,br],S=[];function N(q,G){return q[0]?0:1}d=N(o),m=S[d]=T[d](o);let z=Ae(o[3].steps),w=[];for(let q=0;q<z.length;q+=1)w[q]=zt(Ft(o,z,q));const X=q=>p(w[q],1,1,()=>{w[q]=null});return{c(){e=M("div"),n=M("div"),t=M("div"),s=M("div"),y(r.$$.fragment),c=B(),a.c(),l=B(),u=M("div"),m.c(),g=B(),k=M("div"),L=M("div");for(let q=0;q<w.length;q+=1)w[q].c();v(s,"class","c-agent-setup-logs-summary-left svelte-12293rd"),v(u,"class","c-agent-setup-logs-summary-icon svelte-12293rd"),v(t,"class","c-agent-setup-logs-summary-content svelte-12293rd"),v(n,"class","c-agent-setup-logs-summary svelte-12293rd"),v(n,"role","button"),v(n,"tabindex","0"),v(n,"aria-expanded",o[2]),v(n,"aria-controls","agent-setup-logs-details"),v(L,"class","c-agent-setup-logs svelte-12293rd"),v(k,"class","c-agent-setup-logs-wrapper svelte-12293rd"),O(k,"is-hidden",!o[2]),v(e,"class","c-agent-setup-logs-container svelte-12293rd"),O(e,"c-agent-setup-logs-container--loading",!o[0])},m(q,G){x(q,e,G),R(e,n),R(n,t),R(t,s),C(r,s,null),R(s,c),E[i].m(s,null),R(t,l),R(t,u),S[d].m(u,null),R(e,g),R(e,k),R(k,L);for(let H=0;H<w.length;H+=1)w[H]&&w[H].m(L,null);o[12](e),f=!0,I||(A=[Q(n,"click",o[6]),Q(n,"keydown",o[8])],I=!0)},p(q,G){const H={};4&G&&(H.class="c-agent-setup-logs-toggle-button "+(q[2]?"c-agent-setup-logs-toggle-button--expanded":"")),131072&G&&(H.$$scope={dirty:G,ctx:q}),r.$set(H);let me=i;i=J(q),i!==me&&(D(),p(E[me],1,1,()=>{E[me]=null}),U(),a=E[i],a||(a=E[i]=P[i](q),a.c()),$(a,1),a.m(s,null));let se=d;if(d=N(q),d!==se&&(D(),p(S[se],1,1,()=>{S[se]=null}),U(),m=S[d],m||(m=S[d]=T[d](q),m.c()),$(m,1),m.m(u,null)),(!f||4&G)&&v(n,"aria-expanded",q[2]),40&G){let b;for(z=Ae(q[3].steps),b=0;b<z.length;b+=1){const ce=Ft(q,z,b);w[b]?(w[b].p(ce,G),$(w[b],1)):(w[b]=zt(ce),w[b].c(),$(w[b],1),w[b].m(L,null))}for(D(),b=z.length;b<w.length;b+=1)X(b);U()}(!f||4&G)&&O(k,"is-hidden",!q[2]),(!f||1&G)&&O(e,"c-agent-setup-logs-container--loading",!q[0])},i(q){if(!f){$(r.$$.fragment,q),$(a),$(m);for(let G=0;G<z.length;G+=1)$(w[G]);f=!0}},o(q){p(r.$$.fragment,q),p(a),p(m),w=w.filter(Boolean);for(let G=0;G<w.length;G+=1)p(w[G]);f=!1},d(q){q&&h(e),_(r),E[i].d(),S[d].d(),Jt(w,q),o[12](null),I=!1,ze(A)}}}function xr(o){let e;return{c(){e=F("Waiting to start agent environment...")},m(n,t){x(n,e,t)},d(n){n&&h(e)}}}function wr(o){let e,n;return e=new os({}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function yr(o){let e,n;return e=new te({props:{size:1,class:"c-agent-setup-logs-summary-text",$$slots:{default:[_r]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function Cr(o){let e,n;return e=new te({props:{size:1,class:"c-agent-setup-logs-summary-text",$$slots:{default:[kr]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function _r(o){let e;return{c(){e=F("Environment is being created...")},m(n,t){x(n,e,t)},d(n){n&&h(e)}}}function kr(o){let e;return{c(){e=F("Environment created")},m(n,t){x(n,e,t)},d(n){n&&h(e)}}}function br(o){let e,n;return e=new _e({props:{size:1}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function Ir(o){let e,n;return e=new jt({}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function zt(o){let e,n;return e=new fr({props:{title:o[14].step_description,output:o[14].logs,status:Tt(o[14].status),isLoading:o[14].status===de.running,collapsed:o[14].status!==de.running,showCollapseButton:!!o[14].logs,viewButtonTooltip:"View full output in editor",onViewOutput:o[11]}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p(t,s){const r={};8&s&&(r.title=t[14].step_description),8&s&&(r.output=t[14].logs),8&s&&(r.status=Tt(t[14].status)),8&s&&(r.isLoading=t[14].status===de.running),8&s&&(r.collapsed=t[14].status!==de.running),8&s&&(r.showCollapseButton=!!t[14].logs),e.$set(r)},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function Sr(o){let e,n,t,s;const r=[vr,hr],c=[];function i(a,l){return a[3]&&a[3].steps&&a[3].steps.length>0?0:1}return e=i(o),n=c[e]=r[e](o),{c(){n.c(),t=ee()},m(a,l){c[e].m(a,l),x(a,t,l),s=!0},p(a,[l]){let u=e;e=i(a),e===u?c[e].p(a,l):(D(),p(c[u],1,1,()=>{c[u]=null}),U(),n=c[e],n?n.p(a,l):(n=c[e]=r[e](a),n.c()),$(n,1),n.m(t.parentNode,t))},i(a){s||($(n),s=!0)},o(a){p(n),s=!1},d(a){a&&h(t),c[e].d(a)}}}function Tr(o,e,n){let t,s,r,c,i;const a=re(Fe.key);ae(o,a,g=>n(10,c=g));const l=re("chatModel");let u=!1;function d(g){g&&l&&l.extensionClient.openScratchFile(g,"plaintext")}function m(){n(2,u=!u)}return o.$$.update=()=>{var g;1024&o.$$.dirty&&n(9,t=((g=c==null?void 0:c.currentAgent)==null?void 0:g.status)||Te.agentUnspecified),512&o.$$.dirty&&n(0,s=[Te.agentIdle,Te.agentRunning,Te.agentFailed].includes(t)),1024&o.$$.dirty&&n(3,r=c==null?void 0:c.agentSetupLogs),1&o.$$.dirty&&n(2,u=!s)},[s,i,u,r,a,d,m,function(g){g.stopPropagation(),m()},function(g){g.key!=="Enter"&&g.key!==" "||(g.preventDefault(),m())},t,c,(g,k)=>d(k),function(g){oe[g?"unshift":"push"](()=>{i=g,n(1,i)})}]}class Ec extends Z{constructor(e){super(),j(this,e,Tr,Sr,W,{})}}function Rr(o){let e;const n=o[3].default,t=he(n,o,o[4],null);return{c(){t&&t.c()},m(s,r){t&&t.m(s,r),e=!0},p(s,r){t&&t.p&&(!e||16&r)&&ve(t,n,s,s[4],e?we(n,s[4],r,null):xe(s[4]),null)},i(s){e||($(t,s),e=!0)},o(s){p(t,s),e=!1},d(s){t&&t.d(s)}}}function Er(o){let e,n;return e=new zn({props:{class:"c-chat-floating-container c-chat-floating-container--"+o[0],xPos:o[1],yPos:o[2],$$slots:{default:[Rr]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p(t,[s]){const r={};1&s&&(r.class="c-chat-floating-container c-chat-floating-container--"+t[0]),2&s&&(r.xPos=t[1]),4&s&&(r.yPos=t[2]),16&s&&(r.$$scope={dirty:s,ctx:t}),e.$set(r)},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function Mr(o,e,n){let{$$slots:t={},$$scope:s}=e,{position:r="bottom"}=e,{xPos:c="middle"}=e,{yPos:i=r==="top"?"top":"bottom"}=e;return o.$$set=a=>{"position"in a&&n(0,r=a.position),"xPos"in a&&n(1,c=a.xPos),"yPos"in a&&n(2,i=a.yPos),"$$scope"in a&&n(4,s=a.$$scope)},[r,c,i,t,s]}class Lr extends Z{constructor(e){super(),j(this,e,Mr,Er,W,{position:0,xPos:1,yPos:2})}}function Ar(o){let e,n,t=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 384 512"},o[0]],s={};for(let r=0;r<t.length;r+=1)s=Ie(s,t[r]);return{c(){e=pe("svg"),n=new yn(!0),this.h()},l(r){e=Cn(r,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var c=_n(e);n=kn(c,!0),c.forEach(h),this.h()},h(){n.a=null,tt(e,s)},m(r,c){bn(r,e,c),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M174.6 472.6c4.5 4.7 10.8 7.4 17.4 7.4s12.8-2.7 17.4-7.4l168-176c9.2-9.6 8.8-24.8-.8-33.9s-24.8-8.8-33.9.8L216 396.1V56c0-13.3-10.7-24-24-24s-24 10.7-24 24v340.1L41.4 263.4c-9.2-9.6-24.3-9.9-33.9-.8s-9.9 24.3-.8 33.9l168 176z"/>',e)},p(r,[c]){tt(e,s=Ue(t,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 384 512"},1&c&&r[0]]))},i:V,o:V,d(r){r&&h(e)}}}function Fr(o,e,n){return o.$$set=t=>{n(0,e=Ie(Ie({},e),nt(t)))},[e=nt(e)]}class zr extends Z{constructor(e){super(),j(this,e,Fr,Ar,W,{})}}function qt(o){let e,n,t;return n=new ke({props:{class:"c-chat-floating-button",variant:"solid",color:"neutral",size:1,radius:"full",$$slots:{default:[qr]},$$scope:{ctx:o}}}),n.$on("click",o[1]),{c(){e=M("div"),y(n.$$.fragment),v(e,"class","c-msg-list-bottom-button svelte-rg7wt6")},m(s,r){x(s,e,r),C(n,e,null),t=!0},p(s,r){const c={};8&r&&(c.$$scope={dirty:r,ctx:s}),n.$set(c)},i(s){t||($(n.$$.fragment,s),t=!0)},o(s){p(n.$$.fragment,s),t=!1},d(s){s&&h(e),_(n)}}}function qr(o){let e,n;return e=new zr({}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function Br(o){let e,n,t=o[0]&&qt(o);return{c(){t&&t.c(),e=ee()},m(s,r){t&&t.m(s,r),x(s,e,r),n=!0},p(s,r){s[0]?t?(t.p(s,r),1&r&&$(t,1)):(t=qt(s),t.c(),$(t,1),t.m(e.parentNode,e)):t&&(D(),p(t,1,1,()=>{t=null}),U())},i(s){n||($(t),n=!0)},o(s){p(t),n=!1},d(s){s&&h(e),t&&t.d(s)}}}function Pr(o){let e,n;return e=new Lr({props:{position:"bottom",$$slots:{default:[Br]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p(t,[s]){const r={};9&s&&(r.$$scope={dirty:s,ctx:t}),e.$set(r)},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function Nr(o,e,n){let{showScrollDown:t=!1}=e,{messageListElement:s=null}=e;return o.$$set=r=>{"showScrollDown"in r&&n(0,t=r.showScrollDown),"messageListElement"in r&&n(2,s=r.messageListElement)},[t,()=>{s&&Zt(s,{smooth:!0})},s]}class Mc extends Z{constructor(e){super(),j(this,e,Nr,Pr,W,{showScrollDown:0,messageListElement:2})}}function Bt(o){let e,n;return{c(){e=F("Retrying in "),n=F(o[3])},m(t,s){x(t,e,s),x(t,n,s)},p(t,s){8&s&&K(n,t[3])},d(t){t&&(h(e),h(n))}}}function Pt(o){let e,n,t,s,r,c=o[6]?" now":"";return{c(){e=M("button"),n=F("Retry"),t=F(c),v(e,"class","c-remote-agent-error__button c-remote-agent-error__button--retry")},m(i,a){x(i,e,a),R(e,n),R(e,t),s||(r=Q(e,"click",ye(function(){ge(o[0])&&o[0].apply(this,arguments)})),s=!0)},p(i,a){o=i,64&a&&c!==(c=o[6]?" now":"")&&K(t,c)},d(i){i&&h(e),s=!1,r()}}}function Nt(o){let e,n,t;return{c(){e=M("button"),e.textContent="Delete Agent",v(e,"class","c-remote-agent-error__button c-remote-agent-error__button--delete")},m(s,r){x(s,e,r),n||(t=Q(e,"click",ye(function(){ge(o[1])&&o[1].apply(this,arguments)})),n=!0)},p(s,r){o=s},d(s){s&&h(e),n=!1,t()}}}function Hr(o){let e,n,t,s,r,c,i,a,l=o[6]&&o[3]&&Bt(o),u=o[4]&&Pt(o),d=o[5]&&Nt(o);return{c(){e=M("div"),n=M("div"),t=M("span"),s=F(o[7]),r=B(),l&&l.c(),c=B(),i=M("div"),u&&u.c(),a=B(),d&&d.c(),v(t,"class","c-remote-agent-error__message"),v(i,"class","c-remote-agent-error__actions"),v(n,"class","c-remote-agent-error__content svelte-g0g7z3"),v(e,"class","c-remote-agent-error svelte-g0g7z3"),O(e,"c-remote-agent-error--unrecoverable",o[2])},m(m,g){x(m,e,g),R(e,n),R(n,t),R(t,s),R(t,r),l&&l.m(t,null),R(n,c),R(n,i),u&&u.m(i,null),R(i,a),d&&d.m(i,null)},p(m,[g]){128&g&&K(s,m[7]),m[6]&&m[3]?l?l.p(m,g):(l=Bt(m),l.c(),l.m(t,null)):l&&(l.d(1),l=null),m[4]?u?u.p(m,g):(u=Pt(m),u.c(),u.m(i,a)):u&&(u.d(1),u=null),m[5]?d?d.p(m,g):(d=Nt(m),d.c(),d.m(i,null)):d&&(d.d(1),d=null),4&g&&O(e,"c-remote-agent-error--unrecoverable",m[2])},i:V,o:V,d(m){m&&h(e),l&&l.d(),u&&u.d(),d&&d.d()}}}function Or(o,e,n){let t,s,r,c,i,a,l,u,d,{error:m}=e,{onRetry:g}=e,{onDelete:k}=e;function L(){d&&(d(),d=void 0),c&&(d=rs(c,f=>{n(3,u=f)}))}return Ce(L),Ut(()=>{d==null||d()}),o.$$set=f=>{"error"in f&&n(8,m=f.error),"onRetry"in f&&n(0,g=f.onRetry),"onDelete"in f&&n(1,k=f.onDelete)},o.$$.update=()=>{256&o.$$.dirty&&n(10,t="type"in m),1280&o.$$.dirty&&n(2,s=t&&m.type===Ht.agentFailed),256&o.$$.dirty&&n(7,r=m.errorMessage),1280&o.$$.dirty&&n(9,c=t?void 0:m.retryAt),512&o.$$.dirty&&n(6,i=c!==void 0),6&o.$$.dirty&&n(5,a=s&&k),1&o.$$.dirty&&n(4,l=g),512&o.$$.dirty&&c&&L()},[g,k,s,u,l,a,i,r,m,c,t]}class Lc extends Z{constructor(e){super(),j(this,e,Or,Hr,W,{error:8,onRetry:0,onDelete:1})}}export{Ec as A,yc as C,_c as E,vc as G,Mc as M,Lc as R,Sc as S,Es as U,xc as a,Cc as b,wc as c,kc as d,Js as e,bc as f,mc as g,Ic as h,pc as i,to as j,Tc as k,Rc as l,rt as m,fc as n,gc as o,hc as t};
