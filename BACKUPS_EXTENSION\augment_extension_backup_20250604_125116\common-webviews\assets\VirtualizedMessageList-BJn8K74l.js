var Kt=Object.defineProperty;var Ot=(i,t,n)=>t in i?Kt(i,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):i[t]=n;var B=(i,t,n)=>Ot(i,typeof t!="symbol"?t+"":t,n);import{ah as O,ai as V,aj as z,ak as Vt,S as Xt,i as Yt,s as Zt,Q as W,y as M,D as G,c as F,a2 as X,e as v,z as y,f as J,u as h,q as T,t as f,r as P,h as q,B as L,ae as wt,af as H,_ as te,a0 as ee,a5 as lt,ab as ct,a6 as ne,n as k,C as at,w as se,E as j}from"./SpinnerAugment-BJ4-L7QR.js";import{e as Y,u as Tt,o as Pt}from"./BaseButton-C6Dhmpxa.js";import{m as re,G as ie,g as oe,t as ae,a as le,M as ce,n as me,o as ue,A as de,b as ge,R as At,c as he,S as pe,d as fe,e as $e,f as _e,h as Se,C as Ie,i as xe,U as we,j as Et,E as Me,k as ye,l as Le}from"./RemoteAgentRetry-CoFGWWoM.js";import"./Content-Czt02SJi.js";import{g as mt,a as Ht,h as Gt,b as Ft,c as Nt,d as jt,e as Wt,f as Ut,j as Qt,k as ut,m as be,R as Be,S as dt,i as Mt,E as Ce}from"./open-in-new-window-DMlqLwqy.js";import"./folder-BJI1Q8_7.js";import{aq as ve,ar as qe}from"./AugmentMessage-DIzdCIMv.js";import{S as ze}from"./main-panel-CLAFkah5.js";import"./isObjectLike-DflaizF0.js";import"./types-LfaCSdmF.js";import"./MaterialIcon-DIlB9c-0.js";import"./keypress-DD1aQVr0.js";import"./autofix-state-d-ymFdyn.js";import"./Keybindings-4L2d2tRE.js";import"./pen-to-square-Bm4lF9Yl.js";import"./exclamation-triangle-Dn4fXX3v.js";import"./CardAugment-BxTO-shY.js";import"./TextTooltipAugment-Bkzart3o.js";import"./IconButtonAugment-Certjadv.js";import"./index-C-g0ZorP.js";import"./augment-logo-D_UKSkj8.js";import"./ButtonAugment-HnJOGilM.js";import"./expand--BB_Hn_b.js";import"./folder-opened-DzrGzNBt.js";import"./diff-utils-y96qaWKK.js";import"./toggleHighContrast-CwIv4U26.js";import"./preload-helper-Dv6uf1Os.js";import"./index-MyvMQzjq.js";import"./layer-group-CZFSGU8L.js";import"./github-C1PQK5DH.js";import"./types-a569v5Ol.js";import"./chat-types-NgqNgjwU.js";import"./globals-D0QH3NT1.js";import"./test_service_pb-B6vKXZrG.js";import"./file-paths-BcSg4gks.js";import"./types-BSMhNRWH.js";import"./TextAreaAugment-Cj5jK817.js";import"./await_block-CvQ_3xaW.js";import"./mcp-logo-B9nTLE-q.js";import"./ellipsis-BWy9xWah.js";import"./IconFilePath-C-3qORpY.js";import"./LanguageIcon-BH9BM7T7.js";import"./next-edit-types-904A5ehg.js";import"./magnifying-glass-LWYs47rB.js";import"./Filespan-BC4kxbfx.js";import"./chevron-down-B88L5wkj.js";import"./lodash-ChYFUhWY.js";import"./terminal-BQIj5vJ0.js";import"./design-system-init-DA68MSAy.js";import"./StatusIndicator-CAJYwjQb.js";import"./index-CGbmuyBX.js";import"./VSCodeCodicon-CvBJfpPi.js";import"./chat-flags-model-IiDhbRsI.js";class De{constructor(t=10){B(this,"samples",[]);B(this,"maxSamples");this.maxSamples=t}addSample(t,n=performance.now()){this.samples.push({position:t,timestamp:n}),this.samples.length>this.maxSamples&&this.samples.shift()}getVelocity(){if(this.samples.length<2)return 0;const t=this.samples.at(-1),n=this.samples.at(0),e=t.position-n.position,s=t.timestamp-n.timestamp;return s>0?e/s:0}getVelocityPPS(){return 1e3*this.getVelocity()}isFastScroll(t=500){return Math.abs(this.getVelocityPPS())>t}getDirection(){const t=this.getVelocity();return t>.05?"down":t<-.05?"up":"static"}predictPositionAfter(t){if(this.samples.length===0)return 0;const n=this.getVelocity();return this.samples[this.samples.length-1].position+n*t}reset(){this.samples=[]}getCurrentPosition(){return this.samples.length===0?0:this.samples[this.samples.length-1].position}}class Re{captureScrollPosition(t){const{scrollHeight:n,scrollTop:e,clientHeight:s}=t;return{scrollHeight:n,scrollTop:e,clientHeight:s,distanceFromBottom:re(t),timestamp:performance.now()}}restoreScrollPosition(t,n){const e=t.scrollHeight-n.distanceFromBottom-t.clientHeight;t.scrollTop=Math.max(0,e)}}class ke{constructor(t,n={initialVisibleCount:20,batchSize:10}){B(this,"_config");B(this,"_disposables",[]);B(this,"_currentBatchSize");B(this,"_startIndex");B(this,"_endIndex");B(this,"_isLoading");B(this,"_loadingDirection");B(this,"_allItems");B(this,"_groupedAllItems");B(this,"_groupedVisibleItems");B(this,"hasMoreBefore");B(this,"hasMoreAfter");B(this,"isLoading");B(this,"loadingDirection");B(this,"totalItemCount");this._conversationModel=t,this._config={...n,minBatchSize:n.minBatchSize??n.batchSize,maxBatchSize:n.maxBatchSize??3*n.batchSize},this._currentBatchSize=this._config.batchSize,this._startIndex=O(0),this._endIndex=O(void 0),this._isLoading=O(!1),this._loadingDirection=O(null),this._allItems=V(this._conversationModel,s=>s.chatHistory.filter(r=>mt(r)||Ht(r)||Gt(r)||Ft(r)||Nt(r)||jt(r)||Wt(r)||Ut(r)||Qt(r)||ut(r))),this._groupedAllItems=V(this._allItems,s=>{const r=s.map((o,c)=>({turn:o,idx:c}));return this._groupItems(r)}),this._groupedVisibleItems=V([this._groupedAllItems,this._startIndex,this._endIndex],([s,r,o])=>{if(s.length===0)return[];let c=0;for(let l=0;l<s.length;l++){const m=s[l];if(m.turns[m.turns.length-1].idx>=r){c=l;break}if(l===s.length-1&&m.turns[m.turns.length-1].idx>=r){c=l;break}}let a=s.length-1;if(o!==void 0)for(let l=s.length-1;l>=0;l--){const m=s[l];if(m.turns[0].idx<=o){a=l;break}if(l===0&&m.turns[0].idx<=o){a=l;break}}return s.slice(c,a+1).map((l,m,d)=>({...l,isLastGroup:m===d.length-1}))}),this.hasMoreBefore=V([this._startIndex],([s])=>s>0),this.hasMoreAfter=V([this._endIndex,this._allItems],([s,r])=>s!==void 0&&s<r.length-1),this.isLoading=V(this._isLoading,s=>s),this.loadingDirection=V(this._loadingDirection,s=>s),this.totalItemCount=V(this._allItems,s=>s.length);const e=z(this._conversationModel);typeof e.onNewConversation=="function"&&this._disposables.push(e.onNewConversation(()=>{this.resetToBottom()})),this.resetToBottom()}subscribe(t){return this._groupedVisibleItems.subscribe(t)}loadMoreBefore(t){const n=z(this._startIndex),e=z(this._isLoading);if(n<=0||e)return!1;this._isLoading.set(!0),this._loadingDirection.set("before");const s=this._getValidBatchSize(t),r=Math.max(0,n-s);return this._startIndex.set(r),this._isLoading.set(!1),this._loadingDirection.set(null),!0}async loadToStart(t={}){const n=z(this._startIndex),e=z(this._isLoading);if(n<=0||e)return!1;this._isLoading.set(!0),this._loadingDirection.set("before");const{smooth:s,smoothInterval:r=500}=t;if(s)for(;this.loadMoreBefore();)await new Promise(o=>setTimeout(o,r)),await Vt();else this._startIndex.set(0);return this._isLoading.set(!1),this._loadingDirection.set(null),!0}loadMoreAfter(t){const n=z(this._endIndex),e=z(this._isLoading),s=z(this._allItems);if(n===void 0||n>=s.length-1||e)return!1;this._isLoading.set(!0),this._loadingDirection.set("after");const r=this._getValidBatchSize(t),o=Math.min(s.length-1,n+r);return o>=s.length-1?this._endIndex.set(void 0):this._endIndex.set(o),this._isLoading.set(!1),this._loadingDirection.set(null),!0}resetToBottom(){const t=z(this._allItems);if(t.length===0)return;const n=Math.max(0,t.length-this._config.initialVisibleCount);this._startIndex.set(n),this._endIndex.set(void 0)}resetToTop(){const t=z(this._allItems);if(t.length===0)return;const n=Math.min(t.length-1,this._config.initialVisibleCount-1);this._startIndex.set(0),this._endIndex.set(n)}async jumpToMessage(t){const n=z(this._allItems),e=n.findIndex(c=>c.request_id===t);if(e===-1)return!1;const s=Math.floor(this._config.initialVisibleCount/2),r=Math.max(0,e-s),o=e+s>=n.length-5?void 0:Math.min(n.length-1,e+s);return this._startIndex.set(r),this._endIndex.set(o),!0}_groupItems(t){return t.reduce((n,{turn:e,idx:s})=>((e.isToolResult===!0||mt(e)&&be(e))&&n.length>0||ut(e)&&n.length>0?n[n.length-1].turns.push({turn:e,idx:s}):n.push({turns:[{turn:e,idx:s}],firstRequestId:e.request_id,lastRequestId:e.request_id,isLastGroup:!1}),n),[]).map((n,e,s)=>{const r=n.turns.findLast(({turn:c})=>!!c.request_id),o=r==null?void 0:r.turn.request_id;return{...n,lastRequestId:o,isLastGroup:e===s.length-1}})}setDynamicBatchSize(t){this._currentBatchSize=this._getValidBatchSize(t)}getCurrentBatchSize(){return this._currentBatchSize}_getValidBatchSize(t){if(t===void 0)return this._currentBatchSize;const n=this._config.minBatchSize??this._config.batchSize,e=this._config.maxBatchSize??3*this._config.batchSize;return Math.max(n,Math.min(e,t))}dispose(){this._disposables.forEach(t=>t())}}function yt(i,t,n){const e=i.slice();return e[46]=t[n],e[48]=n,e}function Lt(i,t,n){const e=i.slice();e[49]=t[n].turn,e[50]=t[n].idx;const s=e[50]+1===e[11].length;return e[51]=s,e}function bt(i){let t,n;return t=new de({}),{c(){M(t.$$.fragment)},m(e,s){y(t,e,s),n=!0},i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function Bt(i){let t;return{c(){t=W("div"),t.textContent="Loading earlier messages...",F(t,"class","c-msg-list__loading svelte-80qwt2")},m(n,e){v(n,t,e)},d(n){n&&q(t)}}}function Ve(i){let t,n,e,s;const r=[je,Ne],o=[];function c(a,l){return a[17].enableRichCheckpointInfo?0:1}return t=c(i),n=o[t]=r[t](i),{c(){n.c(),e=j()},m(a,l){o[t].m(a,l),v(a,e,l),s=!0},p(a,l){let m=t;t=c(a),t===m?o[t].p(a,l):(T(),f(o[m],1,1,()=>{o[m]=null}),P(),n=o[t],n?n.p(a,l):(n=o[t]=r[t](a),n.c()),h(n,1),n.m(e.parentNode,e))},i(a){s||(h(n),s=!0)},o(a){f(n),s=!1},d(a){a&&q(e),o[t].d(a)}}}function Te(i){let t,n;return t=new Ie({props:{group:i[46].turns,chatModel:i[1],turn:i[49],turnIndex:i[50],isLastTurn:i[51],messageListContainer:i[0]}}),{c(){M(t.$$.fragment)},m(e,s){y(t,e,s),n=!0},p(e,s){const r={};524288&s[0]&&(r.group=e[46].turns),2&s[0]&&(r.chatModel=e[1]),524288&s[0]&&(r.turn=e[49]),524288&s[0]&&(r.turnIndex=e[50]),526336&s[0]&&(r.isLastTurn=e[51]),1&s[0]&&(r.messageListContainer=e[0]),t.$set(r)},i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function Pe(i){let t,n;return t=new xe({props:{stage:i[49].stage,iterationId:i[49].iterationId,stageCount:i[49].stageCount}}),{c(){M(t.$$.fragment)},m(e,s){y(t,e,s),n=!0},p(e,s){const r={};524288&s[0]&&(r.stage=e[49].stage),524288&s[0]&&(r.iterationId=e[49].iterationId),524288&s[0]&&(r.stageCount=e[49].stageCount),t.$set(r)},i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function Ae(i){let t,n;return t=new we({props:{chatModel:i[1],msg:i[49].response_text??""}}),{c(){M(t.$$.fragment)},m(e,s){y(t,e,s),n=!0},p(e,s){const r={};2&s[0]&&(r.chatModel=e[1]),524288&s[0]&&(r.msg=e[49].response_text??""),t.$set(r)},i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function Ee(i){let t,n;return t=new ve({props:{group:i[46].turns,markdown:i[49].response_text??"",messageListContainer:i[0]}}),{c(){M(t.$$.fragment)},m(e,s){y(t,e,s),n=!0},p(e,s){const r={};524288&s[0]&&(r.group=e[46].turns),524288&s[0]&&(r.markdown=e[49].response_text??""),1&s[0]&&(r.messageListContainer=e[0]),t.$set(r)},i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function He(i){let t,n;function e(){return i[36](i[49])}return t=new Et({props:{turn:i[49],preamble:ze,resendTurn:e,$$slots:{default:[We]},$$scope:{ctx:i}}}),{c(){M(t.$$.fragment)},m(s,r){y(t,s,r),n=!0},p(s,r){i=s;const o={};524288&r[0]&&(o.turn=i[49]),524292&r[0]&&(o.resendTurn=e),540672&r[0]|8388608&r[1]&&(o.$$scope={dirty:r,ctx:i}),t.$set(o)},i(s){n||(h(t.$$.fragment,s),n=!0)},o(s){f(t.$$.fragment,s),n=!1},d(s){L(t,s)}}}function Ge(i){let t,n;return t=new Me({props:{flagsModel:i[12],turn:i[49]}}),{c(){M(t.$$.fragment)},m(e,s){y(t,e,s),n=!0},p(e,s){const r={};4096&s[0]&&(r.flagsModel=e[12]),524288&s[0]&&(r.turn=e[49]),t.$set(r)},i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function Fe(i){let t,n;return t=new Et({props:{turn:i[49]}}),{c(){M(t.$$.fragment)},m(e,s){y(t,e,s),n=!0},p(e,s){const r={};524288&s[0]&&(r.turn=e[49]),t.$set(r)},i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function Ne(i){let t,n;return t=new ye({props:{turn:i[49]}}),{c(){M(t.$$.fragment)},m(e,s){y(t,e,s),n=!0},p(e,s){const r={};524288&s[0]&&(r.turn=e[49]),t.$set(r)},i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function je(i){let t,n;return t=new Le({props:{turn:i[49]}}),{c(){M(t.$$.fragment)},m(e,s){y(t,e,s),n=!0},p(e,s){const r={};524288&s[0]&&(r.turn=e[49]),t.$set(r)},i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function We(i){let t,n;return t=new qe({props:{conversationModel:i[14],turn:i[49]}}),{c(){M(t.$$.fragment)},m(e,s){y(t,e,s),n=!0},p(e,s){const r={};16384&s[0]&&(r.conversationModel=e[14]),524288&s[0]&&(r.turn=e[49]),t.$set(r)},i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function Ct(i){let t,n,e,s;function r(){return i[37](i[49])}return{c(){t=W("div"),F(t,"class","c-msg-list__turn-seen")},m(o,c){v(o,t,c),e||(s=lt(n=he.call(null,t,{onSeen:r,track:i[49].seen_state!==dt.seen})),e=!0)},p(o,c){i=o,n&&ct(n.update)&&524288&c[0]&&n.update.call(null,{onSeen:r,track:i[49].seen_state!==dt.seen})},d(o){o&&q(t),e=!1,s()}}}function vt(i,t){let n,e,s,r,o,c,a,l,m,d,w,S,x,C,D=Mt(t[49]);const b=[Fe,Ge,He,Ee,Ae,Pe,Te,Ve],_=[];function p($,I){return 524288&I[0]&&(e=null),524288&I[0]&&(s=null),524288&I[0]&&(r=null),524288&I[0]&&(o=null),524288&I[0]&&(c=null),524288&I[0]&&(a=null),524288&I[0]&&(l=null),524288&I[0]&&(m=null),e==null&&(e=!!Ht($[49])),e?0:(s==null&&(s=!!Ft($[49])),s?1:(r==null&&(r=!!Nt($[49])),r?2:(o==null&&(o=!!jt($[49])),o?3:(c==null&&(c=!!Wt($[49])),c?4:(a==null&&(a=!!Ut($[49])),a?5:(l==null&&(l=!!(mt($[49])||Gt($[49])||Qt($[49]))),l?6:(m==null&&(m=!(!ut($[49])||$[49].status!==Ce.success)),m?7:-1)))))))}~(d=p(t,[-1,-1]))&&(w=_[d]=b[d](t));let g=D&&Ct(t);return{key:i,first:null,c(){n=j(),w&&w.c(),S=G(),g&&g.c(),x=j(),this.first=n},m($,I){v($,n,I),~d&&_[d].m($,I),v($,S,I),g&&g.m($,I),v($,x,I),C=!0},p($,I){let A=d;d=p(t=$,I),d===A?~d&&_[d].p(t,I):(w&&(T(),f(_[A],1,1,()=>{_[A]=null}),P()),~d?(w=_[d],w?w.p(t,I):(w=_[d]=b[d](t),w.c()),h(w,1),w.m(S.parentNode,S)):w=null),524288&I[0]&&(D=Mt(t[49])),D?g?g.p(t,I):(g=Ct(t),g.c(),g.m(x.parentNode,x)):g&&(g.d(1),g=null)},i($){C||(h(w),C=!0)},o($){f(w),C=!1},d($){$&&(q(n),q(S),q(x)),~d&&_[d].d($),g&&g.d($)}}}function qt(i){let t,n,e,s;const r=[Ye,Xe,Oe,Ke,Je,Qe,Ue],o=[];function c(a,l){return a[7]?0:a[4].retryMessage?1:a[4].showResumingRemoteAgent?2:a[4].showGeneratingResponse?3:a[4].showAwaitingUserInput?4:a[4].showRunningSpacer?5:a[4].showStopped?6:-1}return~(t=c(i))&&(n=o[t]=r[t](i)),{c(){n&&n.c(),e=j()},m(a,l){~t&&o[t].m(a,l),v(a,e,l),s=!0},p(a,l){let m=t;t=c(a),t===m?~t&&o[t].p(a,l):(n&&(T(),f(o[m],1,1,()=>{o[m]=null}),P()),~t?(n=o[t],n?n.p(a,l):(n=o[t]=r[t](a),n.c()),h(n,1),n.m(e.parentNode,e)):n=null)},i(a){s||(h(n),s=!0)},o(a){f(n),s=!1},d(a){a&&q(e),~t&&o[t].d(a)}}}function Ue(i){let t,n;return t=new pe({}),{c(){M(t.$$.fragment)},m(e,s){y(t,e,s),n=!0},p:k,i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function Qe(i){let t;return{c(){t=W("div"),F(t,"class","c-agent-running-spacer svelte-80qwt2")},m(n,e){v(n,t,e)},p:k,i:k,o:k,d(n){n&&q(t)}}}function Je(i){let t,n;return t=new fe({}),{c(){M(t.$$.fragment)},m(e,s){y(t,e,s),n=!0},p:k,i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function Ke(i){let t,n;return t=new $e({}),{c(){M(t.$$.fragment)},m(e,s){y(t,e,s),n=!0},p:k,i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function Oe(i){let t,n;return t=new _e({}),{c(){M(t.$$.fragment)},m(e,s){y(t,e,s),n=!0},p:k,i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function Xe(i){let t,n;return t=new Se({props:{message:i[4].retryMessage}}),{c(){M(t.$$.fragment)},m(e,s){y(t,e,s),n=!0},p(e,s){const r={};16&s[0]&&(r.message=e[4].retryMessage),t.$set(r)},i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function Ye(i){let t,n;return t=new At({props:{error:i[7].error,onRetry:i[7].onRetry,onDelete:i[7].onDelete}}),{c(){M(t.$$.fragment)},m(e,s){y(t,e,s),n=!0},p(e,s){const r={};128&s[0]&&(r.error=e[7].error),128&s[0]&&(r.onRetry=e[7].onRetry),128&s[0]&&(r.onDelete=e[7].onDelete),t.$set(r)},i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function Ze(i){let t,n,e,s=[],r=new Map,o=Y(i[46].turns);const c=l=>l[49].request_id??`no-request-id-${l[50]}`;for(let l=0;l<o.length;l+=1){let m=Lt(i,o,l),d=c(m);r.set(d,s[l]=vt(d,m))}let a=i[46].isLastGroup&&qt(i);return{c(){for(let l=0;l<s.length;l+=1)s[l].c();t=G(),a&&a.c(),n=j()},m(l,m){for(let d=0;d<s.length;d+=1)s[d]&&s[d].m(l,m);v(l,t,m),a&&a.m(l,m),v(l,n,m),e=!0},p(l,m){1074419719&m[0]&&(o=Y(l[46].turns),T(),s=Tt(s,m,c,1,l,o,r,t.parentNode,Pt,vt,t,Lt),P()),l[46].isLastGroup?a?(a.p(l,m),524288&m[0]&&h(a,1)):(a=qt(l),a.c(),h(a,1),a.m(n.parentNode,n)):a&&(T(),f(a,1,1,()=>{a=null}),P())},i(l){if(!e){for(let m=0;m<o.length;m+=1)h(s[m]);h(a),e=!0}},o(l){for(let m=0;m<s.length;m+=1)f(s[m]);f(a),e=!1},d(l){l&&(q(t),q(n));for(let m=0;m<s.length;m+=1)s[m].d(l);a&&a.d(l)}}}function zt(i,t){let n,e,s;return e=new ge({props:{class:"c-msg-list__item--grouped",chatModel:t[1],isLastItem:t[46].isLastGroup,userControlsScroll:t[5],requestId:t[46].firstRequestId,releaseScroll:t[38],messageListContainer:t[0],minHeight:t[46].isLastGroup?t[10]:0,dataRequestId:t[46].firstRequestId,$$slots:{default:[Ze]},$$scope:{ctx:t}}}),{key:i,first:null,c(){n=j(),M(e.$$.fragment),this.first=n},m(r,o){v(r,n,o),y(e,r,o),s=!0},p(r,o){t=r;const c={};2&o[0]&&(c.chatModel=t[1]),524288&o[0]&&(c.isLastItem=t[46].isLastGroup),32&o[0]&&(c.userControlsScroll=t[5]),524288&o[0]&&(c.requestId=t[46].firstRequestId),32&o[0]&&(c.releaseScroll=t[38]),1&o[0]&&(c.messageListContainer=t[0]),525312&o[0]&&(c.minHeight=t[46].isLastGroup?t[10]:0),524288&o[0]&&(c.dataRequestId=t[46].firstRequestId),678039&o[0]|8388608&o[1]&&(c.$$scope={dirty:o,ctx:t}),e.$set(c)},i(r){s||(h(e.$$.fragment,r),s=!0)},o(r){f(e.$$.fragment,r),s=!1},d(r){r&&q(n),L(e,r)}}}function Dt(i){let t;return{c(){t=W("div"),t.textContent="Loading more messages...",F(t,"class","c-msg-list__loading svelte-80qwt2")},m(n,e){v(n,t,e)},d(n){n&&q(t)}}}function Rt(i){let t,n;return t=new At({props:{error:i[7].error,onRetry:i[7].onRetry,onDelete:i[7].onDelete}}),{c(){M(t.$$.fragment)},m(e,s){y(t,e,s),n=!0},p(e,s){const r={};128&s[0]&&(r.error=e[7].error),128&s[0]&&(r.onRetry=e[7].onRetry),128&s[0]&&(r.onDelete=e[7].onDelete),t.$set(r)},i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function tn(i){let t,n,e,s,r,o,c,a,l,m,d=[],w=new Map,S=i[8]&&bt(),x=i[16]&&i[18]==="before"&&Bt(),C=Y(i[19]);const D=p=>p[46].firstRequestId??`no-request-id-${p[48]}`;for(let p=0;p<C.length;p+=1){let g=yt(i,C,p),$=D(g);w.set($,d[p]=zt($,g))}let b=i[16]&&i[18]==="after"&&Dt(),_=!i[11].length&&i[7]&&Rt(i);return{c(){t=W("div"),S&&S.c(),n=G(),x&&x.c(),e=G();for(let p=0;p<d.length;p+=1)d[p].c();s=G(),b&&b.c(),r=G(),_&&_.c(),F(t,"class","c-msg-list svelte-80qwt2"),X(t,"c-msg-list--minimal",!i[17].fullFeatured)},m(p,g){v(p,t,g),S&&S.m(t,null),J(t,n),x&&x.m(t,null),J(t,e);for(let $=0;$<d.length;$+=1)d[$]&&d[$].m(t,null);J(t,s),b&&b.m(t,null),J(t,r),_&&_.m(t,null),i[39](t),a=!0,l||(m=[lt(o=ae.call(null,t,{onScrollIntoBottom:i[40],onScrollAwayFromBottom:i[41],onScroll:i[42]})),lt(c=le.call(null,t,{onHeightChange:i[43]}))],l=!0)},p(p,g){p[8]?S?256&g[0]&&h(S,1):(S=bt(),S.c(),h(S,1),S.m(t,n)):S&&(T(),f(S,1,1,()=>{S=null}),P()),p[16]&&p[18]==="before"?x||(x=Bt(),x.c(),x.m(t,e)):x&&(x.d(1),x=null),1074420919&g[0]&&(C=Y(p[19]),T(),d=Tt(d,g,D,1,p,C,w,t,Pt,zt,s,yt),P()),p[16]&&p[18]==="after"?b||(b=Dt(),b.c(),b.m(t,r)):b&&(b.d(1),b=null),!p[11].length&&p[7]?_?(_.p(p,g),2176&g[0]&&h(_,1)):(_=Rt(p),_.c(),h(_,1),_.m(t,null)):_&&(T(),f(_,1,1,()=>{_=null}),P()),o&&ct(o.update)&&32865&g[0]&&o.update.call(null,{onScrollIntoBottom:p[40],onScrollAwayFromBottom:p[41],onScroll:p[42]}),c&&ct(c.update)&&8&g[0]&&c.update.call(null,{onHeightChange:p[43]}),(!a||131072&g[0])&&X(t,"c-msg-list--minimal",!p[17].fullFeatured)},i(p){if(!a){h(S);for(let g=0;g<C.length;g+=1)h(d[g]);h(_),a=!0}},o(p){f(S);for(let g=0;g<d.length;g+=1)f(d[g]);f(_),a=!1},d(p){p&&q(t),S&&S.d(),x&&x.d();for(let g=0;g<d.length;g+=1)d[g].d();b&&b.d(),_&&_.d(),i[39](null),l=!1,ne(m)}}}function kt(i){let t,n;return t=new ce({props:{messageListElement:i[0],showScrollDown:!i[6]}}),{c(){M(t.$$.fragment)},m(e,s){y(t,e,s),n=!0},p(e,s){const r={};1&s[0]&&(r.messageListElement=e[0]),64&s[0]&&(r.showScrollDown=!e[6]),t.$set(r)},i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function en(i){let t,n,e,s;n=new ie({props:{$$slots:{default:[tn]},$$scope:{ctx:i}}});let r=i[9]&&kt(i);return{c(){t=W("div"),M(n.$$.fragment),e=G(),r&&r.c(),F(t,"class","c-msg-list-container svelte-80qwt2"),F(t,"data-testid","chat-message-list"),X(t,"c-msg-list--minimal",!i[17].fullFeatured)},m(o,c){v(o,t,c),y(n,t,null),J(t,e),r&&r.m(t,null),s=!0},p(o,c){const a={};1039871&c[0]|8388608&c[1]&&(a.$$scope={dirty:c,ctx:o}),n.$set(a),o[9]?r?(r.p(o,c),512&c[0]&&h(r,1)):(r=kt(o),r.c(),h(r,1),r.m(t,null)):r&&(T(),f(r,1,1,()=>{r=null}),P()),(!s||131072&c[0])&&X(t,"c-msg-list--minimal",!o[17].fullFeatured)},i(o){s||(h(n.$$.fragment,o),h(r),s=!0)},o(o){f(n.$$.fragment,o),f(r),s=!1},d(o){o&&q(t),L(n),r&&r.d()}}}function nn(i,t,n){let e,s,r,o,c,a,l,m,d,w,S,x,C,D,b,_,p,g,$,I=k,A=k,gt=()=>(A(),A=at(U,u=>n(35,_=u)),U),Z=k;i.$$.on_destroy.push(()=>I()),i.$$.on_destroy.push(()=>A()),i.$$.on_destroy.push(()=>Z());let{chatModel:U}=t;gt();let{onboardingWorkspaceModel:tt}=t,{msgListElement:R}=t;const Jt=wt("agentConversationModel"),{agentExchangeStatus:ht,isCurrConversationAgentic:pt}=Jt;H(i,ht,u=>n(34,b=u)),H(i,pt,u=>n(33,D=u));const ft=wt(Be.key);H(i,ft,u=>n(32,C=u));const E=new ke(V(U,u=>u.currentConversationModel),{initialVisibleCount:20,batchSize:10,minBatchSize:5,maxBatchSize:20});H(i,E,u=>n(19,$=u));const{hasMoreBefore:$t,isLoading:_t,loadingDirection:St}=E;H(i,$t,u=>n(15,S=u)),H(i,_t,u=>n(16,x=u)),H(i,St,u=>n(18,g=u));const Q=new De(3),It=new Re;te(()=>{E.dispose()});let K=!1;function N(){n(5,K=!0)}async function et(){if(!R||!S||x)return;N();const u=It.captureScrollPosition(R),rt=Q.getVelocityPPS(),it=me(rt,E.getCurrentBatchSize()),ot=E.loadMoreBefore(it);u&&(N(),It.restoreScrollPosition(R,u),await Vt()),ot&&R&&R.scrollTop<=1&&S&&et()}ee(()=>{var u;((u=w.lastExchange)==null?void 0:u.seen_state)===dt.unseen&&N()});let nt=0,st=!0;const xt=u=>w.markSeen(u);return i.$$set=u=>{"chatModel"in u&&gt(n(1,U=u.chatModel)),"onboardingWorkspaceModel"in u&&n(2,tt=u.onboardingWorkspaceModel),"msgListElement"in u&&n(0,R=u.msgListElement)},i.$$.update=()=>{16&i.$$.dirty[1]&&(n(13,e=_.currentConversationModel),I(),I=at(e,u=>n(14,w=u))),16&i.$$.dirty[1]&&(n(12,s=_.flags),Z(),Z=at(s,u=>n(17,p=u))),30&i.$$.dirty[1]&&n(31,r=oe(_,b,D,C)),1&i.$$.dirty[1]&&n(11,o=r.chatHistory),8&i.$$.dirty[0]&&n(10,c=nt),1&i.$$.dirty[1]&&n(4,a=r.lastGroupConfig),1&i.$$.dirty[1]&&n(9,l=r.doShowFloatingButtons),1&i.$$.dirty[1]&&n(8,m=r.doShowAgentSetupLogs),16&i.$$.dirty[0]&&n(7,d=a.remoteAgentErrorConfig)},[R,U,tt,nt,a,K,st,d,m,l,c,o,s,e,w,S,x,p,g,$,ht,pt,ft,E,$t,_t,St,Q,N,et,xt,r,C,D,b,_,u=>tt.retryProjectSummary(u),u=>xt(u),()=>n(5,K=!0),function(u){se[u?"unshift":"push"](()=>{R=u,n(0,R)})},()=>{n(5,K=!1),n(6,st=!0),E.resetToBottom()},()=>{N(),n(6,st=!1)},u=>{if(u<=1&&N(),R){Q.addSample(u);const rt=Q.getVelocityPPS(),it=Q.getDirection(),ot=ue(rt,{baseThreshold:200,predictTime:300});it==="up"&&u<ot&&S&&et()}},u=>n(3,nt=u)]}class rs extends Xt{constructor(t){super(),Yt(this,t,nn,en,Zt,{chatModel:1,onboardingWorkspaceModel:2,msgListElement:0},null,[-1,-1])}}export{rs as default};
