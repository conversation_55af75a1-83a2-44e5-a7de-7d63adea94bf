#!/usr/bin/env python3
"""
INJECTION FINALE APRÈS DÉBLOCAGE DES PERMISSIONS
================================================

Maintenant que les permissions sont débloquées, effectuons l'injection.
"""

import shutil
from pathlib import Path
import datetime

def injection_finale():
    """Effectue l'injection finale maintenant que les permissions sont OK"""
    
    extension_js = Path("C:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.470.1/out/extension.js")
    injection_code_file = Path("C:/Users/<USER>/Desktop/Travail/Projet7/injection_code_manual.js")
    backup_dir = Path("C:/Users/<USER>/Desktop/Travail/Projet7/BACKUPS_EXTENSION")
    
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    
    print(f"🔧 INJECTION FINALE EXTENSION AUGMENT")
    print(f"=" * 50)
    
    try:
        # Vérifier que les fichiers existent
        if not extension_js.exists():
            print(f"❌ Extension non trouvée: {extension_js}")
            return False
        
        if not injection_code_file.exists():
            print(f"❌ Code d'injection non trouvé: {injection_code_file}")
            return False
        
        # Créer une nouvelle sauvegarde
        backup_dir.mkdir(parents=True, exist_ok=True)
        backup_file = backup_dir / f"extension_js_final_backup_{timestamp}.js"
        shutil.copy2(extension_js, backup_file)
        print(f"✅ Nouvelle sauvegarde: {backup_file}")
        
        # Lire le fichier original
        print(f"📖 Lecture du fichier original...")
        with open(extension_js, 'r', encoding='utf-8') as f:
            original_content = f.read()
        
        print(f"📊 Contenu original: {len(original_content)} caractères")
        
        # Lire le code d'injection
        print(f"📖 Lecture du code d'injection...")
        with open(injection_code_file, 'r', encoding='utf-8') as f:
            injection_code = f.read()
        
        print(f"📊 Code d'injection: {len(injection_code)} caractères")
        
        # Créer le contenu modifié
        print(f"🔧 Création du contenu modifié...")
        modified_content = injection_code + "\n" + original_content
        
        print(f"📊 Contenu modifié: {len(modified_content)} caractères")
        print(f"📊 Différence: +{len(modified_content) - len(original_content)} caractères")
        
        # Écrire le fichier modifié
        print(f"💾 Écriture du fichier modifié...")
        with open(extension_js, 'w', encoding='utf-8') as f:
            f.write(modified_content)
        
        print(f"✅ Fichier écrit avec succès!")
        
        # Vérification immédiate
        print(f"🔍 Vérification immédiate...")
        with open(extension_js, 'r', encoding='utf-8') as f:
            verification_content = f.read()
        
        print(f"📊 Taille après écriture: {len(verification_content)} caractères")
        
        # Chercher nos marqueurs
        markers = [
            "AUGMENT AUTO LOGGING SYSTEM",
            "AugmentAutoLogger",
            "augment_conversation_auto.txt"
        ]
        
        found_markers = 0
        for marker in markers:
            if marker in verification_content:
                print(f"✅ Marqueur trouvé: {marker}")
                found_markers += 1
            else:
                print(f"❌ Marqueur manquant: {marker}")
        
        if found_markers == len(markers):
            print(f"\n🎉 INJECTION FINALE RÉUSSIE!")
            print(f"✅ Tous les marqueurs sont présents")
            print(f"✅ Extension modifiée avec succès")
            
            # Afficher le début du fichier
            print(f"\n📝 Début du fichier modifié:")
            print(f"{'='*60}")
            print(verification_content[:400])
            print(f"{'='*60}")
            
            return True
        else:
            print(f"\n❌ INJECTION INCOMPLÈTE")
            print(f"⚠️ Seulement {found_markers}/{len(markers)} marqueurs trouvés")
            return False
    
    except PermissionError as e:
        print(f"❌ Erreur de permission: {e}")
        return False
    
    except Exception as e:
        print(f"❌ Erreur inattendue: {e}")
        return False

def main():
    """Fonction principale"""
    
    if injection_finale():
        print(f"\n🎉 MISSION ACCOMPLIE!")
        print(f"✅ L'extension Augment contient maintenant le code de logging automatique")
        print(f"✅ Tous les nouveaux projets auront automatiquement le logging")
        print(f"✅ Les fichiers 'augment_conversation_auto.txt' seront créés automatiquement")
        
        print(f"\n📋 PROCHAINES ÉTAPES:")
        print(f"1. 🔄 Redémarrez VSCode complètement")
        print(f"2. 📁 Ouvrez un nouveau projet")
        print(f"3. 💬 Démarrez une conversation Augment")
        print(f"4. 🔍 Vérifiez qu'un fichier 'augment_conversation_auto.txt' est créé")
        
        print(f"\n🧪 TEST IMMÉDIAT:")
        print(f"Fermez ce projet, ouvrez un nouveau projet, et votre conversation")
        print(f"sera automatiquement sauvegardée dans un fichier texte!")
        
    else:
        print(f"\n❌ INJECTION ÉCHOUÉE")
        print(f"Des protections supplémentaires empêchent la modification")

if __name__ == "__main__":
    main()
