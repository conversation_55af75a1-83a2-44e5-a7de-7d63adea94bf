#!/usr/bin/env python3
"""
INTERCEPTEUR AVANCÉ TEMPS RÉEL - RÉDUCTION DÉCALAGE
===================================================

Solution multi-niveaux pour intercepter les données AVANT qu'elles
atteignent state.vscdb, réduisant drastiquement le décalage.

TECHNIQUES D'INTERCEPTION:
1. Hook mémoire processus Augment
2. Surveillance fichiers temporaires
3. Interception réseau/IPC
4. Surveillance base SQLite en temps réel
5. Détection changements filesystem ultra-rapide
"""

import os
import sys
import time
import json
import sqlite3
import threading
import datetime
import subprocess
import psutil
import mmap
from pathlib import Path
import win32api
import win32con
import win32file
import win32event

class AdvancedRealTimeInterceptor:
    def __init__(self):
        self.project_dir = Path(__file__).parent.absolute()
        self.output_file = self.project_dir / "augment_realtime_advanced.txt"

        # Fichier alternatif pour éviter les verrous
        timestamp_suffix = datetime.datetime.now().strftime("%H%M%S")
        self.output_file_alt = self.project_dir / f"augment_live_{timestamp_suffix}.txt"
        
        # État de surveillance
        self.running = False
        self.last_message_count = 0
        self.intercepted_data = []
        
        # Processus et fichiers surveillés
        self.augment_processes = []
        self.vscode_processes = []
        self.temp_files = []
        self.state_files = []
        
        print(f"🚀 INTERCEPTEUR AVANCÉ TEMPS RÉEL")
        print(f"=" * 60)
        print(f"📁 Projet: {self.project_dir}")
        print(f"📝 Sortie principale: {self.output_file}")
        print(f"📝 Sortie alternative: {self.output_file_alt}")
        
        self.initialize_output_file()
        self.identify_target_processes()
        self.setup_advanced_monitoring()
    
    def identify_target_processes(self):
        """Identifie tous les processus cibles pour l'interception"""
        print(f"\n🔍 IDENTIFICATION DES PROCESSUS CIBLES")
        
        for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'memory_info']):
            try:
                name = proc.info['name'].lower()
                cmdline = ' '.join(proc.info['cmdline'] or []).lower()
                
                # Processus Augment
                if 'augment' in name or 'augment' in cmdline:
                    self.augment_processes.append(proc)
                    print(f"🎯 Augment: PID {proc.info['pid']} - {proc.info['memory_info'].rss / 1024 / 1024:.1f} MB")
                
                # Processus VSCode
                elif 'code' in name or 'electron' in name:
                    if 'vscode' in cmdline or 'code.exe' in cmdline:
                        self.vscode_processes.append(proc)
                        print(f"💻 VSCode: PID {proc.info['pid']} - {proc.info['memory_info'].rss / 1024 / 1024:.1f} MB")
                        
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        print(f"📊 Processus identifiés: {len(self.augment_processes)} Augment, {len(self.vscode_processes)} VSCode")

    def find_all_augment_workspaces(self):
        """Trouve tous les workspaces contenant des conversations Augment"""
        augment_workspaces = []

        workspace_base = Path("C:/Users/<USER>/AppData/Roaming/Code/User/workspaceStorage")

        if not workspace_base.exists():
            print(f"❌ Répertoire workspaces non trouvé: {workspace_base}")
            return augment_workspaces

        for workspace_dir in workspace_base.iterdir():
            if workspace_dir.is_dir():
                state_file = workspace_dir / "state.vscdb"
                if state_file.exists():
                    # Vérifier si contient des conversations Augment
                    if self.has_augment_conversations(state_file):
                        mtime = state_file.stat().st_mtime
                        augment_workspaces.append({
                            'id': workspace_dir.name,
                            'path': workspace_dir,
                            'state_file': state_file,
                            'mtime': mtime,
                            'last_modified': datetime.datetime.fromtimestamp(mtime)
                        })

        # Trier par date de modification (plus récent en premier)
        augment_workspaces.sort(key=lambda x: x['mtime'], reverse=True)

        return augment_workspaces

    def has_augment_conversations(self, state_file):
        """Vérifie si un workspace contient des conversations Augment"""
        try:
            conn = sqlite3.connect(str(state_file))
            cursor = conn.cursor()
            cursor.execute("SELECT key FROM ItemTable WHERE key = 'memento/webviewView.augment-chat'")
            result = cursor.fetchone()
            conn.close()
            return result is not None
        except Exception:
            return False
    
    def setup_advanced_monitoring(self):
        """Configure la surveillance avancée multi-niveaux"""
        print(f"\n⚙️ CONFIGURATION SURVEILLANCE AVANCÉE")
        
        # 1. Surveillance mémoire processus
        self.setup_memory_monitoring()
        
        # 2. Surveillance fichiers temporaires
        self.setup_temp_file_monitoring()
        
        # 3. Surveillance base SQLite ultra-rapide
        self.setup_sqlite_monitoring()
        
        # 4. Surveillance filesystem Windows
        self.setup_filesystem_monitoring()
        
        # 5. Interception réseau/IPC
        self.setup_network_monitoring()
    
    def setup_memory_monitoring(self):
        """Configure la surveillance mémoire des processus"""
        print(f"🧠 Configuration surveillance mémoire...")
        
        def memory_scanner():
            while self.running:
                for proc in self.augment_processes:
                    try:
                        # Analyser la mémoire du processus pour détecter les données JSON
                        memory_info = proc.memory_info()
                        
                        # Rechercher des patterns JSON dans la mémoire
                        # (Implémentation simplifiée - nécessiterait des outils plus avancés)
                        
                        # Surveillance des changements de mémoire avec ÉCRITURE IMMÉDIATE
                        if hasattr(proc, '_last_memory'):
                            if memory_info.rss != proc._last_memory:
                                memory_mb = memory_info.rss / 1024 / 1024
                                print(f"🔄 Changement mémoire Augment PID {proc.pid}: {memory_mb:.1f} MB")

                                # ÉCRITURE IMMÉDIATE du changement mémoire
                                timestamp = datetime.datetime.now().isoformat()
                                with open(self.output_file, 'a', encoding='utf-8') as f:
                                    f.write(f"[{timestamp}] 🧠 MÉMOIRE IMMÉDIATE: PID {proc.pid} = {memory_mb:.1f} MB\n")
                                    f.flush()
                                    os.fsync(f.fileno())

                        proc._last_memory = memory_info.rss
                        
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue
                
                time.sleep(0.05)  # 50ms
        
        self.memory_thread = threading.Thread(target=memory_scanner, daemon=True)
    
    def setup_temp_file_monitoring(self):
        """Configure la surveillance des fichiers temporaires"""
        print(f"📁 Configuration surveillance fichiers temporaires...")
        
        # Répertoires temporaires à surveiller
        temp_dirs = [
            Path(os.environ.get('TEMP', '')),
            Path(os.environ.get('TMP', '')),
            Path("C:/Users/<USER>/AppData/Local/Temp"),
            Path("C:/Users/<USER>/AppData/Roaming/Code/User"),
            Path("C:/Users/<USER>/AppData/Roaming/Code/CachedExtensions")
        ]
        
        def temp_file_scanner():
            last_scan = {}
            
            while self.running:
                for temp_dir in temp_dirs:
                    if temp_dir.exists():
                        try:
                            # Rechercher des fichiers récents contenant "augment" ou "conversation"
                            for file_path in temp_dir.rglob("*"):
                                if file_path.is_file():
                                    name_lower = file_path.name.lower()
                                    if any(keyword in name_lower for keyword in ['augment', 'conversation', 'chat', 'message']):
                                        
                                        mtime = file_path.stat().st_mtime
                                        if file_path not in last_scan or mtime > last_scan[file_path]:
                                            print(f"📄 Fichier temp modifié: {file_path.name}")
                                            self.analyze_temp_file(file_path)
                                            last_scan[file_path] = mtime
                                            
                        except (PermissionError, OSError):
                            continue
                
                time.sleep(0.1)  # 100ms
        
        self.temp_thread = threading.Thread(target=temp_file_scanner, daemon=True)
    
    def setup_sqlite_monitoring(self):
        """Configure la surveillance SQLite ultra-rapide - WORKSPACE LE PLUS RÉCENT UNIQUEMENT"""
        print(f"🗄️ Configuration surveillance SQLite ultra-rapide...")

        # Trouver UNIQUEMENT le workspace le plus récent avec conversations Augment
        workspaces = self.find_all_augment_workspaces()

        if workspaces:
            # Prendre SEULEMENT le plus récent
            most_recent = workspaces[0]
            self.state_files = [most_recent['state_file']]
            print(f"🎯 Surveillance EXCLUSIVE du workspace le plus récent: {most_recent['id'][:16]}...")
        else:
            self.state_files = []
            print(f"❌ Aucun workspace Augment trouvé")
        
        def sqlite_ultra_monitor():
            last_checks = {}
            
            while self.running:
                for state_file in self.state_files:
                    try:
                        # Vérification ultra-rapide des changements
                        stat = state_file.stat()
                        current_mtime = stat.st_mtime
                        current_size = stat.st_size
                        
                        file_key = str(state_file)
                        if file_key not in last_checks:
                            last_checks[file_key] = {'mtime': 0, 'size': 0}
                        
                        if (current_mtime > last_checks[file_key]['mtime'] or 
                            current_size != last_checks[file_key]['size']):
                            
                            print(f"⚡ SQLite modifié: {state_file.parent.name[:8]}... ({current_size} bytes)")
                            self.process_sqlite_change(state_file)
                            
                            last_checks[file_key] = {'mtime': current_mtime, 'size': current_size}
                    
                    except (OSError, PermissionError):
                        continue
                
                time.sleep(0.01)  # 10ms - ultra-rapide
        
        self.sqlite_thread = threading.Thread(target=sqlite_ultra_monitor, daemon=True)
    
    def setup_filesystem_monitoring(self):
        """Configure la surveillance filesystem Windows native - WORKSPACE LE PLUS RÉCENT UNIQUEMENT"""
        print(f"🔍 Configuration surveillance filesystem Windows...")

        def filesystem_monitor():
            # Surveiller UNIQUEMENT le workspace le plus récent
            workspaces = self.find_all_augment_workspaces()

            if not workspaces:
                print(f"❌ Aucun workspace à surveiller")
                return

            most_recent = workspaces[0]
            workspace_path = str(most_recent['path'])
            
            print(f"🎯 Surveillance filesystem EXCLUSIVE: {most_recent['id'][:16]}...")

            try:
                # Créer un handle pour surveiller le répertoire du workspace le plus récent
                handle = win32file.CreateFile(
                    workspace_path,
                    win32file.GENERIC_READ,
                    win32file.FILE_SHARE_READ | win32file.FILE_SHARE_WRITE | win32file.FILE_SHARE_DELETE,
                    None,
                    win32file.OPEN_EXISTING,
                    win32file.FILE_FLAG_BACKUP_SEMANTICS,
                    None
                )
                
                while self.running:
                    # Surveiller les changements
                    results = win32file.ReadDirectoryChangesW(
                        handle,
                        1024,
                        True,  # Surveillance récursive
                        win32con.FILE_NOTIFY_CHANGE_LAST_WRITE | win32con.FILE_NOTIFY_CHANGE_SIZE,
                        None,
                        None
                    )
                    
                    for action, filename in results:
                        if filename.endswith('state.vscdb'):
                            print(f"🔥 Changement filesystem détecté: {filename}")
                            self.process_filesystem_change(filename, action)
                    
                    time.sleep(0.001)  # 1ms - ultra-rapide
                    
            except Exception as e:
                print(f"⚠️ Erreur surveillance filesystem: {e}")
        
        self.filesystem_thread = threading.Thread(target=filesystem_monitor, daemon=True)
    
    def setup_network_monitoring(self):
        """Configure l'interception réseau/IPC"""
        print(f"🌐 Configuration interception réseau/IPC...")
        
        def network_monitor():
            while self.running:
                # Surveiller les connexions des processus Augment
                for proc in self.augment_processes:
                    try:
                        connections = proc.connections()
                        for conn in connections:
                            if conn.status == 'ESTABLISHED':
                                # Analyser le trafic (implémentation simplifiée)
                                pass
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue
                
                time.sleep(0.1)  # 100ms
        
        self.network_thread = threading.Thread(target=network_monitor, daemon=True)
    
    def analyze_temp_file(self, file_path):
        """Analyse un fichier temporaire modifié"""
        try:
            if file_path.suffix.lower() in ['.json', '.txt', '.log']:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    if 'conversation' in content.lower() or 'message' in content.lower():
                        print(f"💡 Contenu conversation détecté dans: {file_path.name}")
                        self.extract_conversation_from_content(content, f"TEMP:{file_path.name}")
        except Exception:
            pass
    
    def process_sqlite_change(self, state_file):
        """Traite un changement SQLite détecté - ÉCRITURE IMMÉDIATE"""
        try:
            # ÉCRITURE IMMÉDIATE de la détection
            timestamp = datetime.datetime.now().isoformat()
            with open(self.output_file, 'a', encoding='utf-8') as f:
                f.write(f"[{timestamp}] 🔥 DÉTECTION IMMÉDIATE SQLite: {state_file.parent.name[:8]}...\n")
                f.write(f"Taille: {state_file.stat().st_size} bytes\n")
                f.flush()
                os.fsync(f.fileno())

            # Lecture immédiate avec timeout court
            conn = sqlite3.connect(str(state_file), timeout=0.1)
            cursor = conn.cursor()

            cursor.execute("SELECT value FROM ItemTable WHERE key = 'memento/webviewView.augment-chat'")
            result = cursor.fetchone()

            if result:
                self.extract_conversation_from_sqlite(result[0], f"SQLITE:{state_file.parent.name[:8]}")

            conn.close()

        except Exception as e:
            # Écrire même les erreurs immédiatement
            timestamp = datetime.datetime.now().isoformat()
            with open(self.output_file, 'a', encoding='utf-8') as f:
                f.write(f"[{timestamp}] ⚠️ ERREUR SQLite: {str(e)[:100]}\n")
                f.flush()
                os.fsync(f.fileno())
    
    def process_filesystem_change(self, filename, action):
        """Traite un changement filesystem détecté - ÉCRITURE IMMÉDIATE"""
        print(f"⚡ Changement filesystem: {filename} (action: {action})")

        # ÉCRITURE IMMÉDIATE de la détection filesystem
        timestamp = datetime.datetime.now().isoformat()
        with open(self.output_file, 'a', encoding='utf-8') as f:
            f.write(f"[{timestamp}] 🔥 FILESYSTEM IMMÉDIAT: {filename}\n")
            f.write(f"Action: {action} | Détection ultra-rapide\n")
            f.write("-" * 50 + "\n")
            f.flush()
            os.fsync(f.fileno())
    
    def extract_conversation_from_content(self, content, source):
        """Extrait les conversations depuis du contenu brut"""
        try:
            # Essayer de parser comme JSON
            if content.strip().startswith('{'):
                data = json.loads(content)
                self.process_conversation_data(data, source)
        except Exception:
            pass
    
    def extract_conversation_from_sqlite(self, value, source):
        """Extrait les conversations depuis SQLite"""
        try:
            if isinstance(value, bytes):
                value_str = value.decode('utf-8')
            else:
                value_str = str(value)
            
            main_data = json.loads(value_str)
            webview_data = json.loads(main_data['webviewState'])
            
            self.process_conversation_data(webview_data, source)
            
        except Exception:
            pass
    
    def process_conversation_data(self, data, source):
        """Traite les données de conversation extraites"""
        try:
            conversations = data.get('conversations', {})
            current_conv_id = data.get('currentConversationId')
            
            if current_conv_id and current_conv_id in conversations:
                chat_history = conversations[current_conv_id].get('chatHistory', [])
                
                if len(chat_history) > self.last_message_count:
                    new_messages = chat_history[self.last_message_count:]
                    self.write_intercepted_messages(new_messages, source)
                    self.last_message_count = len(chat_history)
                    
        except Exception:
            pass
    
    def write_intercepted_messages(self, messages, source):
        """Écrit les messages interceptés immédiatement - AVEC GESTION VERROUS"""
        timestamp = datetime.datetime.now().isoformat()

        # Tentatives multiples pour éviter les verrous de fichier
        max_attempts = 5
        for attempt in range(max_attempts):
            try:
                # Essayer d'abord le fichier principal, puis l'alternatif
                output_file = self.output_file if attempt < 3 else self.output_file_alt
                with open(output_file, 'a', encoding='utf-8') as f:
                    for i, message_data in enumerate(messages):
                        msg_index = self.last_message_count + i + 1

                        f.write(f"[{timestamp}] MESSAGE #{msg_index} - INTERCEPTÉ ({source}) - TENTATIVE {attempt+1}\n")
                        f.write("-" * 60 + "\n")

                        # Message utilisateur
                        if 'request_message' in message_data and message_data['request_message']:
                            f.write(f"👤 UTILISATEUR:\n{message_data['request_message']}\n\n")

                        # Réponse assistant
                        if 'structured_output_nodes' in message_data:
                            for node in message_data['structured_output_nodes']:
                                if node.get('type') == 0 and node.get('content'):
                                    f.write(f"🤖 ASSISTANT:\n{node['content']}\n\n")
                                    break

                        f.write("=" * 80 + "\n\n")

                    f.flush()
                    os.fsync(f.fileno())

                    print(f"⚡ {len(messages)} messages interceptés depuis {source} (tentative {attempt+1})")
                    break  # Succès, sortir de la boucle

            except (PermissionError, OSError) as e:
                print(f"⚠️ Tentative {attempt+1} échouée: {e}")
                if attempt < max_attempts - 1:
                    time.sleep(0.1)  # Attendre 100ms avant nouvelle tentative
                else:
                    print(f"❌ Échec écriture après {max_attempts} tentatives")
    
    def initialize_output_file(self):
        """Initialise le fichier de sortie"""
        timestamp = datetime.datetime.now().isoformat()
        
        with open(self.output_file, 'w', encoding='utf-8') as f:
            f.write("AUGMENT CONVERSATION - INTERCEPTION AVANCÉE TEMPS RÉEL\n")
            f.write(f"Démarré: {timestamp}\n")
            f.write("Techniques: Mémoire, Fichiers temp, SQLite ultra-rapide, Filesystem, Réseau\n")
            f.write("=" * 80 + "\n\n")
    
    def start_advanced_monitoring(self):
        """Démarre la surveillance avancée"""
        print(f"\n🚀 DÉMARRAGE SURVEILLANCE AVANCÉE")
        print(f"⚡ Interception multi-niveaux activée")
        
        self.running = True
        
        # Démarrer tous les threads de surveillance
        self.memory_thread.start()
        self.temp_thread.start()
        self.sqlite_thread.start()
        self.filesystem_thread.start()
        self.network_thread.start()
        
        print(f"✅ 5 niveaux d'interception actifs")
        print(f"📝 Sortie: {self.output_file}")
        
        return True
    
    def stop_monitoring(self):
        """Arrête la surveillance"""
        self.running = False
        print(f"🛑 Surveillance avancée arrêtée")

def main():
    """Fonction principale"""
    try:
        import win32file, win32con, win32api
    except ImportError:
        print("❌ Module pywin32 requis: pip install pywin32")
        return
    
    interceptor = AdvancedRealTimeInterceptor()
    
    if interceptor.start_advanced_monitoring():
        try:
            print(f"\n💬 Écrivez dans Augment pour voir l'interception avancée...")
            print(f"⚡ Délai réduit à moins de 1 seconde")
            print(f"⏹️  Ctrl+C pour arrêter")
            
            while True:
                time.sleep(1)
                
        except KeyboardInterrupt:
            print(f"\n🛑 Arrêt de l'interception avancée")
            interceptor.stop_monitoring()

if __name__ == "__main__":
    main()
