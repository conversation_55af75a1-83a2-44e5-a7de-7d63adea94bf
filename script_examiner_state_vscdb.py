#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour examiner le fichier state.vscdb d'Augment
"""

import sqlite3
import json
from pathlib import Path

def examiner_state_vscdb():
    """Examine le fichier state.vscdb"""
    
    file_path = "C:/Users/<USER>/AppData/Roaming/Code/User/workspaceStorage/591be8b7f6bd4169258c0affc2eaa1fc/state.vscdb"
    
    print("🔍 EXAMEN DU FICHIER state.vscdb")
    print("=" * 60)
    print(f"📁 Fichier: {file_path}")
    
    # Vérifier si le fichier existe
    if not Path(file_path).exists():
        print("❌ Fichier non trouvé!")
        return None
    
    file_size = Path(file_path).stat().st_size
    print(f"📏 Taille du fichier: {file_size} octets")
    
    # Copier vers un fichier simple
    simple_path = "C:/temp_conversation/state.vscdb"
    Path("C:/temp_conversation").mkdir(exist_ok=True)
    
    try:
        # Copier le fichier
        import shutil
        shutil.copy2(file_path, simple_path)
        print(f"✅ Fichier copié vers: {simple_path}")
        
        # Essayer de lire comme base de données SQLite
        print(f"\n🔧 TENTATIVE DE LECTURE COMME BASE DE DONNÉES SQLite:")
        try:
            conn = sqlite3.connect(simple_path)
            cursor = conn.cursor()
            
            # Lister les tables
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            
            print(f"📊 Tables trouvées: {len(tables)}")
            for table in tables:
                print(f"   📋 Table: {table[0]}")
            
            # Examiner chaque table
            for table in tables:
                table_name = table[0]
                print(f"\n🔍 CONTENU DE LA TABLE '{table_name}':")
                print("-" * 50)
                
                # Obtenir la structure de la table
                cursor.execute(f"PRAGMA table_info({table_name});")
                columns = cursor.fetchall()
                print(f"📋 Colonnes:")
                for col in columns:
                    print(f"   🔸 {col[1]} ({col[2]})")
                
                # Obtenir le nombre de lignes
                cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
                count = cursor.fetchone()[0]
                print(f"📊 Nombre de lignes: {count}")
                
                # Afficher quelques lignes d'exemple
                if count > 0:
                    cursor.execute(f"SELECT * FROM {table_name} LIMIT 5;")
                    rows = cursor.fetchall()
                    
                    print(f"📝 Aperçu des données:")
                    for i, row in enumerate(rows, 1):
                        print(f"   {i}: {str(row)[:100]}{'...' if len(str(row)) > 100 else ''}")
                    
                    # Chercher spécifiquement "tu avais raison finalement"
                    for col in columns:
                        col_name = col[1]
                        try:
                            cursor.execute(f"SELECT * FROM {table_name} WHERE {col_name} LIKE '%tu avais raison finalement%';")
                            matching_rows = cursor.fetchall()
                            if matching_rows:
                                print(f"🎯 PHRASE TROUVÉE dans la colonne '{col_name}'!")
                                for row in matching_rows:
                                    print(f"   📝 Ligne: {str(row)[:200]}...")
                        except:
                            pass
            
            conn.close()
            return simple_path
            
        except sqlite3.Error as e:
            print(f"❌ Erreur SQLite: {e}")
            
            # Essayer de lire comme fichier texte
            print(f"\n🔧 TENTATIVE DE LECTURE COMME FICHIER TEXTE:")
            try:
                with open(simple_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                print(f"📏 Contenu texte: {len(content)} caractères")
                print(f"📝 Aperçu (premiers 500 caractères):")
                print("-" * 60)
                print(content[:500])
                print("-" * 60)
                
                # Chercher la phrase
                if "tu avais raison finalement" in content.lower():
                    print(f"🎯 PHRASE 'TU AVAIS RAISON FINALEMENT' TROUVÉE!")
                    
                    # Extraire le contexte
                    content_lower = content.lower()
                    pos = content_lower.find("tu avais raison finalement")
                    start = max(0, pos - 300)
                    end = min(len(content), pos + 500)
                    context = content[start:end]
                    
                    print(f"📝 CONTEXTE:")
                    print("=" * 80)
                    print(context)
                    print("=" * 80)
                
                return simple_path
                
            except UnicodeDecodeError:
                print(f"❌ Impossible de lire comme texte UTF-8")
                
                # Essayer de lire comme fichier binaire
                print(f"\n🔧 LECTURE COMME FICHIER BINAIRE:")
                try:
                    with open(simple_path, 'rb') as f:
                        binary_content = f.read()
                    
                    print(f"📏 Contenu binaire: {len(binary_content)} octets")
                    
                    # Convertir en texte et chercher
                    try:
                        text_content = binary_content.decode('utf-8', errors='ignore')
                        
                        if "tu avais raison finalement" in text_content.lower():
                            print(f"🎯 PHRASE TROUVÉE DANS LE CONTENU BINAIRE!")
                            
                            # Extraire le contexte
                            content_lower = text_content.lower()
                            pos = content_lower.find("tu avais raison finalement")
                            start = max(0, pos - 300)
                            end = min(len(text_content), pos + 500)
                            context = text_content[start:end]
                            
                            print(f"📝 CONTEXTE:")
                            print("=" * 80)
                            print(context)
                            print("=" * 80)
                        
                        # Sauvegarder le contenu texte extrait
                        text_output = "C:/temp_conversation/state_extracted.txt"
                        with open(text_output, 'w', encoding='utf-8') as f:
                            f.write(text_content)
                        
                        print(f"✅ Contenu texte extrait vers: {text_output}")
                        
                    except Exception as e:
                        print(f"❌ Erreur extraction texte: {e}")
                
                except Exception as e:
                    print(f"❌ Erreur lecture binaire: {e}")
        
        return simple_path
        
    except Exception as e:
        print(f"❌ Erreur générale: {e}")
        return None

if __name__ == "__main__":
    print("🚀 EXAMEN DU FICHIER state.vscdb")
    print("=" * 60)
    
    result = examiner_state_vscdb()
    
    if result:
        print(f"\n✅ EXAMEN TERMINÉ!")
        print(f"📄 Fichier copié: {result}")
    else:
        print(f"\n❌ ÉCHEC DE L'EXAMEN")
