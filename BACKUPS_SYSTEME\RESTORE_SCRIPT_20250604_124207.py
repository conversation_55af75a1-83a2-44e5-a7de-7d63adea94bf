#!/usr/bin/env python3
"""
SCRIPT DE RESTAURATION AUTOMATIQUE
Généré le: 2025-06-04T12:42:07.677683
"""

import shutil
from pathlib import Path

def restore_files():
    backup_dir = Path("C:\Users\<USER>\Desktop\Travail\Projet7\BACKUPS_SYSTEME")
    
    print("🔄 RESTAURATION DES FICHIERS SYSTÈME")
    print("=" * 50)
    
    # Restaurer state.vscdb principal
    state_backup = backup_dir / "state_vscdb_actuel_20250604_124207.vscdb"
    state_original = Path("C:/Users/<USER>/AppData/Roaming/Code/User/workspaceStorage/a35ba43ef26792e61fe787a234121806/state.vscdb")
    
    if state_backup.exists():
        try:
            shutil.copy2(state_backup, state_original)
            print("✅ state.vscdb restauré")
        except Exception as e:
            print(f"❌ Erreur restauration state.vscdb: {e}")
    
    # Restaurer autres workspaces si nécessaire
    # [Ajouter ici d'autres restaurations selon les besoins]
    
    print("🏁 Restauration terminée")

if __name__ == "__main__":
    restore_files()
