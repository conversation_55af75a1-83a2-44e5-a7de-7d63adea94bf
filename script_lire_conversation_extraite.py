#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour lire et afficher la conversation extraite
"""

def lire_conversation_extraite():
    """Lit et affiche la conversation extraite"""
    
    print("📋 LECTURE DE LA CONVERSATION EXTRAITE")
    print("=" * 60)
    
    files_to_check = [
        "C:/temp_conversation/conversation_brute.txt",
        "C:/temp_conversation/conversation_formatee.txt"
    ]
    
    for file_path in files_to_check:
        print(f"\n📄 FICHIER: {file_path}")
        print("-" * 60)
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"📏 Taille: {len(content)} caractères")
            print(f"📄 Lignes: {content.count(chr(10)) + 1}")
            
            # Afficher les premières lignes
            lines = content.split('\n')
            print(f"\n📋 APERÇU (50 premières lignes):")
            print("=" * 80)
            
            for i, line in enumerate(lines[:50], 1):
                print(f"{i:2d}: {line}")
            
            if len(lines) > 50:
                print(f"... (et {len(lines) - 50} lignes supplémentaires)")
            
            print("=" * 80)
            
            # Chercher des patterns de conversation
            if "Human:" in content or "Assistant:" in content:
                print("✅ Structure de conversation détectée!")
            
            if "tu avais raison finalement" in content.lower():
                print("🎯 Phrase 'tu avais raison finalement' trouvée!")
                
                # Trouver et afficher le contexte
                content_lower = content.lower()
                pos = content_lower.find("tu avais raison finalement")
                start = max(0, pos - 200)
                end = min(len(content), pos + 300)
                context = content[start:end]
                
                print(f"\n📝 CONTEXTE AUTOUR DE LA PHRASE:")
                print("-" * 60)
                print(context)
                print("-" * 60)
            
        except Exception as e:
            print(f"❌ Erreur lecture: {e}")

if __name__ == "__main__":
    print("🚀 LECTURE DE LA CONVERSATION EXTRAITE")
    print("=" * 60)
    
    lire_conversation_extraite()
