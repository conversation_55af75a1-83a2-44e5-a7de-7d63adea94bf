import{S as C,i as y,s as T,Q as k,D as W,c as p,a2 as w,e as F,f as z,a4 as V,n as v,h as f,a6 as j,G as S,H as _,a7 as g,a as m,b as I,I as B,J as A,K as M,L as E,d as $,M as N,g as L,j as x,y as G,R as J,z as Q,Y as Z,u as X,t as Y,B as U,Z as O,ae as ee,P as te,V as oe,W as se,X as ne}from"./SpinnerAugment-BJ4-L7QR.js";import{B as ae,a as ce}from"./github-C1PQK5DH.js";import{C as re}from"./IconButtonAugment-Certjadv.js";function ie(t){if(window.augmentPerformance=window.augmentPerformance||{},window.augmentPerformance.initialized)return;window.augmentPerformance.initialized=!0;let e=0,o=performance.now(),n=60;const a=[];let s=0;const c=t.lowFramerateThreshold,i=t.slowInpThreshold;if(requestAnimationFrame(function r(l){const d=l-o;if(e++,d>1e3){n=1e3*e/d,e=0,o=l,a.push(n),a.length>10&&a.shift();const P=a.reduce((b,u)=>b+u,0)/a.length;n<c&&(console.error(`[Augment Performance] Slow framerate detected: ${n.toFixed(1)} fps`),console.error(`[Augment Performance] Avg framerate detected: ${P.toFixed(1)} fps`))}requestAnimationFrame(r)}),PerformanceObserver.supportedEntryTypes.includes("event"))try{new PerformanceObserver(r=>{(l=>{const d=l.getEntries().filter(u=>"interactionId"in u&&"duration"in u&&u.startTime>0);if(d.length===0)return;d.sort((u,h)=>h.duration-u.duration);const P=Math.floor(.98*d.length),b=d[Math.min(P,d.length-1)].duration;if(b>i){console.error(`[Augment Performance] Slow INP detected: ${b.toFixed(1)} ms`);const u=d[0];u&&"target"in u&&console.error("[Augment Performance] Slow interaction target:",u.target,u),b>s&&(s=b)}})(r)}).observe({entryTypes:["event","first-input"],buffered:!0}),window.webVitals!==void 0&&window.webVitals.onINP&&window.webVitals.onINP(r=>{r.value>i&&console.error(`[Augment Performance] Slow INP detected via web-vitals: ${r.value.toFixed(1)} ms`)})}catch(r){console.error("[Augment Performance] Error setting up INP monitoring:",r)}else console.warn("[Augment Performance] PerformanceObserver not supported for INP monitoring");window.augmentPerformance.getFramerate=()=>n,window.augmentPerformance.getWorstINP=()=>s}const q=16,D=200;var H;function K(t){let e,o,n,a,s,c=(t[5]||"")+"",i=(t[4]||"")+"";return{c(){e=k("span"),o=S(c),n=W(),a=k("span"),s=S(i),p(e,"class","c-toggle-text c-toggle-text--off svelte-waglmg"),w(e,"visible",!t[0]&&t[5]),p(a,"class","c-toggle-text c-toggle-text--on svelte-waglmg"),w(a,"visible",t[0]&&t[4])},m(r,l){F(r,e,l),z(e,o),F(r,n,l),F(r,a,l),z(a,s)},p(r,l){32&l&&c!==(c=(r[5]||"")+"")&&_(o,c),33&l&&w(e,"visible",!r[0]&&r[5]),16&l&&i!==(i=(r[4]||"")+"")&&_(s,i),17&l&&w(a,"visible",r[0]&&r[4])},d(r){r&&(f(e),f(n),f(a))}}}function le(t){let e,o,n,a,s,c,i=t[6]&&K(t);return{c(){e=k("label"),i&&i.c(),o=W(),n=k("input"),p(n,"type","checkbox"),p(n,"class","c-toggle-input svelte-waglmg"),n.disabled=t[1],p(n,"aria-label",t[3]),p(n,"role","switch"),w(n,"disabled",t[1]),p(e,"class",a="c-toggle-track c-toggle-track-size--"+t[2]+" svelte-waglmg"),w(e,"checked",t[0]),w(e,"disabled",t[1]),w(e,"has-text",t[6])},m(r,l){F(r,e,l),i&&i.m(e,null),z(e,o),z(e,n),n.checked=t[0],s||(c=[V(n,"change",t[9]),V(n,"keydown",t[7]),V(e,"change",t[8])],s=!0)},p(r,[l]){r[6]?i?i.p(r,l):(i=K(r),i.c(),i.m(e,o)):i&&(i.d(1),i=null),2&l&&(n.disabled=r[1]),8&l&&p(n,"aria-label",r[3]),1&l&&(n.checked=r[0]),2&l&&w(n,"disabled",r[1]),4&l&&a!==(a="c-toggle-track c-toggle-track-size--"+r[2]+" svelte-waglmg")&&p(e,"class",a),5&l&&w(e,"checked",r[0]),6&l&&w(e,"disabled",r[1]),68&l&&w(e,"has-text",r[6])},i:v,o:v,d(r){r&&f(e),i&&i.d(),s=!1,j(c)}}}function de(t,e,o){let n,{checked:a=!1}=e,{disabled:s=!1}=e,{size:c=2}=e,{ariaLabel:i}=e,{onText:r}=e,{offText:l}=e;return t.$$set=d=>{"checked"in d&&o(0,a=d.checked),"disabled"in d&&o(1,s=d.disabled),"size"in d&&o(2,c=d.size),"ariaLabel"in d&&o(3,i=d.ariaLabel),"onText"in d&&o(4,r=d.onText),"offText"in d&&o(5,l=d.offText)},t.$$.update=()=>{48&t.$$.dirty&&o(6,n=r||l)},[a,s,c,i,r,l,n,function(d){s||d.key!=="Enter"&&d.key!==" "||(d.preventDefault(),o(0,a=!a))},function(d){g.call(this,t,d)},function(){a=this.checked,o(0,a)}]}var R;(H={enabled:((R=window.augmentFlags)==null?void 0:R.enablePerformanceMonitoring)??!1,lowFramerateThreshold:q,slowInpThreshold:D}).enabled&&ie({lowFramerateThreshold:H.lowFramerateThreshold||q,slowInpThreshold:H.slowInpThreshold||D});class ke extends C{constructor(e){super(),y(this,e,de,le,T,{checked:0,disabled:1,size:2,ariaLabel:3,onText:4,offText:5})}}function he(t){let e,o,n=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},t[0]],a={};for(let s=0;s<n.length;s+=1)a=m(a,n[s]);return{c(){e=I("svg"),o=new B(!0),this.h()},l(s){e=A(s,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var c=M(e);o=E(c,!0),c.forEach(f),this.h()},h(){o.a=null,$(e,a)},m(s,c){N(s,e,c),o.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M304 128a80 80 0 1 0-160 0 80 80 0 1 0 160 0m-208 0a128 128 0 1 1 256 0 128 128 0 1 1-256 0M49.3 464h349.5c-8.9-63.3-63.3-112-129-112h-91.4c-65.7 0-120.1 48.7-129 112zM0 482.3C0 383.8 79.8 304 178.3 304h91.4c98.5 0 178.3 79.8 178.3 178.3 0 16.4-13.3 29.7-29.7 29.7H29.7C13.3 512 0 498.7 0 482.3"/>',e)},p(s,[c]){$(e,a=L(n,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},1&c&&s[0]]))},i:v,o:v,d(s){s&&f(e)}}}function me(t,e,o){return t.$$set=n=>{o(0,e=m(m({},e),x(n)))},[e=x(e)]}class Le extends C{constructor(e){super(),y(this,e,me,he,T,{})}}function ue(t){let e,o,n=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},t[0]],a={};for(let s=0;s<n.length;s+=1)a=m(a,n[s]);return{c(){e=I("svg"),o=new B(!0),this.h()},l(s){e=A(s,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var c=M(e);o=E(c,!0),c.forEach(f),this.h()},h(){o.a=null,$(e,a)},m(s,c){N(s,e,c),o.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M248 72c0-13.3-10.7-24-24-24s-24 10.7-24 24v160H40c-13.3 0-24 10.7-24 24s10.7 24 24 24h160v160c0 13.3 10.7 24 24 24s24-10.7 24-24V280h160c13.3 0 24-10.7 24-24s-10.7-24-24-24H248z"/>',e)},p(s,[c]){$(e,a=L(n,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},1&c&&s[0]]))},i:v,o:v,d(s){s&&f(e)}}}function fe(t,e,o){return t.$$set=n=>{o(0,e=m(m({},e),x(n)))},[e=x(e)]}class ze extends C{constructor(e){super(),y(this,e,fe,ue,T,{})}}function we(t){let e;const o=t[7].default,n=te(o,t,t[17],null);return{c(){n&&n.c()},m(a,s){n&&n.m(a,s),e=!0},p(a,s){n&&n.p&&(!e||131072&s)&&oe(n,o,a,a[17],e?ne(o,a[17],s,null):se(a[17]),null)},i(a){e||(X(n,a),e=!0)},o(a){Y(n,a),e=!1},d(a){n&&n.d(a)}}}function ge(t){let e,o,n;const a=[{size:t[6]},{variant:pe},{color:t[0]},{highContrast:t[1]},{disabled:t[2]},{class:`c-badge-icon-btn__base-btn ${t[4]}`},t[3]];let s={$$slots:{default:[we]},$$scope:{ctx:t}};for(let c=0;c<a.length;c+=1)s=m(s,a[c]);return o=new re({props:s}),o.$on("click",t[8]),o.$on("keyup",t[9]),o.$on("keydown",t[10]),o.$on("mousedown",t[11]),o.$on("mouseover",t[12]),o.$on("focus",t[13]),o.$on("mouseleave",t[14]),o.$on("blur",t[15]),o.$on("contextmenu",t[16]),{c(){e=k("div"),G(o.$$.fragment),p(e,"class",J(`c-badge-icon-btn c-badge-icon-btn--${t[5].variant} c-badge-icon-btn--size-${t[6]}`)+" svelte-1im94um")},m(c,i){F(c,e,i),Q(o,e,null),n=!0},p(c,[i]){const r=95&i?L(a,[64&i&&{size:c[6]},0,1&i&&{color:c[0]},2&i&&{highContrast:c[1]},4&i&&{disabled:c[2]},16&i&&{class:`c-badge-icon-btn__base-btn ${c[4]}`},8&i&&Z(c[3])]):{};131072&i&&(r.$$scope={dirty:i,ctx:c}),o.$set(r)},i(c){n||(X(o.$$.fragment,c),n=!0)},o(c){Y(o.$$.fragment,c),n=!1},d(c){c&&f(e),U(o)}}}let pe="ghost";function ve(t,e){return typeof t=="string"&&["accent","neutral","error","success","warning","info"].includes(t)?t:e}function be(t,e,o){let n,a;const s=["color","highContrast","disabled"];let c=O(e,s),{$$slots:i={},$$scope:r}=e;const l=ee(ae.CONTEXT_KEY);let{color:d=ve(l.color,"neutral")}=e,{highContrast:P=!1}=e,{disabled:b=!1}=e,u=l.size;return t.$$set=h=>{e=m(m({},e),x(h)),o(18,c=O(e,s)),"color"in h&&o(0,d=h.color),"highContrast"in h&&o(1,P=h.highContrast),"disabled"in h&&o(2,b=h.disabled),"$$scope"in h&&o(17,r=h.$$scope)},t.$$.update=()=>{o(4,{class:n,...a}=c,n,(o(3,a),o(18,c)))},[d,P,b,a,n,l,u,i,function(h){g.call(this,t,h)},function(h){g.call(this,t,h)},function(h){g.call(this,t,h)},function(h){g.call(this,t,h)},function(h){g.call(this,t,h)},function(h){g.call(this,t,h)},function(h){g.call(this,t,h)},function(h){g.call(this,t,h)},function(h){g.call(this,t,h)},r]}const Ie={Root:ce,IconButton:class extends C{constructor(t){super(),y(this,t,be,ge,T,{color:0,highContrast:1,disabled:2})}}};function xe(t){let e,o,n=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 384 512"},t[0]],a={};for(let s=0;s<n.length;s+=1)a=m(a,n[s]);return{c(){e=I("svg"),o=new B(!0),this.h()},l(s){e=A(s,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var c=M(e);o=E(c,!0),c.forEach(f),this.h()},h(){o.a=null,$(e,a)},m(s,c){N(s,e,c),o.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M320 464c8.8 0 16-7.2 16-16V160h-80c-17.7 0-32-14.3-32-32V48H64c-8.8 0-16 7.2-16 16v384c0 8.8 7.2 16 16 16zM0 64C0 28.7 28.7 0 64 0h165.5c17 0 33.3 6.7 45.3 18.7l90.5 90.5c12 12 18.7 28.3 18.7 45.3V448c0 35.3-28.7 64-64 64H64c-35.3 0-64-28.7-64-64z"/>',e)},p(s,[c]){$(e,a=L(n,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 384 512"},1&c&&s[0]]))},i:v,o:v,d(s){s&&f(e)}}}function $e(t,e,o){return t.$$set=n=>{o(0,e=m(m({},e),x(n)))},[e=x(e)]}class Be extends C{constructor(e){super(),y(this,e,$e,xe,T,{})}}function Pe(t){let e,o,n=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 576 512"},t[0]],a={};for(let s=0;s<n.length;s+=1)a=m(a,n[s]);return{c(){e=I("svg"),o=new B(!0),this.h()},l(s){e=A(s,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var c=M(e);o=E(c,!0),c.forEach(f),this.h()},h(){o.a=null,$(e,a)},m(s,c){N(s,e,c),o.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M288 0c-8.5 0-17 1.7-24.8 5.1L53.9 94.8C40.6 100.5 32 113.5 32 128s8.6 27.5 21.9 33.2l209.3 89.7c7.8 3.4 16.3 5.1 24.8 5.1s17-1.7 24.8-5.1l209.3-89.7c13.3-5.7 21.9-18.8 21.9-33.2s-8.6-27.5-21.9-33.2L312.8 5.1C305 1.7 296.5 0 288 0m-5.9 49.2c1.9-.8 3.9-1.2 5.9-1.2s4 .4 5.9 1.2L477.7 128l-183.8 78.8c-1.9.8-3.9 1.2-5.9 1.2s-4-.4-5.9-1.2L98.3 128zM53.9 222.8C40.6 228.5 32 241.5 32 256s8.6 27.5 21.9 33.2l209.3 89.7c7.8 3.4 16.3 5.1 24.8 5.1s17-1.7 24.8-5.1l209.3-89.7c13.3-5.7 21.9-18.8 21.9-33.2s-8.6-27.5-21.9-33.2l-31.2-13.4-60.9 26.1 47.7 20.5-183.8 78.8c-1.9.8-3.9 1.2-5.9 1.2s-4-.4-5.9-1.2L98.3 256l47.7-20.5-60.9-26.1zm0 128C40.6 356.5 32 369.5 32 384s8.6 27.5 21.9 33.2l209.3 89.7c7.8 3.4 16.3 5.1 24.8 5.1s17-1.7 24.8-5.1l209.3-89.7c13.3-5.7 21.9-18.8 21.9-33.2s-8.6-27.5-21.9-33.2l-31.2-13.4-60.9 26.1 47.7 20.5-183.8 78.8c-1.9.8-3.9 1.2-5.9 1.2s-4-.4-5.9-1.2L98.3 384l47.7-20.5-60.9-26.1z"/>',e)},p(s,[c]){$(e,a=L(n,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 576 512"},1&c&&s[0]]))},i:v,o:v,d(s){s&&f(e)}}}function Ce(t,e,o){return t.$$set=n=>{o(0,e=m(m({},e),x(n)))},[e=x(e)]}class Ae extends C{constructor(e){super(),y(this,e,Ce,Pe,T,{})}}export{Ie as B,Be as F,Ae as L,ze as P,ke as T,Le as U};
