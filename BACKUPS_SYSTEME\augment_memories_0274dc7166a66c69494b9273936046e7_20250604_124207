

















# AZR MODEL ARCHITECTURE & MAINTENANCE
- Base-toi sur BASE_CONNAISSANCES_AZR_EXPERT.md pour la maintenance du modèle AZR et ARCHITECTURE_REFERENCE.md comme référence absolue pour toute modification.
- Hardware constraints: no GPU, 8-core CPU, 28GB RAM - optimize for CPU-only execution.
- Protocole de maintenance: consulter documentation, localiser composants, identifier dépendances, respecter architecture en couches, valider contre métriques, tester impacts.
- User wants AZR model integrated with baccarat prediction systems, referencing BASE_CONNAISSANCES_AZR_EXPERT.md as foundation.
- User prefers code structured for efficient navigation and maintenance, and not seeing full code implementations in responses.
- User wants to understand how the AZR system will learn and adapt from real user gameplay sessions in production.
- User prefers testing AZR predictions on large simulated datasets (1000+ games) with strict isolation ensuring the program has no access to future game data to validate prediction accuracy.
- User observed discrepancy between previous AZR test results showing 70-80% accuracy and current 1000-party test showing only 50.2% accuracy, indicating potential testing or implementation issues.
- Adaptive learning was disabled in the AZR prediction system during testing, which may explain poor performance results.
- User noticed that azr_gui_3columns.py interface may not be using the original AZR prediction model that was developed, indicating potential integration issues between GUI and core system.
- User observed that AZR predictor test results are much lower quality than before and believes there are missing Python files from the main directory that work together with the predictor system.
- When debugging missing Python files in a system, analyze the imports in existing files to identify which other files should be imported and copied.
- User prefers organized file structure with related Python files grouped in dedicated folders rather than creating many scattered files, and wants imports analyzed to determine dependencies for proper organization.
- User confirmed that all azr_* files in the directory are actually components of a single integrated program, not separate standalone programs.

# AZR OPTIMIZATION PARAMETERS
- Rollouts optimization plan: reduce temperature (0.7→0.5), rollouts (7→5), step size (0.15→0.05), confidence thresholds (0.6→0.4), reweight selection (confidence 40%→20%, add accuracy 30%).
- Baseline AZR parameters: Learning Rate 1e-4, rollouts 7 (temp 0.7, step 0.15, rand 0.1), confidence thresholds 0.6/0.6/0.7, base_confidence 0.5, pattern_bonus 0.3, confidence_weight 0.4, patterns min_length 3, accuracy 51%.
- When testing model performance, 'Attendre' predictions should be excluded from accuracy calculations.
- DEFAULT_PREDICTION should be 'Attendre' instead of arbitrary predictions when insufficient data is available.
- User believes the 65% accuracy objective is not attainable and has identified potential reasons why.

# BACCARAT THEORY & MECHANICS
- Baccarat uses alternating card distribution creating two separate sabots: Player cards from odd positions (1,3,5,7...) and Banker cards from even positions (2,4,6,8...).
- Casino baccarat uses 8-deck shoes with cut cards, averaging 60 hands per shoe, burning ~6 cards at start.
- Baccarat rules: maximum 6 burn cards at start, cut card placed at ~300th card position (3/4 of shoe).
- Synchronization states: SYNC (when even number of cards used) and DESYNC (when odd number of cards used) - state changes throughout game based on card counts.
- Synchronization logic: starting from DESYNC, if odd number of cards distributed, state returns to SYNC (two desynchronizations result in resynchronization).
- Ties must be included in sync/desync state calculations as they use cards, but excluded from S/O conversion analysis and accuracy calculations.
- User confirmed sync/desync understanding and referenced a previously created baccarat game generator that used cards to generate traditional baccarat games with sync/desync states, P/B/T results, and S/O conversions.

# BACCARAT PREDICTION STRATEGY
- User's approach focuses on card distribution mechanics rather than pattern analysis, treating hands as interdependent due to limited deck.
- For S/O conversion: consecutive same results (PPP or BBB) convert to S,S; alternating results (PBP or BPB) convert to O,O.
- User discovered exploitable baccarat bias through synchronization analysis showing 16.6% difference in S/O patterns.
- Strategic insight: desynchronizations are key triggers in patterns; focus on desync impact over 30-hand sequences for optimal advantage.
- User wants to analyze 30-hand sequences starting with initial desync state to study if prediction accuracy improves with more data.

# BACCARAT PREDICTION SYSTEM IMPLEMENTATION
- In the baccarat prediction system, the true primary data hierarchy is: PAIR/IMPAIR (raw input) → SYNC/DESYNC (synchronization state) → S/O (predictions).
- For prediction system: user inputs initial state (PAIR/IMPAIR burned cards), then for each hand inputs PAIR/IMPAIR cards, system calculates next hand's state and predicts Same (S) or Opposite (O).
- Predictions are made immediately after user inputs PAIR/IMPAIR for a hand, as this determines current synchronization state.
- When in desync state for multiple hands and last hand has impair cards, this causes resynchronization to sync state, enabling Same (S) prediction with 21.4% advantage.
- For testing adaptive systems, use progressive dataset scaling: start with 200 sequences, then test on 400, 600, 800, and 1000 sequences.
- For proper adaptive learning, the baccarat prediction system needs both card parity (PAIR/IMPAIR) AND actual game results (Banker/Player/Tie) as training data inputs.
- TIE PAIR/IMPAIR inputs should influence next predictions since they contribute to the total pair/impair count used for synchronization state calculation in the baccarat system.
- For baccarat prediction testing, when the program receives P/B/T results, it must also receive the corresponding PAIR/IMPAIR data and SYNC/DESYNC state for complete validation.
- User prefers GUI interface where user inputs only Pair/Impair for card burns and each hand, with reset button for new games, and system provides real-time Same/Opposite recommendations after each hand input.
- User prefers compact initialization section (used only once) placed in corner/bottom, reports missing reset button, and interface blocking issue when entering IMPAIR twice.
- User prefers baccarat interface with 3 columns (Player Pair/Impair, Banker Pair/Impair, Tie Pair/Impair), separate counters for hands (B/P only) vs total pair/impair entries (including burned cards and ties), with pair/impair data as raw material and B/P conversions to S/O for prediction validation.
- User prefers smaller buttons in interfaces and expects counters to be clearly visible in the display.
- Reset button should only clear current game data, not delete other saved data unless the current game hasn't been saved.
- User cannot read popup windows and prefers detailed written summaries instead of opening new windows for displaying information.
- User prefers not to have popup windows or dialog boxes opened during interactions.
- User prefers to modify existing operational generators and test with main programs rather than creating separate test programs, wants to use existing codebase for testing.
- For baccarat generator output: include PAIR/IMPAIR data and S/O conversions, but exclude TIE results from S/O calculations while still counting TIE for PAIR/IMPAIR, and ensure output format is compatible with main program.
- The traditional baccarat generator should output data in a format that can be directly used by azr_final_synthesis.py for processing and analysis.
- User prefers to prioritize testing the predicteur anticipatif on generated baccarat games over GUI interface development.
- User prefers to organize GUI/interface files in separate subdirectories within project folders for better code organization.