#!/usr/bin/env python3
"""
SYSTÈME DE MONITORING CONVERSATION AUGMENT EN TEMPS RÉEL
=========================================================

Ce script surveille le fichier state.vscdb et crée automatiquement 
un historique lisible de la conversation en temps réel.

Basé sur l'analyse du processus Augment qui écrit dans :
- Fichier: state.vscdb 
- Clé: memento/webviewView.augment-chat
- Structure: JSON avec chatHistory contenant les messages
"""

import sqlite3
import json
import time
import datetime
from pathlib import Path
import threading
import os

class AugmentConversationMonitor:
    def __init__(self):
        self.state_file = Path("C:/Users/<USER>/AppData/Roaming/Code/User/workspaceStorage/a35ba43ef26792e61fe787a234121806/state.vscdb")
        self.output_file = "conversation_augment_live.txt"
        self.json_output = "conversation_augment_live.json"
        self.last_mtime = 0
        self.last_message_count = 0
        self.current_conversation_id = None
        self.running = False
        
        print(f"🔍 SYSTÈME DE MONITORING AUGMENT INITIALISÉ")
        print(f"📁 Fichier surveillé: {self.state_file}")
        print(f"📝 Sortie texte: {self.output_file}")
        print(f"📊 Sortie JSON: {self.json_output}")
    
    def extract_conversation_data(self):
        """Extrait les données de conversation depuis state.vscdb"""
        try:
            if not self.state_file.exists():
                return None
            
            conn = sqlite3.connect(str(self.state_file))
            cursor = conn.cursor()
            
            # Récupérer la clé des conversations Augment
            cursor.execute("SELECT value FROM ItemTable WHERE key = 'memento/webviewView.augment-chat';")
            result = cursor.fetchone()
            
            if not result:
                conn.close()
                return None
            
            value = result[0]
            if isinstance(value, bytes):
                value_str = value.decode('utf-8')
            else:
                value_str = str(value)
            
            # Parser le JSON
            main_data = json.loads(value_str)
            webview_data = json.loads(main_data['webviewState'])
            
            conn.close()
            return webview_data
            
        except Exception as e:
            print(f"❌ Erreur extraction: {e}")
            return None
    
    def format_message_for_display(self, message_data, index):
        """Formate un message pour l'affichage"""
        timestamp = datetime.datetime.now().isoformat()
        
        # Extraire le contenu principal
        content = ""
        role = "unknown"
        
        if 'request_message' in message_data:
            content = message_data.get('request_message', '')
            role = "USER"
        elif 'structured_output_nodes' in message_data:
            # Chercher le contenu dans les nodes
            for node in message_data['structured_output_nodes']:
                if node.get('type') == 0 and node.get('content'):
                    content = node['content']
                    role = "ASSISTANT"
                    break
        
        # Si pas de contenu trouvé, essayer d'autres champs
        if not content:
            if 'response_text' in message_data:
                content = message_data['response_text']
                role = "ASSISTANT"
            elif isinstance(message_data, str):
                content = message_data
        
        return {
            'index': index,
            'timestamp': timestamp,
            'role': role,
            'content': content,
            'raw_data': message_data
        }
    
    def save_conversation_update(self, new_messages):
        """Sauvegarde les nouveaux messages"""
        if not new_messages:
            return
        
        # Sauvegarder en format texte lisible
        with open(self.output_file, 'a', encoding='utf-8') as f:
            for msg in new_messages:
                f.write(f"\n{'='*80}\n")
                f.write(f"[{msg['timestamp']}] MESSAGE {msg['index']} - {msg['role']}\n")
                f.write(f"{'='*80}\n")
                f.write(f"{msg['content']}\n")
                f.write(f"{'='*80}\n\n")
        
        # Sauvegarder en JSON pour analyse
        try:
            if os.path.exists(self.json_output):
                with open(self.json_output, 'r', encoding='utf-8') as f:
                    existing_data = json.load(f)
            else:
                existing_data = {"messages": []}
            
            existing_data["messages"].extend(new_messages)
            existing_data["last_update"] = datetime.datetime.now().isoformat()
            
            with open(self.json_output, 'w', encoding='utf-8') as f:
                json.dump(existing_data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            print(f"⚠️ Erreur sauvegarde JSON: {e}")
    
    def monitor_loop(self):
        """Boucle principale de monitoring"""
        print(f"🚀 Démarrage du monitoring...")
        
        # Initialiser le fichier de sortie
        with open(self.output_file, 'w', encoding='utf-8') as f:
            f.write(f"HISTORIQUE CONVERSATION AUGMENT - MONITORING TEMPS RÉEL\n")
            f.write(f"Démarré le: {datetime.datetime.now().isoformat()}\n")
            f.write(f"{'='*80}\n\n")
        
        while self.running:
            try:
                if self.state_file.exists():
                    current_mtime = self.state_file.stat().st_mtime
                    
                    if current_mtime > self.last_mtime:
                        print(f"🔄 Modification détectée: {datetime.datetime.now().strftime('%H:%M:%S')}")
                        
                        conversation_data = self.extract_conversation_data()
                        
                        if conversation_data:
                            conversations = conversation_data.get('conversations', {})
                            current_conv_id = conversation_data.get('currentConversationId')
                            
                            if current_conv_id and current_conv_id in conversations:
                                chat_history = conversations[current_conv_id].get('chatHistory', [])
                                
                                if len(chat_history) > self.last_message_count:
                                    # Nouveaux messages détectés
                                    new_messages_raw = chat_history[self.last_message_count:]
                                    new_messages = []
                                    
                                    for i, msg_data in enumerate(new_messages_raw):
                                        formatted_msg = self.format_message_for_display(
                                            msg_data, 
                                            self.last_message_count + i + 1
                                        )
                                        new_messages.append(formatted_msg)
                                    
                                    self.save_conversation_update(new_messages)
                                    self.last_message_count = len(chat_history)
                                    
                                    print(f"✅ {len(new_messages)} nouveaux messages sauvegardés")
                                    
                                    # Afficher un aperçu du dernier message
                                    if new_messages:
                                        last_msg = new_messages[-1]
                                        preview = last_msg['content'][:100] + "..." if len(last_msg['content']) > 100 else last_msg['content']
                                        print(f"   📝 Dernier message ({last_msg['role']}): {preview}")
                        
                        self.last_mtime = current_mtime
                
                time.sleep(1)  # Vérifier chaque seconde
                
            except KeyboardInterrupt:
                print(f"\n🛑 Arrêt demandé par l'utilisateur")
                break
            except Exception as e:
                print(f"❌ Erreur monitoring: {e}")
                time.sleep(5)  # Attendre plus longtemps en cas d'erreur
    
    def start(self):
        """Démarre le monitoring"""
        self.running = True
        
        # Obtenir l'état initial
        conversation_data = self.extract_conversation_data()
        if conversation_data:
            conversations = conversation_data.get('conversations', {})
            current_conv_id = conversation_data.get('currentConversationId')
            
            if current_conv_id and current_conv_id in conversations:
                chat_history = conversations[current_conv_id].get('chatHistory', [])
                self.last_message_count = len(chat_history)
                self.current_conversation_id = current_conv_id
                
                print(f"📊 État initial: {self.last_message_count} messages dans la conversation {current_conv_id[:8]}...")
        
        if self.state_file.exists():
            self.last_mtime = self.state_file.stat().st_mtime
        
        try:
            self.monitor_loop()
        finally:
            self.running = False
            print(f"🏁 Monitoring terminé")
    
    def stop(self):
        """Arrête le monitoring"""
        self.running = False

def main():
    """Fonction principale"""
    print(f"🎯 SYSTÈME DE MONITORING CONVERSATION AUGMENT")
    print(f"=" * 60)
    
    monitor = AugmentConversationMonitor()
    
    try:
        monitor.start()
    except KeyboardInterrupt:
        print(f"\n🛑 Arrêt du monitoring...")
        monitor.stop()

if __name__ == "__main__":
    main()
