MÉTHODES ROLLOUT 1 CLUSTER PAR DÉFAUT IDENTIFIÉES
=================================================

RÉSULTATS DE L'ANALYSE :
=======================
Total méthodes candidates : 127
Méthodes Rollout 1 identifiées : 49
Pourcentage : 38.6%

LISTE DES MÉTHODES ROLLOUT 1 :
=============================

 1. _analyze_complete_impair_pair_index
    Confiance : 120%
    Lignes de code : 70
    Raisons : Mots-clés Rollout1: 172, Analyse indices: 21, Impact croisé: 3, Analyse IMPAIR consécutifs, Analyse corrélations, Analyse 1,2,3 → impact 4,5
    Fonctionnalités : Analyse IMPAIR, Corrélations, Impact 1,2,3→4,5

 2. _analyze_variations_impact_on_outcomes
    Confiance : 115%
    Lignes de code : 65
    Raisons : Mots-clés Rollout1: 94, Analyse indices: 4, Impact croisé: 32, Analyse IMPAIR consécutifs, Analyse SYNC/DESYNC, Analyse corrélations
    Fonctionnalités : Analyse IMPAIR, Analyse SYNC/DESYNC, Corrélations

 3. _analyze_impair_bias_specialized
    Confiance : 100%
    Lignes de code : 48
    Raisons : Mots-clés Rollout1: 58, Analyse indices: 5, Analyse IMPAIR consécutifs, Analyse COMBINED, Analyse 1,2,3 → impact 4,5
    Fonctionnalités : Analyse IMPAIR, Analyse COMBINED, Impact 1,2,3→4,5

 4. _calculate_cross_index_impacts
    Confiance : 100%
    Lignes de code : 100
    Raisons : Mots-clés Rollout1: 159, Analyse indices: 3, Impact croisé: 25, Analyse SYNC/DESYNC, Analyse corrélations
    Fonctionnalités : Analyse SYNC/DESYNC, Corrélations

 5. _identify_enhanced_dominant_correlations
    Confiance : 100%
    Lignes de code : 298
    Raisons : Mots-clés Rollout1: 443, Analyse indices: 2, Impact croisé: 66, Analyse SYNC/DESYNC, Analyse corrélations
    Fonctionnalités : Analyse SYNC/DESYNC, Corrélations

 6. _identify_enhanced_high_confidence_zones
    Confiance : 100%
    Lignes de code : 314
    Raisons : Mots-clés Rollout1: 364, Analyse indices: 2, Impact croisé: 61, Analyse SYNC/DESYNC, Analyse corrélations
    Fonctionnalités : Analyse SYNC/DESYNC, Corrélations

 7. _define_complete_generation_space_DEPRECATED
    Confiance : 100%
    Lignes de code : 287
    Raisons : Mots-clés Rollout1: 281, Impact croisé: 65, Analyse IMPAIR consécutifs, Analyse SYNC/DESYNC, Analyse COMBINED, Analyse corrélations
    Fonctionnalités : Analyse IMPAIR, Analyse SYNC/DESYNC, Analyse COMBINED, Corrélations

 8. _generate_impair_pair_optimized_sequence
    Confiance : 100%
    Lignes de code : 179
    Raisons : Mots-clés Rollout1: 189, Analyse indices: 14, Impact croisé: 7, Analyse SYNC/DESYNC, Analyse corrélations
    Fonctionnalités : Analyse SYNC/DESYNC, Corrélations

 9. _analyze_combined_to_pbt_impact
    Confiance : 90%
    Lignes de code : 44
    Raisons : Mots-clés Rollout1: 44, Analyse indices: 2, Impact croisé: 20, Analyse SYNC/DESYNC
    Fonctionnalités : Analyse SYNC/DESYNC

10. _analyze_combined_to_so_impact
    Confiance : 90%
    Lignes de code : 36
    Raisons : Mots-clés Rollout1: 37, Analyse indices: 2, Impact croisé: 11, Analyse SYNC/DESYNC
    Fonctionnalités : Analyse SYNC/DESYNC

11. _calculate_variation_strength_analysis
    Confiance : 90%
    Lignes de code : 172
    Raisons : Mots-clés Rollout1: 40, Analyse indices: 4, Impact croisé: 14, Analyse SYNC/DESYNC
    Fonctionnalités : Analyse SYNC/DESYNC

12. _generate_sync_based_sequence
    Confiance : 90%
    Lignes de code : 204
    Raisons : Mots-clés Rollout1: 136, Analyse indices: 15, Impact croisé: 4, Analyse SYNC/DESYNC
    Fonctionnalités : Analyse SYNC/DESYNC

13. _generate_combined_index_sequence
    Confiance : 90%
    Lignes de code : 237
    Raisons : Mots-clés Rollout1: 172, Analyse indices: 40, Impact croisé: 6, Analyse SYNC/DESYNC
    Fonctionnalités : Analyse SYNC/DESYNC

14. _generate_so_pattern_sequence
    Confiance : 90%
    Lignes de code : 247
    Raisons : Mots-clés Rollout1: 216, Analyse indices: 17, Impact croisé: 6, Analyse SYNC/DESYNC
    Fonctionnalités : Analyse SYNC/DESYNC

15. _generate_bias_exploitation_synthesis
    Confiance : 85%
    Lignes de code : 165
    Raisons : Mots-clés Rollout1: 70, Impact croisé: 6, Analyse SYNC/DESYNC, Analyse COMBINED, Analyse corrélations
    Fonctionnalités : Analyse SYNC/DESYNC, Analyse COMBINED, Corrélations

16. _calculate_variations_impact
    Confiance : 85%
    Lignes de code : 54
    Raisons : Mots-clés Rollout1: 53, Analyse indices: 6, Impact croisé: 5, Analyse corrélations
    Fonctionnalités : Corrélations

17. _analyze_combined_state_changes_impact
    Confiance : 85%
    Lignes de code : 283
    Raisons : Mots-clés Rollout1: 65, Impact croisé: 21, Analyse SYNC/DESYNC, Analyse COMBINED, Analyse corrélations
    Fonctionnalités : Analyse SYNC/DESYNC, Analyse COMBINED, Corrélations

18. _analyze_complete_combined_index
    Confiance : 80%
    Lignes de code : 37
    Raisons : Mots-clés Rollout1: 51, Analyse indices: 6, Analyse SYNC/DESYNC, Analyse corrélations
    Fonctionnalités : Analyse SYNC/DESYNC, Corrélations

19. _synthesize_complete_analysis
    Confiance : 75%
    Lignes de code : 57
    Raisons : Mots-clés Rollout1: 40, Impact croisé: 16, Analyse SYNC/DESYNC, Analyse corrélations
    Fonctionnalités : Analyse SYNC/DESYNC, Corrélations

20. _analyze_complete_cross_impacts
    Confiance : 75%
    Lignes de code : 85
    Raisons : Mots-clés Rollout1: 147, Impact croisé: 54, Analyse SYNC/DESYNC, Analyse corrélations
    Fonctionnalités : Analyse SYNC/DESYNC, Corrélations

21. _analyze_desync_periods_impact
    Confiance : 75%
    Lignes de code : 256
    Raisons : Mots-clés Rollout1: 163, Impact croisé: 34, Analyse SYNC/DESYNC, Analyse corrélations
    Fonctionnalités : Analyse SYNC/DESYNC, Corrélations

22. _analyze_temporal_correlation_evolution
    Confiance : 75%
    Lignes de code : 201
    Raisons : Mots-clés Rollout1: 255, Impact croisé: 44, Analyse SYNC/DESYNC, Analyse corrélations
    Fonctionnalités : Analyse SYNC/DESYNC, Corrélations

23. _calculate_phase_sync_desync_pb_correlation
    Confiance : 75%
    Lignes de code : 57
    Raisons : Mots-clés Rollout1: 104, Impact croisé: 1, Analyse SYNC/DESYNC, Analyse corrélations
    Fonctionnalités : Analyse SYNC/DESYNC, Corrélations

24. _calculate_phase_sync_desync_so_correlation
    Confiance : 75%
    Lignes de code : 54
    Raisons : Mots-clés Rollout1: 104, Impact croisé: 1, Analyse SYNC/DESYNC, Analyse corrélations
    Fonctionnalités : Analyse SYNC/DESYNC, Corrélations

25. _generate_signals_summary
    Confiance : 75%
    Lignes de code : 117
    Raisons : Mots-clés Rollout1: 107, Impact croisé: 25, Analyse SYNC/DESYNC, Analyse corrélations
    Fonctionnalités : Analyse SYNC/DESYNC, Corrélations

26. _extract_next_hand_prediction
    Confiance : 75%
    Lignes de code : 27
    Raisons : Mots-clés Rollout1: 14, Analyse indices: 3, Analyse 1,2,3 → impact 4,5
    Fonctionnalités : Impact 1,2,3→4,5

27. _generate_quick_access
    Confiance : 70%
    Lignes de code : 101
    Raisons : Mots-clés Rollout1: 32, Analyse indices: 4, Analyse SYNC/DESYNC
    Fonctionnalités : Analyse SYNC/DESYNC

28. _analyze_desync_sync_to_pbt_impact
    Confiance : 65%
    Lignes de code : 34
    Raisons : Mots-clés Rollout1: 118, Impact croisé: 8, Analyse SYNC/DESYNC
    Fonctionnalités : Analyse SYNC/DESYNC

29. _analyze_desync_sync_to_so_impact
    Confiance : 65%
    Lignes de code : 28
    Raisons : Mots-clés Rollout1: 99, Impact croisé: 4, Analyse SYNC/DESYNC
    Fonctionnalités : Analyse SYNC/DESYNC

30. _analyze_tri_dimensional_impacts
    Confiance : 65%
    Lignes de code : 42
    Raisons : Mots-clés Rollout1: 112, Impact croisé: 12, Analyse SYNC/DESYNC
    Fonctionnalités : Analyse SYNC/DESYNC

31. _analyze_consecutive_length_impact
    Confiance : 65%
    Lignes de code : 72
    Raisons : Mots-clés Rollout1: 66, Impact croisé: 16, Analyse IMPAIR consécutifs
    Fonctionnalités : Analyse IMPAIR

32. _calculate_overall_impact_strength
    Confiance : 65%
    Lignes de code : 97
    Raisons : Mots-clés Rollout1: 157, Impact croisé: 83, Analyse SYNC/DESYNC
    Fonctionnalités : Analyse SYNC/DESYNC

33. _analyze_transition_moments_impact
    Confiance : 65%
    Lignes de code : 165
    Raisons : Mots-clés Rollout1: 182, Impact croisé: 26, Analyse SYNC/DESYNC
    Fonctionnalités : Analyse SYNC/DESYNC

34. _extract_consecutive_length_strength
    Confiance : 65%
    Lignes de code : 37
    Raisons : Mots-clés Rollout1: 35, Impact croisé: 28, Analyse IMPAIR consécutifs
    Fonctionnalités : Analyse IMPAIR

35. _extract_desync_periods_strength
    Confiance : 65%
    Lignes de code : 30
    Raisons : Mots-clés Rollout1: 31, Impact croisé: 15, Analyse SYNC/DESYNC
    Fonctionnalités : Analyse SYNC/DESYNC

36. _identify_best_prediction_context
    Confiance : 65%
    Lignes de code : 27
    Raisons : Mots-clés Rollout1: 8, Impact croisé: 2, Analyse SYNC/DESYNC
    Fonctionnalités : Analyse SYNC/DESYNC

37. _generate_complete_synthesis
    Confiance : 60%
    Lignes de code : 90
    Raisons : Mots-clés Rollout1: 67, Impact croisé: 26, Analyse corrélations
    Fonctionnalités : Corrélations

38. _analyze_complete_so_index
    Confiance : 60%
    Lignes de code : 56
    Raisons : Mots-clés Rollout1: 13, Impact croisé: 1, Analyse corrélations
    Fonctionnalités : Corrélations

39. _calculate_phase_impair_pair_pb_correlation
    Confiance : 60%
    Lignes de code : 57
    Raisons : Mots-clés Rollout1: 109, Impact croisé: 1, Analyse corrélations
    Fonctionnalités : Corrélations

40. _calculate_phase_impair_pair_so_correlation
    Confiance : 60%
    Lignes de code : 54
    Raisons : Mots-clés Rollout1: 110, Impact croisé: 1, Analyse corrélations
    Fonctionnalités : Corrélations

41. _get_cluster_specialization_params
    Confiance : 55%
    Lignes de code : 66
    Raisons : Mots-clés Rollout1: 19, Analyse SYNC/DESYNC, Analyse corrélations
    Fonctionnalités : Analyse SYNC/DESYNC, Corrélations

42. _analyze_correlations_std_dev
    Confiance : 55%
    Lignes de code : 34
    Raisons : Mots-clés Rollout1: 97, Analyse SYNC/DESYNC, Analyse corrélations
    Fonctionnalités : Analyse SYNC/DESYNC, Corrélations

43. _analyze_impair_pair_to_so_impact
    Confiance : 50%
    Lignes de code : 28
    Raisons : Mots-clés Rollout1: 96, Impact croisé: 4

44. _calculate_combined_so_impact_strength
    Confiance : 50%
    Lignes de code : 64
    Raisons : Mots-clés Rollout1: 12, Impact croisé: 9

45. _calculate_combined_pbt_impact_strength
    Confiance : 50%
    Lignes de code : 71
    Raisons : Mots-clés Rollout1: 12, Impact croisé: 9

46. _extract_transition_moments_strength
    Confiance : 50%
    Lignes de code : 31
    Raisons : Mots-clés Rollout1: 19, Impact croisé: 15

47. _extract_combined_state_changes_strength
    Confiance : 50%
    Lignes de code : 21
    Raisons : Mots-clés Rollout1: 15, Impact croisé: 7

48. _extract_temporal_evolution_strength
    Confiance : 50%
    Lignes de code : 18
    Raisons : Mots-clés Rollout1: 4, Impact croisé: 4

49. _generate_generation_guidance
    Confiance : 50%
    Lignes de code : 105
    Raisons : Mots-clés Rollout1: 29, Impact croisé: 13


ANALYSE PAR CATÉGORIE :
======================
Méthodes avec analyse IMPAIR : 6
Méthodes avec analyse SYNC/DESYNC : 32
Méthodes avec analyse COMBINED : 4
Méthodes avec corrélations : 24
Méthodes avec impact 1,2,3→4,5 : 3

CONCLUSION :
============
Ces méthodes représentent les fonctionnalités manquantes du Rollout 1 
dans les méthodes universelles. Elles analysent spécifiquement :
- Les indices 1, 2, 3 (PAIR/IMPAIR, SYNC/DESYNC, COMBINED)
- Leur impact sur les indices 4, 5 (P/B/T, S/O)
- Les biais structurels et corrélations croisées

RECOMMANDATION :
===============
Intégrer ces méthodes dans les méthodes universelles du Rollout 1
pour compléter les fonctionnalités manquantes d'analyse des indices.
