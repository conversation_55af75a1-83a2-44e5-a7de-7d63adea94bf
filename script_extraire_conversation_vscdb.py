#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour extraire la conversation complète du fichier state.vscdb
"""

import sqlite3
import json
from pathlib import Path

def extraire_conversation_vscdb():
    """Extrait la conversation du fichier state.vscdb"""
    
    file_path = "C:/temp_conversation/state.vscdb"
    
    print("🔍 EXTRACTION DE LA CONVERSATION DEPUIS state.vscdb")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect(file_path)
        cursor = conn.cursor()
        
        # Chercher spécifiquement la ligne avec la conversation Augment
        cursor.execute("SELECT key, value FROM ItemTable WHERE key LIKE '%augment%' OR value LIKE '%tu avais raison finalement%';")
        augment_rows = cursor.fetchall()

        print(f"📊 Lignes Augment trouvées: {len(augment_rows)}")

        for key, value in augment_rows:
            print(f"\n🔑 Clé: {key}")
            print(f"📏 Taille valeur: {len(str(value))} caractères")

            # Afficher le type et un aperçu de la valeur
            print(f"🔧 Type: {type(value).__name__}")
            print(f"📝 Aperçu: {str(value)[:100]}...")

            # Si c'est la clé de conversation Augment
            if "augment-chat" in key:
                print(f"🎯 DONNÉES DE CONVERSATION AUGMENT TROUVÉES!")

                try:
                    # Convertir en string si nécessaire
                    if isinstance(value, bytes):
                        value_str = value.decode('utf-8')
                    elif isinstance(value, (int, float)):
                        print(f"⚠️ Valeur numérique trouvée: {value}")
                        continue
                    else:
                        value_str = str(value)

                    # Parser le JSON
                    data = json.loads(value_str)

                    print(f"✅ JSON parsé avec succès")
                    print(f"🔧 Structure:")
                    if isinstance(data, dict):
                        for k, v in data.items():
                            print(f"   🔸 {k}: {type(v).__name__}")
                    else:
                        print(f"   📊 Type de données: {type(data).__name__}")

                    # Extraire webviewState qui contient les conversations
                    if isinstance(data, dict) and 'webviewState' in data:
                        webview_state = data['webviewState']
                        
                        # Parser le webviewState (JSON dans JSON)
                        if isinstance(webview_state, str):
                            webview_data = json.loads(webview_state)
                            
                            print(f"\n📋 CONTENU WEBVIEW STATE:")
                            for k, v in webview_data.items():
                                print(f"   🔸 {k}: {type(v).__name__}")
                            
                            # Extraire les conversations
                            if 'conversations' in webview_data:
                                conversations = webview_data['conversations']
                                
                                print(f"\n💬 CONVERSATIONS TROUVÉES: {len(conversations)}")
                                
                                # Examiner chaque conversation
                                for conv_id, conv_data in conversations.items():
                                    print(f"\n--- CONVERSATION {conv_id} ---")
                                    
                                    if isinstance(conv_data, dict):
                                        for k, v in conv_data.items():
                                            if k == 'messages' and isinstance(v, list):
                                                print(f"   📝 Messages: {len(v)}")
                                                
                                                # Chercher "tu avais raison finalement"
                                                for i, message in enumerate(v):
                                                    if isinstance(message, dict) and 'content' in message:
                                                        content = message.get('content', '')
                                                        if "tu avais raison finalement" in content.lower():
                                                            print(f"   🎯 PHRASE TROUVÉE dans le message {i}!")
                                                            
                                                            # Extraire toute la conversation
                                                            return extraire_et_formater_conversation(conversations, conv_id)
                                            else:
                                                print(f"   🔸 {k}: {type(v).__name__} - {str(v)[:50]}...")
                    
                    # Si pas trouvé dans webviewState, chercher ailleurs
                    print(f"\n🔍 RECHERCHE DANS TOUTE LA STRUCTURE...")
                    full_content = json.dumps(data, indent=2)
                    
                    if "tu avais raison finalement" in full_content.lower():
                        print(f"🎯 PHRASE TROUVÉE DANS LA STRUCTURE!")
                        
                        # Sauvegarder le JSON complet pour analyse
                        json_output = "C:/temp_conversation/augment_data_complete.json"
                        with open(json_output, 'w', encoding='utf-8') as f:
                            f.write(full_content)
                        
                        print(f"✅ Données complètes sauvées: {json_output}")
                        
                        # Extraire le contexte autour de la phrase
                        content_lower = full_content.lower()
                        pos = content_lower.find("tu avais raison finalement")
                        start = max(0, pos - 500)
                        end = min(len(full_content), pos + 1000)
                        context = full_content[start:end]
                        
                        print(f"\n📝 CONTEXTE AUTOUR DE LA PHRASE:")
                        print("=" * 80)
                        print(context)
                        print("=" * 80)
                        
                        return json_output, context
                
                except json.JSONDecodeError as e:
                    print(f"❌ Erreur parsing JSON: {e}")

                    # Afficher le contenu brut
                    print(f"📝 Contenu brut (premiers 1000 caractères):")
                    print("-" * 60)
                    print(str(value)[:1000])
                    print("-" * 60)

        # Si pas trouvé dans les clés augment, chercher dans toutes les valeurs
        print(f"\n🔍 RECHERCHE ÉTENDUE DANS TOUTES LES VALEURS...")
        cursor.execute("SELECT key, value FROM ItemTable;")
        all_rows = cursor.fetchall()

        for key, value in all_rows:
            try:
                # Convertir en string pour la recherche
                if isinstance(value, bytes):
                    value_str = value.decode('utf-8', errors='ignore')
                else:
                    value_str = str(value)

                # Chercher la phrase
                if "tu avais raison finalement" in value_str.lower():
                    print(f"\n🎯 PHRASE TROUVÉE DANS LA CLÉ: {key}")
                    print(f"📏 Taille: {len(value_str)} caractères")

                    # Extraire le contexte
                    content_lower = value_str.lower()
                    pos = content_lower.find("tu avais raison finalement")
                    start = max(0, pos - 300)
                    end = min(len(value_str), pos + 500)
                    context = value_str[start:end]

                    print(f"📝 CONTEXTE:")
                    print("=" * 80)
                    print(context)
                    print("=" * 80)

                    # Sauvegarder le contenu complet
                    output_file = f"C:/temp_conversation/conversation_trouvee_{key.replace('/', '_').replace('\\', '_')}.txt"
                    with open(output_file, 'w', encoding='utf-8') as f:
                        f.write(f"CLÉ: {key}\n")
                        f.write("=" * 80 + "\n")
                        f.write(value_str)

                    print(f"✅ Contenu sauvé: {output_file}")

                    conn.close()
                    return output_file, context

            except Exception as e:
                pass

        conn.close()
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return None, None

def extraire_et_formater_conversation(conversations, target_conv_id):
    """Extrait et formate une conversation spécifique"""
    
    print(f"\n🔧 FORMATAGE DE LA CONVERSATION {target_conv_id}")
    print("=" * 60)
    
    if target_conv_id not in conversations:
        print(f"❌ Conversation {target_conv_id} non trouvée")
        return None, None
    
    conv_data = conversations[target_conv_id]
    
    if 'messages' not in conv_data:
        print(f"❌ Pas de messages dans la conversation")
        return None, None
    
    messages = conv_data['messages']
    
    # Créer le contenu formaté
    formatted_lines = []
    formatted_lines.append("=" * 80)
    formatted_lines.append("CONVERSATION AUGMENT - EXTRACTION DEPUIS state.vscdb")
    formatted_lines.append("=" * 80)
    formatted_lines.append("")
    formatted_lines.append(f"💬 ID Conversation: {target_conv_id}")
    formatted_lines.append(f"📊 Nombre de messages: {len(messages)}")
    formatted_lines.append("")
    
    # Formater chaque message
    for i, message in enumerate(messages, 1):
        if isinstance(message, dict):
            role = message.get('role', 'unknown')
            content = message.get('content', '')
            timestamp = message.get('timestamp', 'N/A')
            
            # Déterminer l'icône selon le rôle
            if role.lower() in ['user', 'human']:
                icon = "👤 UTILISATEUR"
            elif role.lower() in ['assistant', 'ai']:
                icon = "🤖 ASSISTANT"
            else:
                icon = f"❓ {role.upper()}"
            
            formatted_lines.append(f"\n--- MESSAGE {i} ---")
            formatted_lines.append(f"{icon}")
            formatted_lines.append(f"🕒 Timestamp: {timestamp}")
            formatted_lines.append("-" * 60)
            formatted_lines.append(content)
            formatted_lines.append("-" * 60)
    
    formatted_lines.append("\n" + "=" * 80)
    formatted_lines.append("FIN DE LA CONVERSATION")
    formatted_lines.append("=" * 80)
    
    formatted_content = "\n".join(formatted_lines)
    
    # Sauvegarder la conversation formatée
    output_file = "C:/temp_conversation/conversation_augment_formatee.txt"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(formatted_content)
    
    print(f"✅ Conversation formatée sauvée: {output_file}")
    
    return output_file, formatted_content

if __name__ == "__main__":
    print("🚀 EXTRACTION DE LA CONVERSATION DEPUIS state.vscdb")
    print("=" * 60)
    
    result_file, content = extraire_conversation_vscdb()
    
    if result_file:
        print(f"\n🎉 CONVERSATION EXTRAITE AVEC SUCCÈS!")
        print(f"📄 Fichier: {result_file}")
    else:
        print(f"\n❌ ÉCHEC DE L'EXTRACTION")
