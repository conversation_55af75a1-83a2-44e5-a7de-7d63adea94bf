#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour rechercher dans la structure précise des conversations Augment
"""

import os
import json
from pathlib import Path

def search_conversations_precise_structure():
    """Recherche dans la structure précise des conversations"""
    
    base_path = Path("C:/Users/<USER>/AppData/Roaming/Code/User/workspaceStorage")
    
    print("🔍 RECHERCHE DANS LA STRUCTURE PRÉCISE DES CONVERSATIONS")
    print("=" * 70)
    print(f"📁 Base: {base_path}")
    
    found_conversations = []
    total_workspaces = 0
    total_conversations = 0
    total_files = 0
    
    # Explorer tous les workspaces
    for workspace_dir in base_path.iterdir():
        if workspace_dir.is_dir():
            total_workspaces += 1
            workspace_id = workspace_dir.name
            
            # Chemin vers les conversations
            conversations_path = workspace_dir / "Augment.vscode-augment" / "augment-user-assets" / "checkpoint-documents"
            
            if conversations_path.exists():
                print(f"\n📁 Workspace: {workspace_id}")
                
                # Explorer chaque conversation
                for conversation_dir in conversations_path.iterdir():
                    if conversation_dir.is_dir():
                        total_conversations += 1
                        conversation_id = conversation_dir.name
                        
                        print(f"  💬 Conversation: {conversation_id}")
                        
                        # Explorer les fichiers de la conversation
                        for file_path in conversation_dir.iterdir():
                            if file_path.is_file() and file_path.name.startswith("document-c__Users_Administrateur_Desktop_"):
                                total_files += 1
                                
                                print(f"    📄 Fichier: {file_path.name}")
                                
                                try:
                                    # Lire le fichier JSON
                                    with open(file_path, 'r', encoding='utf-8') as f:
                                        content = f.read()
                                    
                                    # Chercher "tu avais raison"
                                    if "tu avais raison" in content.lower():
                                        print(f"    🎉 'TU AVAIS RAISON' TROUVÉ!")
                                        
                                        # Parser le JSON pour extraire le contenu
                                        try:
                                            data = json.loads(content)
                                            
                                            # Extraire le contenu de la conversation
                                            original_code = data.get('originalCode', '')
                                            modified_code = data.get('modifiedCode', '')
                                            
                                            # Chercher dans les deux versions
                                            full_content = original_code + "\n" + modified_code
                                            
                                            if "tu avais raison" in full_content.lower():
                                                # Extraire le contexte
                                                content_lower = full_content.lower()
                                                start_pos = content_lower.find("tu avais raison")
                                                context_start = max(0, start_pos - 300)
                                                context_end = min(len(full_content), start_pos + 500)
                                                context = full_content[context_start:context_end]
                                                
                                                found_conversations.append({
                                                    'workspace_id': workspace_id,
                                                    'conversation_id': conversation_id,
                                                    'file_name': file_path.name,
                                                    'file_path': str(file_path),
                                                    'context': context,
                                                    'full_content': full_content
                                                })
                                                
                                                print(f"    📝 Contexte trouvé:")
                                                print("    " + "-" * 50)
                                                print("    " + context.replace('\n', '\n    '))
                                                print("    " + "-" * 50)
                                        
                                        except json.JSONDecodeError:
                                            print(f"    ⚠️ Erreur JSON, recherche dans le contenu brut")
                                            
                                            if "tu avais raison" in content.lower():
                                                content_lower = content.lower()
                                                start_pos = content_lower.find("tu avais raison")
                                                context_start = max(0, start_pos - 300)
                                                context_end = min(len(content), start_pos + 500)
                                                context = content[context_start:context_end]
                                                
                                                found_conversations.append({
                                                    'workspace_id': workspace_id,
                                                    'conversation_id': conversation_id,
                                                    'file_name': file_path.name,
                                                    'file_path': str(file_path),
                                                    'context': context,
                                                    'full_content': content
                                                })
                                                
                                                print(f"    📝 Contexte (brut):")
                                                print("    " + "-" * 50)
                                                print("    " + context.replace('\n', '\n    '))
                                                print("    " + "-" * 50)
                                
                                except Exception as e:
                                    print(f"    ❌ Erreur lecture: {e}")
    
    # Résumé final
    print(f"\n📊 RÉSUMÉ DE LA RECHERCHE:")
    print(f"📁 Workspaces explorés: {total_workspaces}")
    print(f"💬 Conversations trouvées: {total_conversations}")
    print(f"📄 Fichiers analysés: {total_files}")
    print(f"🎯 Conversations avec 'tu avais raison': {len(found_conversations)}")
    
    if found_conversations:
        print(f"\n✅ CONVERSATIONS TROUVÉES AVEC 'TU AVAIS RAISON':")
        for i, conv in enumerate(found_conversations, 1):
            print(f"\n--- CONVERSATION {i} ---")
            print(f"📁 Workspace: {conv['workspace_id']}")
            print(f"💬 Conversation: {conv['conversation_id']}")
            print(f"📄 Fichier: {conv['file_name']}")
            print(f"📝 Contexte:")
            print("-" * 60)
            print(conv['context'])
            print("-" * 60)
    else:
        print(f"\n❌ Aucune conversation trouvée avec 'tu avais raison'")
    
    return found_conversations

if __name__ == "__main__":
    print("🚀 RECHERCHE DANS LA STRUCTURE PRÉCISE DES CONVERSATIONS AUGMENT")
    print("=" * 70)
    
    results = search_conversations_precise_structure()
    
    if results:
        print(f"\n🎉 MISSION ACCOMPLIE ! {len(results)} CONVERSATIONS TROUVÉES !")
        
        # Chercher spécifiquement "finalement"
        finalement_found = []
        for conv in results:
            if "finalement" in conv['full_content'].lower():
                finalement_found.append(conv)
        
        if finalement_found:
            print(f"\n🎯 {len(finalement_found)} CONVERSATIONS AVEC 'FINALEMENT' AUSSI !")
            for conv in finalement_found:
                print(f"\n🔍 CONVERSATION AVEC 'TU AVAIS RAISON FINALEMENT':")
                print(f"📁 Workspace: {conv['workspace_id']}")
                print(f"💬 Conversation: {conv['conversation_id']}")
                print(f"📄 Fichier: {conv['file_name']}")
                
                # Extraire le contexte spécifique à "finalement"
                content_lower = conv['full_content'].lower()
                if "tu avais raison finalement" in content_lower:
                    start_pos = content_lower.find("tu avais raison finalement")
                    context_start = max(0, start_pos - 200)
                    context_end = min(len(conv['full_content']), start_pos + 400)
                    context = conv['full_content'][context_start:context_end]
                    
                    print(f"📝 Contexte 'TU AVAIS RAISON FINALEMENT':")
                    print("=" * 60)
                    print(context)
                    print("=" * 60)
    else:
        print(f"\n❌ AUCUNE CONVERSATION TROUVÉE")
        print("🤔 La phrase pourrait être dans un autre format ou emplacement...")
