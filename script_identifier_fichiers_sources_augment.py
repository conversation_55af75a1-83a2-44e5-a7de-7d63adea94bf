#!/usr/bin/env python3
"""
IDENTIFICATION DES FICHIERS SOURCES AUGMENT
===========================================

Ce script identifie les vrais fichiers sources (exécutables, extensions, modules)
qui génèrent les fichiers de conversation, pour une modification permanente.

Objectif: Modifier les sources pour que TOUS les nouveaux projets aient 
automatiquement le logging de conversation.
"""

import os
import subprocess
from pathlib import Path
import json
import psutil

class AugmentSourceIdentifier:
    def __init__(self):
        self.vscode_paths = []
        self.augment_extensions = []
        self.process_info = []
        self.executable_files = []
        
    def find_vscode_installations(self):
        """Trouve toutes les installations VSCode"""
        print("🔍 RECHERCHE DES INSTALLATIONS VSCODE")
        print("=" * 50)
        
        # Chemins typiques VSCode
        potential_paths = [
            Path("C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code"),
            Path("C:/Program Files/Microsoft VS Code"),
            Path("C:/Program Files (x86)/Microsoft VS Code"),
            Path("C:/Users/<USER>/AppData/Roaming/Code"),
            Path("C:/Users/<USER>/.vscode"),
        ]
        
        for path in potential_paths:
            if path.exists():
                self.vscode_paths.append(path)
                print(f"✅ VSCode trouvé: {path}")
                
                # Lister les fichiers importants
                if path.name == "Microsoft VS Code":
                    exe_file = path / "Code.exe"
                    if exe_file.exists():
                        print(f"  📁 Exécutable principal: {exe_file}")
                        self.executable_files.append(exe_file)
        
        return len(self.vscode_paths)
    
    def find_augment_extensions(self):
        """Trouve les extensions Augment installées"""
        print(f"\n🔍 RECHERCHE DES EXTENSIONS AUGMENT")
        print("=" * 50)
        
        # Répertoires d'extensions VSCode
        extension_dirs = [
            Path("C:/Users/<USER>/.vscode/extensions"),
            Path("C:/Users/<USER>/AppData/Roaming/Code/User/extensions"),
        ]
        
        for ext_dir in extension_dirs:
            if ext_dir.exists():
                print(f"📁 Répertoire d'extensions: {ext_dir}")
                
                # Chercher les extensions Augment
                for item in ext_dir.iterdir():
                    if item.is_dir() and 'augment' in item.name.lower():
                        self.augment_extensions.append(item)
                        print(f"✅ Extension Augment trouvée: {item.name}")
                        
                        # Analyser le contenu de l'extension
                        self.analyze_extension(item)
        
        return len(self.augment_extensions)
    
    def analyze_extension(self, extension_path):
        """Analyse une extension Augment"""
        print(f"  🔍 Analyse de {extension_path.name}:")
        
        # Fichiers importants à chercher
        important_files = [
            "package.json",
            "extension.js",
            "main.js", 
            "index.js",
            "out/extension.js",
            "dist/extension.js"
        ]
        
        for file_name in important_files:
            file_path = extension_path / file_name
            if file_path.exists():
                print(f"    📄 {file_name} ({file_path.stat().st_size} bytes)")
                
                # Analyser package.json pour plus d'infos
                if file_name == "package.json":
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            package_data = json.load(f)
                            print(f"      Version: {package_data.get('version', 'N/A')}")
                            print(f"      Publisher: {package_data.get('publisher', 'N/A')}")
                            print(f"      Main: {package_data.get('main', 'N/A')}")
                    except Exception as e:
                        print(f"      Erreur lecture package.json: {e}")
    
    def find_augment_processes(self):
        """Identifie les processus Augment en cours"""
        print(f"\n🔍 PROCESSUS AUGMENT ACTIFS")
        print("=" * 50)
        
        augment_processes = []
        
        for proc in psutil.process_iter(['pid', 'name', 'exe', 'cmdline']):
            try:
                proc_info = proc.info
                proc_name = proc_info['name'].lower()
                proc_exe = proc_info.get('exe', '')
                cmdline = proc_info.get('cmdline', [])
                proc_cmdline = ' '.join(cmdline).lower() if cmdline else ''
                
                # Chercher les processus liés à Augment
                if ('augment' in proc_name or
                    (proc_exe and 'augment' in proc_exe.lower()) or
                    'augment' in proc_cmdline):
                    
                    augment_processes.append(proc_info)
                    print(f"✅ Processus Augment: PID {proc_info['pid']} - {proc_info['name']}")
                    if proc_exe:
                        print(f"   📁 Exécutable: {proc_exe}")
                        self.executable_files.append(Path(proc_exe))
                    
                    # Analyser la ligne de commande
                    if proc_info.get('cmdline'):
                        cmdline_str = ' '.join(proc_info['cmdline'])
                        if len(cmdline_str) > 100:
                            cmdline_str = cmdline_str[:100] + "..."
                        print(f"   💻 Commande: {cmdline_str}")
                        
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        self.process_info = augment_processes
        return len(augment_processes)
    
    def find_node_modules(self):
        """Cherche les modules Node.js d'Augment"""
        print(f"\n🔍 MODULES NODE.JS AUGMENT")
        print("=" * 50)
        
        # Chercher dans les node_modules
        search_paths = [
            Path("C:/Users/<USER>/AppData/Roaming/npm/node_modules"),
            Path("C:/Users/<USER>/AppData/Local/npm/node_modules"),
        ]
        
        # Ajouter les chemins des extensions trouvées
        for ext_path in self.augment_extensions:
            node_modules = ext_path / "node_modules"
            if node_modules.exists():
                search_paths.append(node_modules)
        
        augment_modules = []
        
        for search_path in search_paths:
            if search_path.exists():
                print(f"📁 Recherche dans: {search_path}")
                
                for item in search_path.iterdir():
                    if item.is_dir() and 'augment' in item.name.lower():
                        augment_modules.append(item)
                        print(f"✅ Module Augment: {item.name}")
                        
                        # Chercher les fichiers JavaScript principaux
                        js_files = list(item.glob("**/*.js"))
                        if js_files:
                            print(f"   📄 {len(js_files)} fichiers JavaScript")
                            for js_file in js_files[:3]:  # Limiter l'affichage
                                print(f"     - {js_file.name}")
        
        return augment_modules
    
    def find_electron_app_files(self):
        """Cherche les fichiers de l'application Electron Augment"""
        print(f"\n🔍 FICHIERS APPLICATION ELECTRON")
        print("=" * 50)
        
        # Chercher dans les répertoires typiques d'applications Electron
        electron_paths = [
            Path("C:/Users/<USER>/AppData/Local/Programs"),
            Path("C:/Program Files"),
            Path("C:/Program Files (x86)"),
        ]
        
        augment_apps = []
        
        for base_path in electron_paths:
            if base_path.exists():
                for item in base_path.iterdir():
                    if item.is_dir() and 'augment' in item.name.lower():
                        augment_apps.append(item)
                        print(f"✅ Application Augment: {item}")
                        
                        # Chercher les fichiers importants
                        important_files = ["app.asar", "main.js", "package.json", "*.exe"]
                        for pattern in important_files:
                            if '*' in pattern:
                                files = list(item.glob(pattern))
                            else:
                                files = [item / pattern] if (item / pattern).exists() else []
                            
                            for file_path in files:
                                if file_path.exists():
                                    print(f"   📄 {file_path.name} ({file_path.stat().st_size} bytes)")
                                    if file_path.suffix == '.exe':
                                        self.executable_files.append(file_path)
        
        return augment_apps
    
    def analyze_registry_entries(self):
        """Analyse les entrées de registre pour Augment"""
        print(f"\n🔍 ENTRÉES REGISTRE AUGMENT")
        print("=" * 50)
        
        try:
            # Chercher dans le registre Windows
            registry_commands = [
                'reg query "HKEY_CURRENT_USER\\Software" /s /f "augment" 2>nul',
                'reg query "HKEY_LOCAL_MACHINE\\Software" /s /f "augment" 2>nul',
            ]
            
            for cmd in registry_commands:
                try:
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
                    if result.returncode == 0 and result.stdout.strip():
                        print(f"✅ Entrées registre trouvées:")
                        lines = result.stdout.strip().split('\n')
                        for line in lines[:5]:  # Limiter l'affichage
                            if line.strip():
                                print(f"   {line.strip()}")
                        if len(lines) > 5:
                            print(f"   ... et {len(lines)-5} autres entrées")
                        break
                except subprocess.TimeoutExpired:
                    print("⚠️ Timeout recherche registre")
                except Exception as e:
                    print(f"⚠️ Erreur recherche registre: {e}")
                    
        except Exception as e:
            print(f"❌ Erreur analyse registre: {e}")
    
    def generate_modification_strategy(self):
        """Génère une stratégie de modification des fichiers sources"""
        print(f"\n🎯 STRATÉGIE DE MODIFICATION DES SOURCES")
        print("=" * 60)
        
        print(f"📊 RÉSUMÉ DES DÉCOUVERTES:")
        print(f"  - Installations VSCode: {len(self.vscode_paths)}")
        print(f"  - Extensions Augment: {len(self.augment_extensions)}")
        print(f"  - Processus Augment: {len(self.process_info)}")
        print(f"  - Fichiers exécutables: {len(self.executable_files)}")
        
        print(f"\n🔧 FICHIERS SOURCES À MODIFIER:")
        
        # Extensions (priorité haute)
        if self.augment_extensions:
            print(f"\n1. EXTENSIONS AUGMENT (Priorité: HAUTE)")
            for ext in self.augment_extensions:
                print(f"   📁 {ext}")
                # Fichiers JavaScript à modifier
                js_files = list(ext.glob("**/*.js"))
                for js_file in js_files:
                    if js_file.stat().st_size > 1000:  # Fichiers significatifs
                        print(f"     🔧 À modifier: {js_file}")
        
        # Exécutables (priorité moyenne)
        if self.executable_files:
            print(f"\n2. EXÉCUTABLES (Priorité: MOYENNE)")
            unique_exes = list(set(self.executable_files))
            for exe in unique_exes:
                print(f"   🔧 {exe}")
        
        print(f"\n💡 APPROCHES RECOMMANDÉES:")
        print(f"  1. Modifier les extensions JavaScript pour ajouter le logging")
        print(f"  2. Créer un hook dans les modules Node.js")
        print(f"  3. Patcher les exécutables si nécessaire")
        print(f"  4. Utiliser l'injection de code dans les processus")
        
        return {
            'extensions': self.augment_extensions,
            'executables': self.executable_files,
            'processes': self.process_info
        }
    
    def run_complete_analysis(self):
        """Exécute l'analyse complète"""
        print(f"🎯 IDENTIFICATION COMPLÈTE DES SOURCES AUGMENT")
        print(f"=" * 70)
        
        self.find_vscode_installations()
        self.find_augment_extensions()
        self.find_augment_processes()
        self.find_node_modules()
        self.find_electron_app_files()
        self.analyze_registry_entries()
        
        return self.generate_modification_strategy()

def main():
    """Fonction principale"""
    identifier = AugmentSourceIdentifier()
    strategy = identifier.run_complete_analysis()
    
    # Sauvegarder les résultats
    results_file = "augment_sources_analysis.json"
    try:
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump({
                'timestamp': '2025-06-04T12:45:00',
                'vscode_paths': [str(p) for p in identifier.vscode_paths],
                'augment_extensions': [str(p) for p in identifier.augment_extensions],
                'executable_files': [str(p) for p in identifier.executable_files],
                'process_info': identifier.process_info
            }, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Résultats sauvegardés dans: {results_file}")
    except Exception as e:
        print(f"⚠️ Erreur sauvegarde: {e}")

if __name__ == "__main__":
    main()
