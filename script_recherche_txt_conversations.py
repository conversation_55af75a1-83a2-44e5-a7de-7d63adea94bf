#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour rechercher spécifiquement les fichiers .txt (conversations) dans les noms
"""

import os
import json
from pathlib import Path

def search_txt_conversations():
    """Recherche spécifiquement les fichiers avec .txt dans le nom (conversations)"""
    
    base_path = Path("C:/Users/<USER>/AppData/Roaming/Code/User/workspaceStorage")
    
    print("🔍 RECHERCHE SPÉCIFIQUE DES FICHIERS .txt (CONVERSATIONS)")
    print("=" * 70)
    print(f"📁 Base: {base_path}")
    
    found_txt_files = []
    total_workspaces = 0
    total_conversations = 0
    total_txt_files = 0
    
    # Explorer tous les workspaces
    for workspace_dir in base_path.iterdir():
        if workspace_dir.is_dir():
            total_workspaces += 1
            workspace_id = workspace_dir.name
            
            # Chemin vers les conversations
            conversations_path = workspace_dir / "Augment.vscode-augment" / "augment-user-assets" / "checkpoint-documents"
            
            if conversations_path.exists():
                print(f"\n📁 Workspace: {workspace_id}")
                
                # Explorer chaque conversation
                for conversation_dir in conversations_path.iterdir():
                    if conversation_dir.is_dir():
                        total_conversations += 1
                        conversation_id = conversation_dir.name
                        
                        print(f"  💬 Conversation: {conversation_id}")
                        
                        # Explorer les fichiers de la conversation
                        for file_path in conversation_dir.iterdir():
                            if file_path.is_file() and file_path.name.startswith("document-c__Users_Administrateur_Desktop_"):
                                
                                # FILTRER SPÉCIFIQUEMENT LES FICHIERS .txt DANS LE NOM
                                if ".txt-" in file_path.name and not ".py-" in file_path.name:
                                    total_txt_files += 1
                                    
                                    print(f"    📄 Fichier .txt trouvé: {file_path.name}")
                                    
                                    try:
                                        # Lire le fichier JSON
                                        with open(file_path, 'r', encoding='utf-8') as f:
                                            content = f.read()
                                        
                                        # Parser le JSON pour extraire le contenu
                                        try:
                                            data = json.loads(content)
                                            
                                            # Extraire le contenu de la conversation
                                            original_code = data.get('originalCode', '')
                                            modified_code = data.get('modifiedCode', '')
                                            
                                            # Chercher dans les deux versions
                                            full_content = original_code + "\n" + modified_code
                                            
                                            print(f"    📏 Taille contenu: {len(full_content)} caractères")
                                            
                                            # Chercher "tu avais raison"
                                            if "tu avais raison" in full_content.lower():
                                                print(f"    🎉 'TU AVAIS RAISON' TROUVÉ!")
                                                
                                                # Extraire le contexte
                                                content_lower = full_content.lower()
                                                start_pos = content_lower.find("tu avais raison")
                                                context_start = max(0, start_pos - 300)
                                                context_end = min(len(full_content), start_pos + 500)
                                                context = full_content[context_start:context_end]
                                                
                                                found_txt_files.append({
                                                    'workspace_id': workspace_id,
                                                    'conversation_id': conversation_id,
                                                    'file_name': file_path.name,
                                                    'file_path': str(file_path),
                                                    'context': context,
                                                    'full_content': full_content
                                                })
                                                
                                                print(f"    📝 Contexte trouvé:")
                                                print("    " + "-" * 50)
                                                print("    " + context.replace('\n', '\n    '))
                                                print("    " + "-" * 50)
                                            else:
                                                # Afficher un aperçu même sans "tu avais raison"
                                                print(f"    📝 Aperçu (premiers 200 caractères):")
                                                print("    " + full_content[:200].replace('\n', '\n    '))
                                                if len(full_content) > 200:
                                                    print("    ...")
                                        
                                        except json.JSONDecodeError:
                                            print(f"    ⚠️ Erreur JSON, contenu brut:")
                                            print("    " + content[:200].replace('\n', '\n    '))
                                
                                    except Exception as e:
                                        print(f"    ❌ Erreur lecture: {e}")
    
    # Résumé final
    print(f"\n📊 RÉSUMÉ DE LA RECHERCHE:")
    print(f"📁 Workspaces explorés: {total_workspaces}")
    print(f"💬 Conversations trouvées: {total_conversations}")
    print(f"📄 Fichiers .txt analysés: {total_txt_files}")
    print(f"🎯 Fichiers .txt avec 'tu avais raison': {len(found_txt_files)}")
    
    if found_txt_files:
        print(f"\n✅ FICHIERS .txt TROUVÉS AVEC 'TU AVAIS RAISON':")
        for i, txt_file in enumerate(found_txt_files, 1):
            print(f"\n--- FICHIER .txt {i} ---")
            print(f"📁 Workspace: {txt_file['workspace_id']}")
            print(f"💬 Conversation: {txt_file['conversation_id']}")
            print(f"📄 Fichier: {txt_file['file_name']}")
            print(f"📝 Contexte:")
            print("-" * 60)
            print(txt_file['context'])
            print("-" * 60)
            
            # Chercher spécifiquement "finalement"
            if "finalement" in txt_file['full_content'].lower():
                print(f"🎯 CONTIENT AUSSI 'FINALEMENT'!")
                
                # Extraire le contexte spécifique à "tu avais raison finalement"
                content_lower = txt_file['full_content'].lower()
                if "tu avais raison finalement" in content_lower:
                    start_pos = content_lower.find("tu avais raison finalement")
                    context_start = max(0, start_pos - 200)
                    context_end = min(len(txt_file['full_content']), start_pos + 400)
                    context = txt_file['full_content'][context_start:context_end]
                    
                    print(f"📝 Contexte 'TU AVAIS RAISON FINALEMENT':")
                    print("=" * 60)
                    print(context)
                    print("=" * 60)
    else:
        print(f"\n❌ Aucun fichier .txt trouvé avec 'tu avais raison'")
    
    return found_txt_files

if __name__ == "__main__":
    print("🚀 RECHERCHE SPÉCIFIQUE DES CONVERSATIONS .txt")
    print("=" * 70)
    
    results = search_txt_conversations()
    
    if results:
        print(f"\n🎉 MISSION ACCOMPLIE ! {len(results)} FICHIERS .txt TROUVÉS !")
    else:
        print(f"\n❌ AUCUN FICHIER .txt TROUVÉ AVEC 'TU AVAIS RAISON'")
        print("🤔 La conversation pourrait être dans un autre format ou emplacement...")
