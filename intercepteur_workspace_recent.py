#!/usr/bin/env python3
"""
INTERCEPTEUR WORKSPACE LE PLUS RÉCENT
=====================================

Version améliorée qui détecte et surveille AUTOMATIQUEMENT
le workspace VSCode le plus récemment modifié.

FONCTIONNALITÉS:
- Détection automatique du workspace le plus récent
- Surveillance dynamique qui suit les changements de workspace
- Écriture temps réel dans le projet actuel
- Aucune configuration manuelle requise
"""

import os
import sqlite3
import json
import time
import datetime
import threading
from pathlib import Path

class AutoRecentWorkspaceInterceptor:
    def __init__(self):
        self.project_dir = Path(__file__).parent.absolute()
        self.output_file = self.project_dir / "augment_conversation_live_recent.txt"
        
        # Surveillance dynamique
        self.current_workspace = None
        self.current_state_file = None
        self.last_message_count = 0
        self.running = False
        
        # Répertoire de base des workspaces
        self.workspace_base = Path("C:/Users/<USER>/AppData/Roaming/Code/User/workspaceStorage")
        
        print(f"🎯 INTERCEPTEUR WORKSPACE LE PLUS RÉCENT")
        print(f"=" * 60)
        print(f"📁 Projet: {self.project_dir}")
        print(f"📝 Sortie: {self.output_file}")
        print(f"🔍 Base workspaces: {self.workspace_base}")
        
        # Initialiser le fichier de sortie
        self.initialize_output_file()
        
        # Détecter le workspace initial
        self.detect_most_recent_workspace()
    
    def find_all_augment_workspaces(self):
        """Trouve tous les workspaces contenant des conversations Augment"""
        augment_workspaces = []
        
        if not self.workspace_base.exists():
            print(f"❌ Répertoire workspaces non trouvé: {self.workspace_base}")
            return augment_workspaces
        
        for workspace_dir in self.workspace_base.iterdir():
            if workspace_dir.is_dir():
                state_file = workspace_dir / "state.vscdb"
                if state_file.exists():
                    # Vérifier si contient des conversations Augment
                    if self.has_augment_conversations(state_file):
                        mtime = state_file.stat().st_mtime
                        augment_workspaces.append({
                            'id': workspace_dir.name,
                            'path': workspace_dir,
                            'state_file': state_file,
                            'mtime': mtime,
                            'last_modified': datetime.datetime.fromtimestamp(mtime)
                        })
        
        # Trier par date de modification (plus récent en premier)
        augment_workspaces.sort(key=lambda x: x['mtime'], reverse=True)
        
        return augment_workspaces
    
    def has_augment_conversations(self, state_file):
        """Vérifie si un workspace contient des conversations Augment"""
        try:
            conn = sqlite3.connect(str(state_file))
            cursor = conn.cursor()
            cursor.execute("SELECT key FROM ItemTable WHERE key = 'memento/webviewView.augment-chat'")
            result = cursor.fetchone()
            conn.close()
            return result is not None
        except Exception:
            return False
    
    def detect_most_recent_workspace(self):
        """Détecte automatiquement le workspace le plus récent"""
        workspaces = self.find_all_augment_workspaces()
        
        if not workspaces:
            print(f"❌ Aucun workspace Augment trouvé")
            return False
        
        # Prendre le plus récent
        most_recent = workspaces[0]
        
        # Vérifier si c'est un changement
        if self.current_workspace != most_recent['id']:
            self.current_workspace = most_recent['id']
            self.current_state_file = most_recent['state_file']
            self.last_message_count = 0  # Reset pour nouveau workspace
            
            print(f"🔄 WORKSPACE LE PLUS RÉCENT DÉTECTÉ:")
            print(f"   📁 ID: {most_recent['id'][:16]}...")
            print(f"   📄 State file: {most_recent['state_file']}")
            print(f"   🕒 Modifié: {most_recent['last_modified'].strftime('%H:%M:%S')}")
            
            # Obtenir l'état initial
            self.get_initial_state()
            
            return True
        
        return False
    
    def get_initial_state(self):
        """Obtient l'état initial du workspace actuel"""
        try:
            conversation_data = self.extract_conversation_data()
            if conversation_data:
                conversations = conversation_data.get('conversations', {})
                current_conv_id = conversation_data.get('currentConversationId')
                
                if current_conv_id and current_conv_id in conversations:
                    chat_history = conversations[current_conv_id].get('chatHistory', [])
                    self.last_message_count = len(chat_history)
                    
                    print(f"📊 État initial: {self.last_message_count} messages")
        except Exception as e:
            print(f"⚠️ Erreur état initial: {e}")
    
    def extract_conversation_data(self):
        """Extrait les données de conversation du workspace actuel"""
        try:
            if not self.current_state_file or not self.current_state_file.exists():
                return None
            
            conn = sqlite3.connect(str(self.current_state_file))
            cursor = conn.cursor()
            
            cursor.execute("SELECT value FROM ItemTable WHERE key = 'memento/webviewView.augment-chat'")
            result = cursor.fetchone()
            
            if not result:
                conn.close()
                return None
            
            value = result[0]
            if isinstance(value, bytes):
                value_str = value.decode('utf-8')
            else:
                value_str = str(value)
            
            main_data = json.loads(value_str)
            webview_data = json.loads(main_data['webviewState'])
            
            conn.close()
            return webview_data
            
        except Exception as e:
            return None
    
    def process_workspace_changes(self):
        """Traite les changements dans le workspace actuel"""
        try:
            conversation_data = self.extract_conversation_data()
            
            if conversation_data:
                conversations = conversation_data.get('conversations', {})
                current_conv_id = conversation_data.get('currentConversationId')
                
                if current_conv_id and current_conv_id in conversations:
                    chat_history = conversations[current_conv_id].get('chatHistory', [])
                    
                    if len(chat_history) > self.last_message_count:
                        new_messages = chat_history[self.last_message_count:]
                        self.write_new_messages(new_messages)
                        self.last_message_count = len(chat_history)
                        
                        print(f"⚡ {len(new_messages)} nouveaux messages - Workspace {self.current_workspace[:8]}...")
        
        except Exception as e:
            print(f"⚠️ Erreur traitement: {e}")
    
    def write_new_messages(self, new_messages):
        """Écrit les nouveaux messages dans le fichier de sortie"""
        timestamp = datetime.datetime.now().isoformat()
        
        with open(self.output_file, 'a', encoding='utf-8') as f:
            for i, message_data in enumerate(new_messages):
                msg_index = self.last_message_count + i + 1
                
                f.write(f"[{timestamp}] MESSAGE #{msg_index} - WORKSPACE RÉCENT\n")
                f.write(f"Workspace: {self.current_workspace[:16]}...\n")
                f.write("-" * 60 + "\n")
                
                # Message utilisateur
                if 'request_message' in message_data and message_data['request_message']:
                    f.write(f"👤 UTILISATEUR:\n{message_data['request_message']}\n\n")
                
                # Réponse assistant
                if 'structured_output_nodes' in message_data:
                    for node in message_data['structured_output_nodes']:
                        if node.get('type') == 0 and node.get('content'):
                            f.write(f"🤖 ASSISTANT:\n{node['content']}\n\n")
                            break
                
                # Outils utilisés
                tools_used = []
                if 'structured_output_nodes' in message_data:
                    for node in message_data['structured_output_nodes']:
                        if node.get('type') == 5 and node.get('tool_use'):
                            tool_info = node['tool_use']
                            tools_used.append(tool_info.get('tool_name', 'unknown'))
                
                if tools_used:
                    f.write(f"🔧 OUTILS: {', '.join(tools_used)}\n\n")
                
                f.write("=" * 80 + "\n\n")
            
            f.flush()
            os.fsync(f.fileno())
    
    def initialize_output_file(self):
        """Initialise le fichier de sortie"""
        timestamp = datetime.datetime.now().isoformat()
        
        with open(self.output_file, 'w', encoding='utf-8') as f:
            f.write("AUGMENT CONVERSATION - WORKSPACE LE PLUS RÉCENT\n")
            f.write(f"Démarré: {timestamp}\n")
            f.write(f"Projet: {self.project_dir}\n")
            f.write("Surveillance automatique du workspace le plus récemment modifié\n")
            f.write("=" * 80 + "\n\n")
    
    def start_dynamic_monitoring(self):
        """Démarre la surveillance dynamique"""
        print(f"\n🚀 SURVEILLANCE DYNAMIQUE DÉMARRÉE")
        print(f"🔄 Détection automatique du workspace le plus récent")
        print(f"⚡ Surveillance temps réel activée")
        
        self.running = True
        
        def monitoring_loop():
            last_workspace_check = 0
            
            while self.running:
                current_time = time.time()
                
                # Vérifier le workspace le plus récent toutes les 5 secondes
                if current_time - last_workspace_check > 5:
                    self.detect_most_recent_workspace()
                    last_workspace_check = current_time
                
                # Surveiller les changements dans le workspace actuel
                if self.current_state_file and self.current_state_file.exists():
                    self.process_workspace_changes()
                
                time.sleep(0.1)  # 100ms entre les vérifications
        
        # Démarrer en arrière-plan
        thread = threading.Thread(target=monitoring_loop, daemon=True)
        thread.start()
        
        return thread
    
    def stop_monitoring(self):
        """Arrête la surveillance"""
        self.running = False
        print(f"🛑 Surveillance arrêtée")

def main():
    """Fonction principale"""
    interceptor = AutoRecentWorkspaceInterceptor()
    
    monitoring_thread = interceptor.start_dynamic_monitoring()
    
    try:
        print(f"\n💬 Écrivez dans Augment pour voir la capture automatique...")
        print(f"🔄 Le système suit automatiquement le workspace le plus récent")
        print(f"⏹️  Ctrl+C pour arrêter")
        
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print(f"\n🛑 Arrêt de la surveillance automatique")
        interceptor.stop_monitoring()

if __name__ == "__main__":
    main()
