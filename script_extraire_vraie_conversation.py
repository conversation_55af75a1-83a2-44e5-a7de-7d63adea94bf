#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour extraire la vraie conversation du fichier JSON
"""

import json
import re
from pathlib import Path

def extraire_vraie_conversation():
    """Extrait la vraie conversation du fichier JSON"""
    
    file_path = "C:/temp_conversation/conversation_specifique.json"
    
    print("🔍 EXTRACTION DE LA VRAIE CONVERSATION")
    print("=" * 60)
    
    try:
        # Lire le fichier JSON
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        data = json.loads(content)
        original_code = data.get('originalCode', '')
        modified_code = data.get('modifiedCode', '')
        combined_content = original_code + "\n" + modified_code
        
        print(f"📏 Contenu total: {len(combined_content)} caractères")
        
        # Chercher le marqueur de début de conversation
        conversation_start = None
        
        # Patterns possibles pour le début de conversation
        start_patterns = [
            "<<HUMAN_CONVERSATION_START>>",
            "Human:",
            "User:",
            "# Role",
            "Answer the user's request"
        ]
        
        for pattern in start_patterns:
            if pattern in combined_content:
                start_pos = combined_content.find(pattern)
                print(f"✅ Marqueur trouvé: '{pattern}' à la position {start_pos}")
                conversation_start = start_pos
                break
        
        if conversation_start is None:
            # Si pas de marqueur spécial, chercher autour de "tu avais raison finalement"
            phrase_pos = combined_content.lower().find("tu avais raison finalement")
            if phrase_pos != -1:
                print(f"🎯 Phrase trouvée à la position {phrase_pos}")
                # Prendre un large contexte autour
                conversation_start = max(0, phrase_pos - 2000)
                print(f"📍 Début de contexte à la position {conversation_start}")
        
        if conversation_start is not None:
            # Extraire la conversation
            conversation_content = combined_content[conversation_start:]
            
            print(f"📝 CONTENU DE LA CONVERSATION (premiers 2000 caractères):")
            print("=" * 80)
            print(conversation_content[:2000])
            print("=" * 80)
            
            # Sauvegarder la conversation brute
            raw_output = "C:/temp_conversation/conversation_brute.txt"
            with open(raw_output, 'w', encoding='utf-8') as f:
                f.write(conversation_content)
            
            print(f"✅ Conversation brute sauvée: {raw_output}")
            
            # Analyser et formater la conversation
            formatted_conversation = formater_conversation(conversation_content)
            
            # Sauvegarder la conversation formatée
            formatted_output = "C:/temp_conversation/conversation_formatee.txt"
            with open(formatted_output, 'w', encoding='utf-8') as f:
                f.write(formatted_conversation)
            
            print(f"✅ Conversation formatée sauvée: {formatted_output}")
            
            return conversation_content, formatted_conversation
        else:
            print("❌ Aucun début de conversation trouvé")
            return None, None
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return None, None

def formater_conversation(content):
    """Formate la conversation pour la rendre lisible"""
    
    print(f"\n🔧 FORMATAGE DE LA CONVERSATION")
    print("-" * 40)
    
    lines = content.split('\n')
    formatted_lines = []
    
    # En-tête
    formatted_lines.append("=" * 80)
    formatted_lines.append("CONVERSATION AUGMENT - EXTRACTION ET FORMATAGE")
    formatted_lines.append("=" * 80)
    formatted_lines.append("")
    
    in_conversation = False
    current_speaker = None
    message_content = []
    
    for line in lines:
        line = line.strip()
        
        # Détecter les changements de locuteur
        if line.startswith("Human:") or line.startswith("User:"):
            # Sauvegarder le message précédent
            if current_speaker and message_content:
                formatted_lines.append(f"\n{current_speaker}")
                formatted_lines.append("-" * 40)
                formatted_lines.extend(message_content)
                formatted_lines.append("")
            
            current_speaker = "👤 UTILISATEUR"
            message_content = []
            in_conversation = True
            
            # Ajouter le contenu après "Human:"
            content_after = line[6:].strip() if line.startswith("Human:") else line[5:].strip()
            if content_after:
                message_content.append(content_after)
                
        elif line.startswith("Assistant:") or line.startswith("AI:"):
            # Sauvegarder le message précédent
            if current_speaker and message_content:
                formatted_lines.append(f"\n{current_speaker}")
                formatted_lines.append("-" * 40)
                formatted_lines.extend(message_content)
                formatted_lines.append("")
            
            current_speaker = "🤖 ASSISTANT"
            message_content = []
            in_conversation = True
            
            # Ajouter le contenu après "Assistant:"
            content_after = line[10:].strip() if line.startswith("Assistant:") else line[3:].strip()
            if content_after:
                message_content.append(content_after)
                
        elif in_conversation and line:
            # Continuer le message en cours
            message_content.append(line)
    
    # Sauvegarder le dernier message
    if current_speaker and message_content:
        formatted_lines.append(f"\n{current_speaker}")
        formatted_lines.append("-" * 40)
        formatted_lines.extend(message_content)
        formatted_lines.append("")
    
    # Si pas de structure Human:/Assistant: détectée, afficher le contenu brut
    if not in_conversation:
        formatted_lines.append("📝 CONTENU BRUT (aucune structure de conversation détectée)")
        formatted_lines.append("-" * 60)
        formatted_lines.extend(lines)
    
    # Pied de page
    formatted_lines.append("\n" + "=" * 80)
    formatted_lines.append("FIN DE LA CONVERSATION")
    formatted_lines.append("=" * 80)
    
    return "\n".join(formatted_lines)

if __name__ == "__main__":
    print("🚀 EXTRACTION DE LA VRAIE CONVERSATION")
    print("=" * 60)
    
    raw_content, formatted_content = extraire_vraie_conversation()
    
    if raw_content:
        print(f"\n🎉 CONVERSATION EXTRAITE AVEC SUCCÈS!")
        print(f"📄 Fichiers créés:")
        print(f"   📝 Brut: C:/temp_conversation/conversation_brute.txt")
        print(f"   📋 Formaté: C:/temp_conversation/conversation_formatee.txt")
    else:
        print(f"\n❌ ÉCHEC DE L'EXTRACTION")
