import{R as c,a as i}from"./types-LfaCSdmF.js";import{C as m,a as M}from"./chat-types-NgqNgjwU.js";function T(e){return function(t){try{if(isNaN(t.getTime()))return"Unknown time";const n=new Date().getTime()-t.getTime(),a=Math.floor(n/1e3),r=Math.floor(a/60),s=Math.floor(r/60),o=Math.floor(s/24);return a<60?`${a}s ago`:r<60?`${r}m ago`:s<24?`${s}h ago`:o<30?`${o}d ago`:t.toLocaleDateString()}catch(n){return console.error("Error formatting date:",n),"Unknown time"}}(new Date(e))}function x(e,t){const n=setInterval(()=>{const a=e.getTime()-Date.now();if(a<=0)return void clearInterval(n);const r=Math.floor(a/1e3),s=Math.floor(r/60),o=Math.floor(s/60),u=Math.floor(o/24);t(r<60?`${r}s`:s<60?`${s}m ${r%60}s`:o<24?`${o}h`:u<30?`${u}d`:"1mo")},1e3);return()=>clearInterval(n)}function I(e){if(e===void 0)return"neutral";switch(e){case c.agentPending:case c.agentStarting:case c.agentRunning:return"info";case c.agentIdle:return"success";case c.agentFailed:return"error";default:return"neutral"}}function P(e){if(e===void 0)return"neutral";switch(e){case i.workspaceRunning:return"info";case i.workspacePausing:case i.workspacePaused:case i.workspaceResuming:default:return"neutral"}}function S(e){switch(e){case c.agentStarting:return"Starting";case c.agentRunning:return"Running";case c.agentIdle:return"Idle";case c.agentPending:return"Pending";case c.agentFailed:return"Failed";default:return"Unknown"}}function $(e){switch(e){case i.workspaceRunning:return"Running";case i.workspacePausing:return"Pausing";case i.workspacePaused:return"Paused";case i.workspaceResuming:return"Resuming";default:return"Unknown"}}const l=e=>{let t={};for(const n of e){const a=t[n.new_path];t[n.new_path]=a?{...a,new_contents:n.new_contents,new_path:n.new_path}:n}return Object.values(t).filter(n=>!n.old_path||!n.new_path||n.old_path!==n.new_path||n.old_contents!==n.new_contents)},v=e=>{const t=e.flatMap(n=>n.changed_files);return l(t)},E=(e,t)=>{var a;const n=w(e,t);return((a=e[n])==null?void 0:a.exchange.request_message)??""},w=(e,t)=>{var n;return t<0||t>=e.length?-1:(n=e[t])!=null&&n.exchange.request_message?t:e.slice(0,t).findLastIndex(a=>a.exchange.request_message)},p=(e,t)=>{const n=e.slice(t).findIndex(a=>a.exchange.request_message);if(n!==-1)return t+n},L=(e,t)=>{if(t<0||t>=e.length)return[];const n=((r,s)=>{const o=w(r,s);let u=p(r,s);return u===void 0&&(u=r.length),r.slice(o+1,u)})(e,t),a=n.flatMap(r=>r.changed_files);return l(a)},U=(e,t)=>{var u,f;const n=p(e,t),a=e.slice(t,n),r=(u=e[t].exchange.response_nodes)==null?void 0:u.find(g=>g.type===m.TOOL_USE);if(!r)return[];const s=(f=r.tool_use)==null?void 0:f.tool_use_id;if(!s)return[];if(!a.find(g=>{var d;return(d=g.exchange.request_nodes)==null?void 0:d.some(h=>{var _;return h.type===M.TOOL_RESULT&&((_=h.tool_result_node)==null?void 0:_.tool_use_id)===s})}))return[];const o=a.flatMap(g=>g.changed_files);return l(o)},O="STREAM_CANCELLED",q="STREAM_TIMEOUT";export{O as S,$ as a,q as b,v as c,I as d,P as e,T as f,S as g,U as h,E as i,L as j,x as s};
