ANALYSEUR TEMPOREL AUGMENT - DÉLAIS D'ENREGISTREMENT
Démarré: 2025-06-04 16:50:37
================================================================================
🎯 OBJECTIF : Mesurer les délais Interface → DB
📊 MÉTRIQUES ANALYSÉES :
   • Timestamp détection WAL
   • D<PERSON>lai jusqu'à extraction réussie
   • Taille changement WAL
   • Type de message (user/assistant)
   • Contenu du message
   • D<PERSON>lai total estimé
⏱️  TIMINGS AUGMENT : 800ms → 1600ms
================================================================================

[16:50:38.919] ANALYSE #1 - SUCCÈS
Séquence WAL: #1 | Delta: +0 bytes
Délai détection → extraction: 101ms
Type: unknown | Index: 0
------------------------------------------------------------
[Message vide ou en cours de génération]
================================================================================

[16:50:38.919] ANALYSE #2 - SUCCÈS
Séquence WAL: #1 | Delta: +0 bytes
Délai détection → extraction: 101ms
Type: unknown | Index: 1
------------------------------------------------------------
[Message vide ou en cours de génération]
================================================================================

[16:51:30.702] ANALYSE #3 - SUCCÈS
Séquence WAL: #2 | Delta: +0 bytes
Délai détection → extraction: 101ms
Type: unknown | Index: 0
------------------------------------------------------------
[Message vide ou en cours de génération]
================================================================================

[16:51:30.741] ANALYSE #4 - SUCCÈS
Séquence WAL: #3 | Delta: +0 bytes
Délai détection → extraction: 123ms
Type: unknown | Index: 0
------------------------------------------------------------
[Message vide ou en cours de génération]
================================================================================

