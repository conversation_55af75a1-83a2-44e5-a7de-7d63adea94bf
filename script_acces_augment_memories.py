#!/usr/bin/env python3
"""
Script pour accéder aux Augment-Memories
"""

import os
import json
from pathlib import Path

def find_augment_memories():
    """Trouve et lit les fichiers Augment-Memories"""
    
    # Chemin principal selon les mémoires
    base_path = Path("C:/Users/<USER>/AppData/Roaming/Code/User/workspaceStorage")
    
    print("=== RECHERCHE DES AUGMENT-MEMORIES ===")
    
    if not base_path.exists():
        print(f"Chemin de base non trouvé: {base_path}")
        return
    
    # Recherche des dossiers Augment
    augment_dirs = []
    for workspace_dir in base_path.iterdir():
        if workspace_dir.is_dir():
            augment_path = workspace_dir / "Augment.vscode-augment"
            if augment_path.exists():
                augment_dirs.append((workspace_dir.name, augment_path))
    
    print(f"Trouvé {len(augment_dirs)} dossiers Augment:")
    for workspace_id, augment_path in augment_dirs:
        print(f"  - {workspace_id}: {augment_path}")
        
        # Lister le contenu
        try:
            for item in augment_path.iterdir():
                print(f"    {item.name} ({'dir' if item.is_dir() else 'file'})")
                
                # Si c'est le dossier Augment-Memories
                if item.name == "Augment-Memories" and item.is_dir():
                    print(f"    === CONTENU AUGMENT-MEMORIES ===")
                    for memory_file in item.iterdir():
                        print(f"      {memory_file.name}")
                        
                        # Lire le contenu si c'est un fichier
                        if memory_file.is_file():
                            try:
                                with open(memory_file, 'r', encoding='utf-8') as f:
                                    content = f.read()
                                    print(f"      Contenu ({len(content)} caractères):")
                                    print(f"      {content[:200]}...")
                                    print()
                            except Exception as e:
                                print(f"      Erreur lecture: {e}")
                
                # Si c'est un fichier directement dans Augment.vscode-augment
                elif item.is_file():
                    try:
                        with open(item, 'r', encoding='utf-8') as f:
                            content = f.read()
                            print(f"    Contenu de {item.name} ({len(content)} caractères):")
                            print(f"    {content[:200]}...")
                            print()
                    except Exception as e:
                        print(f"    Erreur lecture {item.name}: {e}")
                        
        except Exception as e:
            print(f"    Erreur accès: {e}")
        
        print()

if __name__ == "__main__":
    find_augment_memories()
