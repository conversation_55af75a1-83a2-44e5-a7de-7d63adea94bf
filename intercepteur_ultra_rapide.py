#!/usr/bin/env python3
"""
INTERCEPTEUR ULTRA-RAPIDE - OPTIMISÉ POUR VITESSE MAXIMALE
Détection instantanée des messages avec optimisations avancées
"""

import sqlite3
import json
import os
import threading
from datetime import datetime
import hashlib
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

class IntercepteurUltraRapide(FileSystemEventHandler):
    def __init__(self):
        self.workspace_path = r"C:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\a35ba43ef26792e61fe787a234121806"
        self.db_path = os.path.join(self.workspace_path, "state.vscdb")
        self.output_file = "messages_ultra_rapide.txt"
        
        # Optimisations de performance
        self.derniere_signature = ""
        self.derniers_messages = []
        self.running = True
        self.observer = None
        self.lock = threading.Lock()
        
        # Initialisation
        self.init_output_file()
        self.derniere_signature = self.get_fast_signature()
        self.derniers_messages = self.extraire_messages_rapide()
        
    def init_output_file(self):
        """Initialise le fichier de sortie"""
        with open(self.output_file, 'w', encoding='utf-8') as f:
            f.write("INTERCEPTEUR ULTRA-RAPIDE - MESSAGES TEMPS RÉEL\n")
            f.write(f"Démarré: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("=" * 80 + "\n\n")
    
    def get_fast_signature(self):
        """Signature ultra-rapide basée sur taille + timestamp"""
        try:
            stat = os.stat(self.db_path)
            # Combinaison taille + nanoseconde de modification
            return f"{stat.st_size}_{stat.st_mtime_ns}"
        except:
            return "ERROR"
    
    def get_partial_hash(self):
        """Hash partiel des derniers KB seulement"""
        try:
            with open(self.db_path, 'rb') as f:
                # Lire seulement les derniers 16KB au lieu de tout le fichier
                f.seek(-16384, 2)
                data = f.read()
                return hashlib.md5(data).hexdigest()[:8]
        except:
            return "ERROR"
    
    def extraire_messages_rapide(self):
        """Extraction optimisée des messages"""
        try:
            # Connexion SQLite avec optimisations
            conn = sqlite3.connect(self.db_path, timeout=1.0)
            conn.execute("PRAGMA journal_mode=WAL")  # Mode WAL pour lecture rapide
            cursor = conn.cursor()
            
            # Requête optimisée
            cursor.execute("SELECT value FROM ItemTable WHERE key = 'memento/webviewView.augment-chat' LIMIT 1")
            row = cursor.fetchone()
            
            if not row:
                conn.close()
                return []
            
            value = row[0]
            if isinstance(value, bytes):
                value_str = value.decode('utf-8')
            else:
                value_str = str(value)
            
            main_data = json.loads(value_str)
            webview_data = json.loads(main_data['webviewState'])
            
            conversations = webview_data.get('conversations', {})
            current_conv_id = webview_data.get('currentConversationId')
            
            if current_conv_id and current_conv_id in conversations:
                chat_history = conversations[current_conv_id].get('chatHistory', [])
                conn.close()
                return chat_history
            
            conn.close()
            return []
            
        except Exception as e:
            print(f"❌ Erreur extraction: {e}")
            return []
    
    def on_modified(self, event):
        """Événement de modification de fichier - INSTANTANÉ"""
        if event.is_directory:
            return
            
        if event.src_path.endswith('state.vscdb'):
            self.analyser_changement_instantane()
    
    def analyser_changement_instantane(self):
        """Analyse instantanée des changements"""
        with self.lock:
            signature_actuelle = self.get_fast_signature()
            
            if signature_actuelle != self.derniere_signature:
                timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                
                # Extraction rapide des messages
                messages_actuels = self.extraire_messages_rapide()
                
                if len(messages_actuels) > len(self.derniers_messages):
                    nouveaux = len(messages_actuels) - len(self.derniers_messages)
                    
                    print(f"🔥 [{timestamp}] {nouveaux} NOUVEAUX MESSAGES DÉTECTÉS !")
                    print(f"   📊 Signature: {signature_actuelle}")
                    print(f"   💬 Total: {len(messages_actuels)} messages")
                    
                    # Écriture instantanée des nouveaux messages
                    self.ecrire_nouveaux_messages(messages_actuels[-nouveaux:], timestamp)
                    
                    self.derniers_messages = messages_actuels.copy()
                
                self.derniere_signature = signature_actuelle
    
    def ecrire_nouveaux_messages(self, nouveaux_messages, timestamp):
        """Écriture instantanée des nouveaux messages"""
        try:
            with open(self.output_file, 'a', encoding='utf-8') as f:
                for i, msg in enumerate(nouveaux_messages):
                    role = msg.get('role', 'unknown')
                    content = msg.get('content', '')
                    
                    f.write(f"[{timestamp}] MESSAGE INTERCEPTÉ\n")
                    f.write(f"Role: {role}\n")
                    f.write(f"Content: {content[:200]}{'...' if len(content) > 200 else ''}\n")
                    f.write("-" * 60 + "\n")
                
                f.flush()  # Force l'écriture immédiate
                os.fsync(f.fileno())  # Synchronisation disque
                
        except Exception as e:
            print(f"❌ Erreur écriture: {e}")
    
    def demarrer_surveillance(self):
        """Démarre la surveillance filesystem ultra-rapide"""
        print("🚀 INTERCEPTEUR ULTRA-RAPIDE DÉMARRÉ")
        print("=" * 60)
        print(f"📁 DB: {self.db_path}")
        print(f"📝 Sortie: {self.output_file}")
        print(f"🎯 Workspace: a35ba43ef26792e6...")
        print(f"📊 État initial: {len(self.derniers_messages)} messages")
        print("💬 Écrivez dans Augment pour voir l'interception instantanée...")
        print("⏹️  Ctrl+C pour arrêter")
        print("=" * 60)
        
        # Configuration de l'observateur filesystem
        self.observer = Observer()
        self.observer.schedule(self, self.workspace_path, recursive=False)
        self.observer.start()
        
        try:
            while self.running:
                # Thread principal reste actif
                threading.Event().wait(1)
                
        except KeyboardInterrupt:
            print("\n🛑 Arrêt de l'intercepteur...")
            self.running = False
            if self.observer:
                self.observer.stop()
                self.observer.join()

if __name__ == "__main__":
    intercepteur = IntercepteurUltraRapide()
    intercepteur.demarrer_surveillance()
