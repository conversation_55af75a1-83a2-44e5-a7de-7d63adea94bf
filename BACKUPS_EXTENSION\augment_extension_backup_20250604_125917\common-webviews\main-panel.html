<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Augment</title>
    <script nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
/**
 * Monaco bootstrap script
 *
 * This script is included directly in HTML files to load Monaco editor.
 * It's kept as a simple JS file to avoid any build/transpilation requirements.
 */

// Define the Monaco CDN version
const MONACO_VERSION = "0.52.2";
const MONACO_CDN_BASE = `https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/${MONACO_VERSION}/min`;

// Initialize augmentDeps if it doesn't exist
window.augmentDeps = window.augmentDeps || {};

// Create a promise that will resolve when Monaco is ready
let monacoResolve;
window.augmentDeps.monaco = new Promise((resolve) => {
  monacoResolve = resolve;
});

// If Monaco is already loaded, don't load it again
if (window.monaco) {
  console.log("Monaco already loaded, skipping bootstrap");
  initializeMonacoDeps();
} else {
  // Load the Monaco loader script
  const loaderScript = document.createElement("script");
  loaderScript.src = `${MONACO_CDN_BASE}/vs/loader.min.js`;
  loaderScript.onload = initializeMonaco;
  document.head.appendChild(loaderScript);
}

// Initialize Monaco after the loader script has loaded
function initializeMonaco() {
  // require is provided by loader.min.js
  require.config({
    paths: { vs: `${MONACO_CDN_BASE}/vs` },
  });

  require(["vs/editor/editor.main"], () => {
    initializeMonacoDeps();
  });
}

// Initialize Monaco dependencies after Monaco has loaded
function initializeMonacoDeps() {
  // Resolve the monaco promise
  if (monacoResolve) {
    monacoResolve(window.monaco);
  }
}

</script>
    <meta property="csp-nonce" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <script type="module" crossorigin src="./assets/main-panel-CLAFkah5.js" nonce="nonce-L8MbLSPfSRYovcFARu9n/A=="></script>
    <link rel="modulepreload" crossorigin href="./assets/SpinnerAugment-BJ4-L7QR.js" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="modulepreload" crossorigin href="./assets/github-C1PQK5DH.js" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="modulepreload" crossorigin href="./assets/BaseButton-C6Dhmpxa.js" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="modulepreload" crossorigin href="./assets/IconButtonAugment-Certjadv.js" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="modulepreload" crossorigin href="./assets/layer-group-CZFSGU8L.js" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="modulepreload" crossorigin href="./assets/design-system-init-DA68MSAy.js" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="modulepreload" crossorigin href="./assets/globals-D0QH3NT1.js" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="modulepreload" crossorigin href="./assets/Content-Czt02SJi.js" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="modulepreload" crossorigin href="./assets/TextTooltipAugment-Bkzart3o.js" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="modulepreload" crossorigin href="./assets/chat-types-NgqNgjwU.js" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="modulepreload" crossorigin href="./assets/test_service_pb-B6vKXZrG.js" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="modulepreload" crossorigin href="./assets/types-LfaCSdmF.js" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="modulepreload" crossorigin href="./assets/file-paths-BcSg4gks.js" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="modulepreload" crossorigin href="./assets/types-a569v5Ol.js" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="modulepreload" crossorigin href="./assets/open-in-new-window-DMlqLwqy.js" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="modulepreload" crossorigin href="./assets/folder-opened-DzrGzNBt.js" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="modulepreload" crossorigin href="./assets/types-BSMhNRWH.js" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="modulepreload" crossorigin href="./assets/CardAugment-BxTO-shY.js" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="modulepreload" crossorigin href="./assets/index-C-g0ZorP.js" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="modulepreload" crossorigin href="./assets/TextAreaAugment-Cj5jK817.js" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="modulepreload" crossorigin href="./assets/preload-helper-Dv6uf1Os.js" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="modulepreload" crossorigin href="./assets/toggleHighContrast-CwIv4U26.js" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="modulepreload" crossorigin href="./assets/index-MyvMQzjq.js" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="modulepreload" crossorigin href="./assets/diff-utils-y96qaWKK.js" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="modulepreload" crossorigin href="./assets/folder-BJI1Q8_7.js" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="modulepreload" crossorigin href="./assets/index-CGbmuyBX.js" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="modulepreload" crossorigin href="./assets/StatusIndicator-CAJYwjQb.js" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="modulepreload" crossorigin href="./assets/pen-to-square-Bm4lF9Yl.js" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="modulepreload" crossorigin href="./assets/ButtonAugment-HnJOGilM.js" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="modulepreload" crossorigin href="./assets/exclamation-triangle-Dn4fXX3v.js" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="modulepreload" crossorigin href="./assets/isObjectLike-DflaizF0.js" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="modulepreload" crossorigin href="./assets/augment-logo-D_UKSkj8.js" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="modulepreload" crossorigin href="./assets/Keybindings-4L2d2tRE.js" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="modulepreload" crossorigin href="./assets/await_block-CvQ_3xaW.js" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="modulepreload" crossorigin href="./assets/ellipsis-BWy9xWah.js" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="modulepreload" crossorigin href="./assets/keypress-DD1aQVr0.js" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="modulepreload" crossorigin href="./assets/Filespan-BC4kxbfx.js" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="modulepreload" crossorigin href="./assets/lodash-ChYFUhWY.js" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="modulepreload" crossorigin href="./assets/MaterialIcon-DIlB9c-0.js" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="modulepreload" crossorigin href="./assets/terminal-BQIj5vJ0.js" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="modulepreload" crossorigin href="./assets/autofix-state-d-ymFdyn.js" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="modulepreload" crossorigin href="./assets/VSCodeCodicon-CvBJfpPi.js" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="modulepreload" crossorigin href="./assets/chat-flags-model-IiDhbRsI.js" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="stylesheet" crossorigin href="./assets/SpinnerAugment-DnPofOlT.css" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="stylesheet" crossorigin href="./assets/github-DDCjb6F1.css" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="stylesheet" crossorigin href="./assets/IconButtonAugment-BTu-iglL.css" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="stylesheet" crossorigin href="./assets/layer-group-Dnu6blpM.css" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="stylesheet" crossorigin href="./assets/design-system-init-CRmW_T8r.css" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="stylesheet" crossorigin href="./assets/BaseButton-DvMdfQ3F.css" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="stylesheet" crossorigin href="./assets/TextTooltipAugment-BIMZ5dVo.css" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="stylesheet" crossorigin href="./assets/Content-D0WttAzY.css" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="stylesheet" crossorigin href="./assets/index-eY12-hdZ.css" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="stylesheet" crossorigin href="./assets/TextAreaAugment-J75lFxU7.css" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="stylesheet" crossorigin href="./assets/diff-utils-CiAPKcVt.css" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="stylesheet" crossorigin href="./assets/folder-CiVHUelA.css" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="stylesheet" crossorigin href="./assets/StatusIndicator-BRrHlRmq.css" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="stylesheet" crossorigin href="./assets/pen-to-square-Dvw-pMXw.css" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="stylesheet" crossorigin href="./assets/ButtonAugment-CNK8zC8i.css" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="stylesheet" crossorigin href="./assets/Keybindings-CFCfDdvf.css" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="stylesheet" crossorigin href="./assets/CardAugment-BAo8Ti0V.css" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="stylesheet" crossorigin href="./assets/Filespan-tclW2Ian.css" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="stylesheet" crossorigin href="./assets/MaterialIcon-BO_oU5T3.css" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="stylesheet" crossorigin href="./assets/VSCodeCodicon-DVaocTud.css" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="stylesheet" crossorigin href="./assets/index-McRKs1sU.css" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="stylesheet" crossorigin href="./assets/toggleHighContrast-D4zjdeIP.css" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
    <link rel="stylesheet" crossorigin href="./assets/main-panel-B54j0lBe.css" nonce="nonce-L8MbLSPfSRYovcFARu9n/A==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
