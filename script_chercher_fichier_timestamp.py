#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour chercher le fichier avec le timestamp spécifique
"""

import os
from pathlib import Path

def chercher_fichier_timestamp():
    """Cherche le fichier avec le timestamp 1749026244639"""
    
    base_path = Path("C:/Users/<USER>/AppData/Roaming/Code/User/workspaceStorage/591be8b7f6bd4169258c0affc2eaa1fc")
    
    print("🔍 RECHERCHE DU FICHIER AVEC TIMESTAMP 1749026244639")
    print("=" * 60)
    print(f"📁 Base: {base_path}")
    
    if not base_path.exists():
        print(f"❌ Le répertoire de base n'existe pas!")
        return None
    
    # Chercher récursivement
    for root, dirs, files in os.walk(base_path):
        for file in files:
            if "1749026244639" in file:
                file_path = Path(root) / file
                print(f"✅ FICHIER TROUVÉ!")
                print(f"📁 Chemin: {file_path}")
                print(f"📄 Nom: {file}")
                return str(file_path)
    
    print(f"❌ Aucun fichier trouvé avec ce timestamp")
    
    # Lister les fichiers récents dans le workspace
    print(f"\n📋 FICHIERS RÉCENTS DANS LE WORKSPACE:")
    try:
        augment_path = base_path / "Augment.vscode-augment"
        if augment_path.exists():
            for root, dirs, files in os.walk(augment_path):
                for file in files:
                    if file.endswith('.json') and "script_conversation" in file:
                        file_path = Path(root) / file
                        print(f"📄 {file}")
                        print(f"   📁 {file_path}")
    except Exception as e:
        print(f"❌ Erreur: {e}")
    
    return None

if __name__ == "__main__":
    print("🚀 RECHERCHE DU FICHIER SPÉCIFIQUE")
    print("=" * 60)
    
    result = chercher_fichier_timestamp()
    
    if result:
        print(f"\n✅ FICHIER TROUVÉ: {result}")
    else:
        print(f"\n❌ FICHIER NON TROUVÉ")
