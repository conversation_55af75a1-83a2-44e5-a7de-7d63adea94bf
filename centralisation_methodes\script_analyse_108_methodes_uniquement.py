#!/usr/bin/env python3
"""
SCRIPT D'ANALYSE PRÉCISE DES 108 MÉTHODES NON CENTRALISÉES UNIQUEMENT
====================================================================

Objectif : Extraire et analyser UNIQUEMENT les paramètres des 108 méthodes
non centralisées, en excluant les 31 méthodes universelles et les 16 méthodes
spécifiques aux clusters.
"""

import re
import json
from collections import defaultdict, Counter
from typing import Dict, List, Set

class PreciseParameterAnalyzer:
    """Analyseur précis pour les 108 méthodes non centralisées uniquement"""
    
    def __init__(self):
        # Méthodes à EXCLURE de l'analyse (31 universelles + 16 spécifiques clusters)
        self.excluded_methods = {
            # 31 méthodes universelles centralisées
            '_rollout_analyzer', '_analyze_impair_consecutive_bias', '_analyze_pair_priority_2_autonomous',
            '_analyze_sync_alternation_bias', '_correlate_impair_with_sync', '_correlate_impair_with_combined',
            '_correlate_impair_with_pb', '_correlate_impair_with_so', '_correlate_bias_to_pb_variations',
            '_correlate_bias_to_so_variations', '_analyze_combined_structural_bias',
            '_generate_priority_based_synthesis_autonomous', '_generate_bias_signals_summary',
            '_generate_bias_generation_guidance', '_generate_bias_quick_access', '_rollout_generator',
            '_define_optimized_generation_space', '_generate_sequences_from_signals',
            '_generate_fallback_sequences', '_enrich_sequences_with_complete_indexes',
            '_rollout_predictor', '_evaluate_sequence_quality', '_evaluate_signal_alignment',
            '_evaluate_fallback_alignment', '_analyze_sequence_consistency', '_assess_risk_reward_ratio',
            '_validate_sequence_logic', '_calculate_sequence_score', '_select_best_sequence',
            '_calculate_cluster_confidence_azr_calibrated', '_convert_pb_sequence_to_so',
            
            # 16 méthodes spécifiques clusters
            '_rollout_analyzer_c2_patterns_courts', '_rollout_analyzer_c3_patterns_moyens',
            '_analyze_impair_consecutive_bias_c2_specialized', '_analyze_sync_alternation_bias_c2_specialized',
            '_apply_c2_short_patterns_specialization', '_generate_bias_signals_summary_c2',
            '_generate_bias_generation_guidance_c2', '_generate_bias_quick_access_c2',
            '_analyze_impair_consecutive_bias_c3_specialized', '_analyze_sync_alternation_bias_c3_specialized',
            '_apply_c3_medium_patterns_specialization', '_get_cluster_specialization_params',
            '_create_generic_cluster_analyzer', '_analyze_impair_bias_specialized',
            '_analyze_sync_bias_specialized', '_apply_cluster_specialization'
        }
        
        self.parameters = []
        self.current_method = None
        self.in_excluded_method = False
        
        # Patterns de détection
        self.patterns = {
            'float_values': r'([0-9]+\.[0-9]+)',
            'int_values': r'= ([0-9]+)(?![0-9\.])',
            'comparisons': r'(>= |<= |== |!= )([0-9]+\.?[0-9]*)',
            'cluster_ids': r'cluster_id == ([0-9]+)',
            'array_access': r'\[([0-9]+)\]',
            'min_max': r'(min|max)\([^,]+, ([0-9]+\.?[0-9]*)\)'
        }
        
        # Contextes pour catégorisation
        self.context_keywords = {
            'COEFFICIENTS': ['coefficient', 'factor', 'multiplier', 'ratio'],
            'SEUILS_DETECTION': ['threshold', 'limit', 'minimum', 'maximum'],
            'VALEURS_CONFIANCE': ['confidence', 'probability', 'certainty'],
            'RATIOS_POURCENTAGES': ['ratio', 'percentage', 'percent'],
            'LONGUEURS_TAILLES': ['length', 'size', 'count', 'window'],
            'INDICES_POSITIONS': ['index', 'position', 'offset'],
            'VALEURS_INITIALISATION': ['init', 'default', 'start', 'begin'],
            'SEUILS_QUALITE': ['quality', 'score', 'grade'],
            'FACTEURS_BOOST': ['boost', 'bonus', 'enhancement'],
            'IDENTIFIANTS_CLUSTER': ['cluster_id', 'cluster'],
            'TIMING_PERFORMANCE': ['time', 'ms', 'duration'],
            'PROBABILITES': ['probability', 'chance', 'likelihood'],
            'MULTIPLICATEURS': ['multiplier', 'factor', 'scale'],
            'CONSTANTES_MATHEMATIQUES': ['pi', 'e', 'sqrt', 'log'],
            'VALEURS_LIMITES': ['min', 'max', 'limit', 'bound']
        }
    
    def analyze_108_methods_only(self, file_path: str) -> Dict:
        """Analyse UNIQUEMENT les 108 méthodes non centralisées"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Analyse ligne par ligne avec exclusion des méthodes centralisées
            lines = content.split('\n')
            extracted_params = []
            
            for line_num, line in enumerate(lines, 1):
                # Détection début de méthode
                if 'def ' in line and '(' in line:
                    method_name = self._extract_method_name(line)
                    self.current_method = method_name
                    self.in_excluded_method = any(excluded in method_name for excluded in self.excluded_methods)
                
                # Si on est dans une méthode exclue, ignorer la ligne
                if self.in_excluded_method:
                    continue
                
                # Ignorer les commentaires
                if line.strip().startswith('#') or line.strip().startswith('//'):
                    continue
                
                # Extraction des paramètres pour les 108 méthodes uniquement
                for pattern_name, pattern in self.patterns.items():
                    matches = re.finditer(pattern, line)
                    for match in matches:
                        param = {
                            'value': match.group(1) if match.groups() else match.group(0),
                            'line_number': line_num,
                            'line_content': line.strip(),
                            'pattern_type': pattern_name,
                            'context': self._extract_context(line),
                            'method_name': self.current_method,
                            'variable_name': self._extract_variable_name(line)
                        }
                        extracted_params.append(param)
            
            # Catégorisation
            categorized_params = self._categorize_parameters(extracted_params)
            
            # Analyse de fréquence
            frequency_analysis = self._analyze_frequency(extracted_params)
            
            # Analyse par méthode
            method_analysis = self._analyze_by_method(extracted_params)
            
            return {
                'total_parameters_108_methods': len(extracted_params),
                'methods_analyzed': len([m for m in set([p['method_name'] for p in extracted_params]) if m]),
                'excluded_methods_count': len(self.excluded_methods),
                'extracted_parameters': extracted_params,
                'categorized_parameters': categorized_params,
                'frequency_analysis': frequency_analysis,
                'method_analysis': method_analysis,
                'recommendations': self._generate_precise_recommendations(categorized_params)
            }
            
        except Exception as e:
            return {'error': f"Erreur analyse 108 méthodes: {e}"}
    
    def _extract_method_name(self, line: str) -> str:
        """Extraction du nom de méthode"""
        match = re.search(r'def ([a-zA-Z_][a-zA-Z0-9_]*)', line)
        return match.group(1) if match else 'unknown'
    
    def _extract_context(self, line: str) -> str:
        """Extraction du contexte d'une ligne"""
        line_lower = line.lower()
        for category, keywords in self.context_keywords.items():
            for keyword in keywords:
                if keyword in line_lower:
                    return category
        return 'UNKNOWN'
    
    def _extract_variable_name(self, line: str) -> str:
        """Extraction du nom de variable si possible"""
        var_pattern = r'([a-zA-Z_][a-zA-Z0-9_]*)\s*[=:]'
        match = re.search(var_pattern, line)
        return match.group(1) if match else 'unknown'
    
    def _categorize_parameters(self, parameters: List[Dict]) -> Dict:
        """Catégorisation des paramètres par type"""
        categorized = defaultdict(list)
        
        for param in parameters:
            context = param['context']
            if context != 'UNKNOWN':
                categorized[context].append(param)
            else:
                # Catégorisation par valeur
                value = param['value']
                try:
                    num_value = float(value)
                    if num_value == 0.0:
                        categorized['VALEURS_INITIALISATION'].append(param)
                    elif num_value == 1.0:
                        categorized['CONSTANTES_MATHEMATIQUES'].append(param)
                    elif 0.0 < num_value < 1.0:
                        categorized['COEFFICIENTS'].append(param)
                    elif num_value >= 1.0 and num_value == int(num_value):
                        categorized['LONGUEURS_TAILLES'].append(param)
                    else:
                        categorized['VALEURS_LIMITES'].append(param)
                except ValueError:
                    categorized['UNKNOWN'].append(param)
        
        return dict(categorized)
    
    def _analyze_frequency(self, parameters: List[Dict]) -> Dict:
        """Analyse de fréquence des valeurs"""
        values = [param['value'] for param in parameters]
        frequency = Counter(values)
        
        return {
            'most_common': frequency.most_common(20),
            'unique_values': len(frequency),
            'total_occurrences': len(values),
            'duplicates': {k: v for k, v in frequency.items() if v > 1}
        }
    
    def _analyze_by_method(self, parameters: List[Dict]) -> Dict:
        """Analyse par méthode"""
        method_stats = defaultdict(int)
        method_params = defaultdict(list)
        
        for param in parameters:
            method = param['method_name']
            if method and method != 'unknown':
                method_stats[method] += 1
                method_params[method].append(param['value'])
        
        return {
            'methods_with_most_params': sorted(method_stats.items(), key=lambda x: x[1], reverse=True)[:10],
            'total_methods_with_params': len(method_stats),
            'method_parameter_details': dict(method_params)
        }
    
    def _generate_precise_recommendations(self, categorized_params: Dict) -> Dict:
        """Recommandations précises pour les 108 méthodes"""
        recommendations = {
            'critical_priority': [],
            'high_priority': [],
            'medium_priority': [],
            'azrconfig_additions_needed': {}
        }
        
        # Priorités selon fréquence et impact
        for category, params in categorized_params.items():
            if len(params) > 15:  # Très haute fréquence
                recommendations['critical_priority'].append({
                    'category': category,
                    'count': len(params),
                    'reason': 'Très haute fréquence dans 108 méthodes - impact critique'
                })
            elif len(params) > 5:  # Fréquence élevée
                recommendations['high_priority'].append({
                    'category': category,
                    'count': len(params),
                    'reason': 'Fréquence élevée dans 108 méthodes - impact important'
                })
            else:  # Fréquence modérée
                recommendations['medium_priority'].append({
                    'category': category,
                    'count': len(params),
                    'reason': 'Fréquence modérée dans 108 méthodes'
                })
        
        return recommendations

def main():
    """Fonction principale d'analyse précise"""
    analyzer = PreciseParameterAnalyzer()
    
    # Analyse UNIQUEMENT des 108 méthodes
    results = analyzer.analyze_108_methods_only('centralisation_methodes/class.txt')
    
    if 'error' in results:
        print(f"Erreur: {results['error']}")
        return
    
    # Sauvegarde des résultats
    output_file = 'centralisation_methodes/analyse_108_methodes_uniquement.json'
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"Analyse précise terminée. Résultats sauvés dans {output_file}")
    print(f"Total paramètres 108 méthodes: {results['total_parameters_108_methods']}")
    print(f"Méthodes analysées: {results['methods_analyzed']}")
    print(f"Méthodes exclues: {results['excluded_methods_count']}")

if __name__ == "__main__":
    main()
