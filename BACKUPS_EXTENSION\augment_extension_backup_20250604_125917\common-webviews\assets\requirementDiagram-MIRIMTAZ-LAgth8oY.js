import{_ as n,d as Tt,s as Lt,g as Ct,c as Mt,b as Ft,l as bt,x as Dt,j as St,k as Pt,a1 as Vt,e as Nt}from"./AugmentMessage-DIzdCIMv.js";import{G as Ut}from"./graph-Ds8GaKdA.js";import{l as Yt}from"./layout-rofJ78-X.js";import"./SpinnerAugment-BJ4-L7QR.js";import"./github-C1PQK5DH.js";import"./pen-to-square-Bm4lF9Yl.js";import"./augment-logo-D_UKSkj8.js";import"./TextTooltipAugment-Bkzart3o.js";import"./BaseButton-C6Dhmpxa.js";import"./IconButtonAugment-Certjadv.js";import"./Content-Czt02SJi.js";import"./globals-D0QH3NT1.js";import"./open-in-new-window-DMlqLwqy.js";import"./types-LfaCSdmF.js";import"./chat-types-NgqNgjwU.js";import"./test_service_pb-B6vKXZrG.js";import"./file-paths-BcSg4gks.js";import"./types-a569v5Ol.js";import"./folder-BJI1Q8_7.js";import"./folder-opened-DzrGzNBt.js";import"./types-BSMhNRWH.js";import"./index-C-g0ZorP.js";import"./CardAugment-BxTO-shY.js";import"./TextAreaAugment-Cj5jK817.js";import"./diff-utils-y96qaWKK.js";import"./toggleHighContrast-CwIv4U26.js";import"./preload-helper-Dv6uf1Os.js";import"./index-MyvMQzjq.js";import"./keypress-DD1aQVr0.js";import"./await_block-CvQ_3xaW.js";import"./ButtonAugment-HnJOGilM.js";import"./expand--BB_Hn_b.js";import"./mcp-logo-B9nTLE-q.js";import"./ellipsis-BWy9xWah.js";import"./IconFilePath-C-3qORpY.js";import"./LanguageIcon-BH9BM7T7.js";import"./next-edit-types-904A5ehg.js";import"./magnifying-glass-LWYs47rB.js";import"./MaterialIcon-DIlB9c-0.js";import"./Filespan-BC4kxbfx.js";import"./chevron-down-B88L5wkj.js";import"./lodash-ChYFUhWY.js";import"./terminal-BQIj5vJ0.js";import"./_baseUniq-BZgk2Caz.js";import"./_basePickBy-DiH5splf.js";var ot=function(){var t=n(function(i,d,o,r){for(o=o||{},r=i.length;r--;o[i[r]]=d);return o},"o"),e=[1,3],l=[1,4],u=[1,5],c=[1,6],p=[5,6,8,9,11,13,31,32,33,34,35,36,44,62,63],h=[1,18],a=[2,7],_=[1,22],E=[1,23],f=[1,24],k=[1,25],I=[1,26],w=[1,27],q=[1,20],A=[1,28],v=[1,29],F=[62,63],ht=[5,8,9,11,13,31,32,33,34,35,36,44,51,53,62,63],ct=[1,47],ut=[1,48],pt=[1,49],dt=[1,50],yt=[1,51],mt=[1,52],_t=[1,53],L=[53,54],D=[1,64],P=[1,60],V=[1,61],U=[1,62],Y=[1,63],B=[1,65],G=[1,69],z=[1,70],X=[1,67],J=[1,68],S=[5,8,9,11,13,31,32,33,34,35,36,44,62,63],rt={trace:n(function(){},"trace"),yy:{},symbols_:{error:2,start:3,directive:4,NEWLINE:5,RD:6,diagram:7,EOF:8,acc_title:9,acc_title_value:10,acc_descr:11,acc_descr_value:12,acc_descr_multiline_value:13,requirementDef:14,elementDef:15,relationshipDef:16,requirementType:17,requirementName:18,STRUCT_START:19,requirementBody:20,ID:21,COLONSEP:22,id:23,TEXT:24,text:25,RISK:26,riskLevel:27,VERIFYMTHD:28,verifyType:29,STRUCT_STOP:30,REQUIREMENT:31,FUNCTIONAL_REQUIREMENT:32,INTERFACE_REQUIREMENT:33,PERFORMANCE_REQUIREMENT:34,PHYSICAL_REQUIREMENT:35,DESIGN_CONSTRAINT:36,LOW_RISK:37,MED_RISK:38,HIGH_RISK:39,VERIFY_ANALYSIS:40,VERIFY_DEMONSTRATION:41,VERIFY_INSPECTION:42,VERIFY_TEST:43,ELEMENT:44,elementName:45,elementBody:46,TYPE:47,type:48,DOCREF:49,ref:50,END_ARROW_L:51,relationship:52,LINE:53,END_ARROW_R:54,CONTAINS:55,COPIES:56,DERIVES:57,SATISFIES:58,VERIFIES:59,REFINES:60,TRACES:61,unqString:62,qString:63,$accept:0,$end:1},terminals_:{2:"error",5:"NEWLINE",6:"RD",8:"EOF",9:"acc_title",10:"acc_title_value",11:"acc_descr",12:"acc_descr_value",13:"acc_descr_multiline_value",19:"STRUCT_START",21:"ID",22:"COLONSEP",24:"TEXT",26:"RISK",28:"VERIFYMTHD",30:"STRUCT_STOP",31:"REQUIREMENT",32:"FUNCTIONAL_REQUIREMENT",33:"INTERFACE_REQUIREMENT",34:"PERFORMANCE_REQUIREMENT",35:"PHYSICAL_REQUIREMENT",36:"DESIGN_CONSTRAINT",37:"LOW_RISK",38:"MED_RISK",39:"HIGH_RISK",40:"VERIFY_ANALYSIS",41:"VERIFY_DEMONSTRATION",42:"VERIFY_INSPECTION",43:"VERIFY_TEST",44:"ELEMENT",47:"TYPE",49:"DOCREF",51:"END_ARROW_L",53:"LINE",54:"END_ARROW_R",55:"CONTAINS",56:"COPIES",57:"DERIVES",58:"SATISFIES",59:"VERIFIES",60:"REFINES",61:"TRACES",62:"unqString",63:"qString"},productions_:[0,[3,3],[3,2],[3,4],[4,2],[4,2],[4,1],[7,0],[7,2],[7,2],[7,2],[7,2],[7,2],[14,5],[20,5],[20,5],[20,5],[20,5],[20,2],[20,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[27,1],[27,1],[27,1],[29,1],[29,1],[29,1],[29,1],[15,5],[46,5],[46,5],[46,2],[46,1],[16,5],[16,5],[52,1],[52,1],[52,1],[52,1],[52,1],[52,1],[52,1],[18,1],[18,1],[23,1],[23,1],[25,1],[25,1],[45,1],[45,1],[48,1],[48,1],[50,1],[50,1]],performAction:n(function(i,d,o,r,y,s,W){var g=s.length-1;switch(y){case 4:this.$=s[g].trim(),r.setAccTitle(this.$);break;case 5:case 6:this.$=s[g].trim(),r.setAccDescription(this.$);break;case 7:this.$=[];break;case 13:r.addRequirement(s[g-3],s[g-4]);break;case 14:r.setNewReqId(s[g-2]);break;case 15:r.setNewReqText(s[g-2]);break;case 16:r.setNewReqRisk(s[g-2]);break;case 17:r.setNewReqVerifyMethod(s[g-2]);break;case 20:this.$=r.RequirementType.REQUIREMENT;break;case 21:this.$=r.RequirementType.FUNCTIONAL_REQUIREMENT;break;case 22:this.$=r.RequirementType.INTERFACE_REQUIREMENT;break;case 23:this.$=r.RequirementType.PERFORMANCE_REQUIREMENT;break;case 24:this.$=r.RequirementType.PHYSICAL_REQUIREMENT;break;case 25:this.$=r.RequirementType.DESIGN_CONSTRAINT;break;case 26:this.$=r.RiskLevel.LOW_RISK;break;case 27:this.$=r.RiskLevel.MED_RISK;break;case 28:this.$=r.RiskLevel.HIGH_RISK;break;case 29:this.$=r.VerifyType.VERIFY_ANALYSIS;break;case 30:this.$=r.VerifyType.VERIFY_DEMONSTRATION;break;case 31:this.$=r.VerifyType.VERIFY_INSPECTION;break;case 32:this.$=r.VerifyType.VERIFY_TEST;break;case 33:r.addElement(s[g-3]);break;case 34:r.setNewElementType(s[g-2]);break;case 35:r.setNewElementDocRef(s[g-2]);break;case 38:r.addRelationship(s[g-2],s[g],s[g-4]);break;case 39:r.addRelationship(s[g-2],s[g-4],s[g]);break;case 40:this.$=r.Relationships.CONTAINS;break;case 41:this.$=r.Relationships.COPIES;break;case 42:this.$=r.Relationships.DERIVES;break;case 43:this.$=r.Relationships.SATISFIES;break;case 44:this.$=r.Relationships.VERIFIES;break;case 45:this.$=r.Relationships.REFINES;break;case 46:this.$=r.Relationships.TRACES}},"anonymous"),table:[{3:1,4:2,6:e,9:l,11:u,13:c},{1:[3]},{3:8,4:2,5:[1,7],6:e,9:l,11:u,13:c},{5:[1,9]},{10:[1,10]},{12:[1,11]},t(p,[2,6]),{3:12,4:2,6:e,9:l,11:u,13:c},{1:[2,2]},{4:17,5:h,7:13,8:a,9:l,11:u,13:c,14:14,15:15,16:16,17:19,23:21,31:_,32:E,33:f,34:k,35:I,36:w,44:q,62:A,63:v},t(p,[2,4]),t(p,[2,5]),{1:[2,1]},{8:[1,30]},{4:17,5:h,7:31,8:a,9:l,11:u,13:c,14:14,15:15,16:16,17:19,23:21,31:_,32:E,33:f,34:k,35:I,36:w,44:q,62:A,63:v},{4:17,5:h,7:32,8:a,9:l,11:u,13:c,14:14,15:15,16:16,17:19,23:21,31:_,32:E,33:f,34:k,35:I,36:w,44:q,62:A,63:v},{4:17,5:h,7:33,8:a,9:l,11:u,13:c,14:14,15:15,16:16,17:19,23:21,31:_,32:E,33:f,34:k,35:I,36:w,44:q,62:A,63:v},{4:17,5:h,7:34,8:a,9:l,11:u,13:c,14:14,15:15,16:16,17:19,23:21,31:_,32:E,33:f,34:k,35:I,36:w,44:q,62:A,63:v},{4:17,5:h,7:35,8:a,9:l,11:u,13:c,14:14,15:15,16:16,17:19,23:21,31:_,32:E,33:f,34:k,35:I,36:w,44:q,62:A,63:v},{18:36,62:[1,37],63:[1,38]},{45:39,62:[1,40],63:[1,41]},{51:[1,42],53:[1,43]},t(F,[2,20]),t(F,[2,21]),t(F,[2,22]),t(F,[2,23]),t(F,[2,24]),t(F,[2,25]),t(ht,[2,49]),t(ht,[2,50]),{1:[2,3]},{8:[2,8]},{8:[2,9]},{8:[2,10]},{8:[2,11]},{8:[2,12]},{19:[1,44]},{19:[2,47]},{19:[2,48]},{19:[1,45]},{19:[2,53]},{19:[2,54]},{52:46,55:ct,56:ut,57:pt,58:dt,59:yt,60:mt,61:_t},{52:54,55:ct,56:ut,57:pt,58:dt,59:yt,60:mt,61:_t},{5:[1,55]},{5:[1,56]},{53:[1,57]},t(L,[2,40]),t(L,[2,41]),t(L,[2,42]),t(L,[2,43]),t(L,[2,44]),t(L,[2,45]),t(L,[2,46]),{54:[1,58]},{5:D,20:59,21:P,24:V,26:U,28:Y,30:B},{5:G,30:z,46:66,47:X,49:J},{23:71,62:A,63:v},{23:72,62:A,63:v},t(S,[2,13]),{22:[1,73]},{22:[1,74]},{22:[1,75]},{22:[1,76]},{5:D,20:77,21:P,24:V,26:U,28:Y,30:B},t(S,[2,19]),t(S,[2,33]),{22:[1,78]},{22:[1,79]},{5:G,30:z,46:80,47:X,49:J},t(S,[2,37]),t(S,[2,38]),t(S,[2,39]),{23:81,62:A,63:v},{25:82,62:[1,83],63:[1,84]},{27:85,37:[1,86],38:[1,87],39:[1,88]},{29:89,40:[1,90],41:[1,91],42:[1,92],43:[1,93]},t(S,[2,18]),{48:94,62:[1,95],63:[1,96]},{50:97,62:[1,98],63:[1,99]},t(S,[2,36]),{5:[1,100]},{5:[1,101]},{5:[2,51]},{5:[2,52]},{5:[1,102]},{5:[2,26]},{5:[2,27]},{5:[2,28]},{5:[1,103]},{5:[2,29]},{5:[2,30]},{5:[2,31]},{5:[2,32]},{5:[1,104]},{5:[2,55]},{5:[2,56]},{5:[1,105]},{5:[2,57]},{5:[2,58]},{5:D,20:106,21:P,24:V,26:U,28:Y,30:B},{5:D,20:107,21:P,24:V,26:U,28:Y,30:B},{5:D,20:108,21:P,24:V,26:U,28:Y,30:B},{5:D,20:109,21:P,24:V,26:U,28:Y,30:B},{5:G,30:z,46:110,47:X,49:J},{5:G,30:z,46:111,47:X,49:J},t(S,[2,14]),t(S,[2,15]),t(S,[2,16]),t(S,[2,17]),t(S,[2,34]),t(S,[2,35])],defaultActions:{8:[2,2],12:[2,1],30:[2,3],31:[2,8],32:[2,9],33:[2,10],34:[2,11],35:[2,12],37:[2,47],38:[2,48],40:[2,53],41:[2,54],83:[2,51],84:[2,52],86:[2,26],87:[2,27],88:[2,28],90:[2,29],91:[2,30],92:[2,31],93:[2,32],95:[2,55],96:[2,56],98:[2,57],99:[2,58]},parseError:n(function(i,d){if(!d.recoverable){var o=new Error(i);throw o.hash=d,o}this.trace(i)},"parseError"),parse:n(function(i){var d=this,o=[0],r=[],y=[null],s=[],W=this.table,g="",tt=0,gt=0,$t=s.slice.call(arguments,1),R=Object.create(this.lexer),C={yy:{}};for(var nt in this.yy)Object.prototype.hasOwnProperty.call(this.yy,nt)&&(C.yy[nt]=this.yy[nt]);R.setInput(i,C.yy),C.yy.lexer=R,C.yy.parser=this,R.yylloc===void 0&&(R.yylloc={});var st=R.yylloc;s.push(st);var Ot=R.options&&R.options.ranges;function Et(){var x;return typeof(x=r.pop()||R.lex()||1)!="number"&&(x instanceof Array&&(x=(r=x).pop()),x=d.symbols_[x]||x),x}typeof C.yy.parseError=="function"?this.parseError=C.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError,n(function(x){o.length=o.length-2*x,y.length=y.length-x,s.length=s.length-x},"popStack"),n(Et,"lex");for(var T,M,b,Rt,et,$,ft,it,Q={};;){if(M=o[o.length-1],this.defaultActions[M]?b=this.defaultActions[M]:(T==null&&(T=Et()),b=W[M]&&W[M][T]),b===void 0||!b.length||!b[0]){var It="";for(et in it=[],W[M])this.terminals_[et]&&et>2&&it.push("'"+this.terminals_[et]+"'");It=R.showPosition?"Parse error on line "+(tt+1)+`:
`+R.showPosition()+`
Expecting `+it.join(", ")+", got '"+(this.terminals_[T]||T)+"'":"Parse error on line "+(tt+1)+": Unexpected "+(T==1?"end of input":"'"+(this.terminals_[T]||T)+"'"),this.parseError(It,{text:R.match,token:this.terminals_[T]||T,line:R.yylineno,loc:st,expected:it})}if(b[0]instanceof Array&&b.length>1)throw new Error("Parse Error: multiple actions possible at state: "+M+", token: "+T);switch(b[0]){case 1:o.push(T),y.push(R.yytext),s.push(R.yylloc),o.push(b[1]),T=null,gt=R.yyleng,g=R.yytext,tt=R.yylineno,st=R.yylloc;break;case 2:if($=this.productions_[b[1]][1],Q.$=y[y.length-$],Q._$={first_line:s[s.length-($||1)].first_line,last_line:s[s.length-1].last_line,first_column:s[s.length-($||1)].first_column,last_column:s[s.length-1].last_column},Ot&&(Q._$.range=[s[s.length-($||1)].range[0],s[s.length-1].range[1]]),(Rt=this.performAction.apply(Q,[g,gt,tt,C.yy,b[1],y,s].concat($t)))!==void 0)return Rt;$&&(o=o.slice(0,-1*$*2),y=y.slice(0,-1*$),s=s.slice(0,-1*$)),o.push(this.productions_[b[1]][0]),y.push(Q.$),s.push(Q._$),ft=W[o[o.length-2]][o[o.length-1]],o.push(ft);break;case 3:return!0}}return!0},"parse")},vt=function(){return{EOF:1,parseError:n(function(i,d){if(!this.yy.parser)throw new Error(i);this.yy.parser.parseError(i,d)},"parseError"),setInput:n(function(i,d){return this.yy=d||this.yy||{},this._input=i,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:n(function(){var i=this._input[0];return this.yytext+=i,this.yyleng++,this.offset++,this.match+=i,this.matched+=i,i.match(/(?:\r\n?|\n).*/g)?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),i},"input"),unput:n(function(i){var d=i.length,o=i.split(/(?:\r\n?|\n)/g);this._input=i+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-d),this.offset-=d;var r=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),o.length-1&&(this.yylineno-=o.length-1);var y=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:o?(o.length===r.length?this.yylloc.first_column:0)+r[r.length-o.length].length-o[0].length:this.yylloc.first_column-d},this.options.ranges&&(this.yylloc.range=[y[0],y[0]+this.yyleng-d]),this.yyleng=this.yytext.length,this},"unput"),more:n(function(){return this._more=!0,this},"more"),reject:n(function(){return this.options.backtrack_lexer?(this._backtrack=!0,this):this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"reject"),less:n(function(i){this.unput(this.match.slice(i))},"less"),pastInput:n(function(){var i=this.matched.substr(0,this.matched.length-this.match.length);return(i.length>20?"...":"")+i.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:n(function(){var i=this.match;return i.length<20&&(i+=this._input.substr(0,20-i.length)),(i.substr(0,20)+(i.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:n(function(){var i=this.pastInput(),d=new Array(i.length+1).join("-");return i+this.upcomingInput()+`
`+d+"^"},"showPosition"),test_match:n(function(i,d){var o,r,y;if(this.options.backtrack_lexer&&(y={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(y.yylloc.range=this.yylloc.range.slice(0))),(r=i[0].match(/(?:\r\n?|\n).*/g))&&(this.yylineno+=r.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:r?r[r.length-1].length-r[r.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+i[0].length},this.yytext+=i[0],this.match+=i[0],this.matches=i,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(i[0].length),this.matched+=i[0],o=this.performAction.call(this,this.yy,this,d,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),o)return o;if(this._backtrack){for(var s in y)this[s]=y[s];return!1}return!1},"test_match"),next:n(function(){if(this.done)return this.EOF;var i,d,o,r;this._input||(this.done=!0),this._more||(this.yytext="",this.match="");for(var y=this._currentRules(),s=0;s<y.length;s++)if((o=this._input.match(this.rules[y[s]]))&&(!d||o[0].length>d[0].length)){if(d=o,r=s,this.options.backtrack_lexer){if((i=this.test_match(o,y[s]))!==!1)return i;if(this._backtrack){d=!1;continue}return!1}if(!this.options.flex)break}return d?(i=this.test_match(d,y[r]))!==!1&&i:this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:n(function(){var i=this.next();return i||this.lex()},"lex"),begin:n(function(i){this.conditionStack.push(i)},"begin"),popState:n(function(){return this.conditionStack.length-1>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:n(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:n(function(i){return(i=this.conditionStack.length-1-Math.abs(i||0))>=0?this.conditionStack[i]:"INITIAL"},"topState"),pushState:n(function(i){this.begin(i)},"pushState"),stateStackSize:n(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:n(function(i,d,o,r){switch(o){case 0:return"title";case 1:return this.begin("acc_title"),9;case 2:return this.popState(),"acc_title_value";case 3:return this.begin("acc_descr"),11;case 4:return this.popState(),"acc_descr_value";case 5:this.begin("acc_descr_multiline");break;case 6:case 48:this.popState();break;case 7:return"acc_descr_multiline_value";case 8:return 5;case 9:case 10:case 11:break;case 12:return 8;case 13:return 6;case 14:return 19;case 15:return 30;case 16:return 22;case 17:return 21;case 18:return 24;case 19:return 26;case 20:return 28;case 21:return 31;case 22:return 32;case 23:return 33;case 24:return 34;case 25:return 35;case 26:return 36;case 27:return 37;case 28:return 38;case 29:return 39;case 30:return 40;case 31:return 41;case 32:return 42;case 33:return 43;case 34:return 44;case 35:return 55;case 36:return 56;case 37:return 57;case 38:return 58;case 39:return 59;case 40:return 60;case 41:return 61;case 42:return 47;case 43:return 49;case 44:return 51;case 45:return 54;case 46:return 53;case 47:this.begin("string");break;case 49:return"qString";case 50:return d.yytext=d.yytext.trim(),62}},"anonymous"),rules:[/^(?:title\s[^#\n;]+)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?:(\r?\n)+)/i,/^(?:\s+)/i,/^(?:#[^\n]*)/i,/^(?:%[^\n]*)/i,/^(?:$)/i,/^(?:requirementDiagram\b)/i,/^(?:\{)/i,/^(?:\})/i,/^(?::)/i,/^(?:id\b)/i,/^(?:text\b)/i,/^(?:risk\b)/i,/^(?:verifyMethod\b)/i,/^(?:requirement\b)/i,/^(?:functionalRequirement\b)/i,/^(?:interfaceRequirement\b)/i,/^(?:performanceRequirement\b)/i,/^(?:physicalRequirement\b)/i,/^(?:designConstraint\b)/i,/^(?:low\b)/i,/^(?:medium\b)/i,/^(?:high\b)/i,/^(?:analysis\b)/i,/^(?:demonstration\b)/i,/^(?:inspection\b)/i,/^(?:test\b)/i,/^(?:element\b)/i,/^(?:contains\b)/i,/^(?:copies\b)/i,/^(?:derives\b)/i,/^(?:satisfies\b)/i,/^(?:verifies\b)/i,/^(?:refines\b)/i,/^(?:traces\b)/i,/^(?:type\b)/i,/^(?:docref\b)/i,/^(?:<-)/i,/^(?:->)/i,/^(?:-)/i,/^(?:["])/i,/^(?:["])/i,/^(?:[^"]*)/i,/^(?:[\w][^\r\n\{\<\>\-\=]*)/i],conditions:{acc_descr_multiline:{rules:[6,7],inclusive:!1},acc_descr:{rules:[4],inclusive:!1},acc_title:{rules:[2],inclusive:!1},unqString:{rules:[],inclusive:!1},token:{rules:[],inclusive:!1},string:{rules:[48,49],inclusive:!1},INITIAL:{rules:[0,1,3,5,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,50],inclusive:!0}}}}();function Z(){this.yy={}}return rt.lexer=vt,n(Z,"Parser"),Z.prototype=rt,rt.Parser=Z,new Z}();ot.parser=ot;var Bt=ot,lt=[],N={},K=new Map,O={},j=new Map,Qt=n((t,e)=>(K.has(t)||K.set(t,{name:t,type:e,id:N.id,text:N.text,risk:N.risk,verifyMethod:N.verifyMethod}),N={},K.get(t)),"addRequirement"),Ht=n(()=>K,"getRequirements"),Wt=n(t=>{N!==void 0&&(N.id=t)},"setNewReqId"),Kt=n(t=>{N!==void 0&&(N.text=t)},"setNewReqText"),jt=n(t=>{N!==void 0&&(N.risk=t)},"setNewReqRisk"),Gt=n(t=>{N!==void 0&&(N.verifyMethod=t)},"setNewReqVerifyMethod"),zt=n(t=>(j.has(t)||(j.set(t,{name:t,type:O.type,docRef:O.docRef}),bt.info("Added new requirement: ",t)),O={},j.get(t)),"addElement"),Xt=n(()=>j,"getElements"),Jt=n(t=>{O!==void 0&&(O.type=t)},"setNewElementType"),Zt=n(t=>{O!==void 0&&(O.docRef=t)},"setNewElementDocRef"),te=n((t,e,l)=>{lt.push({type:t,src:e,dst:l})},"addRelationship"),ee=n(()=>lt,"getRelationships"),ie=n(()=>{lt=[],N={},K=new Map,O={},j=new Map,Dt()},"clear"),re={RequirementType:{REQUIREMENT:"Requirement",FUNCTIONAL_REQUIREMENT:"Functional Requirement",INTERFACE_REQUIREMENT:"Interface Requirement",PERFORMANCE_REQUIREMENT:"Performance Requirement",PHYSICAL_REQUIREMENT:"Physical Requirement",DESIGN_CONSTRAINT:"Design Constraint"},RiskLevel:{LOW_RISK:"Low",MED_RISK:"Medium",HIGH_RISK:"High"},VerifyType:{VERIFY_ANALYSIS:"Analysis",VERIFY_DEMONSTRATION:"Demonstration",VERIFY_INSPECTION:"Inspection",VERIFY_TEST:"Test"},Relationships:{CONTAINS:"contains",COPIES:"copies",DERIVES:"derives",SATISFIES:"satisfies",VERIFIES:"verifies",REFINES:"refines",TRACES:"traces"},getConfig:n(()=>Tt().req,"getConfig"),addRequirement:Qt,getRequirements:Ht,setNewReqId:Wt,setNewReqText:Kt,setNewReqRisk:jt,setNewReqVerifyMethod:Gt,setAccTitle:Lt,getAccTitle:Ct,setAccDescription:Mt,getAccDescription:Ft,addElement:zt,getElements:Xt,setNewElementType:Jt,setNewElementDocRef:Zt,addRelationship:te,getRelationships:ee,clear:ie},ne=n(t=>`

  marker {
    fill: ${t.relationColor};
    stroke: ${t.relationColor};
  }

  marker.cross {
    stroke: ${t.lineColor};
  }

  svg {
    font-family: ${t.fontFamily};
    font-size: ${t.fontSize};
  }

  .reqBox {
    fill: ${t.requirementBackground};
    fill-opacity: 1.0;
    stroke: ${t.requirementBorderColor};
    stroke-width: ${t.requirementBorderSize};
  }
  
  .reqTitle, .reqLabel{
    fill:  ${t.requirementTextColor};
  }
  .reqLabelBox {
    fill: ${t.relationLabelBackground};
    fill-opacity: 1.0;
  }

  .req-title-line {
    stroke: ${t.requirementBorderColor};
    stroke-width: ${t.requirementBorderSize};
  }
  .relationshipLine {
    stroke: ${t.relationColor};
    stroke-width: 1;
  }
  .relationshipLabel {
    fill: ${t.relationLabelColor};
  }

`,"getStyles"),at={CONTAINS:"contains",ARROW:"arrow"},xt={ReqMarkers:at,insertLineEndings:n((t,e)=>{let l=t.append("defs").append("marker").attr("id",at.CONTAINS+"_line_ending").attr("refX",0).attr("refY",e.line_height/2).attr("markerWidth",e.line_height).attr("markerHeight",e.line_height).attr("orient","auto").append("g");l.append("circle").attr("cx",e.line_height/2).attr("cy",e.line_height/2).attr("r",e.line_height/2).attr("fill","none"),l.append("line").attr("x1",0).attr("x2",e.line_height).attr("y1",e.line_height/2).attr("y2",e.line_height/2).attr("stroke-width",1),l.append("line").attr("y1",0).attr("y2",e.line_height).attr("x1",e.line_height/2).attr("x2",e.line_height/2).attr("stroke-width",1),t.append("defs").append("marker").attr("id",at.ARROW+"_line_ending").attr("refX",e.line_height).attr("refY",.5*e.line_height).attr("markerWidth",e.line_height).attr("markerHeight",e.line_height).attr("orient","auto").append("path").attr("d",`M0,0
      L${e.line_height},${e.line_height/2}
      M${e.line_height},${e.line_height/2}
      L0,${e.line_height}`).attr("stroke-width",1)},"insertLineEndings")},m={},kt=0,At=n((t,e)=>t.insert("rect","#"+e).attr("class","req reqBox").attr("x",0).attr("y",0).attr("width",m.rect_min_width+"px").attr("height",m.rect_min_height+"px"),"newRectNode"),wt=n((t,e,l)=>{let u=m.rect_min_width/2,c=t.append("text").attr("class","req reqLabel reqTitle").attr("id",e).attr("x",u).attr("y",m.rect_padding).attr("dominant-baseline","hanging"),p=0;l.forEach(a=>{p==0?c.append("tspan").attr("text-anchor","middle").attr("x",m.rect_min_width/2).attr("dy",0).text(a):c.append("tspan").attr("text-anchor","middle").attr("x",m.rect_min_width/2).attr("dy",.75*m.line_height).text(a),p++});let h=1.5*m.rect_padding+p*m.line_height*.75;return t.append("line").attr("class","req-title-line").attr("x1","0").attr("x2",m.rect_min_width).attr("y1",h).attr("y2",h),{titleNode:c,y:h}},"newTitleNode"),qt=n((t,e,l,u)=>{let c=t.append("text").attr("class","req reqLabel").attr("id",e).attr("x",m.rect_padding).attr("y",u).attr("dominant-baseline","hanging"),p=0,h=[];return l.forEach(a=>{let _=a.length;for(;_>30&&p<3;){let E=a.substring(0,30);_=(a=a.substring(30,a.length)).length,h[h.length]=E,p++}if(p==3){let E=h[h.length-1];h[h.length-1]=E.substring(0,E.length-4)+"..."}else h[h.length]=a;p=0}),h.forEach(a=>{c.append("tspan").attr("x",m.rect_padding).attr("dy",m.line_height).text(a)}),c},"newBodyNode"),se=n((t,e,l,u)=>{const c=e.node().getTotalLength(),p=e.node().getPointAtLength(.5*c),h="rel"+kt;kt++;const a=t.append("text").attr("class","req relationshipLabel").attr("id",h).attr("x",p.x).attr("y",p.y).attr("text-anchor","middle").attr("dominant-baseline","middle").text(u).node().getBBox();t.insert("rect","#"+h).attr("class","req reqLabelBox").attr("x",p.x-a.width/2).attr("y",p.y-a.height/2).attr("width",a.width).attr("height",a.height).attr("fill","white").attr("fill-opacity","85%")},"addEdgeLabel"),ae=n(function(t,e,l,u,c){const p=l.edge(H(e.src),H(e.dst)),h=Vt().x(function(_){return _.x}).y(function(_){return _.y}),a=t.insert("path","#"+u).attr("class","er relationshipLine").attr("d",h(p.points)).attr("fill","none");e.type==c.db.Relationships.CONTAINS?a.attr("marker-start","url("+Nt.getUrl(m.arrowMarkerAbsolute)+"#"+e.type+"_line_ending)"):(a.attr("stroke-dasharray","10,7"),a.attr("marker-end","url("+Nt.getUrl(m.arrowMarkerAbsolute)+"#"+xt.ReqMarkers.ARROW+"_line_ending)")),se(t,a,m,`<<${e.type}>>`)},"drawRelationshipFromLayout"),oe=n((t,e,l)=>{t.forEach((u,c)=>{c=H(c),bt.info("Added new requirement: ",c);const p=l.append("g").attr("id",c),h=At(p,"req-"+c);let a=[],_=wt(p,c+"_title",[`<<${u.type}>>`,`${u.name}`]);a.push(_.titleNode);let E=qt(p,c+"_body",[`Id: ${u.id}`,`Text: ${u.text}`,`Risk: ${u.risk}`,`Verification: ${u.verifyMethod}`],_.y);a.push(E);const f=h.node().getBBox();e.setNode(c,{width:f.width,height:f.height,shape:"rect",id:c})})},"drawReqs"),le=n((t,e,l)=>{t.forEach((u,c)=>{const p=H(c),h=l.append("g").attr("id",p),a="element-"+p,_=At(h,a);let E=[],f=wt(h,a+"_title",["<<Element>>",`${c}`]);E.push(f.titleNode);let k=qt(h,a+"_body",[`Type: ${u.type||"Not Specified"}`,`Doc Ref: ${u.docRef||"None"}`],f.y);E.push(k);const I=_.node().getBBox();e.setNode(p,{width:I.width,height:I.height,shape:"rect",id:p})})},"drawElements"),he=n((t,e)=>(t.forEach(function(l){let u=H(l.src),c=H(l.dst);e.setEdge(u,c,{relationship:l})}),t),"addRelationships"),ce=n(function(t,e){e.nodes().forEach(function(l){l!==void 0&&e.node(l)!==void 0&&(t.select("#"+l),t.select("#"+l).attr("transform","translate("+(e.node(l).x-e.node(l).width/2)+","+(e.node(l).y-e.node(l).height/2)+" )"))})},"adjustEntities"),H=n(t=>t.replace(/\s/g,"").replace(/\./g,"_"),"elementString"),ii={parser:Bt,db:re,renderer:{draw:n((t,e,l,u)=>{const c=(m=Tt().requirement).securityLevel;let p;c==="sandbox"&&(p=St("#i"+e));const h=St(c==="sandbox"?p.nodes()[0].contentDocument.body:"body").select(`[id='${e}']`);xt.insertLineEndings(h,m);const a=new Ut({multigraph:!1,compound:!1,directed:!0}).setGraph({rankdir:m.layoutDirection,marginx:20,marginy:20,nodesep:100,edgesep:100,ranksep:100}).setDefaultEdgeLabel(function(){return{}});let _=u.db.getRequirements(),E=u.db.getElements(),f=u.db.getRelationships();oe(_,a,h),le(E,a,h),he(f,a),Yt(a),ce(h,a),f.forEach(function(A){ae(h,A,a,e,u)});const k=m.rect_padding,I=h.node().getBBox(),w=I.width+2*k,q=I.height+2*k;Pt(h,q,w,m.useMaxWidth),h.attr("viewBox",`${I.x-k} ${I.y-k} ${w} ${q}`)},"draw")},styles:ne};export{ii as diagram};
