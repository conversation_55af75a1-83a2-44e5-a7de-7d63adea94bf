#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour examiner le contenu du fichier de conversation JSON
"""

import json
from pathlib import Path

def examiner_fichier_conversation():
    """Examine le contenu du fichier de conversation"""
    
    file_path = "C:/Users/<USER>/AppData/Roaming/Code/User/workspaceStorage/591be8b7f6bd4169258c0affc2eaa1fc/Augment.vscode-augment/augment-user-assets/checkpoint-documents/d6413184-fd14-466b-8484-4d5e99f66884/document-c__Users_Administrateur_Desktop_Travail_Projet6_script_conversation_chronologique.py-1749026244639-fceea3fb-0877-4f8f-b5ba-ade44173e3e8.json"
    
    print("🔍 EXAMEN DU FICHIER DE CONVERSATION")
    print("=" * 60)
    print(f"📁 Fichier: {file_path}")
    
    try:
        # Lire le fichier
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"📏 Taille du fichier: {len(content)} caractères")
        
        # Copier vers un fichier simple
        simple_path = "C:/temp_conversation/conversation.json"
        Path("C:/temp_conversation").mkdir(exist_ok=True)
        
        with open(simple_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ Fichier copié vers: {simple_path}")
        
        # Parser le JSON
        try:
            data = json.loads(content)
            print("✅ JSON parsé avec succès")
            
            # Examiner la structure
            print(f"\n🔧 STRUCTURE DU JSON:")
            if isinstance(data, dict):
                for key, value in data.items():
                    print(f"🔑 {key}: {type(value).__name__} ({len(str(value))} caractères)")
            
            # Examiner le contenu original et modifié
            original_code = data.get('originalCode', '')
            modified_code = data.get('modifiedCode', '')
            
            print(f"\n📝 CONTENU ORIGINAL (premiers 500 caractères):")
            print("-" * 60)
            print(original_code[:500])
            print("-" * 60)
            
            print(f"\n📝 CONTENU MODIFIÉ (premiers 500 caractères):")
            print("-" * 60)
            print(modified_code[:500])
            print("-" * 60)
            
            # Analyser le format de conversation
            print(f"\n🔍 ANALYSE DU FORMAT DE CONVERSATION:")
            
            # Chercher des patterns de conversation
            combined_content = original_code + "\n" + modified_code
            
            # Chercher des indicateurs de messages
            patterns = [
                "Human:",
                "Assistant:",
                "User:",
                "AI:",
                "<<HUMAN_CONVERSATION_START>>",
                "**",
                "##",
                "🎯",
                "✅",
                "❌"
            ]
            
            found_patterns = []
            for pattern in patterns:
                count = combined_content.count(pattern)
                if count > 0:
                    found_patterns.append(f"{pattern} ({count}x)")
            
            if found_patterns:
                print(f"🔑 Patterns trouvés: {', '.join(found_patterns)}")
            
            # Chercher des séparateurs de messages
            lines = combined_content.split('\n')
            print(f"📄 Nombre de lignes: {len(lines)}")
            
            # Afficher quelques lignes pour voir le format
            print(f"\n📋 APERÇU DES PREMIÈRES LIGNES:")
            for i, line in enumerate(lines[:20], 1):
                if line.strip():
                    print(f"{i:2d}: {line[:80]}{'...' if len(line) > 80 else ''}")
            
            return data, simple_path
            
        except json.JSONDecodeError as e:
            print(f"❌ Erreur parsing JSON: {e}")
            return None, simple_path
            
    except Exception as e:
        print(f"❌ Erreur lecture fichier: {e}")
        return None, None

if __name__ == "__main__":
    print("🚀 EXAMEN DU FICHIER DE CONVERSATION")
    print("=" * 60)
    
    data, file_path = examiner_fichier_conversation()
    
    if data:
        print(f"\n✅ EXAMEN TERMINÉ!")
        print(f"📄 Fichier copié vers: {file_path}")
        print(f"🔍 Prêt pour l'analyse du format de conversation")
    else:
        print(f"\n❌ ERREUR lors de l'examen")
