#!/usr/bin/env python3
"""
CRÉATEUR D'HISTORIQUE COMPLET DE CONVERSATION AUGMENT
=====================================================

Ce script extrait l'historique complet de la conversation actuelle
et le formate de manière lisible avec chronologie précise.
"""

import sqlite3
import json
import datetime
from pathlib import Path

def extraire_historique_complet():
    """Extrait et formate l'historique complet de la conversation"""
    
    state_file = Path("C:/Users/<USER>/AppData/Roaming/Code/User/workspaceStorage/a35ba43ef26792e61fe787a234121806/state.vscdb")
    
    print("🔍 EXTRACTION HISTORIQUE COMPLET CONVERSATION AUGMENT")
    print("=" * 60)
    
    if not state_file.exists():
        print(f"❌ Fichier state.vscdb non trouvé: {state_file}")
        return
    
    try:
        conn = sqlite3.connect(str(state_file))
        cursor = conn.cursor()
        
        # Récupérer les données de conversation
        cursor.execute("SELECT value FROM ItemTable WHERE key = 'memento/webviewView.augment-chat';")
        result = cursor.fetchone()
        
        if not result:
            print("❌ Clé de conversation non trouvée")
            conn.close()
            return
        
        value = result[0]
        if isinstance(value, bytes):
            value_str = value.decode('utf-8')
        else:
            value_str = str(value)
        
        # Parser les données
        main_data = json.loads(value_str)
        webview_data = json.loads(main_data['webviewState'])
        
        conversations = webview_data.get('conversations', {})
        current_conv_id = webview_data.get('currentConversationId')
        
        print(f"📊 Conversations trouvées: {len(conversations)}")
        print(f"🎯 Conversation actuelle: {current_conv_id}")
        
        if current_conv_id and current_conv_id in conversations:
            conversation = conversations[current_conv_id]
            chat_history = conversation.get('chatHistory', [])
            
            print(f"💬 Messages dans l'historique: {len(chat_history)}")
            
            # Créer l'historique formaté
            output_file = f"historique_conversation_complete_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            
            with open(output_file, 'w', encoding='utf-8') as f:
                # En-tête
                f.write("HISTORIQUE COMPLET CONVERSATION AUGMENT\n")
                f.write("=" * 80 + "\n")
                f.write(f"Conversation ID: {current_conv_id}\n")
                f.write(f"Créée le: {conversation.get('createdAtIso', 'N/A')}\n")
                f.write(f"Dernière interaction: {conversation.get('lastInteractedAtIso', 'N/A')}\n")
                f.write(f"Nombre de messages: {len(chat_history)}\n")
                f.write(f"Extrait le: {datetime.datetime.now().isoformat()}\n")
                f.write("=" * 80 + "\n\n")
                
                # Traiter chaque message
                for i, message in enumerate(chat_history):
                    f.write(f"MESSAGE {i+1}\n")
                    f.write("-" * 40 + "\n")
                    
                    # Timestamp si disponible
                    if 'timestamp' in message:
                        f.write(f"Timestamp: {message['timestamp']}\n")
                    
                    # Request ID
                    if 'request_id' in message:
                        f.write(f"Request ID: {message['request_id']}\n")
                    
                    # Message utilisateur
                    if 'request_message' in message and message['request_message']:
                        f.write(f"\n👤 UTILISATEUR:\n")
                        f.write(f"{message['request_message']}\n")
                    
                    # Réponse assistant
                    if 'structured_output_nodes' in message:
                        assistant_content = ""
                        for node in message['structured_output_nodes']:
                            if node.get('type') == 0 and node.get('content'):
                                assistant_content = node['content']
                                break
                        
                        if assistant_content:
                            f.write(f"\n🤖 ASSISTANT:\n")
                            f.write(f"{assistant_content}\n")
                    
                    # Outils utilisés
                    if 'structured_output_nodes' in message:
                        tools_used = []
                        for node in message['structured_output_nodes']:
                            if node.get('type') == 5 and node.get('tool_use'):
                                tool_info = node['tool_use']
                                tools_used.append({
                                    'name': tool_info.get('tool_name', 'unknown'),
                                    'input': tool_info.get('input_json', '{}')
                                })
                        
                        if tools_used:
                            f.write(f"\n🔧 OUTILS UTILISÉS:\n")
                            for tool in tools_used:
                                f.write(f"  - {tool['name']}\n")
                                try:
                                    input_data = json.loads(tool['input'])
                                    if 'command' in input_data:
                                        f.write(f"    Commande: {input_data['command'][:100]}...\n")
                                    elif 'path' in input_data:
                                        f.write(f"    Chemin: {input_data['path']}\n")
                                except:
                                    pass
                    
                    # Résultats d'outils
                    if 'structured_request_nodes' in message:
                        for node in message['structured_request_nodes']:
                            if node.get('type') == 1 and 'tool_result_node' in node:
                                result_content = node['tool_result_node'].get('content', '')
                                if result_content and len(result_content) > 50:
                                    f.write(f"\n📋 RÉSULTAT OUTIL:\n")
                                    # Limiter l'affichage pour éviter les très longs résultats
                                    if len(result_content) > 500:
                                        f.write(f"{result_content[:500]}...\n[Résultat tronqué - {len(result_content)} caractères total]\n")
                                    else:
                                        f.write(f"{result_content}\n")
                    
                    f.write("\n" + "=" * 80 + "\n\n")
            
            print(f"✅ Historique complet sauvegardé dans: {output_file}")
            
            # Créer aussi une version JSON structurée
            json_output = f"historique_conversation_complete_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            structured_history = {
                "conversation_id": current_conv_id,
                "created_at": conversation.get('createdAtIso'),
                "last_interaction": conversation.get('lastInteractedAtIso'),
                "extracted_at": datetime.datetime.now().isoformat(),
                "message_count": len(chat_history),
                "messages": []
            }
            
            for i, message in enumerate(chat_history):
                structured_msg = {
                    "index": i + 1,
                    "timestamp": message.get('timestamp'),
                    "request_id": message.get('request_id'),
                    "user_message": message.get('request_message', ''),
                    "assistant_response": "",
                    "tools_used": [],
                    "raw_data": message
                }
                
                # Extraire réponse assistant
                if 'structured_output_nodes' in message:
                    for node in message['structured_output_nodes']:
                        if node.get('type') == 0 and node.get('content'):
                            structured_msg["assistant_response"] = node['content']
                            break
                
                # Extraire outils
                if 'structured_output_nodes' in message:
                    for node in message['structured_output_nodes']:
                        if node.get('type') == 5 and node.get('tool_use'):
                            tool_info = node['tool_use']
                            structured_msg["tools_used"].append({
                                'name': tool_info.get('tool_name'),
                                'input': tool_info.get('input_json')
                            })
                
                structured_history["messages"].append(structured_msg)
            
            with open(json_output, 'w', encoding='utf-8') as f:
                json.dump(structured_history, f, indent=2, ensure_ascii=False)
            
            print(f"✅ Version JSON sauvegardée dans: {json_output}")
            
            # Statistiques
            user_messages = sum(1 for msg in chat_history if msg.get('request_message'))
            assistant_messages = sum(1 for msg in chat_history if any(
                node.get('type') == 0 and node.get('content') 
                for node in msg.get('structured_output_nodes', [])
            ))
            
            print(f"\n📈 STATISTIQUES:")
            print(f"  Messages utilisateur: {user_messages}")
            print(f"  Réponses assistant: {assistant_messages}")
            print(f"  Total messages: {len(chat_history)}")
        
        else:
            print(f"❌ Conversation actuelle non trouvée")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    extraire_historique_complet()
