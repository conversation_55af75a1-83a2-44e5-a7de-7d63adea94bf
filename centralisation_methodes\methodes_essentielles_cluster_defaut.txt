MÉTHODES ESSENTIELLES DU CLUSTER PAR DÉFAUT - R<PERSON><PERSON>ÉRENCE COMPLÈTE
================================================================

Date de création : 2024-06-04
Source : centralisation_methodes/class.txt
Objectif : Centralisation des méthodes du cluster par défaut vers AZRCluster universelle

TOTAL : 31 MÉTHODES ESSENTIELLES IDENTIFIÉES
==========================================

🎯 ROLLOUT 1 ANALYSEUR : 15 méthodes
===================================

MÉTHODE PRINCIPALE :
1. _rollout_analyzer() - Ligne 108-220
   Description : Rollout 1 Analyseur de Biais - Exploitation des contraintes structurelles du baccarat
   Appelle : méthodes 2-11

MÉTHODES SUPPORT PRIORITÉS :
2. _analyze_impair_consecutive_bias() - Ligne 413-640
   Description : PRIORITÉ 1 - Analyse COMPLÈTE des IMPAIRS (isolés + séquences)
   Appelle : méthodes 12-15

3. _analyze_pair_priority_2_autonomous() - Ligne 641-751
   Description : PRIORITÉ 2 - Analyse AUTONOME des PAIRS en contexte des IMPAIRS
   
4. _analyze_sync_alternation_bias() - Ligne 753-907
   Description : PRIORITÉ 3 - Analyse des biais d'alternance sync/desync (3ème carte)
   
5. _analyze_combined_structural_bias() - Ligne 2250-2511
   Description : PRIORITÉ 4 - Analyse BIAIS COMBINÉS (tous indices)

MÉTHODES CORRÉLATION PRINCIPALES :
6. _correlate_bias_to_pb_variations() - Ligne 1950-2000
   Description : Corrélation des biais structurels avec variations P/B
   
7. _correlate_bias_to_so_variations() - Ligne 2001-2049
   Description : Corrélation avec variations S/O

MÉTHODES SYNTHÈSE :
8. _generate_priority_based_synthesis_autonomous() - Ligne 2512-2834
   Description : Synthèse autonome des biais (ROLLOUT 1 INDÉPENDANT)
   
9. _generate_bias_signals_summary() - Ligne 2835-2850
   Description : Génération signaux de biais pour Rollout 2
   
10. _generate_bias_generation_guidance() - Ligne 2851-2863
    Description : Guidance de génération pour Rollout 2
    
11. _generate_bias_quick_access() - Ligne 2864-2876
    Description : Accès rapide aux signaux prioritaires

MÉTHODES CORRÉLATION SUPPORT (appelées par méthode 2) :
12. _correlate_impair_with_sync() - Ligne 1787-1827
    Description : Corrélation Index 1 (IMPAIR) → Index 2 (SYNC/DESYNC)
    
13. _correlate_impair_with_combined() - Ligne 1828-1869
    Description : Corrélation Index 1 (IMPAIR) → Index 3 (COMBINED)
    
14. _correlate_impair_with_pb() - Ligne 1870-1909
    Description : Corrélation IMPAIR → P/B (Index 4)
    
15. _correlate_impair_with_so() - Ligne 1910-1949
    Description : Corrélation IMPAIR → S/O (Index 5)

🎯 ROLLOUT 2 GÉNÉRATEUR : 5 méthodes
===================================

MÉTHODE PRINCIPALE :
16. _rollout_generator() - Ligne 3201-3306
    Description : Rollout 2 Générateur - Génération séquences candidates basées sur analyse complète
    Appelle : méthodes 17-20

MÉTHODES SUPPORT :
17. _define_optimized_generation_space() - Ligne 4531-4567
    Description : Définition espace de génération basé sur signaux optimisés
    
18. _generate_sequences_from_signals() - Ligne 4568-4653
    Description : Génération séquences candidates basées sur signaux
    
19. _generate_fallback_sequences() - Ligne 4654-4830
    Description : Génération classique si pas de signaux optimisés
    
20. _enrich_sequences_with_complete_indexes() - Ligne 4831-4889
    Description : Enrichissement séquences avec tous les indices

🎯 ROLLOUT 3 PRÉDICTEUR : 11 méthodes
====================================

MÉTHODE PRINCIPALE :
21. _rollout_predictor() - Ligne 3308-3431
    Description : Rollout 3 Prédicteur - Sélection séquence optimale finale
    Appelle : méthodes 22-25

MÉTHODES SUPPORT PRINCIPALES :
22. _evaluate_sequence_quality() - Ligne 3437-3471
    Description : Évalue la qualité d'une séquence candidate
    Appelle : méthodes 26-30
    
23. _select_best_sequence() - Ligne 3749-3786
    Description : Sélectionne la meilleure séquence parmi les candidates évaluées
    
24. _calculate_cluster_confidence_azr_calibrated() - Ligne 3833-4095
    Description : Calcule la confiance calibrée selon les formules AZR officielles
    
25. _convert_pb_sequence_to_so() - Ligne 4096-4530
    Description : Convertit une séquence P/B en séquence S/O (Same/Opposite)

MÉTHODES ÉVALUATION SUPPORT (appelées par méthode 22) :
26. _evaluate_signal_alignment() - Ligne 3473-3527
    Description : Évalue l'alignement de la séquence avec les signaux du Rollout 1
    
27. _analyze_sequence_consistency() - Ligne 3553-3611
    Description : Analyse la cohérence interne d'une séquence
    
28. _assess_risk_reward_ratio() - Ligne 3613-3657
    Description : Évalue le ratio risque/récompense d'une séquence
    
29. _validate_sequence_logic() - Ligne 3659-3747
    Description : Valide la logique d'une séquence par rapport aux découvertes du Rollout 1
    
30. _calculate_sequence_score() - Ligne 3465 (référencée)
    Description : Calcul du score total pondéré d'une séquence
    
31. _evaluate_fallback_alignment() - Ligne 3529-3551
    Description : Évaluation d'alignement fallback basée sur les données détaillées

STRUCTURE HIÉRARCHIQUE :
=======================

ROLLOUT 1 (Analyseur) → ROLLOUT 2 (Générateur) → ROLLOUT 3 (Prédicteur)
     ↓                        ↓                        ↓
15 méthodes support      5 méthodes support      11 méthodes support

DÉPENDANCES :
- Rollout 2 utilise les signaux générés par Rollout 1
- Rollout 3 utilise les séquences générées par Rollout 2 et l'analyse du Rollout 1
- Toutes les méthodes doivent être centralisées avec Parameter Object Pattern (AZRConfig)

ANALYSE CENTRALISATION PARAMÈTRES :
===================================

VÉRIFICATION EN COURS - PARAMÈTRES IDENTIFIÉS DANS LES MÉTHODES :

MÉTHODE 2 : _analyze_impair_consecutive_bias() - Ligne 413-640
------------------------------------------------------------
Paramètres utilisés via self.config :
✅ zero_value (ligne 433, 434, 578, 587-590)
✅ one_value (ligne 449, 453, 488, 498, 527, 535)
✅ two_value (ligne 453)
✅ rollout_analyzer_sequence_increment (ligne 450, 485)
✅ attention_level_base (ligne 517)
✅ rarity_factor_high_str (ligne 518)
✅ rollout1_impair_consecutive_common (ligne 519)
✅ attention_level_max (ligne 527)
✅ attention_power_base (ligne 527)
✅ length_threshold_3 (ligne 534)
✅ rarity_factor_ultra_high_str (ligne 534)
✅ rarity_factor_very_high_str (ligne 534)
✅ quality_threshold_30_percent (ligne 535)
✅ multiplier_increment_02 (ligne 535)

STATUT : PARAMÈTRES CENTRALISÉS ✅

MÉTHODE 2 (suite) : _analyze_impair_consecutive_bias() - Ligne 600-640
--------------------------------------------------------------------
Paramètres supplémentaires identifiés :
✅ normalization_factor_10 (ligne 602)
✅ correlation_strength_threshold (ligne 606, 608, 610, 612)
✅ correlation_strong_bonus_factor (ligne 607, 609)
✅ correlation_bonus_factor (ligne 611, 613)
✅ rollout_analyzer_min_sequence_length (ligne 622, 627)
✅ get_cluster_recent_window_size() (ligne 625) - MÉTHODE SPÉCIALISÉE
✅ variance_threshold_minimum (ligne 631)
✅ half_value (ligne 633, 635)

MÉTHODE 3 : _analyze_pair_priority_2_autonomous() - Ligne 641-751
---------------------------------------------------------------
Paramètres utilisés via self.config :
✅ zero_value (ligne 658, 659, 669, 692)
✅ one_value (ligne 668, 678, 685, 691, 696)
✅ two_value (ligne 669)

STATUT GLOBAL : PARAMÈTRES CENTRALISÉS ✅

OBSERVATION IMPORTANTE :
=======================
✅ Tous les paramètres utilisés dans les méthodes sont déjà centralisés dans AZRConfig
✅ La méthode get_cluster_recent_window_size() est déjà implémentée pour la spécialisation
✅ Les méthodes utilisent correctement self.config pour accéder aux paramètres

CONCLUSION :
============
Les méthodes du cluster par défaut sont PRÊTES pour la centralisation car :
1. Tous les paramètres sont déjà centralisés dans AZRConfig
2. La structure Parameter Object Pattern est déjà en place
3. Les spécialisations cluster sont déjà supportées

PROCHAINE ÉTAPE :
================
Commencer la centralisation des 31 méthodes essentielles en les copiant et adaptant.
