ANALYSEUR CORRÉLATION LONGUEUR ↔ DÉLAI
Démarré: 2025-06-04 17:02:06
================================================================================
🎯 OBJECTIF : Mesurer la corrélation longueur → délai
📊 VARIABLES MESURÉES :
   • Longueur message (caractères)
   • Délai d'enregistrement (ms)
   • Type de message (user/assistant)
   • Complexité contenu (texte/code/JSON)
   • Delta WAL (bytes)
   • Timestamp précis
📈 ANALYSE STATISTIQUE :
   • Corrélation longueur/délai
   • Moyenne par type de message
   • Outliers et anomalies
================================================================================

[17:02:34.268] CORRÉLATION #1
Role: unknown | Index: 0
Longueur: 0 chars | Type: VIDE
Délai: 51ms | Delta WAL: +0 bytes
Ratio: 0.00 chars/ms
------------------------------------------------------------
Aperçu: [vide]
================================================================================

[17:02:34.268] CORRÉLATION #2
Role: unknown | Index: 1
Longueur: 0 chars | Type: VIDE
Délai: 51ms | Delta WAL: +0 bytes
Ratio: 0.00 chars/ms
------------------------------------------------------------
Aperçu: [vide]
================================================================================

[17:02:45.909] CORRÉLATION #3
Role: unknown | Index: 0
Longueur: 0 chars | Type: VIDE
Délai: 51ms | Delta WAL: +0 bytes
Ratio: 0.00 chars/ms
------------------------------------------------------------
Aperçu: [vide]
================================================================================

