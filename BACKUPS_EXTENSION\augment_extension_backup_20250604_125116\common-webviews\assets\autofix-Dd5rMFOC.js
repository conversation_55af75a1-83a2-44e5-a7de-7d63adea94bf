var ot=Object.defineProperty;var rt=(s,t,e)=>t in s?ot(s,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):s[t]=e;var _=(s,t,e)=>rt(s,typeof t!="symbol"?t+"":t,e);import{S as T,i as k,s as z,Q as b,y as S,D as C,c as D,ac as lt,e as v,f as y,z as F,av as G,a4 as j,u as $,t as g,h as w,B as q,F as nt,a6 as ct,G as R,a3 as Q,H as it,aw as ut,n as B,q as L,r as P,al as dt,ap as st,af as ft,_ as $t,w as U,x as W,E as pt,A as Y}from"./SpinnerAugment-BJ4-L7QR.js";import{A as mt}from"./autofix-state-d-ymFdyn.js";import{e as E,h as gt,W as M}from"./BaseButton-C6Dhmpxa.js";import{B as N}from"./ButtonAugment-HnJOGilM.js";import{s as ht,n as xt}from"./IconFilePath-C-3qORpY.js";import{C as yt,f as bt}from"./file-reader-ChKpCF92.js";import{A as It}from"./IconButtonAugment-Certjadv.js";import{M as vt}from"./index-MyvMQzjq.js";import"./LanguageIcon-BH9BM7T7.js";import"./next-edit-types-904A5ehg.js";import"./VSCodeCodicon-CvBJfpPi.js";import"./keypress-DD1aQVr0.js";import"./toggleHighContrast-CwIv4U26.js";import"./preload-helper-Dv6uf1Os.js";import"./resize-observer-DdAtcrRr.js";import"./isObjectLike-DflaizF0.js";var I=(s=>(s.testLog="testLog",s.solution="solution",s))(I||{});function J(s,t,e){const a=s.slice();return a[12]=t[e],a}function wt(s){let t;return{c(){t=R("Test log")},m(e,a){v(e,t,a)},d(e){e&&w(t)}}}function At(s){let t;return{c(){t=R("Solution")},m(e,a){v(e,t,a)},d(e){e&&w(t)}}}function K(s){let t,e,a,o,i,n=s[2].indexOf(s[12])+1+"";return{c(){t=b("option"),e=R("Test Attempt "),a=R(n),o=C(),t.__value=i=s[12].id,Q(t,t.__value)},m(l,r){v(l,t,r),y(t,e),y(t,a),y(t,o)},p(l,r){4&r&&n!==(n=l[2].indexOf(l[12])+1+"")&&it(a,n),4&r&&i!==(i=l[12].id)&&(t.__value=i,Q(t,t.__value))},d(l){l&&w(t)}}}function Dt(s){let t,e,a,o,i,n,l,r,c,d,m;a=new N({props:{variant:s[1]===I.testLog?"classic":"solid",$$slots:{default:[wt]},$$scope:{ctx:s}}}),a.$on("click",s[8]),i=new N({props:{variant:s[1]===I.solution?"classic":"solid",disabled:!s[3],$$slots:{default:[At]},$$scope:{ctx:s}}}),i.$on("click",s[9]);let A=E(s[2]),p=[];for(let f=0;f<A.length;f+=1)p[f]=K(J(s,A,f));return{c(){t=b("div"),e=b("div"),S(a.$$.fragment),o=C(),S(i.$$.fragment),n=C(),l=b("div"),r=b("select");for(let f=0;f<p.length;f+=1)p[f].c();D(e,"class","tab-buttons svelte-u0jqym"),D(r,"class","iteration-selector svelte-u0jqym"),s[0]===void 0&&lt(()=>s[10].call(r)),D(l,"class","iteration-selector-container svelte-u0jqym"),D(t,"class","autofix-details-header svelte-u0jqym")},m(f,u){v(f,t,u),y(t,e),F(a,e,null),y(e,o),F(i,e,null),y(t,n),y(t,l),y(l,r);for(let h=0;h<p.length;h+=1)p[h]&&p[h].m(r,null);G(r,s[0],!0),c=!0,d||(m=[j(r,"change",s[10]),j(r,"change",s[11])],d=!0)},p(f,[u]){const h={};2&u&&(h.variant=f[1]===I.testLog?"classic":"solid"),32768&u&&(h.$$scope={dirty:u,ctx:f}),a.$set(h);const O={};if(2&u&&(O.variant=f[1]===I.solution?"classic":"solid"),8&u&&(O.disabled=!f[3]),32768&u&&(O.$$scope={dirty:u,ctx:f}),i.$set(O),4&u){let x;for(A=E(f[2]),x=0;x<A.length;x+=1){const H=J(f,A,x);p[x]?p[x].p(H,u):(p[x]=K(H),p[x].c(),p[x].m(r,null))}for(;x<p.length;x+=1)p[x].d(1);p.length=A.length}5&u&&G(r,f[0])},i(f){c||($(a.$$.fragment,f),$(i.$$.fragment,f),c=!0)},o(f){g(a.$$.fragment,f),g(i.$$.fragment,f),c=!1},d(f){f&&w(t),q(a),q(i),nt(p,f),d=!1,ct(m)}}}function _t(s,t,e){let a,o,i,{autofixData:n}=t,{currentTab:l=I.testLog}=t,{currentIterationId:r}=t;function c(m){e(1,l=m)}function d(m){e(1,l=I.testLog),e(0,r=m)}return s.$$set=m=>{"autofixData"in m&&e(6,n=m.autofixData),"currentTab"in m&&e(1,l=m.currentTab),"currentIterationId"in m&&e(0,r=m.currentIterationId)},s.$$.update=()=>{64&s.$$.dirty&&e(2,a=n.autofixIterations||[]),5&s.$$.dirty&&a.length>0&&!r&&e(0,r=a[a.length-1].id),5&s.$$.dirty&&e(7,o=a.find(m=>m.id===r)),128&s.$$.dirty&&e(3,i=(o==null?void 0:o.suggestedSolutions)!==void 0)},[r,l,a,i,c,d,n,o,()=>c(I.testLog),()=>c(I.solution),function(){r=ut(this),e(0,r),e(2,a),e(6,n),e(2,a),e(6,n)},()=>d(r)]}class St extends T{constructor(t){super(),k(this,t,_t,Dt,z,{autofixData:6,currentTab:1,currentIterationId:0})}}function Ft(s){var o;let t,e,a=((o=s[0])==null?void 0:o.commandOutput)+"";return{c(){t=b("div"),e=R(a),D(t,"class","c-autofix-details-log svelte-18pm6ob")},m(i,n){v(i,t,n),y(t,e)},p(i,[n]){var l;1&n&&a!==(a=((l=i[0])==null?void 0:l.commandOutput)+"")&&it(e,a)},i:B,o:B,d(i){i&&w(t)}}}function qt(s,t,e){let a,{iterationId:o}=t,{autofixData:i}=t;return s.$$set=n=>{"iterationId"in n&&e(1,o=n.iterationId),"autofixData"in n&&e(2,i=n.autofixData)},s.$$.update=()=>{var n;6&s.$$.dirty&&e(0,a=(n=i.autofixIterations)==null?void 0:n.filter(l=>l.id===o)[0])},[a,o,i]}class Ot extends T{constructor(t){super(),k(this,t,qt,Ft,z,{iterationId:1,autofixData:2})}}function V(s,t,e){const a=s.slice();return a[6]=t[e][0],a[7]=t[e][1],a}function X(s){let t,e,a;return e=new dt({}),{c(){t=b("li"),S(e.$$.fragment),D(t,"class","c-code-roll__item svelte-1iyponq")},m(o,i){v(o,t,i),F(e,t,null),a=!0},i(o){a||($(e.$$.fragment,o),a=!0)},o(o){g(e.$$.fragment,o),a=!1},d(o){o&&w(t),q(e)}}}function Z(s){var i;let t,e,a,o;return e=new yt({props:{codeActions:s[1],filepath:(i=s[7][0])==null?void 0:i.qualifiedPathName,readFile:s[3],onCodeAction:s[0],suggestions:s[7]}}),{c(){t=b("li"),S(e.$$.fragment),a=C(),D(t,"class","c-code-roll__item svelte-1iyponq")},m(n,l){v(n,t,l),F(e,t,null),y(t,a),o=!0},p(n,l){var c;const r={};2&l&&(r.codeActions=n[1]),16&l&&(r.filepath=(c=n[7][0])==null?void 0:c.qualifiedPathName),8&l&&(r.readFile=n[3]),1&l&&(r.onCodeAction=n[0]),16&l&&(r.suggestions=n[7]),e.$set(r)},i(n){o||($(e.$$.fragment,n),o=!0)},o(n){g(e.$$.fragment,n),o=!1},d(n){n&&w(t),q(e)}}}function Ct(s){let t,e,a,o=s[2]&&X(),i=E(s[4]),n=[];for(let r=0;r<i.length;r+=1)n[r]=Z(V(s,i,r));const l=r=>g(n[r],1,1,()=>{n[r]=null});return{c(){t=b("ul"),o&&o.c(),e=C();for(let r=0;r<n.length;r+=1)n[r].c();D(t,"class","c-code-roll svelte-1iyponq")},m(r,c){v(r,t,c),o&&o.m(t,null),y(t,e);for(let d=0;d<n.length;d+=1)n[d]&&n[d].m(t,null);a=!0},p(r,[c]){if(r[2]?o?4&c&&$(o,1):(o=X(),o.c(),$(o,1),o.m(t,e)):o&&(L(),g(o,1,1,()=>{o=null}),P()),27&c){let d;for(i=E(r[4]),d=0;d<i.length;d+=1){const m=V(r,i,d);n[d]?(n[d].p(m,c),$(n[d],1)):(n[d]=Z(m),n[d].c(),$(n[d],1),n[d].m(t,null))}for(L(),d=i.length;d<n.length;d+=1)l(d);P()}},i(r){if(!a){$(o);for(let c=0;c<i.length;c+=1)$(n[c]);a=!0}},o(r){g(o),n=n.filter(Boolean);for(let c=0;c<n.length;c+=1)g(n[c]);a=!1},d(r){r&&w(t),o&&o.d(),nt(n,r)}}}function Mt(s,t,e){let a,{suggestions:o=[]}=t,{onCodeAction:i=xt}=t,{codeActions:n=[]}=t,{loading:l=!0}=t,{readFile:r}=t;return s.$$set=c=>{"suggestions"in c&&e(5,o=c.suggestions),"onCodeAction"in c&&e(0,i=c.onCodeAction),"codeActions"in c&&e(1,n=c.codeActions),"loading"in c&&e(2,l=c.loading),"readFile"in c&&e(3,r=c.readFile)},s.$$.update=()=>{32&s.$$.dirty&&e(4,a=new Map(ht(o)))},[i,n,l,r,a,o]}class Rt extends T{constructor(t){super(),k(this,t,Mt,Ct,z,{suggestions:5,onCodeAction:0,codeActions:1,loading:2,readFile:3})}}function Lt(s){let t;return{c(){t=R("Apply & Retest")},m(e,a){v(e,t,a)},d(e){e&&w(t)}}}function Pt(s){let t,e,a,o,i;return o=new N({props:{$$slots:{default:[Lt]},$$scope:{ctx:s}}}),o.$on("click",s[1]),{c(){t=b("div"),e=b("span"),e.textContent=`Here are the suggestions for how to fix this failure. You must review each change before
    continuing.`,a=C(),S(o.$$.fragment),D(t,"class","autofix-details-footer svelte-ry884d")},m(n,l){v(n,t,l),y(t,e),y(t,a),F(o,t,null),i=!0},p(n,[l]){const r={};4&l&&(r.$$scope={dirty:l,ctx:n}),o.$set(r)},i(n){i||($(o.$$.fragment,n),i=!0)},o(n){g(o.$$.fragment,n),i=!1},d(n){n&&w(t),q(o)}}}function Tt(s){const t=st();return[t,()=>t("applyAndRetestClick")]}class kt extends T{constructor(t){super(),k(this,t,Tt,Pt,z,{})}}function tt(s){let t,e;return t=new Rt({props:{suggestions:s[2],readFile:s[0],loading:!1}}),{c(){S(t.$$.fragment)},m(a,o){F(t,a,o),e=!0},p(a,o){const i={};4&o&&(i.suggestions=a[2]),1&o&&(i.readFile=a[0]),t.$set(i)},i(a){e||($(t.$$.fragment,a),e=!0)},o(a){g(t.$$.fragment,a),e=!1},d(a){q(t,a)}}}function et(s){let t,e;return t=new kt({}),t.$on("applyAndRetestClick",s[7]),{c(){S(t.$$.fragment)},m(a,o){F(t,a,o),e=!0},p:B,i(a){e||($(t.$$.fragment,a),e=!0)},o(a){g(t.$$.fragment,a),e=!1},d(a){q(t,a)}}}function zt(s){let t,e,a,o,i=s[2]&&tt(s),n=s[1]&&et(s);return{c(){t=b("div"),e=b("div"),i&&i.c(),a=C(),n&&n.c(),D(e,"class","c-autofix-details-solution-coderoll-container svelte-1uzxw9z"),D(t,"class","c-autofix-details-solution svelte-1uzxw9z")},m(l,r){v(l,t,r),y(t,e),i&&i.m(e,null),y(t,a),n&&n.m(t,null),o=!0},p(l,[r]){l[2]?i?(i.p(l,r),4&r&&$(i,1)):(i=tt(l),i.c(),$(i,1),i.m(e,null)):i&&(L(),g(i,1,1,()=>{i=null}),P()),l[1]?n?(n.p(l,r),2&r&&$(n,1)):(n=et(l),n.c(),$(n,1),n.m(t,null)):n&&(L(),g(n,1,1,()=>{n=null}),P())},i(l){o||($(i),$(n),o=!0)},o(l){g(i),g(n),o=!1},d(l){l&&w(t),i&&i.d(),n&&n.d()}}}function Et(s,t,e){let a,o,{iterationId:i}=t,{autofixData:n}=t,{readFile:l}=t;const r=st();let c;return s.$$set=d=>{"iterationId"in d&&e(4,i=d.iterationId),"autofixData"in d&&e(5,n=d.autofixData),"readFile"in d&&e(0,l=d.readFile)},s.$$.update=()=>{var d,m,A,p,f;48&s.$$.dirty&&e(6,c=(d=n.autofixIterations)==null?void 0:d.filter(u=>u.id===i)[0]),64&s.$$.dirty&&e(2,a=(A=(m=c==null?void 0:c.suggestedSolutions)==null?void 0:m.at(-1))==null?void 0:A.replacements),48&s.$$.dirty&&e(1,o=i===((f=(p=n.autofixIterations)==null?void 0:p.at(-1))==null?void 0:f.id))},[l,o,a,r,i,n,c,()=>a&&r("applyAndRetest",a)]}class jt extends T{constructor(t){super(),k(this,t,Et,zt,z,{iterationId:4,autofixData:5,readFile:0})}}class Bt{constructor(t){_(this,"_asyncMsgSender");_(this,"_latestData");_(this,"subscribers",new Set);_(this,"_readFile");_(this,"dispose",()=>{this.subscribers.clear()});_(this,"notifySubscribers",()=>{this.subscribers.forEach(t=>t(this))});_(this,"subscribe",t=>(this.subscribers.add(t),t(this),()=>{this.subscribers.delete(t)}));_(this,"initialize",async()=>{const t=await this._asyncMsgSender.send({type:M.autofixPanelDetailsInitRequest},2e3);this.onOpenSpecificStage(t.data.iterationId,t.data.stage)});_(this,"applyAndRetest",async t=>{await this._asyncMsgSender.send({type:M.autofixPanelApplyAndRetestRequest,data:{selectedSolutions:t}},2e3)});_(this,"handleMessageFromExtension",async t=>{const e=t.data;switch(e.type){case M.autofixPanelStateUpdate:this._latestData=e.data,this.notifySubscribers();break;case M.autofixPanelExecuteCommandPartialOutput:this.handlePartialOutput(e.data.iterationId,e.data.output);break;case M.autofixPanelOpenSpecificStage:this.onOpenSpecificStage(e.data.iterationId,e.data.stage);break;case M.empty:break;default:console.warn("AutofixModel got unexpected message: ",e)}});this.onOpenSpecificStage=t,this._asyncMsgSender=new It(e=>gt.postMessage(e)),this._readFile=bt(this._asyncMsgSender),this.initialize()}get latestData(){return this._latestData}get readFile(){return this._readFile}handlePartialOutput(t,e){var a,o;(o=(a=this._latestData)==null?void 0:a.autofixIterations)==null||o.forEach(i=>{i.id===t&&(i.commandOutput=e)}),this.notifySubscribers()}}function at(s){let t,e,a,o,i,n,l,r;function c(u){s[5](u)}function d(u){s[6](u)}let m={autofixData:s[0]};s[1]!==void 0&&(m.currentIterationId=s[1]),s[2]!==void 0&&(m.currentTab=s[2]),t=new St({props:m}),U.push(()=>W(t,"currentIterationId",c)),U.push(()=>W(t,"currentTab",d));const A=[Ht,Nt],p=[];function f(u,h){return u[2]===I.testLog?0:u[2]===I.solution?1:-1}return~(i=f(s))&&(n=p[i]=A[i](s)),{c(){S(t.$$.fragment),o=C(),n&&n.c(),l=pt()},m(u,h){F(t,u,h),v(u,o,h),~i&&p[i].m(u,h),v(u,l,h),r=!0},p(u,h){const O={};1&h&&(O.autofixData=u[0]),!e&&2&h&&(e=!0,O.currentIterationId=u[1],Y(()=>e=!1)),!a&&4&h&&(a=!0,O.currentTab=u[2],Y(()=>a=!1)),t.$set(O);let x=i;i=f(u),i===x?~i&&p[i].p(u,h):(n&&(L(),g(p[x],1,1,()=>{p[x]=null}),P()),~i?(n=p[i],n?n.p(u,h):(n=p[i]=A[i](u),n.c()),$(n,1),n.m(l.parentNode,l)):n=null)},i(u){r||($(t.$$.fragment,u),$(n),r=!0)},o(u){g(t.$$.fragment,u),g(n),r=!1},d(u){u&&(w(o),w(l)),q(t,u),~i&&p[i].d(u)}}}function Nt(s){let t,e;return t=new jt({props:{autofixData:s[0],iterationId:s[1],readFile:s[3].readFile}}),t.$on("applyAndRetest",s[7]),{c(){S(t.$$.fragment)},m(a,o){F(t,a,o),e=!0},p(a,o){const i={};1&o&&(i.autofixData=a[0]),2&o&&(i.iterationId=a[1]),t.$set(i)},i(a){e||($(t.$$.fragment,a),e=!0)},o(a){g(t.$$.fragment,a),e=!1},d(a){q(t,a)}}}function Ht(s){let t,e;return t=new Ot({props:{iterationId:s[1],autofixData:s[0]}}),{c(){S(t.$$.fragment)},m(a,o){F(t,a,o),e=!0},p(a,o){const i={};2&o&&(i.iterationId=a[1]),1&o&&(i.autofixData=a[0]),t.$set(i)},i(a){e||($(t.$$.fragment,a),e=!0)},o(a){g(t.$$.fragment,a),e=!1},d(a){q(t,a)}}}function Gt(s){let t,e,a=s[0]&&at(s);return{c(){t=b("div"),a&&a.c(),D(t,"class","autofix-container svelte-107lb5t")},m(o,i){v(o,t,i),a&&a.m(t,null),e=!0},p(o,i){o[0]?a?(a.p(o,i),1&i&&$(a,1)):(a=at(o),a.c(),$(a,1),a.m(t,null)):a&&(L(),g(a,1,1,()=>{a=null}),P())},i(o){e||($(a),e=!0)},o(o){g(a),e=!1},d(o){o&&w(t),a&&a.d()}}}function Qt(s){let t,e,a,o;return t=new vt.Root({props:{$$slots:{default:[Gt]},$$scope:{ctx:s}}}),{c(){S(t.$$.fragment)},m(i,n){var l;F(t,i,n),e=!0,a||(o=j(window,"message",(l=s[3])==null?void 0:l.handleMessageFromExtension),a=!0)},p(i,[n]){const l={};519&n&&(l.$$scope={dirty:n,ctx:i}),t.$set(l)},i(i){e||($(t.$$.fragment,i),e=!0)},o(i){g(t.$$.fragment,i),e=!1},d(i){q(t,i),a=!1,o()}}}function Ut(s,t,e){let a,o,i,n=I.testLog,l=new Bt((r,c)=>{e(1,i=r),e(2,n=c===mt.runTest?I.testLog:I.solution)});return ft(s,l,r=>e(4,a=r)),$t(()=>{l.dispose()}),s.$$.update=()=>{16&s.$$.dirty&&e(0,o=a.latestData)},[o,i,n,l,a,function(r){i=r,e(1,i)},function(r){n=r,e(2,n)},r=>l==null?void 0:l.applyAndRetest(r.detail)]}new class extends T{constructor(s){super(),k(this,s,Ut,Qt,z,{})}}({target:document.getElementById("app")});
