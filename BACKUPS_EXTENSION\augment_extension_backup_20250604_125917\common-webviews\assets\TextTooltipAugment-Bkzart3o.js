var N=Object.defineProperty;var S=(i,t,s)=>t in i?N(i,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):i[t]=s;var _=(i,t,s)=>S(i,typeof t!="symbol"?t+"":t,s);import{W as j}from"./BaseButton-C6Dhmpxa.js";import{A as I}from"./IconButtonAugment-Certjadv.js";import{S as Q,i as V,s as X,y as h,z as x,u as $,t as p,B as C,a9 as k,D as J,E as K,e as f,q as z,r as b,h as g,w as L,P as A,V as B,W as G,X as H,Q as U,aa as m,T as Y,G as Z,H as tt}from"./SpinnerAugment-BJ4-L7QR.js";import{R as st,a as nt,C as et}from"./Content-Czt02SJi.js";class xt extends I{constructor(s){super(n=>{this._host.postMessage(n)});_(this,"_consumers",[]);this._host=s,this.onMessageFromExtension=this.onMessageFromExtension.bind(this)}dispose(){this._consumers=[]}postMessage(s){this._host.postMessage(s)}registerConsumer(s){this._consumers.push(s)}onMessageFromExtension(s){s.data.type!==j.asyncWrapper&&this._consumers.forEach(n=>{n.handleMessageFromExtension(s)})}}const w={Root:st,Trigger:nt,Content:et},ot=i=>({}),T=i=>({});function it(i){let t;const s=i[18].default,n=A(s,i,i[20],null);return{c(){n&&n.c()},m(e,o){n&&n.m(e,o),t=!0},p(e,o){n&&n.p&&(!t||1048576&o)&&B(n,s,e,e[20],t?H(s,e[20],o,null):G(e[20]),null)},i(e){t||($(n,e),t=!0)},o(e){p(n,e),t=!1},d(e){n&&n.d(e)}}}function F(i){let t,s;return t=new w.Content({props:{side:i[6],align:i[10],$$slots:{default:[lt]},$$scope:{ctx:i}}}),{c(){h(t.$$.fragment)},m(n,e){x(t,n,e),s=!0},p(n,e){const o={};64&e&&(o.side=n[6]),1024&e&&(o.align=n[10]),1081359&e&&(o.$$scope={dirty:e,ctx:n}),t.$set(o)},i(n){s||($(t.$$.fragment,n),s=!0)},o(n){p(t.$$.fragment,n),s=!1},d(n){C(t,n)}}}function rt(i){let t,s;return t=new Y({props:{size:1,class:"tooltip-text",$$slots:{default:[ct]},$$scope:{ctx:i}}}),{c(){h(t.$$.fragment)},m(n,e){x(t,n,e),s=!0},p(n,e){const o={};1048577&e&&(o.$$scope={dirty:e,ctx:n}),t.$set(o)},i(n){s||($(t.$$.fragment,n),s=!0)},o(n){p(t.$$.fragment,n),s=!1},d(n){C(t,n)}}}function at(i){let t;const s=i[18].content,n=A(s,i,i[20],T);return{c(){n&&n.c()},m(e,o){n&&n.m(e,o),t=!0},p(e,o){n&&n.p&&(!t||1048576&o)&&B(n,s,e,e[20],t?H(s,e[20],o,ot):G(e[20]),T)},i(e){t||($(n,e),t=!0)},o(e){p(n,e),t=!1},d(e){n&&n.d(e)}}}function ct(i){let t;return{c(){t=Z(i[0])},m(s,n){f(s,t,n)},p(s,n){1&n&&tt(t,s[0])},d(s){s&&g(t)}}}function lt(i){let t,s,n,e;const o=[at,rt],r=[];function l(c,d){return c[15].content?0:1}return s=l(i),n=r[s]=o[s](i),{c(){t=U("div"),n.c(),m(t,"width",i[1]),m(t,"min-width",i[2]),m(t,"max-width",i[3])},m(c,d){f(c,t,d),r[s].m(t,null),e=!0},p(c,d){let u=s;s=l(c),s===u?r[s].p(c,d):(z(),p(r[u],1,1,()=>{r[u]=null}),b(),n=r[s],n?n.p(c,d):(n=r[s]=o[s](c),n.c()),$(n,1),n.m(t,null)),2&d&&m(t,"width",c[1]),4&d&&m(t,"min-width",c[2]),8&d&&m(t,"max-width",c[3])},i(c){e||($(n),e=!0)},o(c){p(n),e=!1},d(c){c&&g(t),r[s].d()}}}function dt(i){let t,s,n,e;t=new w.Trigger({props:{referenceClientRect:i[13],class:i[11],$$slots:{default:[it]},$$scope:{ctx:i}}});let o=(i[0]||i[15].content)&&F(i);return{c(){h(t.$$.fragment),s=J(),o&&o.c(),n=K()},m(r,l){x(t,r,l),f(r,s,l),o&&o.m(r,l),f(r,n,l),e=!0},p(r,l){const c={};8192&l&&(c.referenceClientRect=r[13]),2048&l&&(c.class=r[11]),1048576&l&&(c.$$scope={dirty:l,ctx:r}),t.$set(c),r[0]||r[15].content?o?(o.p(r,l),32769&l&&$(o,1)):(o=F(r),o.c(),$(o,1),o.m(n.parentNode,n)):o&&(z(),p(o,1,1,()=>{o=null}),b())},i(r){e||($(t.$$.fragment,r),$(o),e=!0)},o(r){p(t.$$.fragment,r),p(o),e=!1},d(r){r&&(g(s),g(n)),C(t,r),o&&o.d(r)}}}function $t(i){let t,s,n={delayDurationMs:i[4],onOpenChange:i[12],triggerOn:i[5],nested:i[7],hasPointerEvents:i[8],offset:i[9],tippyTheme:"default text-tooltip-augment",$$slots:{default:[dt]},$$scope:{ctx:i}};return t=new w.Root({props:n}),i[19](t),{c(){h(t.$$.fragment)},m(e,o){x(t,e,o),s=!0},p(e,[o]){const r={};16&o&&(r.delayDurationMs=e[4]),4096&o&&(r.onOpenChange=e[12]),32&o&&(r.triggerOn=e[5]),128&o&&(r.nested=e[7]),256&o&&(r.hasPointerEvents=e[8]),512&o&&(r.offset=e[9]),1092687&o&&(r.$$scope={dirty:o,ctx:e}),t.$set(r)},i(e){s||($(t.$$.fragment,e),s=!0)},o(e){p(t.$$.fragment,e),s=!1},d(e){i[19](null),C(t,e)}}}function pt(i,t,s){let{$$slots:n={},$$scope:e}=t;const o=k(n);let r,{content:l}=t,{width:c}=t,{minWidth:d}=t,{maxWidth:u="360px"}=t,{delayDurationMs:O}=t,{triggerOn:y}=t,{side:M="top"}=t,{nested:E}=t,{hasPointerEvents:W}=t,{offset:R}=t,{align:q="center"}=t,{class:v=""}=t,{onOpenChange:D}=t,{referenceClientRect:P}=t;return i.$$set=a=>{"content"in a&&s(0,l=a.content),"width"in a&&s(1,c=a.width),"minWidth"in a&&s(2,d=a.minWidth),"maxWidth"in a&&s(3,u=a.maxWidth),"delayDurationMs"in a&&s(4,O=a.delayDurationMs),"triggerOn"in a&&s(5,y=a.triggerOn),"side"in a&&s(6,M=a.side),"nested"in a&&s(7,E=a.nested),"hasPointerEvents"in a&&s(8,W=a.hasPointerEvents),"offset"in a&&s(9,R=a.offset),"align"in a&&s(10,q=a.align),"class"in a&&s(11,v=a.class),"onOpenChange"in a&&s(12,D=a.onOpenChange),"referenceClientRect"in a&&s(13,P=a.referenceClientRect),"$$scope"in a&&s(20,e=a.$$scope)},[l,c,d,u,O,y,M,E,W,R,q,v,D,P,r,o,()=>r==null?void 0:r.requestOpen(),()=>r==null?void 0:r.requestClose(),n,function(a){L[a?"unshift":"push"](()=>{r=a,s(14,r)})},e]}class Ct extends Q{constructor(t){super(),V(this,t,pt,$t,X,{content:0,width:1,minWidth:2,maxWidth:3,delayDurationMs:4,triggerOn:5,side:6,nested:7,hasPointerEvents:8,offset:9,align:10,class:11,onOpenChange:12,referenceClientRect:13,requestOpen:16,requestClose:17})}get requestOpen(){return this.$$.ctx[16]}get requestClose(){return this.$$.ctx[17]}}export{xt as M,Ct as T};
