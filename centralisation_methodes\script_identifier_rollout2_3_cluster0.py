#!/usr/bin/env python3
"""
SCRIPT POUR IDENTIFIER LES MÉTHODES ROLLOUT 2 & 3 CLUSTER 0 (PAR DÉFAUT)
========================================================================

Objectif : Identifier les méthodes des Rollout 2 (Générateur) et Rollout 3 
(Prédicteur) du cluster par défaut parmi les 59 méthodes restantes.

Calcul : 162 total - 31 universelles - 72 Rollout 1 cluster 0 = 59 restantes
"""

import re
from typing import List, Dict, Set

def get_31_universal_methods():
    """Liste des 31 méthodes universelles"""
    return {
        '_rollout_analyzer', '_analyze_impair_consecutive_bias', '_analyze_pair_priority_2_autonomous',
        '_analyze_sync_alternation_bias', '_correlate_impair_with_sync', '_correlate_impair_with_combined',
        '_correlate_impair_with_pb', '_correlate_impair_with_so', '_correlate_bias_to_pb_variations',
        '_correlate_bias_to_so_variations', '_analyze_combined_structural_bias',
        '_generate_priority_based_synthesis_autonomous', '_generate_bias_signals_summary',
        '_generate_bias_generation_guidance', '_generate_bias_quick_access', '_rollout_generator',
        '_define_optimized_generation_space', '_generate_sequences_from_signals',
        '_generate_fallback_sequences', '_enrich_sequences_with_complete_indexes',
        '_rollout_predictor', '_evaluate_sequence_quality', '_evaluate_signal_alignment',
        '_evaluate_fallback_alignment', '_analyze_sequence_consistency', '_assess_risk_reward_ratio',
        '_validate_sequence_logic', '_calculate_sequence_score', '_select_best_sequence',
        '_calculate_cluster_confidence_azr_calibrated', '_convert_pb_sequence_to_so'
    }

def get_72_rollout1_cluster0_methods():
    """Liste des 72 méthodes Rollout 1 cluster 0"""
    return {
        '_generate_all_possible_sequences', '_generate_so_based_sequence', '_classify_confidence_level',
        '_convert_pb_sequence_to_so_with_history', '_calculate_sequence_probability',
        '_calculate_sequence_quality_metrics', '_generate_pair_sync_sequence', '_generate_impair_sync_sequence',
        '_generate_pb_sequence', '_generate_generic_signal_sequence', '_analyze_complete_cross_impacts',
        '_analyze_impair_pair_to_so_impact', '_analyze_desync_sync_to_pbt_impact', '_identify_desync_periods',
        '_analyze_desync_sync_to_so_impact', '_analyze_combined_to_pbt_impact', '_analyze_combined_to_so_impact',
        '_analyze_tri_dimensional_impacts', '_analyze_variations_impact_on_outcomes', '_analyze_consecutive_length_impact',
        '_find_consecutive_sequences_with_positions', '_calculate_asymmetric_impair_alert_level',
        '_find_consecutive_sequences', '_calculate_asymmetric_pair_alert_level', '_calculate_impair_rarity_score',
        '_calculate_pair_commonality_score', '_calculate_asymmetric_significance', '_identify_dominant_desync_sync_so_pattern',
        '_calculate_combined_so_impact_strength', '_calculate_combined_pbt_impact_strength',
        '_identify_dominant_impair_pair_so_pattern', '_calculate_overall_impact_strength',
        '_analyze_transition_moments_impact', '_calculate_distribution', '_analyze_desync_periods_impact',
        '_analyze_combined_state_changes_impact', '_analyze_temporal_correlation_evolution',
        '_calculate_phase_impair_pair_pb_correlation', '_calculate_phase_impair_pair_so_correlation',
        '_calculate_phase_sync_desync_pb_correlation', '_calculate_phase_sync_desync_so_correlation',
        '_calculate_phase_correlation_strength', '_analyze_correlation_trend', '_calculate_correlation_stability',
        '_calculate_variance', '_generate_temporal_recommendation', '_calculate_evolution_strength',
        '_calculate_temporal_consistency', '_calculate_temporal_predictability', '_calculate_variation_strength_analysis',
        '_extract_consecutive_length_strength', '_extract_transition_moments_strength', '_extract_desync_periods_strength',
        '_extract_combined_state_changes_strength', '_extract_temporal_evolution_strength', '_calculate_confidence_level',
        '_generate_exploitation_recommendation', '_identify_best_prediction_context', '_calculate_strength_distribution',
        '_calculate_variation_consistency', '_assess_sample_size_adequacy', '_calculate_statistical_significance',
        '_calculate_pattern_stability', '_assess_overall_quality', '_identify_enhanced_dominant_correlations',
        '_identify_enhanced_high_confidence_zones', '_generate_impair_pair_optimized_sequence',
        '_generate_sync_based_sequence', '_generate_combined_index_sequence', '_generate_so_pattern_sequence',
        '_classify_combined_transition_type', '_count_consecutive_pattern'
    }

def extract_all_methods_from_class_txt():
    """Extrait toutes les méthodes du fichier class.txt"""
    try:
        with open('centralisation_methodes/class.txt', 'r', encoding='utf-8') as f:
            content = f.read()
        
        methods = []
        lines = content.split('\n')
        
        for line_num, line in enumerate(lines, 1):
            if 'def ' in line and '(' in line:
                match = re.search(r'def ([a-zA-Z_][a-zA-Z0-9_]*)', line)
                if match:
                    method_name = match.group(1)
                    methods.append({
                        'name': method_name,
                        'line_number': line_num,
                        'signature': line.strip()
                    })
        
        return methods
    except Exception as e:
        print(f"Erreur lecture class.txt: {e}")
        return []

def is_rollout2_method(method_name: str, method_content: str) -> Dict:
    """Détermine si une méthode appartient au Rollout 2 (Générateur) cluster 0"""
    
    # Mots-clés Rollout 2 (Génération)
    generation_keywords = [
        'generate_sequence', 'create_sequence', 'build_sequence',
        'construct_pattern', 'generate_pattern', 'create_pattern',
        'build_prediction', 'generate_prediction'
    ]
    
    # Mots-clés spécifiques cluster par défaut
    default_cluster_keywords = [
        'standard', 'default', 'reference', 'base'
    ]
    
    # Exclusions (spécialisations autres clusters)
    cluster_specialization_patterns = [
        '_c2_', '_c3_', '_c4_', '_c5_', '_c6_', '_c7_',
        'cluster_2', 'cluster_3', 'cluster_4', 'cluster_5', 'cluster_6', 'cluster_7'
    ]
    
    content_lower = method_content.lower()
    name_lower = method_name.lower()
    
    # Vérifier exclusions (autres clusters)
    for pattern in cluster_specialization_patterns:
        if pattern in name_lower or pattern in content_lower:
            return {'is_rollout2': False, 'reason': f'Spécialisé autre cluster: {pattern}'}
    
    # Score génération
    generation_score = 0
    found_generation_keywords = []
    
    for keyword in generation_keywords:
        count = content_lower.count(keyword)
        if count > 0:
            generation_score += count
            found_generation_keywords.append(keyword)
    
    # Vérifier si c'est bien de la génération (pas juste analyse)
    has_sequence_creation = any(keyword in content_lower for keyword in [
        'sequence.append', 'sequences.append', 'new_sequence', 'created_sequence'
    ])
    
    # Vérifier cluster par défaut
    is_default_cluster = any(keyword in content_lower for keyword in default_cluster_keywords) or \
                        'cluster_id' not in content_lower or \
                        'cluster_id == 0' in content_lower
    
    # Détermination finale
    is_rollout2 = (generation_score >= 2 or has_sequence_creation) and is_default_cluster
    
    return {
        'is_rollout2': is_rollout2,
        'generation_score': generation_score,
        'found_keywords': found_generation_keywords,
        'has_sequence_creation': has_sequence_creation,
        'is_default_cluster': is_default_cluster,
        'confidence': generation_score * 20 + (30 if has_sequence_creation else 0) + (20 if is_default_cluster else 0)
    }

def is_rollout3_method(method_name: str, method_content: str) -> Dict:
    """Détermine si une méthode appartient au Rollout 3 (Prédicteur) cluster 0 - VERSION CORRIGÉE"""

    content_lower = method_content.lower()
    name_lower = method_name.lower()

    # CORRECTION 1 : Exclusion explicite Rollout 2
    rollout2_patterns = ['rollout2', 'rollout_2', 'rollout 2']
    for pattern in rollout2_patterns:
        if pattern in name_lower or pattern in content_lower:
            return {'is_rollout3': False, 'reason': f'Appartient au Rollout 2: {pattern}'}

    # CORRECTION 2 : Exclusion DEPRECATED
    if 'deprecated' in name_lower or 'deprecated' in content_lower:
        return {'is_rollout3': False, 'reason': 'Méthode DEPRECATED'}

    # CORRECTION 3 : Vérification directe Rollout 3
    rollout3_explicit = ['rollout3', 'rollout_3', 'rollout 3']
    has_explicit_rollout3 = any(pattern in name_lower or pattern in content_lower for pattern in rollout3_explicit)

    # CORRECTION 4 : Exclusions spécialisations autres clusters
    cluster_specialization_patterns = [
        '_c2_', '_c3_', '_c4_', '_c5_', '_c6_', '_c7_',
        'cluster_2', 'cluster_3', 'cluster_4', 'cluster_5', 'cluster_6', 'cluster_7'
    ]

    for pattern in cluster_specialization_patterns:
        if pattern in name_lower or pattern in content_lower:
            return {'is_rollout3': False, 'reason': f'Spécialisé autre cluster: {pattern}'}

    # Mots-clés Rollout 3 (Prédiction) - plus restrictifs
    prediction_keywords = [
        'predict_next', 'final_prediction', 'best_sequence', 'optimal_choice',
        'prediction_result', 'final_result', 'extract_next_hand'
    ]

    # Mots-clés de sélection/validation
    selection_keywords = [
        'select_best', 'choose_optimal', 'final', 'best', 'optimal'
    ]

    # Score prédiction
    prediction_score = 0
    selection_score = 0
    found_prediction_keywords = []
    found_selection_keywords = []

    for keyword in prediction_keywords:
        count = content_lower.count(keyword)
        if count > 0:
            prediction_score += count
            found_prediction_keywords.append(keyword)

    for keyword in selection_keywords:
        count = content_lower.count(keyword)
        if count > 0:
            selection_score += count
            found_selection_keywords.append(keyword)

    # Vérifier retour de prédiction finale
    returns_prediction = any(pattern in content_lower for pattern in [
        'return prediction', 'return final', 'return best', 'return selected'
    ])

    # Vérifier cluster par défaut (pas de spécialisation)
    is_default_cluster = not any(f'cluster_{i}' in content_lower for i in range(2, 8))

    # NOUVELLE LOGIQUE : Priorité à l'explicite
    if has_explicit_rollout3:
        confidence = 200  # Très haute confiance si explicitement Rollout 3
        is_rollout3 = True
    else:
        # Sinon, critères plus stricts
        is_rollout3 = (prediction_score >= 2 or selection_score >= 3) and is_default_cluster and returns_prediction
        confidence = prediction_score * 30 + selection_score * 15 + (25 if returns_prediction else 0) + (20 if is_default_cluster else 0)

    return {
        'is_rollout3': is_rollout3,
        'has_explicit_rollout3': has_explicit_rollout3,
        'prediction_score': prediction_score,
        'selection_score': selection_score,
        'found_prediction_keywords': found_prediction_keywords,
        'found_selection_keywords': found_selection_keywords,
        'returns_prediction': returns_prediction,
        'is_default_cluster': is_default_cluster,
        'confidence': confidence
    }

def extract_method_content(content: str, method_name: str, line_number: int) -> str:
    """Extrait le contenu d'une méthode"""
    lines = content.split('\n')
    method_lines = []
    
    # Commencer à partir de la ligne de définition
    in_method = False
    indent_level = None
    
    for i, line in enumerate(lines):
        if i + 1 >= line_number:  # À partir de la ligne de définition
            if not in_method and f"def {method_name}(" in line:
                in_method = True
                indent_level = len(line) - len(line.lstrip())
                method_lines.append(line)
            elif in_method:
                # Si on trouve une nouvelle méthode au même niveau d'indentation, on s'arrête
                if line.strip() and not line.startswith(' ' * (indent_level + 1)) and line.strip() != '':
                    if line.strip().startswith('def ') or line.strip().startswith('class '):
                        break
                method_lines.append(line)
    
    return '\n'.join(method_lines)

def identify_rollout2_3_cluster0_methods():
    """Identifie les méthodes Rollout 2 & 3 du cluster 0 parmi les 59 restantes"""
    
    print("🔍 IDENTIFICATION ROLLOUT 2 & 3 CLUSTER 0 (PAR DÉFAUT)")
    print("=" * 55)
    
    # 1. Toutes les méthodes
    all_methods = extract_all_methods_from_class_txt()
    print(f"Total méthodes dans class.txt: {len(all_methods)}")
    
    # 2. Exclusions
    universal_methods = get_31_universal_methods()
    rollout1_methods = get_72_rollout1_cluster0_methods()
    excluded_methods = universal_methods.union(rollout1_methods)
    
    print(f"Méthodes universelles: {len(universal_methods)}")
    print(f"Méthodes Rollout 1 cluster 0: {len(rollout1_methods)}")
    print(f"Total exclusions: {len(excluded_methods)}")
    
    # 3. Méthodes restantes (59)
    remaining_methods = [m for m in all_methods if m['name'] not in excluded_methods]
    print(f"Méthodes restantes à analyser: {len(remaining_methods)}")
    
    # 4. Analyse du contenu
    try:
        with open('centralisation_methodes/class.txt', 'r', encoding='utf-8') as f:
            file_content = f.read()
    except:
        file_content = ""
    
    rollout2_methods = []
    rollout3_methods = []
    other_methods = []
    duplicates_detected = []

    # CORRECTION 3 : Détection des duplications
    method_names = [m['name'] for m in remaining_methods]
    method_counts = {}
    for name in method_names:
        method_counts[name] = method_counts.get(name, 0) + 1

    duplicates = {name: count for name, count in method_counts.items() if count > 1}
    if duplicates:
        print(f"\n⚠️ DUPLICATIONS DÉTECTÉES: {duplicates}")
        for name, count in duplicates.items():
            duplicates_detected.append(f"{name} ({count} occurrences)")

    print("\nAnalyse des méthodes restantes...")

    for method in remaining_methods:
        method_content = extract_method_content(file_content, method['name'], method['line_number'])

        # Test Rollout 2
        rollout2_analysis = is_rollout2_method(method['name'], method_content)

        # Test Rollout 3
        rollout3_analysis = is_rollout3_method(method['name'], method_content)

        if rollout2_analysis['is_rollout2']:
            rollout2_methods.append({
                'name': method['name'],
                'line_number': method['line_number'],
                'analysis': rollout2_analysis
            })
            print(f"✅ ROLLOUT 2: {method['name']} - Confiance: {rollout2_analysis['confidence']}%")

        elif rollout3_analysis['is_rollout3']:
            rollout3_methods.append({
                'name': method['name'],
                'line_number': method['line_number'],
                'analysis': rollout3_analysis
            })
            print(f"🎯 ROLLOUT 3: {method['name']} - Confiance: {rollout3_analysis['confidence']}%")
            if rollout3_analysis.get('has_explicit_rollout3'):
                print(f"   ✅ EXPLICITEMENT ROLLOUT 3")

        else:
            other_methods.append({
                'name': method['name'],
                'line_number': method['line_number'],
                'rollout2_reason': rollout2_analysis.get('reason', 'Score insuffisant'),
                'rollout3_reason': rollout3_analysis.get('reason', 'Score insuffisant')
            })
    
    print(f"\nRésultats:")
    print(f"Rollout 2 cluster 0: {len(rollout2_methods)}")
    print(f"Rollout 3 cluster 0: {len(rollout3_methods)}")
    print(f"Autres méthodes: {len(other_methods)}")
    
    return {
        'remaining_methods_count': len(remaining_methods),
        'rollout2_methods': rollout2_methods,
        'rollout3_methods': rollout3_methods,
        'other_methods': other_methods,
        'duplicates_detected': duplicates_detected
    }

def generate_rollout2_3_report(results: Dict):
    """Génère un rapport des méthodes Rollout 2 & 3 cluster 0"""
    
    rollout2_methods = results['rollout2_methods']
    rollout3_methods = results['rollout3_methods']
    other_methods = results['other_methods']
    
    report = f"""MÉTHODES ROLLOUT 2 & 3 CLUSTER 0 (PAR DÉFAUT) - VERSION CORRIGÉE
====================================================================

MÉTHODOLOGIE CORRIGÉE :
======================
1. Exclusion des 31 méthodes universelles
2. Exclusion des 72 méthodes Rollout 1 cluster 0
3. Analyse des {results['remaining_methods_count']} méthodes restantes
4. CORRECTIONS APPLIQUÉES basées sur erreurs détectées :
   ✅ Exclusion explicite méthodes Rollout 2
   ✅ Exclusion méthodes DEPRECATED
   ✅ Détection des duplications
   ✅ Vérification directe "rollout 3" dans nom/contenu

RÉSULTATS CORRIGÉS :
==================
Méthodes restantes analysées : {results['remaining_methods_count']}
Rollout 2 cluster 0 (Générateur) : {len(rollout2_methods)}
Rollout 3 cluster 0 (Prédicteur) : {len(rollout3_methods)}
Autres méthodes : {len(other_methods)}
Duplications détectées : {len(results.get('duplicates_detected', []))}

MÉTHODES ROLLOUT 2 CLUSTER 0 (GÉNÉRATEUR) - {len(rollout2_methods)} :
{'=' * 55}

"""
    
    # Trier par confiance décroissante
    rollout2_methods.sort(key=lambda x: x['analysis']['confidence'], reverse=True)
    
    for i, method in enumerate(rollout2_methods, 1):
        analysis = method['analysis']
        report += f"{i:2d}. {method['name']} - Ligne {method['line_number']}\n"
        report += f"    Confiance : {analysis['confidence']}%\n"
        report += f"    Score génération : {analysis['generation_score']}\n"
        report += f"    Mots-clés trouvés : {', '.join(analysis['found_keywords'])}\n"
        report += f"    Création séquences : {'OUI' if analysis['has_sequence_creation'] else 'NON'}\n"
        report += f"    Cluster par défaut : {'OUI' if analysis['is_default_cluster'] else 'NON'}\n\n"
    
    report += f"\nMÉTHODES ROLLOUT 3 CLUSTER 0 (PRÉDICTEUR) - {len(rollout3_methods)} :\n"
    report += "=" * 54 + "\n\n"
    
    # Trier par confiance décroissante
    rollout3_methods.sort(key=lambda x: x['analysis']['confidence'], reverse=True)
    
    for i, method in enumerate(rollout3_methods, 1):
        analysis = method['analysis']
        report += f"{i:2d}. {method['name']} - Ligne {method['line_number']}\n"
        report += f"    Confiance : {analysis['confidence']}%\n"
        report += f"    Score prédiction : {analysis['prediction_score']}\n"
        report += f"    Score sélection : {analysis['selection_score']}\n"
        report += f"    Mots-clés prédiction : {', '.join(analysis['found_prediction_keywords'])}\n"
        report += f"    Mots-clés sélection : {', '.join(analysis['found_selection_keywords'])}\n"
        report += f"    Retourne prédiction : {'OUI' if analysis['returns_prediction'] else 'NON'}\n"
        report += f"    Cluster par défaut : {'OUI' if analysis['is_default_cluster'] else 'NON'}\n\n"
    
    if other_methods:
        report += f"\nAUTRES MÉTHODES NON CLASSÉES - {len(other_methods)} :\n"
        report += "=" * 40 + "\n\n"
        
        for method in other_methods[:10]:  # Limiter à 10 pour la lisibilité
            report += f"• {method['name']} - Ligne {method['line_number']}\n"

    # Section duplications
    if results.get('duplicates_detected'):
        report += f"\nDUPLICATIONS DÉTECTÉES - {len(results['duplicates_detected'])} :\n"
        report += "=" * 45 + "\n\n"
        for duplicate in results['duplicates_detected']:
            report += f"⚠️ {duplicate}\n"

    report += f"""

CORRECTIONS APPLIQUÉES :
=======================
✅ calculate_rollout2_reward → Exclu (appartient au Rollout 2)
✅ Méthodes DEPRECATED → Exclues automatiquement
✅ Duplications → Détectées et signalées
✅ Vérification directe → Priorité aux mentions explicites "rollout 3"

VALIDATION DES 3 ROLLOUTS CLUSTER 0 :
====================================
✅ Rollout 1 (Analyseur) : 72 méthodes identifiées
✅ Rollout 2 (Générateur) : {len(rollout2_methods)} méthodes identifiées
✅ Rollout 3 (Prédicteur) : {len(rollout3_methods)} méthodes identifiées

TOTAL CLUSTER 0 : {72 + len(rollout2_methods) + len(rollout3_methods)} méthodes

CONCLUSION :
============
Les 3 rollouts du cluster par défaut (C0) sont maintenant identifiés.
Ces méthodes représentent l'architecture complète du cluster de référence
qui peut servir de base pour l'universalisation vers tous les clusters.
"""
    
    return report

def main():
    """Fonction principale"""
    results = identify_rollout2_3_cluster0_methods()
    
    # Génération du rapport
    report = generate_rollout2_3_report(results)
    
    # Sauvegarde
    output_file = 'centralisation_methodes/methodes_rollout2_3_cluster0.txt'
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"\n📋 Rapport sauvé dans {output_file}")
    print(f"🎯 Rollout 2: {len(results['rollout2_methods'])} méthodes")
    print(f"🎯 Rollout 3: {len(results['rollout3_methods'])} méthodes")

if __name__ == "__main__":
    main()
