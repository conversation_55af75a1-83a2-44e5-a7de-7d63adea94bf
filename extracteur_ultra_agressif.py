#!/usr/bin/env python3
"""
EXTRACTEUR ULTRA-AGRESSIF - HAUTE FRÉQUENCE
Surveillance continue + polling agressif + forçage des commits
"""

import os
import time
import threading
from datetime import datetime
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import json
import sqlite3

class ExtracteurUltraAgressif(FileSystemEventHandler):
    def __init__(self):
        self.workspace_path = r"C:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\a35ba43ef26792e61fe787a234121806"
        self.db_path = os.path.join(self.workspace_path, "state.vscdb")
        self.wal_path = self.db_path + "-wal"
        self.output_file = "messages_ultra_agressifs.txt"
        
        self.running = True
        self.observer = None
        self.lock = threading.Lock()
        self.dernier_nombre_messages = 0
        self.numero_message_global = 1
        self.surveillance_active = False
        
        self.init_output_file()
        self.charger_etat_initial()
        self.demarrer_surveillance_continue()
    
    def init_output_file(self):
        """Initialise le fichier de sortie ultra-agressif"""
        with open(self.output_file, 'w', encoding='utf-8') as f:
            f.write("EXTRACTEUR ULTRA-AGRESSIF - HAUTE FRÉQUENCE\n")
            f.write(f"Démarré: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("=" * 80 + "\n")
            f.write("🚀 Fréquence: 100 vérifications/seconde\n")
            f.write("⚡ Commits forcés: PRAGMA wal_checkpoint(FULL)\n")
            f.write("🔄 Polling agressif: Multi-thread\n")
            f.write("=" * 80 + "\n\n")
    
    def charger_etat_initial(self):
        """Charge l'état initial avec commits forcés"""
        try:
            messages_existants = self.extraire_avec_commits_forces()
            self.dernier_nombre_messages = len(messages_existants)
            self.numero_message_global = self.dernier_nombre_messages + 1
            
            print(f"📊 État initial: {self.dernier_nombre_messages} messages existants")
            print(f"🔢 Prochain numéro: #{self.numero_message_global}")
            
        except Exception as e:
            print(f"❌ Erreur chargement: {e}")
            self.dernier_nombre_messages = 0
            self.numero_message_global = 1
    
    def extraire_avec_commits_forces(self):
        """Extraction avec forçage des commits SQLite"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=5.0)
            
            # FORCER LES COMMITS ET SYNCHRONISATION
            conn.execute("PRAGMA synchronous=FULL")
            conn.execute("PRAGMA cache_size=0")  # Désactiver cache
            conn.execute("PRAGMA wal_checkpoint(FULL)")  # Forcer commit WAL
            conn.execute("PRAGMA journal_mode=WAL")
            
            cursor = conn.cursor()
            
            # Requête avec retry
            for attempt in range(3):
                try:
                    cursor.execute("SELECT key, value FROM ItemTable WHERE key LIKE '%augment-chat%'")
                    rows = cursor.fetchall()
                    
                    for key, value in rows:
                        if 'webviewView.augment-chat' in key:
                            if isinstance(value, bytes):
                                value_str = value.decode('utf-8')
                            else:
                                value_str = str(value)
                            
                            main_data = json.loads(value_str)
                            webview_data = json.loads(main_data['webviewState'])
                            
                            conversations = webview_data.get('conversations', {})
                            current_conv_id = webview_data.get('currentConversationId')
                            
                            if current_conv_id and current_conv_id in conversations:
                                chat_history = conversations[current_conv_id].get('chatHistory', [])
                                conn.close()
                                return chat_history
                    
                    break  # Sortir de la boucle retry si succès
                    
                except Exception as e:
                    print(f"   ⚠️ Retry {attempt + 1}: {e}")
                    time.sleep(0.1)
            
            conn.close()
            return []
            
        except Exception as e:
            print(f"❌ Erreur extraction forcée: {e}")
            return []
    
    def demarrer_surveillance_continue(self):
        """Démarre la surveillance continue en arrière-plan"""
        def surveillance_haute_frequence():
            print("🚀 Surveillance haute fréquence démarrée (100/sec)")
            while self.running:
                if self.surveillance_active:
                    self.verifier_ultra_rapide()
                time.sleep(0.01)  # 100 vérifications par seconde !
        
        # Thread de surveillance continue
        threading.Thread(target=surveillance_haute_frequence, daemon=True).start()
    
    def on_modified(self, event):
        """Activation de la surveillance ultra-agressive"""
        if event.is_directory:
            return
        
        if os.path.basename(event.src_path) == "state.vscdb-wal":
            timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
            print(f"⚡ [{timestamp}] WAL MODIFIÉ - ACTIVATION SURVEILLANCE ULTRA-AGRESSIVE !")
            
            # Activer la surveillance haute fréquence pendant 5 secondes
            self.surveillance_active = True
            threading.Timer(5.0, self.desactiver_surveillance).start()
            
            # Lancer aussi des threads de polling agressif
            for i in range(3):
                threading.Thread(target=self.polling_agressif, args=(timestamp, i)).start()
    
    def desactiver_surveillance(self):
        """Désactive la surveillance haute fréquence"""
        self.surveillance_active = False
        print("   🔄 Surveillance haute fréquence désactivée")
    
    def verifier_ultra_rapide(self):
        """Vérification ultra-rapide sans logs verbeux"""
        try:
            messages_actuels = self.extraire_avec_commits_forces()
            nombre_actuel = len(messages_actuels)
            
            if nombre_actuel > self.dernier_nombre_messages:
                timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                nouveaux_count = nombre_actuel - self.dernier_nombre_messages
                
                print(f"🔥 [{timestamp}] {nouveaux_count} MESSAGES ULTRA-RAPIDES !")
                
                nouveaux_messages = messages_actuels[self.dernier_nombre_messages:]
                self.ecrire_messages_ultra_agressifs(nouveaux_messages, timestamp)
                self.dernier_nombre_messages = nombre_actuel
                
        except Exception:
            pass  # Ignorer les erreurs pour ne pas spammer
    
    def polling_agressif(self, timestamp_initial, thread_id):
        """Polling agressif avec commits forcés"""
        print(f"   🔄 Thread {thread_id}: Polling agressif démarré")
        
        for attempt in range(100):  # 100 tentatives en 5 secondes
            try:
                time.sleep(0.05)  # 50ms entre tentatives
                
                messages_actuels = self.extraire_avec_commits_forces()
                nombre_actuel = len(messages_actuels)
                
                if nombre_actuel > self.dernier_nombre_messages:
                    with self.lock:
                        if nombre_actuel > self.dernier_nombre_messages:  # Double vérification
                            timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                            nouveaux_count = nombre_actuel - self.dernier_nombre_messages
                            
                            delai = (datetime.strptime(timestamp, '%H:%M:%S.%f') - 
                                    datetime.strptime(timestamp_initial, '%H:%M:%S.%f')).total_seconds() * 1000
                            
                            print(f"🎯 [{timestamp}] Thread {thread_id}: {nouveaux_count} MESSAGES (délai: {delai:.1f}ms)")
                            
                            nouveaux_messages = messages_actuels[self.dernier_nombre_messages:]
                            self.ecrire_messages_ultra_agressifs(nouveaux_messages, timestamp)
                            self.dernier_nombre_messages = nombre_actuel
                            return  # Mission accomplie !
                
            except Exception as e:
                if attempt % 20 == 0:  # Log seulement toutes les 20 tentatives
                    print(f"   ⚠️ Thread {thread_id} tentative {attempt}: {e}")
        
        print(f"   ⏰ Thread {thread_id}: Timeout après 100 tentatives")
    
    def ecrire_messages_ultra_agressifs(self, nouveaux_messages, timestamp):
        """Écriture ultra-agressive des messages"""
        try:
            with open(self.output_file, 'a', encoding='utf-8') as f:
                for message in nouveaux_messages:
                    role = message.get('role', 'unknown')
                    content = message.get('content', '')
                    
                    # Formatage du rôle
                    if role == 'user':
                        role_display = '👤 UTILISATEUR'
                    elif role == 'assistant':
                        role_display = '🤖 ASSISTANT'
                    else:
                        role_display = f'❓ {role.upper()}'
                    
                    # Écriture avec métadonnées
                    f.write(f"[{timestamp}] MESSAGE #{self.numero_message_global} - {role_display}\n")
                    f.write("Mode: ULTRA-AGRESSIF | Commits forcés: OUI\n")
                    f.write("-" * 60 + "\n")
                    
                    if content and content.strip():
                        content_clean = content.strip()[:5000]  # Limiter à 5000 chars
                        f.write(f"{content_clean}\n")
                    else:
                        f.write("[Message vide ou en cours de génération]\n")
                    
                    f.write("=" * 80 + "\n\n")
                    
                    # Log avec aperçu du contenu
                    content_preview = content[:50].replace('\n', ' ') if content else '[vide]'
                    print(f"   ✅ MESSAGE #{self.numero_message_global} ({role}): {content_preview}...")
                    self.numero_message_global += 1
                
                f.flush()
                os.fsync(f.fileno())
                
        except Exception as e:
            print(f"   ❌ Erreur écriture ultra-agressive: {e}")
    
    def demarrer_surveillance(self):
        """Démarre la surveillance ultra-agressive"""
        print("🚀 EXTRACTEUR ULTRA-AGRESSIF DÉMARRÉ")
        print("=" * 60)
        print(f"📁 Workspace: a35ba43ef26792e6...")
        print(f"📄 Sortie: {self.output_file}")
        print(f"🔢 Messages existants: {self.dernier_nombre_messages}")
        print(f"🔢 Prochain numéro: #{self.numero_message_global}")
        print("⚡ Fréquence: 100 vérifications/seconde")
        print("🔄 Polling: 3 threads agressifs")
        print("💬 Écrivez dans Augment pour voir l'extraction ultra-agressive...")
        print("⏹️  Ctrl+C pour arrêter")
        print("=" * 60)
        
        # Surveillance filesystem
        self.observer = Observer()
        self.observer.schedule(self, self.workspace_path, recursive=False)
        self.observer.start()
        
        try:
            while self.running:
                threading.Event().wait(1)
                
        except KeyboardInterrupt:
            print("\n🛑 Arrêt de l'extracteur ultra-agressif...")
            self.running = False
            if self.observer:
                self.observer.stop()
                self.observer.join()

if __name__ == "__main__":
    extracteur = ExtracteurUltraAgressif()
    extracteur.demarrer_surveillance()
