#!/usr/bin/env python3
"""
EXTRACTEUR WAL CORRIGÉ - DÉTECTION FIABLE PAR INDEX
Détection des nouveaux messages par comparaison d'index plutôt que par ID
"""

import os
import time
import threading
from datetime import datetime
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import json
import sqlite3

class ExtracteurWALCorrige(FileSystemEventHandler):
    def __init__(self):
        self.workspace_path = r"C:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\a35ba43ef26792e61fe787a234121806"
        self.db_path = os.path.join(self.workspace_path, "state.vscdb")
        self.wal_path = self.db_path + "-wal"
        self.output_file = "messages_corriges_numerotes.txt"
        
        self.running = True
        self.observer = None
        self.lock = threading.Lock()
        self.derniere_taille_wal = 0
        self.dernier_nombre_messages = 0
        self.numero_message_global = 1
        
        self.init_output_file()
        self.init_wal_monitoring()
        self.charger_etat_initial()
    
    def init_output_file(self):
        """Initialise le fichier de sortie"""
        with open(self.output_file, 'w', encoding='utf-8') as f:
            f.write("EXTRACTEUR WAL CORRIGÉ - DÉTECTION PAR INDEX\n")
            f.write(f"Démarré: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("=" * 80 + "\n")
            f.write("Méthode: Comparaison par nombre de messages (plus fiable)\n")
            f.write("Format: [TIMESTAMP] MESSAGE #N - ROLE\n")
            f.write("=" * 80 + "\n\n")
    
    def init_wal_monitoring(self):
        """Initialise la surveillance WAL"""
        try:
            if os.path.exists(self.wal_path):
                self.derniere_taille_wal = os.path.getsize(self.wal_path)
                print(f"📊 WAL initial: {self.derniere_taille_wal} bytes")
            else:
                print("⚠️ Fichier WAL non trouvé")
                self.derniere_taille_wal = 0
        except Exception as e:
            print(f"❌ Erreur init WAL: {e}")
            self.derniere_taille_wal = 0
    
    def charger_etat_initial(self):
        """Charge l'état initial - compte simplement les messages"""
        try:
            messages_existants = self.extraire_tous_messages()
            self.dernier_nombre_messages = len(messages_existants)
            self.numero_message_global = self.dernier_nombre_messages + 1
            
            print(f"📊 État initial: {self.dernier_nombre_messages} messages existants")
            print(f"🔢 Prochain numéro global: {self.numero_message_global}")
            
        except Exception as e:
            print(f"❌ Erreur chargement état: {e}")
            self.dernier_nombre_messages = 0
            self.numero_message_global = 1
    
    def extraire_tous_messages(self):
        """Extrait TOUS les messages de la conversation actuelle"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=2.0)
            conn.execute("PRAGMA journal_mode=WAL")
            cursor = conn.cursor()
            
            cursor.execute("SELECT value FROM ItemTable WHERE key = 'memento/webviewView.augment-chat' LIMIT 1")
            row = cursor.fetchone()
            
            if not row:
                conn.close()
                return []
            
            value = row[0]
            if isinstance(value, bytes):
                value_str = value.decode('utf-8')
            else:
                value_str = str(value)
            
            main_data = json.loads(value_str)
            webview_data = json.loads(main_data['webviewState'])
            
            conversations = webview_data.get('conversations', {})
            current_conv_id = webview_data.get('currentConversationId')
            
            if current_conv_id and current_conv_id in conversations:
                chat_history = conversations[current_conv_id].get('chatHistory', [])
                conn.close()
                return chat_history
            
            conn.close()
            return []
            
        except Exception as e:
            print(f"❌ Erreur extraction: {e}")
            return []
    
    def on_modified(self, event):
        """Détection instantanée des modifications WAL"""
        if event.is_directory:
            return
        
        if os.path.basename(event.src_path) == "state.vscdb-wal":
            timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
            print(f"⚡ [{timestamp}] WAL MODIFIÉ - VÉRIFICATION NOUVEAUX MESSAGES...")
            threading.Thread(target=self.verifier_nouveaux_messages, args=(timestamp,)).start()
    
    def verifier_nouveaux_messages(self, timestamp_detection):
        """Vérifie s'il y a de nouveaux messages par comparaison de nombre"""
        try:
            # Petit délai pour s'assurer que les données sont commitées
            time.sleep(0.1)  # 100ms
            
            # Extraire tous les messages actuels
            messages_actuels = self.extraire_tous_messages()
            nombre_actuel = len(messages_actuels)
            
            print(f"   📊 Messages: {self.dernier_nombre_messages} → {nombre_actuel}")
            
            if nombre_actuel > self.dernier_nombre_messages:
                # Il y a de nouveaux messages !
                nouveaux_count = nombre_actuel - self.dernier_nombre_messages
                timestamp_extraction = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                
                delai = (datetime.strptime(timestamp_extraction, '%H:%M:%S.%f') - 
                        datetime.strptime(timestamp_detection, '%H:%M:%S.%f')).total_seconds() * 1000
                
                print(f"🔥 [{timestamp_extraction}] {nouveaux_count} NOUVEAUX MESSAGES DÉTECTÉS !")
                print(f"   ⏱️ Délai: {delai:.1f}ms")
                
                # Extraire seulement les nouveaux messages
                nouveaux_messages = messages_actuels[self.dernier_nombre_messages:]
                
                self.ecrire_nouveaux_messages(nouveaux_messages, timestamp_extraction)
                self.dernier_nombre_messages = nombre_actuel
                
            else:
                print(f"   📝 Aucun nouveau message (stable à {nombre_actuel})")
                
        except Exception as e:
            print(f"   ❌ Erreur vérification: {e}")
    
    def ecrire_nouveaux_messages(self, nouveaux_messages, timestamp):
        """Écrit les nouveaux messages avec numérotation globale"""
        with self.lock:
            try:
                with open(self.output_file, 'a', encoding='utf-8') as f:
                    for message in nouveaux_messages:
                        role = message.get('role', 'unknown')
                        content = message.get('content', '')
                        
                        # Formatage du rôle avec emojis
                        if role == 'user':
                            role_display = '👤 UTILISATEUR'
                        elif role == 'assistant':
                            role_display = '🤖 ASSISTANT'
                        else:
                            role_display = f'❓ {role.upper()}'
                        
                        # Écriture du message avec numéro global
                        f.write(f"[{timestamp}] MESSAGE #{self.numero_message_global} - {role_display}\n")
                        f.write("-" * 60 + "\n")
                        
                        if content and content.strip():
                            # Nettoyer le contenu
                            content_clean = self.nettoyer_contenu(content)
                            f.write(f"{content_clean}\n")
                        else:
                            f.write("[Message vide ou en cours de génération]\n")
                        
                        f.write("=" * 80 + "\n\n")
                        
                        print(f"   ✅ MESSAGE #{self.numero_message_global} écrit ({role}): {content[:50]}...")
                        self.numero_message_global += 1
                    
                    f.flush()
                    os.fsync(f.fileno())
                    
            except Exception as e:
                print(f"   ❌ Erreur écriture: {e}")
    
    def nettoyer_contenu(self, content):
        """Nettoie le contenu du message"""
        if not content:
            return "[Contenu vide]"
        
        # Supprimer les caractères de contrôle
        import re
        content = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', '', content)
        
        # Limiter la longueur si très long
        if len(content) > 10000:
            content = content[:10000] + "\n[... contenu tronqué ...]"
        
        return content.strip()
    
    def demarrer_surveillance(self):
        """Démarre la surveillance WAL corrigée"""
        print("🚀 EXTRACTEUR WAL CORRIGÉ DÉMARRÉ")
        print("=" * 60)
        print(f"📁 Workspace: a35ba43ef26792e6...")
        print(f"📝 WAL: {self.wal_path}")
        print(f"📄 Sortie: {self.output_file}")
        print(f"🔢 Messages existants: {self.dernier_nombre_messages}")
        print(f"🔢 Prochain numéro: #{self.numero_message_global}")
        print("💬 Écrivez dans Augment pour voir l'extraction corrigée...")
        print("⏹️  Ctrl+C pour arrêter")
        print("=" * 60)
        
        # Surveillance du répertoire workspace
        self.observer = Observer()
        self.observer.schedule(self, self.workspace_path, recursive=False)
        self.observer.start()
        
        try:
            while self.running:
                threading.Event().wait(1)
                
        except KeyboardInterrupt:
            print("\n🛑 Arrêt de l'extracteur corrigé...")
            self.running = False
            if self.observer:
                self.observer.stop()
                self.observer.join()

if __name__ == "__main__":
    extracteur = ExtracteurWALCorrige()
    extracteur.demarrer_surveillance()
