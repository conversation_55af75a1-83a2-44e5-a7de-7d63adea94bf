
// ===== AUGMENT AUTO LOGGING SYSTEM =====
const fs = require('fs');
const path = require('path');

class AugmentAutoLogger {
    constructor() {
        this.initializeLogging();
    }
    
    initializeLogging() {
        try {
            // Obtenir le workspace
            const workspaceRoot = vscode?.workspace?.workspaceFolders?.[0]?.uri?.fsPath || process.cwd();
            
            // Créer les fichiers de log
            this.logFile = path.join(workspaceRoot, 'augment_conversation_auto.txt');
            this.jsonLog = path.join(workspaceRoot, 'augment_conversation_auto.json');
            
            // Initialiser les fichiers
            const timestamp = new Date().toISOString();
            const header = `AUGMENT CONVERSATION - LOG AUTOMATIQUE\nDémarré: ${timestamp}\nWorkspace: ${workspaceRoot}\n${'='.repeat(80)}\n\n`;
            
            if (!fs.existsSync(this.logFile)) {
                fs.writeFileSync(this.logFile, header, 'utf8');
            }
            
            const jsonData = {
                started_at: timestamp,
                workspace_root: workspaceRoot,
                messages: []
            };
            
            if (!fs.existsSync(this.jsonLog)) {
                fs.writeFileSync(this.jsonLog, JSON.stringify(jsonData, null, 2), 'utf8');
            }
            
            console.log('[AUGMENT AUTO LOGGER] Système activé pour:', workspaceRoot);
            
        } catch (error) {
            console.error('[AUGMENT AUTO LOGGER] Erreur:', error);
        }
    }
}

// Initialiser le logger automatiquement
setTimeout(() => {
    try {
        if (typeof vscode !== 'undefined') {
            new AugmentAutoLogger();
        }
    } catch (e) {
        console.log('[AUGMENT AUTO LOGGER] Initialisation différée');
    }
}, 3000);

// ===== FIN AUTO LOGGING =====

