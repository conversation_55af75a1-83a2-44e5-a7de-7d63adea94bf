#!/usr/bin/env python3
"""
SCRIPT POUR IDENTIFIER LES MÉTHODES ROLLOUT 1 CLUSTER PAR DÉFAUT
================================================================

Objectif : Identifier les méthodes spécifiques au Rollout 1 du cluster par défaut
qui analysent les indices 1, 2, 3 pour mesurer leur impact sur les indices 4, 5

Indices :
- Index 1 : PAIR/IMPAIR
- Index 2 : SYNC/DESYNC  
- Index 3 : COMBINED (PAIR_SYNC, IMPAIR_DESYNC, etc.)
- Index 4 : P/B/T (Player/Banker/Tie)
- Index 5 : S/O (Same/Opposite)
"""

import re
from typing import List, Dict

def extract_all_methods_from_class_txt():
    """Extrait toutes les méthodes du fichier class.txt avec leur contenu"""
    try:
        with open('centralisation_methodes/class.txt', 'r', encoding='utf-8') as f:
            content = f.read()
        
        methods = []
        lines = content.split('\n')
        
        current_method = None
        method_content = []
        indent_level = None
        
        for line_num, line in enumerate(lines, 1):
            # Détection début de méthode
            if 'def ' in line and '(' in line:
                # Sauvegarder la méthode précédente
                if current_method:
                    methods.append({
                        'name': current_method,
                        'content': '\n'.join(method_content),
                        'line_count': len(method_content)
                    })
                
                # Nouvelle méthode
                match = re.search(r'def ([a-zA-Z_][a-zA-Z0-9_]*)', line)
                if match:
                    current_method = match.group(1)
                    method_content = [line]
                    indent_level = len(line) - len(line.lstrip())
            
            elif current_method:
                # Continuer la méthode actuelle
                if line.strip() == '' or line.startswith(' ' * (indent_level + 1)):
                    method_content.append(line)
                elif line.strip() and not line.startswith(' ' * (indent_level + 1)):
                    # Fin de méthode si nouvelle définition au même niveau
                    if line.strip().startswith('def ') or line.strip().startswith('class '):
                        methods.append({
                            'name': current_method,
                            'content': '\n'.join(method_content),
                            'line_count': len(method_content)
                        })
                        # Commencer nouvelle méthode
                        match = re.search(r'def ([a-zA-Z_][a-zA-Z0-9_]*)', line)
                        if match:
                            current_method = match.group(1)
                            method_content = [line]
                            indent_level = len(line) - len(line.lstrip())
                        else:
                            current_method = None
                            method_content = []
                    else:
                        method_content.append(line)
        
        # Sauvegarder la dernière méthode
        if current_method:
            methods.append({
                'name': current_method,
                'content': '\n'.join(method_content),
                'line_count': len(method_content)
            })
        
        return methods
    except Exception as e:
        print(f"Erreur lecture class.txt: {e}")
        return []

def is_rollout1_method(method_name: str, method_content: str) -> Dict:
    """Détermine si une méthode appartient au Rollout 1 cluster par défaut"""
    
    # Mots-clés spécifiques au Rollout 1
    rollout1_keywords = [
        'impair', 'pair', 'sync', 'desync', 'combined',
        'analyze', 'bias', 'correlation', 'impact',
        'consecutive', 'alternation', 'structural'
    ]
    
    # Mots-clés d'analyse des indices
    index_analysis_keywords = [
        'pbt_result', 'so_conversion', 'position_type',
        'sync_state', 'combined_state', 'pb_outcomes', 'so_outcomes'
    ]
    
    # Mots-clés d'impact croisé (1,2,3 → 4,5)
    cross_impact_keywords = [
        'correlate', 'impact', 'influence', 'effect',
        'to_pb', 'to_so', 'pb_correlation', 'so_correlation'
    ]
    
    # Exclusions (méthodes universelles et spécifiques clusters)
    excluded_patterns = [
        '_rollout_analyzer$',  # Méthode universelle
        '_c2_', '_c3_', '_c4_', '_c5_', '_c6_', '_c7_',  # Spécifiques clusters
        'universal', 'generic', 'cluster_specialized'
    ]
    
    # Vérifier exclusions
    for pattern in excluded_patterns:
        if re.search(pattern, method_name):
            return {'is_rollout1': False, 'reason': f'Exclu: {pattern}'}
    
    content_lower = method_content.lower()
    name_lower = method_name.lower()
    
    # Scores de correspondance
    rollout1_score = 0
    index_analysis_score = 0
    cross_impact_score = 0
    
    # Compter les mots-clés Rollout 1
    for keyword in rollout1_keywords:
        rollout1_score += content_lower.count(keyword) + name_lower.count(keyword)
    
    # Compter les mots-clés d'analyse des indices
    for keyword in index_analysis_keywords:
        index_analysis_score += content_lower.count(keyword)
    
    # Compter les mots-clés d'impact croisé
    for keyword in cross_impact_keywords:
        cross_impact_score += content_lower.count(keyword)
    
    # Critères spécifiques Rollout 1
    has_impair_analysis = 'impair' in content_lower and 'consecutive' in content_lower
    has_sync_analysis = 'sync' in content_lower and ('desync' in content_lower or 'alternation' in content_lower)
    has_combined_analysis = 'combined' in content_lower and ('bias' in content_lower or 'structural' in content_lower)
    has_correlation_analysis = 'correlat' in content_lower and ('pb' in content_lower or 'so' in content_lower)
    
    # Analyse spécifique des indices
    analyzes_index_1_2_3 = any(idx in content_lower for idx in ['position_type', 'sync_state', 'combined_state'])
    measures_impact_4_5 = any(idx in content_lower for idx in ['pbt_result', 'so_conversion', 'pb_outcomes', 'so_outcomes'])
    
    # Détermination finale
    is_rollout1 = False
    confidence = 0
    reasons = []
    
    if rollout1_score >= 3:
        confidence += 30
        reasons.append(f"Mots-clés Rollout1: {rollout1_score}")
    
    if index_analysis_score >= 2:
        confidence += 25
        reasons.append(f"Analyse indices: {index_analysis_score}")
    
    if cross_impact_score >= 1:
        confidence += 20
        reasons.append(f"Impact croisé: {cross_impact_score}")
    
    if has_impair_analysis:
        confidence += 15
        reasons.append("Analyse IMPAIR consécutifs")
    
    if has_sync_analysis:
        confidence += 15
        reasons.append("Analyse SYNC/DESYNC")
    
    if has_combined_analysis:
        confidence += 10
        reasons.append("Analyse COMBINED")
    
    if has_correlation_analysis:
        confidence += 10
        reasons.append("Analyse corrélations")
    
    if analyzes_index_1_2_3 and measures_impact_4_5:
        confidence += 20
        reasons.append("Analyse 1,2,3 → impact 4,5")
    
    # Seuil de décision
    if confidence >= 50:
        is_rollout1 = True
    
    return {
        'is_rollout1': is_rollout1,
        'confidence': confidence,
        'reasons': reasons,
        'scores': {
            'rollout1_keywords': rollout1_score,
            'index_analysis': index_analysis_score,
            'cross_impact': cross_impact_score
        },
        'specific_analysis': {
            'impair_analysis': has_impair_analysis,
            'sync_analysis': has_sync_analysis,
            'combined_analysis': has_combined_analysis,
            'correlation_analysis': has_correlation_analysis,
            'analyzes_1_2_3': analyzes_index_1_2_3,
            'measures_impact_4_5': measures_impact_4_5
        }
    }

def identify_rollout1_methods():
    """Identifie toutes les méthodes du Rollout 1 cluster par défaut"""
    
    print("Extraction de toutes les méthodes...")
    all_methods = extract_all_methods_from_class_txt()
    print(f"Total méthodes extraites: {len(all_methods)}")
    
    # Exclure les 31 méthodes universelles
    universal_methods = {
        '_rollout_analyzer', '_analyze_impair_consecutive_bias', '_analyze_pair_priority_2_autonomous',
        '_analyze_sync_alternation_bias', '_correlate_impair_with_sync', '_correlate_impair_with_combined',
        '_correlate_impair_with_pb', '_correlate_impair_with_so', '_correlate_bias_to_pb_variations',
        '_correlate_bias_to_so_variations', '_analyze_combined_structural_bias',
        '_generate_priority_based_synthesis_autonomous', '_generate_bias_signals_summary',
        '_generate_bias_generation_guidance', '_generate_bias_quick_access', '_rollout_generator',
        '_define_optimized_generation_space', '_generate_sequences_from_signals',
        '_generate_fallback_sequences', '_enrich_sequences_with_complete_indexes',
        '_rollout_predictor', '_evaluate_sequence_quality', '_evaluate_signal_alignment',
        '_evaluate_fallback_alignment', '_analyze_sequence_consistency', '_assess_risk_reward_ratio',
        '_validate_sequence_logic', '_calculate_sequence_score', '_select_best_sequence',
        '_calculate_cluster_confidence_azr_calibrated', '_convert_pb_sequence_to_so'
    }
    
    # Filtrer les méthodes candidates
    candidate_methods = [m for m in all_methods if m['name'] not in universal_methods]
    print(f"Méthodes candidates (après exclusion 31 universelles): {len(candidate_methods)}")
    
    rollout1_methods = []
    
    print("\nAnalyse des méthodes candidates...")
    for method in candidate_methods:
        analysis = is_rollout1_method(method['name'], method['content'])
        
        if analysis['is_rollout1']:
            rollout1_methods.append({
                'name': method['name'],
                'analysis': analysis,
                'line_count': method['line_count']
            })
            print(f"✅ {method['name']} - Confiance: {analysis['confidence']}%")
    
    return rollout1_methods, len(candidate_methods)

def generate_rollout1_report(rollout1_methods: List[Dict], total_candidates: int):
    """Génère un rapport des méthodes Rollout 1"""
    
    report = f"""MÉTHODES ROLLOUT 1 CLUSTER PAR DÉFAUT IDENTIFIÉES
=================================================

RÉSULTATS DE L'ANALYSE :
=======================
Total méthodes candidates : {total_candidates}
Méthodes Rollout 1 identifiées : {len(rollout1_methods)}
Pourcentage : {len(rollout1_methods)/total_candidates*100:.1f}%

LISTE DES MÉTHODES ROLLOUT 1 :
=============================

"""
    
    # Trier par confiance décroissante
    rollout1_methods.sort(key=lambda x: x['analysis']['confidence'], reverse=True)
    
    for i, method in enumerate(rollout1_methods, 1):
        analysis = method['analysis']
        report += f"{i:2d}. {method['name']}\n"
        report += f"    Confiance : {analysis['confidence']}%\n"
        report += f"    Lignes de code : {method['line_count']}\n"
        report += f"    Raisons : {', '.join(analysis['reasons'])}\n"
        
        # Détails de l'analyse spécifique
        specific = analysis['specific_analysis']
        features = []
        if specific['impair_analysis']: features.append("Analyse IMPAIR")
        if specific['sync_analysis']: features.append("Analyse SYNC/DESYNC")
        if specific['combined_analysis']: features.append("Analyse COMBINED")
        if specific['correlation_analysis']: features.append("Corrélations")
        if specific['analyzes_1_2_3'] and specific['measures_impact_4_5']: features.append("Impact 1,2,3→4,5")
        
        if features:
            report += f"    Fonctionnalités : {', '.join(features)}\n"
        report += "\n"
    
    report += f"""
ANALYSE PAR CATÉGORIE :
======================
Méthodes avec analyse IMPAIR : {sum(1 for m in rollout1_methods if m['analysis']['specific_analysis']['impair_analysis'])}
Méthodes avec analyse SYNC/DESYNC : {sum(1 for m in rollout1_methods if m['analysis']['specific_analysis']['sync_analysis'])}
Méthodes avec analyse COMBINED : {sum(1 for m in rollout1_methods if m['analysis']['specific_analysis']['combined_analysis'])}
Méthodes avec corrélations : {sum(1 for m in rollout1_methods if m['analysis']['specific_analysis']['correlation_analysis'])}
Méthodes avec impact 1,2,3→4,5 : {sum(1 for m in rollout1_methods if m['analysis']['specific_analysis']['analyzes_1_2_3'] and m['analysis']['specific_analysis']['measures_impact_4_5'])}

CONCLUSION :
============
Ces méthodes représentent les fonctionnalités manquantes du Rollout 1 
dans les méthodes universelles. Elles analysent spécifiquement :
- Les indices 1, 2, 3 (PAIR/IMPAIR, SYNC/DESYNC, COMBINED)
- Leur impact sur les indices 4, 5 (P/B/T, S/O)
- Les biais structurels et corrélations croisées

RECOMMANDATION :
===============
Intégrer ces méthodes dans les méthodes universelles du Rollout 1
pour compléter les fonctionnalités manquantes d'analyse des indices.
"""
    
    return report

def main():
    """Fonction principale"""
    print("🔍 IDENTIFICATION DES MÉTHODES ROLLOUT 1 CLUSTER PAR DÉFAUT")
    print("=" * 60)
    
    rollout1_methods, total_candidates = identify_rollout1_methods()
    
    # Génération du rapport
    report = generate_rollout1_report(rollout1_methods, total_candidates)
    
    # Sauvegarde
    output_file = 'centralisation_methodes/methodes_rollout1_cluster_defaut.txt'
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"\n📋 Rapport sauvé dans {output_file}")
    print(f"🎯 {len(rollout1_methods)} méthodes Rollout 1 identifiées sur {total_candidates} candidates")

if __name__ == "__main__":
    main()
