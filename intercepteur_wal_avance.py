#!/usr/bin/env python3
"""
INTERCEPTEUR WAL AVANCÉ - LECTURE DIRECTE DU WRITE-AHEAD LOG
Interception des données AVANT commit dans la DB principale
"""

import os
import time
import threading
import struct
from datetime import datetime
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import json
import sqlite3

class IntercepteurWAL(FileSystemEventHandler):
    def __init__(self):
        self.workspace_path = r"C:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\a35ba43ef26792e61fe787a234121806"
        self.db_path = os.path.join(self.workspace_path, "state.vscdb")
        self.wal_path = self.db_path + "-wal"
        self.shm_path = self.db_path + "-shm"
        self.output_file = "messages_wal_instantane.txt"
        
        self.running = True
        self.observer = None
        self.lock = threading.Lock()
        self.derniere_taille_wal = 0
        self.derniers_messages = []
        
        self.init_output_file()
        self.init_wal_monitoring()
    
    def init_output_file(self):
        """Initialise le fichier de sortie"""
        with open(self.output_file, 'w', encoding='utf-8') as f:
            f.write("INTERCEPTEUR WAL AVANCÉ - DONNÉES INSTANTANÉES\n")
            f.write(f"Démarré: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("=" * 80 + "\n")
            f.write(f"📁 DB: {self.db_path}\n")
            f.write(f"📝 WAL: {self.wal_path}\n")
            f.write(f"🧠 SHM: {self.shm_path}\n")
            f.write("=" * 80 + "\n\n")
    
    def init_wal_monitoring(self):
        """Initialise la surveillance WAL"""
        try:
            if os.path.exists(self.wal_path):
                self.derniere_taille_wal = os.path.getsize(self.wal_path)
                print(f"📊 WAL initial: {self.derniere_taille_wal} bytes")
            else:
                print("⚠️ Fichier WAL non trouvé - sera créé lors de la première écriture")
                self.derniere_taille_wal = 0
        except Exception as e:
            print(f"❌ Erreur init WAL: {e}")
            self.derniere_taille_wal = 0
    
    def on_modified(self, event):
        """Détection instantanée des modifications WAL"""
        if event.is_directory:
            return
        
        filename = os.path.basename(event.src_path)
        
        if filename == "state.vscdb-wal":
            timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
            print(f"⚡ [{timestamp}] WAL MODIFIÉ INSTANTANÉMENT !")
            threading.Thread(target=self.analyser_wal_instantane, args=(timestamp,)).start()
        
        elif filename == "state.vscdb-shm":
            timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
            print(f"🧠 [{timestamp}] SHM (Shared Memory) modifié")
    
    def analyser_wal_instantane(self, timestamp_detection):
        """Analyse instantanée du fichier WAL"""
        try:
            if not os.path.exists(self.wal_path):
                print(f"   ⚠️ WAL non accessible")
                return
            
            taille_actuelle = os.path.getsize(self.wal_path)
            
            if taille_actuelle > self.derniere_taille_wal:
                nouveaux_bytes = taille_actuelle - self.derniere_taille_wal
                print(f"   📈 WAL: +{nouveaux_bytes} bytes ({self.derniere_taille_wal} → {taille_actuelle})")
                
                # Lire les nouveaux données du WAL
                self.lire_nouveaux_data_wal(timestamp_detection, nouveaux_bytes)
                self.derniere_taille_wal = taille_actuelle
            
        except Exception as e:
            print(f"   ❌ Erreur analyse WAL: {e}")
    
    def lire_nouveaux_data_wal(self, timestamp_detection, nouveaux_bytes):
        """Lit les nouvelles données du WAL"""
        try:
            with open(self.wal_path, 'rb') as f:
                # Aller à la position des nouvelles données
                f.seek(-nouveaux_bytes, 2)
                nouveaux_data = f.read(nouveaux_bytes)
                
                print(f"   📖 Lecture de {len(nouveaux_data)} bytes depuis WAL")
                
                # Analyser les données WAL
                self.analyser_data_wal(nouveaux_data, timestamp_detection)
                
        except Exception as e:
            print(f"   ❌ Erreur lecture WAL: {e}")
    
    def analyser_data_wal(self, data, timestamp_detection):
        """Analyse les données brutes du WAL"""
        try:
            # Le WAL SQLite a un format spécifique
            # Header: 32 bytes, puis frames de données
            
            if len(data) < 32:
                print(f"   ⚠️ Données WAL trop courtes: {len(data)} bytes")
                return
            
            # Chercher des patterns JSON dans les données
            data_str = data.decode('utf-8', errors='ignore')
            
            # Chercher des patterns de conversation
            if 'augment-chat' in data_str or 'chatHistory' in data_str or 'conversations' in data_str:
                print(f"   🎯 DONNÉES CONVERSATION DÉTECTÉES dans WAL !")
                self.extraire_conversations_wal(data_str, timestamp_detection)
            else:
                print(f"   📄 Données WAL sans conversation (taille: {len(data)} bytes)")
                
        except Exception as e:
            print(f"   ❌ Erreur analyse données WAL: {e}")
    
    def extraire_conversations_wal(self, data_str, timestamp_detection):
        """Extrait les conversations depuis les données WAL"""
        try:
            # Chercher les structures JSON dans les données
            start_pos = 0
            conversations_trouvees = []
            
            while True:
                # Chercher le début d'un JSON
                json_start = data_str.find('{', start_pos)
                if json_start == -1:
                    break
                
                # Essayer de parser le JSON
                for json_end in range(json_start + 1, len(data_str)):
                    if data_str[json_end] == '}':
                        try:
                            json_candidate = data_str[json_start:json_end + 1]
                            parsed = json.loads(json_candidate)
                            
                            # Vérifier si c'est une conversation
                            if self.is_conversation_data(parsed):
                                conversations_trouvees.append(parsed)
                                print(f"   ✅ Conversation extraite du WAL !")
                                
                        except json.JSONDecodeError:
                            continue
                
                start_pos = json_start + 1
            
            if conversations_trouvees:
                self.traiter_conversations_wal(conversations_trouvees, timestamp_detection)
            
        except Exception as e:
            print(f"   ❌ Erreur extraction conversations WAL: {e}")
    
    def is_conversation_data(self, data):
        """Vérifie si les données contiennent des conversations"""
        if isinstance(data, dict):
            return ('conversations' in data or 
                   'chatHistory' in data or 
                   'currentConversationId' in data or
                   'webviewState' in data)
        return False
    
    def traiter_conversations_wal(self, conversations, timestamp_detection):
        """Traite les conversations extraites du WAL"""
        timestamp_traitement = datetime.now().strftime('%H:%M:%S.%f')[:-3]
        
        with self.lock:
            for conv_data in conversations:
                self.ecrire_conversation_wal(conv_data, timestamp_detection, timestamp_traitement)
    
    def ecrire_conversation_wal(self, conv_data, timestamp_detection, timestamp_traitement):
        """Écrit les données de conversation depuis WAL"""
        try:
            with open(self.output_file, 'a', encoding='utf-8') as f:
                f.write(f"[{timestamp_traitement}] DONNÉES WAL INTERCEPTÉES\n")
                f.write(f"Détection: {timestamp_detection}\n")
                f.write(f"Traitement: {timestamp_traitement}\n")
                f.write("-" * 60 + "\n")
                
                # Écrire les données de conversation
                if isinstance(conv_data, dict):
                    f.write(f"Type: {type(conv_data).__name__}\n")
                    f.write(f"Clés: {list(conv_data.keys())}\n")
                    
                    # Extraire les messages si possible
                    if 'conversations' in conv_data:
                        f.write("CONVERSATIONS TROUVÉES:\n")
                        for conv_id, conv in conv_data['conversations'].items():
                            if 'chatHistory' in conv:
                                f.write(f"  Conversation {conv_id}: {len(conv['chatHistory'])} messages\n")
                
                f.write("=" * 80 + "\n\n")
                f.flush()
                os.fsync(f.fileno())
                
        except Exception as e:
            print(f"   ❌ Erreur écriture WAL: {e}")
    
    def demarrer_surveillance(self):
        """Démarre la surveillance WAL"""
        print("🚀 INTERCEPTEUR WAL AVANCÉ DÉMARRÉ")
        print("=" * 60)
        print(f"📁 Workspace: a35ba43ef26792e6...")
        print(f"📝 WAL: {self.wal_path}")
        print(f"📄 Sortie: {self.output_file}")
        print(f"📊 WAL initial: {self.derniere_taille_wal} bytes")
        print("💬 Écrivez dans Augment pour voir l'interception WAL...")
        print("⏹️  Ctrl+C pour arrêter")
        print("=" * 60)
        
        # Surveillance du répertoire workspace
        self.observer = Observer()
        self.observer.schedule(self, self.workspace_path, recursive=False)
        self.observer.start()
        
        try:
            while self.running:
                threading.Event().wait(1)
                
        except KeyboardInterrupt:
            print("\n🛑 Arrêt de l'intercepteur WAL...")
            self.running = False
            if self.observer:
                self.observer.stop()
                self.observer.join()

if __name__ == "__main__":
    intercepteur = IntercepteurWAL()
    intercepteur.demarrer_surveillance()
