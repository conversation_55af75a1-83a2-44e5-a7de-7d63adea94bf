import{i as f}from"./init-g68aIKmP.js";class i extends Map{constructor(t,r=l){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:r}}),t!=null)for(const[u,n]of t)this.set(u,n)}get(t){return super.get(c(this,t))}has(t){return super.has(c(this,t))}set(t,r){return super.set(function({_intern:u,_key:n},e){const o=n(e);return u.has(o)?u.get(o):(u.set(o,e),e)}(this,t),r)}delete(t){return super.delete(function({_intern:r,_key:u},n){const e=u(n);return r.has(e)&&(n=r.get(e),r.delete(e)),n}(this,t))}}function c({_intern:s,_key:t},r){const u=t(r);return s.has(u)?s.get(u):r}function l(s){return s!==null&&typeof s=="object"?s.valueOf():s}const a=Symbol("implicit");function h(){var s=new i,t=[],r=[],u=a;function n(e){let o=s.get(e);if(o===void 0){if(u!==a)return u;s.set(e,o=t.push(e)-1)}return r[o%r.length]}return n.domain=function(e){if(!arguments.length)return t.slice();t=[],s=new i;for(const o of e)s.has(o)||s.set(o,t.push(o)-1);return n},n.range=function(e){return arguments.length?(r=Array.from(e),n):r.slice()},n.unknown=function(e){return arguments.length?(u=e,n):u},n.copy=function(){return h(t,r).unknown(u)},f.apply(n,arguments),n}export{h as o};
