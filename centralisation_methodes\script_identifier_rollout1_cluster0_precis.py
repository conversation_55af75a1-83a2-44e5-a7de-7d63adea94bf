#!/usr/bin/env python3
"""
SCRIPT PRÉCIS POUR IDENTIFIER LES MÉTHODES ROLLOUT 1 CLUSTER 0 (PAR DÉFAUT)
===========================================================================

Objectif : Identifier avec certitude les méthodes qui appartiennent au 
Rollout 1 du cluster 0 (cluster par défaut/standard) en analysant :
1. Les méthodes appelées par _rollout_analyzer (Rollout 1 cluster 0)
2. Les méthodes qui analysent les indices 1,2,3 → impact 4,5
3. Les méthodes purement analytiques (pas de génération)
"""

import re
from typing import List, Dict, Set

def extract_method_calls_from_rollout_analyzer():
    """Extrait toutes les méthodes appelées par _rollout_analyzer"""
    try:
        with open('centralisation_methodes/class.txt', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Trouver la méthode _rollout_analyzer
        lines = content.split('\n')
        in_rollout_analyzer = False
        rollout_analyzer_content = []
        indent_level = None
        
        for line in lines:
            if 'def _rollout_analyzer(' in line:
                in_rollout_analyzer = True
                indent_level = len(line) - len(line.lstrip())
                rollout_analyzer_content.append(line)
            elif in_rollout_analyzer:
                # Continuer jusqu'à la prochaine méthode au même niveau
                if line.strip() and not line.startswith(' ' * (indent_level + 1)):
                    if line.strip().startswith('def '):
                        break
                rollout_analyzer_content.append(line)
        
        rollout_analyzer_text = '\n'.join(rollout_analyzer_content)
        
        # Extraire les appels de méthodes self._method_name()
        method_calls = re.findall(r'self\.(_[a-zA-Z_][a-zA-Z0-9_]*)\(', rollout_analyzer_text)
        
        # Nettoyer et dédupliquer
        unique_methods = list(set(method_calls))
        
        return unique_methods, rollout_analyzer_text
        
    except Exception as e:
        print(f"Erreur: {e}")
        return [], ""

def find_methods_called_by_rollout1_methods(rollout1_methods: List[str]):
    """Trouve récursivement toutes les méthodes appelées par les méthodes Rollout 1"""
    try:
        with open('centralisation_methodes/class.txt', 'r', encoding='utf-8') as f:
            content = f.read()
        
        all_rollout1_methods = set(rollout1_methods)
        methods_to_check = rollout1_methods.copy()
        
        while methods_to_check:
            current_method = methods_to_check.pop(0)
            
            # Trouver le contenu de cette méthode
            method_pattern = f'def {current_method}\\('
            method_match = re.search(method_pattern, content)
            
            if method_match:
                # Extraire le contenu de la méthode
                start_pos = method_match.start()
                lines = content[start_pos:].split('\n')
                
                method_content = []
                indent_level = None
                
                for i, line in enumerate(lines):
                    if i == 0:  # Première ligne (def)
                        indent_level = len(line) - len(line.lstrip())
                        method_content.append(line)
                    else:
                        # Continuer jusqu'à la prochaine méthode au même niveau
                        if line.strip() and not line.startswith(' ' * (indent_level + 1)):
                            if line.strip().startswith('def '):
                                break
                        method_content.append(line)
                
                method_text = '\n'.join(method_content)
                
                # Trouver les appels de méthodes dans cette méthode
                called_methods = re.findall(r'self\.(_[a-zA-Z_][a-zA-Z0-9_]*)\(', method_text)
                
                for called_method in called_methods:
                    if called_method not in all_rollout1_methods:
                        all_rollout1_methods.add(called_method)
                        methods_to_check.append(called_method)
        
        return list(all_rollout1_methods)
        
    except Exception as e:
        print(f"Erreur analyse récursive: {e}")
        return rollout1_methods

def verify_method_is_analytical(method_name: str, content: str) -> Dict:
    """Vérifie qu'une méthode est bien analytique (pas de génération)"""
    
    # Extraire le contenu de la méthode
    method_pattern = f'def {method_name}\\('
    method_match = re.search(method_pattern, content)
    
    if not method_match:
        return {'is_analytical': False, 'reason': 'Méthode non trouvée'}
    
    start_pos = method_match.start()
    lines = content[start_pos:].split('\n')
    
    method_content = []
    indent_level = None
    
    for i, line in enumerate(lines):
        if i == 0:  # Première ligne (def)
            indent_level = len(line) - len(line.lstrip())
            method_content.append(line)
        else:
            if line.strip() and not line.startswith(' ' * (indent_level + 1)):
                if line.strip().startswith('def '):
                    break
            method_content.append(line)
    
    method_text = '\n'.join(method_content).lower()
    
    # Mots-clés de génération (excluent la méthode)
    generation_keywords = [
        'generate_sequence', 'create_sequence', 'build_sequence',
        'predict_next', 'forecast', 'generate_prediction'
    ]
    
    # Mots-clés analytiques (confirment la méthode)
    analytical_keywords = [
        'analyze', 'calculate', 'correlate', 'measure', 'assess',
        'evaluate', 'compute', 'determine', 'extract', 'identify'
    ]
    
    # Vérifier exclusions
    for keyword in generation_keywords:
        if keyword in method_text:
            return {'is_analytical': False, 'reason': f'Contient génération: {keyword}'}
    
    # Vérifier confirmations
    analytical_score = 0
    found_keywords = []
    for keyword in analytical_keywords:
        if keyword in method_text:
            analytical_score += method_text.count(keyword)
            found_keywords.append(keyword)
    
    # Vérifier retour de données analytiques
    returns_analysis = any(pattern in method_text for pattern in [
        'return {', 'analysis', 'correlation', 'bias', 'impact', 'strength'
    ])
    
    is_analytical = analytical_score >= 2 and returns_analysis
    
    return {
        'is_analytical': is_analytical,
        'analytical_score': analytical_score,
        'found_keywords': found_keywords,
        'returns_analysis': returns_analysis,
        'method_length': len(method_content)
    }

def identify_rollout1_cluster0_methods():
    """Identifie précisément les méthodes du Rollout 1 cluster 0"""
    
    print("🔍 IDENTIFICATION PRÉCISE ROLLOUT 1 CLUSTER 0")
    print("=" * 50)
    
    # 1. Méthodes directement appelées par _rollout_analyzer
    direct_methods, rollout_analyzer_content = extract_method_calls_from_rollout_analyzer()
    print(f"Méthodes directement appelées par _rollout_analyzer: {len(direct_methods)}")
    
    # 2. Méthodes appelées récursivement
    all_rollout1_methods = find_methods_called_by_rollout1_methods(direct_methods)
    print(f"Total méthodes Rollout 1 (récursif): {len(all_rollout1_methods)}")
    
    # 3. Vérification que chaque méthode est bien analytique
    try:
        with open('centralisation_methodes/class.txt', 'r', encoding='utf-8') as f:
            content = f.read()
    except:
        content = ""
    
    verified_methods = []
    excluded_methods = []
    
    for method in all_rollout1_methods:
        verification = verify_method_is_analytical(method, content)
        
        if verification['is_analytical']:
            verified_methods.append({
                'name': method,
                'verification': verification
            })
        else:
            excluded_methods.append({
                'name': method,
                'reason': verification['reason']
            })
    
    print(f"Méthodes analytiques vérifiées: {len(verified_methods)}")
    print(f"Méthodes exclues: {len(excluded_methods)}")
    
    return {
        'direct_methods': direct_methods,
        'all_methods': all_rollout1_methods,
        'verified_analytical_methods': verified_methods,
        'excluded_methods': excluded_methods,
        'rollout_analyzer_content': rollout_analyzer_content
    }

def generate_precise_report(results: Dict):
    """Génère un rapport précis des méthodes Rollout 1 cluster 0"""
    
    verified_methods = results['verified_analytical_methods']
    excluded_methods = results['excluded_methods']
    direct_methods = results['direct_methods']
    
    report = f"""MÉTHODES ROLLOUT 1 CLUSTER 0 (PAR DÉFAUT) - IDENTIFICATION PRÉCISE
====================================================================

MÉTHODOLOGIE :
=============
1. Analyse de _rollout_analyzer (Rollout 1 cluster 0/1 standard)
2. Extraction récursive des méthodes appelées
3. Vérification analytique (pas de génération)
4. Confirmation par analyse de contenu

RÉSULTATS :
==========
Méthodes directement appelées : {len(direct_methods)}
Total méthodes trouvées : {len(results['all_methods'])}
Méthodes analytiques vérifiées : {len(verified_methods)}
Méthodes exclues : {len(excluded_methods)}

MÉTHODES DIRECTEMENT APPELÉES PAR _ROLLOUT_ANALYZER :
===================================================
"""
    
    for i, method in enumerate(direct_methods, 1):
        report += f"{i:2d}. {method}\n"
    
    report += f"\nMÉTHODES ROLLOUT 1 CLUSTER 0 VÉRIFIÉES ({len(verified_methods)}) :\n"
    report += "=" * 55 + "\n\n"
    
    # Trier par score analytique décroissant
    verified_methods.sort(key=lambda x: x['verification']['analytical_score'], reverse=True)
    
    for i, method_data in enumerate(verified_methods, 1):
        method = method_data['name']
        verification = method_data['verification']
        
        report += f"{i:2d}. {method}\n"
        report += f"    Score analytique : {verification['analytical_score']}\n"
        report += f"    Mots-clés trouvés : {', '.join(verification['found_keywords'])}\n"
        report += f"    Retourne analyse : {'OUI' if verification['returns_analysis'] else 'NON'}\n"
        report += f"    Lignes de code : {verification['method_length']}\n\n"
    
    if excluded_methods:
        report += f"\nMÉTHODES EXCLUES ({len(excluded_methods)}) :\n"
        report += "=" * 30 + "\n\n"
        
        for method_data in excluded_methods:
            report += f"❌ {method_data['name']} - {method_data['reason']}\n"
    
    report += f"""
VALIDATION :
===========
✅ Ces méthodes appartiennent CERTAINEMENT au Rollout 1 cluster 0
✅ Elles sont appelées directement ou indirectement par _rollout_analyzer
✅ Elles sont purement analytiques (pas de génération)
✅ Elles analysent les indices 1,2,3 et leur impact sur 4,5

FONCTIONNALITÉS ROLLOUT 1 CLUSTER 0 :
====================================
- Analyse des biais IMPAIR consécutifs (priorité 1)
- Analyse des alternances SYNC/DESYNC (priorité 2)  
- Analyse des biais COMBINED (priorité 3)
- Corrélations croisées indices → P/B/T et S/O
- Mesure des impacts structurels
- Évaluation des déviations statistiques

CONCLUSION :
============
Ces {len(verified_methods)} méthodes constituent le cœur analytique du 
Rollout 1 pour le cluster par défaut (C0-C1 standard).

Elles représentent les fonctionnalités manquantes à intégrer dans 
les méthodes universelles pour compléter l'architecture.
"""
    
    return report

def main():
    """Fonction principale"""
    results = identify_rollout1_cluster0_methods()
    
    # Génération du rapport
    report = generate_precise_report(results)
    
    # Sauvegarde
    output_file = 'centralisation_methodes/methodes_rollout1_cluster0_precises.txt'
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"\n📋 Rapport précis sauvé dans {output_file}")
    print(f"🎯 {len(results['verified_analytical_methods'])} méthodes Rollout 1 cluster 0 identifiées avec certitude")

if __name__ == "__main__":
    main()
