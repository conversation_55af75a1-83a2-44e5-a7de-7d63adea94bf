#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour examiner le fichier spécifique indiqué par l'utilisateur
"""

import json
from pathlib import Path

def examiner_fichier_specifique():
    """Examine le fichier spécifique"""
    
    file_path = "C:/Users/<USER>/AppData/Roaming/Code/User/workspaceStorage/591be8b7f6bd4169258c0affc2eaa1fc/Augment.vscode-augment/augment-user-assets/checkpoint-documents/d6413184-fd14-466b-8484-4d5e99f66884/document-c__Users_Administrateur_Desktop_Travail_Projet6_script_conversation_chronologique.py-1749026244639-fceea3fb-0877-4f8f-b5ba-ade44173e3e8.json"
    
    print("🔍 EXAMEN DU FICHIER SPÉCIFIQUE")
    print("=" * 60)
    print(f"📁 Fichier: {file_path}")
    
    try:
        # Lire le fichier
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"📏 Taille du fichier: {len(content)} caractères")
        
        # Copier vers un fichier simple
        simple_path = "C:/temp_conversation/conversation_specifique.json"
        Path("C:/temp_conversation").mkdir(exist_ok=True)
        
        with open(simple_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ Fichier copié vers: {simple_path}")
        
        # Parser le JSON
        try:
            data = json.loads(content)
            print("✅ JSON parsé avec succès")
            
            # Examiner la structure
            print(f"\n🔧 STRUCTURE DU JSON:")
            if isinstance(data, dict):
                for key, value in data.items():
                    print(f"🔑 {key}: {type(value).__name__} ({len(str(value))} caractères)")
            
            # Examiner le contenu original et modifié
            original_code = data.get('originalCode', '')
            modified_code = data.get('modifiedCode', '')
            
            print(f"\n📝 ANALYSE DU CONTENU:")
            print(f"📏 originalCode: {len(original_code)} caractères")
            print(f"📏 modifiedCode: {len(modified_code)} caractères")
            
            # Chercher des indicateurs de conversation
            combined_content = original_code + "\n" + modified_code
            
            conversation_patterns = [
                "Human:",
                "Assistant:",
                "User:",
                "<<HUMAN_CONVERSATION_START>>",
                "tu avais raison",
                "finalement",
                "🎯",
                "✅",
                "❌",
                "**",
                "##"
            ]
            
            found_patterns = []
            for pattern in conversation_patterns:
                count = combined_content.count(pattern)
                if count > 0:
                    found_patterns.append(f"{pattern} ({count}x)")
            
            print(f"\n🔑 Patterns de conversation trouvés:")
            for pattern in found_patterns:
                print(f"   {pattern}")
            
            # Afficher les premières lignes pour voir le format
            lines = combined_content.split('\n')
            print(f"\n📋 APERÇU DES PREMIÈRES 30 LIGNES:")
            print("-" * 80)
            for i, line in enumerate(lines[:30], 1):
                print(f"{i:2d}: {line}")
            print("-" * 80)
            
            # Chercher spécifiquement "<<HUMAN_CONVERSATION_START>>"
            if "<<HUMAN_CONVERSATION_START>>" in combined_content:
                print(f"\n🎉 DÉBUT DE CONVERSATION TROUVÉ!")
                start_pos = combined_content.find("<<HUMAN_CONVERSATION_START>>")
                conversation_part = combined_content[start_pos:]
                
                print(f"📝 CONTENU DE LA CONVERSATION (premiers 1000 caractères):")
                print("=" * 80)
                print(conversation_part[:1000])
                print("=" * 80)
                
                return data, simple_path, conversation_part
            
            # Si pas de marqueur spécial, chercher "tu avais raison finalement"
            if "tu avais raison finalement" in combined_content.lower():
                print(f"\n🎯 'TU AVAIS RAISON FINALEMENT' TROUVÉ!")
                start_pos = combined_content.lower().find("tu avais raison finalement")
                context_start = max(0, start_pos - 500)
                context_end = min(len(combined_content), start_pos + 1000)
                context = combined_content[context_start:context_end]
                
                print(f"📝 CONTEXTE AUTOUR DE LA PHRASE:")
                print("=" * 80)
                print(context)
                print("=" * 80)
                
                return data, simple_path, context
            
            return data, simple_path, combined_content
            
        except json.JSONDecodeError as e:
            print(f"❌ Erreur parsing JSON: {e}")
            return None, simple_path, None
            
    except Exception as e:
        print(f"❌ Erreur lecture fichier: {e}")
        return None, None, None

if __name__ == "__main__":
    print("🚀 EXAMEN DU FICHIER SPÉCIFIQUE")
    print("=" * 60)
    
    data, file_path, conversation_content = examiner_fichier_specifique()
    
    if data and conversation_content:
        print(f"\n✅ EXAMEN TERMINÉ!")
        print(f"📄 Fichier copié vers: {file_path}")
        print(f"🔍 Contenu de conversation détecté!")
    else:
        print(f"\n❌ ERREUR lors de l'examen")
