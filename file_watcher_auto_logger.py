#!/usr/bin/env python3
"""
FILE WATCHER AUTOMATIQUE POUR LOGGING CONVERSATION AUGMENT
==========================================================

Ce script utilise le système de surveillance de fichiers natif de Windows
pour détecter instantanément les modifications du fichier state.vscdb
et écrire automatiquement dans un fichier parallèle.

Solution la plus élégante et non-intrusive.
"""

import sqlite3
import json
import datetime
import time
import threading
from pathlib import Path
import os

# Essayer d'importer watchdog pour surveillance de fichiers
try:
    from watchdog.observers import Observer
    from watchdog.events import FileSystemEventHandler
    WATCHDOG_AVAILABLE = True
except ImportError:
    WATCHDOG_AVAILABLE = False
    print("⚠️ Module watchdog non disponible. Installation: pip install watchdog")

class ConversationFileWatcher(FileSystemEventHandler):
    def __init__(self, logger_instance):
        self.logger = logger_instance
        super().__init__()
    
    def on_modified(self, event):
        if event.is_directory:
            return
        
        if event.src_path.endswith('state.vscdb'):
            self.logger.process_file_change()

class AutoConversationLogger:
    def __init__(self):
        self.state_file = Path("C:/Users/<USER>/AppData/Roaming/Code/User/workspaceStorage/a35ba43ef26792e61fe787a234121806/state.vscdb")
        self.log_file = Path("C:/Users/<USER>/Desktop/Travail/Projet7/conversation_live_auto.txt")
        self.json_log = Path("C:/Users/<USER>/Desktop/Travail/Projet7/conversation_live_auto.json")
        
        self.last_message_count = 0
        self.last_conversation_id = None
        self.observer = None
        self.running = False
        
        # Initialiser les fichiers
        self.init_log_files()
        
        # Obtenir l'état initial
        self.get_initial_state()
    
    def init_log_files(self):
        """Initialise les fichiers de log"""
        # Fichier texte
        with open(self.log_file, 'w', encoding='utf-8') as f:
            f.write("CONVERSATION AUGMENT - LOG AUTOMATIQUE EN TEMPS RÉEL\n")
            f.write(f"Démarré: {datetime.datetime.now().isoformat()}\n")
            f.write(f"Source: {self.state_file}\n")
            f.write("=" * 80 + "\n\n")
        
        # Fichier JSON
        initial_data = {
            "started_at": datetime.datetime.now().isoformat(),
            "source_file": str(self.state_file),
            "conversation_id": None,
            "messages": [],
            "total_messages": 0
        }
        with open(self.json_log, 'w', encoding='utf-8') as f:
            json.dump(initial_data, f, indent=2, ensure_ascii=False)
    
    def get_initial_state(self):
        """Obtient l'état initial de la conversation"""
        try:
            conversation_data = self.extract_conversation_data()
            if conversation_data:
                conversations = conversation_data.get('conversations', {})
                current_conv_id = conversation_data.get('currentConversationId')
                
                if current_conv_id and current_conv_id in conversations:
                    chat_history = conversations[current_conv_id].get('chatHistory', [])
                    self.last_message_count = len(chat_history)
                    self.last_conversation_id = current_conv_id
                    
                    print(f"📊 État initial: {self.last_message_count} messages")
        except Exception as e:
            print(f"⚠️ Erreur état initial: {e}")
    
    def extract_conversation_data(self):
        """Extrait les données de conversation"""
        try:
            if not self.state_file.exists():
                return None
            
            conn = sqlite3.connect(str(self.state_file))
            cursor = conn.cursor()
            
            cursor.execute("SELECT value FROM ItemTable WHERE key = 'memento/webviewView.augment-chat';")
            result = cursor.fetchone()
            
            if not result:
                conn.close()
                return None
            
            value = result[0]
            if isinstance(value, bytes):
                value_str = value.decode('utf-8')
            else:
                value_str = str(value)
            
            main_data = json.loads(value_str)
            webview_data = json.loads(main_data['webviewState'])
            
            conn.close()
            return webview_data
            
        except Exception as e:
            return None
    
    def format_message(self, message_data, index):
        """Formate un message pour le log"""
        timestamp = datetime.datetime.now().isoformat()
        
        # Extraire contenu utilisateur
        user_content = message_data.get('request_message', '')
        
        # Extraire réponse assistant
        assistant_content = ""
        tools_used = []
        
        if 'structured_output_nodes' in message_data:
            for node in message_data['structured_output_nodes']:
                if node.get('type') == 0 and node.get('content'):
                    assistant_content = node['content']
                elif node.get('type') == 5 and node.get('tool_use'):
                    tool_info = node['tool_use']
                    tools_used.append(tool_info.get('tool_name', 'unknown'))
        
        return {
            'index': index,
            'timestamp': timestamp,
            'user_message': user_content,
            'assistant_response': assistant_content,
            'tools_used': tools_used,
            'request_id': message_data.get('request_id', ''),
            'message_timestamp': message_data.get('timestamp', '')
        }
    
    def append_new_messages(self, new_messages):
        """Ajoute les nouveaux messages aux logs"""
        if not new_messages:
            return
        
        # Log texte - écriture immédiate
        with open(self.log_file, 'a', encoding='utf-8') as f:
            for msg in new_messages:
                f.write(f"[{msg['timestamp']}] MESSAGE #{msg['index']}\n")
                f.write("-" * 60 + "\n")
                
                if msg['user_message']:
                    f.write(f"👤 UTILISATEUR:\n{msg['user_message']}\n\n")
                
                if msg['assistant_response']:
                    f.write(f"🤖 ASSISTANT:\n{msg['assistant_response']}\n\n")
                
                if msg['tools_used']:
                    f.write(f"🔧 OUTILS: {', '.join(msg['tools_used'])}\n\n")
                
                f.write("=" * 80 + "\n\n")
            
            # Forcer l'écriture immédiate
            f.flush()
            os.fsync(f.fileno())
        
        # Log JSON - mise à jour
        try:
            with open(self.json_log, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            data['messages'].extend(new_messages)
            data['last_update'] = datetime.datetime.now().isoformat()
            data['total_messages'] = len(data['messages'])
            data['conversation_id'] = self.last_conversation_id
            
            with open(self.json_log, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            pass
        
        print(f"✅ {len(new_messages)} nouveaux messages loggés automatiquement")
    
    def process_file_change(self):
        """Traite un changement de fichier détecté"""
        try:
            # Petite pause pour éviter les lectures partielles
            time.sleep(0.1)
            
            conversation_data = self.extract_conversation_data()
            
            if conversation_data:
                conversations = conversation_data.get('conversations', {})
                current_conv_id = conversation_data.get('currentConversationId')
                
                if current_conv_id and current_conv_id in conversations:
                    chat_history = conversations[current_conv_id].get('chatHistory', [])
                    
                    # Vérifier s'il y a de nouveaux messages
                    if len(chat_history) > self.last_message_count:
                        new_messages_raw = chat_history[self.last_message_count:]
                        new_messages = []
                        
                        for i, msg_data in enumerate(new_messages_raw):
                            formatted_msg = self.format_message(
                                msg_data, 
                                self.last_message_count + i + 1
                            )
                            new_messages.append(formatted_msg)
                        
                        # Écrire immédiatement dans les logs
                        self.append_new_messages(new_messages)
                        
                        self.last_message_count = len(chat_history)
                        self.last_conversation_id = current_conv_id
            
        except Exception as e:
            print(f"⚠️ Erreur traitement changement: {e}")
    
    def start_watching(self):
        """Démarre la surveillance automatique"""
        if not WATCHDOG_AVAILABLE:
            print("❌ Module watchdog requis: pip install watchdog")
            return False
        
        if self.running:
            return True
        
        try:
            # Créer l'observateur
            self.observer = Observer()
            event_handler = ConversationFileWatcher(self)
            
            # Surveiller le répertoire contenant state.vscdb
            watch_dir = str(self.state_file.parent)
            self.observer.schedule(event_handler, watch_dir, recursive=False)
            
            # Démarrer
            self.observer.start()
            self.running = True
            
            print(f"🚀 Surveillance automatique démarrée")
            print(f"📁 Répertoire surveillé: {watch_dir}")
            print(f"📝 Log automatique: {self.log_file}")
            print(f"📊 Log JSON: {self.json_log}")
            print(f"💬 Tapez dans Augment pour voir le logging en action...")
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur démarrage surveillance: {e}")
            return False
    
    def stop_watching(self):
        """Arrête la surveillance"""
        if self.observer and self.running:
            self.observer.stop()
            self.observer.join()
            self.running = False
            print(f"🛑 Surveillance arrêtée")

def start_auto_logging():
    """Démarre le logging automatique"""
    logger = AutoConversationLogger()
    
    if logger.start_watching():
        return logger
    else:
        return None

if __name__ == "__main__":
    print("🎯 SYSTÈME DE LOGGING AUTOMATIQUE CONVERSATION AUGMENT")
    print("=" * 60)
    
    logger = start_auto_logging()
    
    if logger:
        try:
            print("⏹️  Appuyez sur Ctrl+C pour arrêter")
            
            while True:
                time.sleep(1)
                
        except KeyboardInterrupt:
            print("\n🛑 Arrêt du logging automatique")
            logger.stop_watching()
    else:
        print("❌ Impossible de démarrer le système")
