#!/usr/bin/env python3
"""
MODIFICATION FORCÉE DE L'EXTENSION AUGMENT
==========================================

Script alternatif pour modifier l'extension même si VSCode est en cours d'exécution.
Utilise une approche de copie/modification/remplacement.
"""

import shutil
import tempfile
import os
import time
from pathlib import Path
import datetime

class ForceExtensionModifier:
    def __init__(self):
        self.extension_js = Path("C:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.470.1/out/extension.js")
        self.backup_dir = Path("C:/Users/<USER>/Desktop/Travail/Projet7/BACKUPS_EXTENSION")
        self.timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Code à injecter (version simplifiée)
        self.logging_code = '''
// ===== AUGMENT AUTO LOGGING SYSTEM =====
const fs = require('fs');
const path = require('path');

class AugmentAutoLogger {
    constructor() {
        this.initializeLogging();
    }
    
    initializeLogging() {
        try {
            // Obtenir le workspace
            const workspaceRoot = vscode?.workspace?.workspaceFolders?.[0]?.uri?.fsPath || process.cwd();
            
            // Créer les fichiers de log
            this.logFile = path.join(workspaceRoot, 'augment_conversation_auto.txt');
            this.jsonLog = path.join(workspaceRoot, 'augment_conversation_auto.json');
            
            // Initialiser les fichiers
            const timestamp = new Date().toISOString();
            const header = `AUGMENT CONVERSATION - LOG AUTOMATIQUE\\nDémarré: ${timestamp}\\nWorkspace: ${workspaceRoot}\\n${'='.repeat(80)}\\n\\n`;
            
            if (!fs.existsSync(this.logFile)) {
                fs.writeFileSync(this.logFile, header, 'utf8');
            }
            
            const jsonData = {
                started_at: timestamp,
                workspace_root: workspaceRoot,
                messages: []
            };
            
            if (!fs.existsSync(this.jsonLog)) {
                fs.writeFileSync(this.jsonLog, JSON.stringify(jsonData, null, 2), 'utf8');
            }
            
            console.log('[AUGMENT AUTO LOGGER] Système activé pour:', workspaceRoot);
            
        } catch (error) {
            console.error('[AUGMENT AUTO LOGGER] Erreur:', error);
        }
    }
}

// Initialiser le logger automatiquement
setTimeout(() => {
    try {
        if (typeof vscode !== 'undefined') {
            new AugmentAutoLogger();
        }
    } catch (e) {
        console.log('[AUGMENT AUTO LOGGER] Initialisation différée');
    }
}, 3000);

// ===== FIN AUTO LOGGING =====

'''
    
    def force_modify_extension(self):
        """Force la modification de l'extension"""
        print(f"🔧 MODIFICATION FORCÉE DE L'EXTENSION AUGMENT")
        print(f"=" * 60)
        
        try:
            # 1. Créer sauvegarde
            self.backup_dir.mkdir(parents=True, exist_ok=True)
            backup_file = self.backup_dir / f"extension_js_force_backup_{self.timestamp}.js"
            shutil.copy2(self.extension_js, backup_file)
            print(f"✅ Sauvegarde créée: {backup_file}")
            
            # 2. Lire le fichier original
            with open(self.extension_js, 'r', encoding='utf-8') as f:
                original_content = f.read()
            
            print(f"📊 Fichier original: {len(original_content)} caractères")
            
            # 3. Créer le contenu modifié
            modified_content = self.logging_code + original_content
            
            # 4. Créer un fichier temporaire
            with tempfile.NamedTemporaryFile(mode='w', encoding='utf-8', delete=False, suffix='.js') as temp_file:
                temp_file.write(modified_content)
                temp_path = temp_file.name
            
            print(f"📝 Fichier temporaire créé: {temp_path}")
            
            # 5. Essayer de remplacer le fichier original
            max_attempts = 5
            for attempt in range(max_attempts):
                try:
                    # Essayer de supprimer l'original
                    if self.extension_js.exists():
                        os.remove(self.extension_js)
                    
                    # Copier le fichier modifié
                    shutil.move(temp_path, self.extension_js)
                    
                    print(f"✅ Extension modifiée avec succès!")
                    print(f"📊 Nouvelle taille: {len(modified_content)} caractères")
                    print(f"📊 Code ajouté: {len(self.logging_code)} caractères")
                    
                    # Nettoyer
                    if os.path.exists(temp_path):
                        os.remove(temp_path)
                    
                    return True
                    
                except PermissionError as e:
                    print(f"⚠️ Tentative {attempt + 1}/{max_attempts} - Permission refusée")
                    if attempt < max_attempts - 1:
                        print(f"   Attente de 2 secondes...")
                        time.sleep(2)
                    else:
                        print(f"❌ Impossible de modifier après {max_attempts} tentatives")
                        # Nettoyer le fichier temporaire
                        if os.path.exists(temp_path):
                            os.remove(temp_path)
                        return False
                
                except Exception as e:
                    print(f"❌ Erreur inattendue: {e}")
                    if os.path.exists(temp_path):
                        os.remove(temp_path)
                    return False
            
        except Exception as e:
            print(f"❌ Erreur générale: {e}")
            return False
    
    def create_alternative_solution(self):
        """Crée une solution alternative si la modification directe échoue"""
        print(f"\n🔄 CRÉATION SOLUTION ALTERNATIVE")
        print(f"=" * 50)
        
        # Créer un script d'injection qui s'exécute au démarrage de VSCode
        injection_script = Path("C:/Users/<USER>/Desktop/Travail/Projet7/augment_auto_injector.js")
        
        with open(injection_script, 'w', encoding='utf-8') as f:
            f.write(self.logging_code)
        
        print(f"✅ Script d'injection créé: {injection_script}")
        
        # Créer un script PowerShell pour l'injection
        ps_script = Path("C:/Users/<USER>/Desktop/Travail/Projet7/inject_augment_logging.ps1")
        
        ps_content = f'''# Script PowerShell pour injecter le logging Augment
$extensionFile = "C:\\Users\\<USER>\\.vscode\\extensions\\augment.vscode-augment-0.470.1\\out\\extension.js"
$injectionCode = Get-Content "{injection_script}" -Raw

# Lire le fichier original
$originalContent = Get-Content $extensionFile -Raw

# Créer le contenu modifié
$modifiedContent = $injectionCode + $originalContent

# Sauvegarder l'original
$backupFile = "C:\\Users\\<USER>\\Desktop\\Travail\\Projet7\\BACKUPS_EXTENSION\\extension_js_ps_backup_{self.timestamp}.js"
Copy-Item $extensionFile $backupFile

# Écrire le fichier modifié
$modifiedContent | Out-File $extensionFile -Encoding UTF8

Write-Host "✅ Extension modifiée via PowerShell"
'''
        
        with open(ps_script, 'w', encoding='utf-8') as f:
            f.write(ps_content)
        
        print(f"✅ Script PowerShell créé: {ps_script}")
        print(f"\n💡 Pour activer la solution alternative:")
        print(f"   1. Fermez VSCode complètement")
        print(f"   2. Exécutez: PowerShell -ExecutionPolicy Bypass -File {ps_script}")
        print(f"   3. Redémarrez VSCode")
        
        return True

def main():
    """Fonction principale"""
    modifier = ForceExtensionModifier()
    
    # Essayer la modification directe
    if modifier.force_modify_extension():
        print(f"\n🎉 MODIFICATION RÉUSSIE!")
        print(f"✅ L'extension Augment intègre maintenant le logging automatique")
        print(f"✅ Tous les nouveaux projets auront automatiquement le logging")
        print(f"\n💡 REDÉMARREZ VSCODE pour activer les modifications")
    else:
        print(f"\n⚠️ MODIFICATION DIRECTE ÉCHOUÉE")
        print(f"Création d'une solution alternative...")
        modifier.create_alternative_solution()

if __name__ == "__main__":
    main()
