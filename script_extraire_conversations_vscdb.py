#!/usr/bin/env python3
"""
Script pour extraire les conversations Augment du fichier state.vscdb
"""

import sqlite3
import json
from pathlib import Path
import re

def extraire_conversations():
    """Extrait les conversations Augment de la base de données"""
    
    state_file = Path("C:/Users/<USER>/AppData/Roaming/Code/User/workspaceStorage/a35ba43ef26792e61fe787a234121806/state.vscdb")
    
    print("=== EXTRACTION DES CONVERSATIONS AUGMENT ===")
    
    try:
        conn = sqlite3.connect(str(state_file))
        cursor = conn.cursor()
        
        # Récupérer toutes les clés pour identifier celles liées à Augment
        cursor.execute("SELECT key, value FROM ItemTable;")
        all_data = cursor.fetchall()
        
        print(f"Total d'entrées dans la base: {len(all_data)}")
        
        # Filtrer les clés potentiellement liées à Augment
        augment_keys = []
        conversation_keys = []
        
        for key, value in all_data:
            key_lower = key.lower()
            if any(keyword in key_lower for keyword in ['augment', 'chat', 'conversation', 'message', 'history']):
                if 'augment' in key_lower:
                    augment_keys.append((key, value))
                else:
                    conversation_keys.append((key, value))
        
        print(f"\nClés contenant 'augment': {len(augment_keys)}")
        print(f"Clés liées aux conversations: {len(conversation_keys)}")
        
        # Afficher les clés Augment
        print("\n--- CLÉS AUGMENT ---")
        for key, value in augment_keys:
            print(f"Clé: {key}")
            try:
                if isinstance(value, bytes):
                    value_str = value.decode('utf-8')
                else:
                    value_str = str(value)
                
                # Essayer de parser comme JSON
                try:
                    json_data = json.loads(value_str)
                    print(f"  Type: JSON ({len(json_data)} éléments)" if isinstance(json_data, dict) else f"  Type: JSON")
                    print(f"  Aperçu: {str(json_data)[:200]}...")
                except json.JSONDecodeError:
                    print(f"  Type: Texte ({len(value_str)} caractères)")
                    print(f"  Aperçu: {value_str[:200]}...")
                    
            except Exception as e:
                print(f"  Erreur lecture: {e}")
            print()
        
        # Afficher les clés de conversation
        print("\n--- CLÉS CONVERSATION ---")
        for key, value in conversation_keys[:5]:  # Limiter à 5 pour éviter trop d'output
            print(f"Clé: {key}")
            try:
                if isinstance(value, bytes):
                    value_str = value.decode('utf-8')
                else:
                    value_str = str(value)
                print(f"  Aperçu: {value_str[:200]}...")
            except Exception as e:
                print(f"  Erreur lecture: {e}")
            print()
        
        # Rechercher des patterns spécifiques dans toutes les valeurs
        print("\n--- RECHERCHE DE PATTERNS CONVERSATION ---")
        patterns = [
            r'"user".*?"content"',
            r'"assistant".*?"content"',
            r'"messages"',
            r'"conversation"',
            r'"history"'
        ]
        
        for key, value in all_data:
            try:
                if isinstance(value, bytes):
                    value_str = value.decode('utf-8')
                else:
                    value_str = str(value)
                
                for pattern in patterns:
                    if re.search(pattern, value_str, re.IGNORECASE):
                        print(f"Pattern '{pattern}' trouvé dans clé: {key}")
                        print(f"  Extrait: {value_str[:300]}...")
                        break
                        
            except Exception as e:
                continue
        
        conn.close()
        
    except Exception as e:
        print(f"Erreur: {e}")

def surveiller_modifications():
    """Surveille les modifications en temps réel"""
    
    state_file = Path("C:/Users/<USER>/AppData/Roaming/Code/User/workspaceStorage/a35ba43ef26792e61fe787a234121806/state.vscdb")
    
    print(f"\n=== SURVEILLANCE DES MODIFICATIONS ===")
    
    if state_file.exists():
        initial_mtime = state_file.stat().st_mtime
        initial_size = state_file.stat().st_size
        
        print(f"Fichier initial - Taille: {initial_size}, Modif: {initial_mtime}")
        print("Tapez quelque chose dans Augment pour voir les changements...")
        
        return initial_mtime, initial_size
    
    return None, None

if __name__ == "__main__":
    extraire_conversations()
    surveiller_modifications()
