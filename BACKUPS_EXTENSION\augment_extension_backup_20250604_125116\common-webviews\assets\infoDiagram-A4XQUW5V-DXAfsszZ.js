import{_ as o,l as p,I as a,k as e,K as s}from"./AugmentMessage-DIzdCIMv.js";import{p as n}from"./gitGraph-YCYPL57B-Di_2i6IF.js";import"./SpinnerAugment-BJ4-L7QR.js";import"./github-C1PQK5DH.js";import"./pen-to-square-Bm4lF9Yl.js";import"./augment-logo-D_UKSkj8.js";import"./TextTooltipAugment-Bkzart3o.js";import"./BaseButton-C6Dhmpxa.js";import"./IconButtonAugment-Certjadv.js";import"./Content-Czt02SJi.js";import"./globals-D0QH3NT1.js";import"./open-in-new-window-DMlqLwqy.js";import"./types-LfaCSdmF.js";import"./chat-types-NgqNgjwU.js";import"./test_service_pb-B6vKXZrG.js";import"./file-paths-BcSg4gks.js";import"./types-a569v5Ol.js";import"./folder-BJI1Q8_7.js";import"./folder-opened-DzrGzNBt.js";import"./types-BSMhNRWH.js";import"./index-C-g0ZorP.js";import"./CardAugment-BxTO-shY.js";import"./TextAreaAugment-Cj5jK817.js";import"./diff-utils-y96qaWKK.js";import"./toggleHighContrast-CwIv4U26.js";import"./preload-helper-Dv6uf1Os.js";import"./index-MyvMQzjq.js";import"./keypress-DD1aQVr0.js";import"./await_block-CvQ_3xaW.js";import"./ButtonAugment-HnJOGilM.js";import"./expand--BB_Hn_b.js";import"./mcp-logo-B9nTLE-q.js";import"./ellipsis-BWy9xWah.js";import"./IconFilePath-C-3qORpY.js";import"./LanguageIcon-BH9BM7T7.js";import"./next-edit-types-904A5ehg.js";import"./magnifying-glass-LWYs47rB.js";import"./MaterialIcon-DIlB9c-0.js";import"./Filespan-BC4kxbfx.js";import"./chevron-down-B88L5wkj.js";import"./lodash-ChYFUhWY.js";import"./terminal-BQIj5vJ0.js";import"./_baseUniq-BZgk2Caz.js";import"./_basePickBy-DiH5splf.js";import"./clone-BkzPDDdR.js";var d={version:s},or={parser:{parse:o(async r=>{const t=await n("info",r);p.debug(t)},"parse")},db:{getVersion:o(()=>d.version,"getVersion")},renderer:{draw:o((r,t,m)=>{p.debug(`rendering info diagram
`+r);const i=a(t);e(i,100,400,!0),i.append("g").append("text").attr("x",100).attr("y",40).attr("class","version").attr("font-size",32).style("text-anchor","middle").text(`v${m}`)},"draw")}};export{or as diagram};
