#!/usr/bin/env python3
"""
MODIFICATION DE L'EXTENSION AUGMENT POUR LOGGING AUTOMATIQUE
============================================================

Ce script modifie l'extension Augment VSCode pour intégrer automatiquement
le système de logging de conversation dans TOUS les nouveaux projets.

Approche: Injection de code dans extension.js pour créer automatiquement
le système de logging à chaque démarrage de conversation.
"""

import shutil
import json
from pathlib import Path
import datetime
import re

class AugmentExtensionModifier:
    def __init__(self):
        self.extension_path = Path("C:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.470.1")
        self.extension_js = self.extension_path / "out" / "extension.js"
        self.package_json = self.extension_path / "package.json"
        
        # Répertoire de sauvegarde
        self.backup_dir = Path("C:/Users/<USER>/Desktop/Travail/Projet7/BACKUPS_EXTENSION")
        self.timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Code à injecter pour le logging automatique
        self.logging_code = self.generate_logging_code()
    
    def generate_logging_code(self):
        """Génère le code JavaScript à injecter dans l'extension"""
        return '''
// ===== AUGMENT AUTO LOGGING SYSTEM - INJECTED CODE =====
// Système de logging automatique des conversations Augment
// Injecté automatiquement pour tous les nouveaux projets

const fs = require('fs');
const path = require('path');
const sqlite3 = require('sqlite3');

class AugmentAutoLogger {
    constructor() {
        this.isActive = false;
        this.logFile = null;
        this.jsonLog = null;
        this.lastMessageCount = 0;
        this.workspaceRoot = null;
        this.stateDbPath = null;
        
        this.initializeAutoLogging();
    }
    
    initializeAutoLogging() {
        try {
            // Obtenir le workspace root
            if (vscode && vscode.workspace && vscode.workspace.workspaceFolders) {
                this.workspaceRoot = vscode.workspace.workspaceFolders[0].uri.fsPath;
            } else {
                this.workspaceRoot = process.cwd();
            }
            
            // Créer les fichiers de log dans le workspace
            this.logFile = path.join(this.workspaceRoot, 'augment_conversation_auto.txt');
            this.jsonLog = path.join(this.workspaceRoot, 'augment_conversation_auto.json');
            
            // Identifier le fichier state.vscdb
            this.findStateDbPath();
            
            // Initialiser les fichiers de log
            this.initializeLogFiles();
            
            // Démarrer la surveillance
            this.startMonitoring();
            
            console.log('[AUGMENT AUTO LOGGER] Système de logging automatique activé');
            console.log('[AUGMENT AUTO LOGGER] Fichiers de log:', this.logFile, this.jsonLog);
            
        } catch (error) {
            console.error('[AUGMENT AUTO LOGGER] Erreur initialisation:', error);
        }
    }
    
    findStateDbPath() {
        // Trouver le fichier state.vscdb du workspace actuel
        const appDataPath = process.env.APPDATA || process.env.HOME + '/.config';
        const workspaceStoragePath = path.join(appDataPath, 'Code', 'User', 'workspaceStorage');
        
        try {
            const workspaceDirs = fs.readdirSync(workspaceStoragePath);
            for (const dir of workspaceDirs) {
                const stateFile = path.join(workspaceStoragePath, dir, 'state.vscdb');
                if (fs.existsSync(stateFile)) {
                    // Vérifier si c'est le bon workspace (heuristique)
                    const stats = fs.statSync(stateFile);
                    if (Date.now() - stats.mtime.getTime() < 24 * 60 * 60 * 1000) { // Modifié dans les 24h
                        this.stateDbPath = stateFile;
                        break;
                    }
                }
            }
        } catch (error) {
            console.error('[AUGMENT AUTO LOGGER] Erreur recherche state.vscdb:', error);
        }
    }
    
    initializeLogFiles() {
        const timestamp = new Date().toISOString();
        
        // Fichier texte
        const textHeader = `AUGMENT CONVERSATION - LOG AUTOMATIQUE\\n` +
                          `Démarré: ${timestamp}\\n` +
                          `Workspace: ${this.workspaceRoot}\\n` +
                          `State DB: ${this.stateDbPath}\\n` +
                          `${'='.repeat(80)}\\n\\n`;
        
        fs.writeFileSync(this.logFile, textHeader, 'utf8');
        
        // Fichier JSON
        const jsonData = {
            started_at: timestamp,
            workspace_root: this.workspaceRoot,
            state_db_path: this.stateDbPath,
            messages: [],
            total_messages: 0
        };
        
        fs.writeFileSync(this.jsonLog, JSON.stringify(jsonData, null, 2), 'utf8');
    }
    
    startMonitoring() {
        if (!this.stateDbPath || !fs.existsSync(this.stateDbPath)) {
            console.log('[AUGMENT AUTO LOGGER] State DB non trouvé, surveillance désactivée');
            return;
        }
        
        // Surveiller les modifications du fichier state.vscdb
        let lastMtime = 0;
        
        const checkForChanges = () => {
            try {
                const stats = fs.statSync(this.stateDbPath);
                if (stats.mtime.getTime() > lastMtime) {
                    lastMtime = stats.mtime.getTime();
                    this.processConversationChanges();
                }
            } catch (error) {
                console.error('[AUGMENT AUTO LOGGER] Erreur surveillance:', error);
            }
        };
        
        // Vérifier toutes les 2 secondes
        setInterval(checkForChanges, 2000);
        this.isActive = true;
    }
    
    processConversationChanges() {
        // Cette méthode sera appelée quand state.vscdb change
        // Ici on peut ajouter la logique de parsing SQLite si nécessaire
        // Pour l'instant, on log juste le changement
        
        const timestamp = new Date().toISOString();
        const logEntry = `[${timestamp}] Changement détecté dans la conversation\\n`;
        
        try {
            fs.appendFileSync(this.logFile, logEntry, 'utf8');
            console.log('[AUGMENT AUTO LOGGER] Changement de conversation loggé');
        } catch (error) {
            console.error('[AUGMENT AUTO LOGGER] Erreur écriture log:', error);
        }
    }
}

// Initialiser le système de logging automatique
let augmentAutoLogger = null;

// Hook dans l'activation de l'extension
const originalActivate = activate;
if (typeof activate === 'function') {
    activate = function(context) {
        // Appeler l'activation originale
        const result = originalActivate(context);
        
        // Initialiser notre système de logging
        setTimeout(() => {
            augmentAutoLogger = new AugmentAutoLogger();
        }, 2000); // Délai pour laisser l'extension se charger
        
        return result;
    };
}

// ===== FIN DU CODE INJECTÉ =====
'''
    
    def create_backup(self):
        """Crée une sauvegarde complète de l'extension"""
        try:
            self.backup_dir.mkdir(parents=True, exist_ok=True)
            
            # Sauvegarder l'extension complète
            backup_extension_dir = self.backup_dir / f"augment_extension_backup_{self.timestamp}"
            shutil.copytree(self.extension_path, backup_extension_dir)
            
            print(f"✅ Sauvegarde extension créée: {backup_extension_dir}")
            
            # Sauvegarder spécifiquement extension.js
            backup_js = self.backup_dir / f"extension_js_backup_{self.timestamp}.js"
            shutil.copy2(self.extension_js, backup_js)
            
            print(f"✅ Sauvegarde extension.js créée: {backup_js}")
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur sauvegarde: {e}")
            return False
    
    def analyze_extension_js(self):
        """Analyse le fichier extension.js pour trouver les points d'injection"""
        try:
            with open(self.extension_js, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"📊 Analyse de extension.js:")
            print(f"  Taille: {len(content)} caractères")
            
            # Chercher des patterns importants
            patterns = {
                'activate_function': r'function activate\s*\(',
                'exports_activate': r'exports\.activate\s*=',
                'conversation_start': r'conversation|chat|message',
                'vscode_import': r'require\s*\(\s*[\'"]vscode[\'"]',
                'sqlite_usage': r'sqlite|database|state\.vscdb'
            }
            
            findings = {}
            for pattern_name, pattern in patterns.items():
                matches = re.findall(pattern, content, re.IGNORECASE)
                findings[pattern_name] = len(matches)
                if matches:
                    print(f"  {pattern_name}: {len(matches)} occurrences")
            
            # Trouver le meilleur point d'injection
            injection_point = self.find_injection_point(content)
            
            return content, findings, injection_point
            
        except Exception as e:
            print(f"❌ Erreur analyse extension.js: {e}")
            return None, None, None
    
    def find_injection_point(self, content):
        """Trouve le meilleur endroit pour injecter le code"""
        
        # Chercher la fonction activate
        activate_match = re.search(r'function activate\s*\([^)]*\)\s*{', content)
        if activate_match:
            return activate_match.end()
        
        # Chercher exports.activate
        exports_match = re.search(r'exports\.activate\s*=\s*function\s*\([^)]*\)\s*{', content)
        if exports_match:
            return exports_match.end()
        
        # Chercher module.exports
        module_match = re.search(r'module\.exports\s*=\s*{[^}]*activate\s*:', content)
        if module_match:
            return module_match.start()
        
        # Par défaut, injecter au début
        return 0
    
    def inject_logging_code(self, content, injection_point):
        """Injecte le code de logging dans l'extension"""
        
        # Injecter le code au point d'injection
        modified_content = (
            content[:injection_point] + 
            self.logging_code + 
            content[injection_point:]
        )
        
        return modified_content
    
    def modify_extension(self):
        """Modifie l'extension pour intégrer le logging automatique"""
        print(f"🔧 MODIFICATION DE L'EXTENSION AUGMENT")
        print(f"=" * 60)
        
        # Vérifier que l'extension existe
        if not self.extension_js.exists():
            print(f"❌ Extension non trouvée: {self.extension_js}")
            return False
        
        # Créer sauvegarde
        if not self.create_backup():
            return False
        
        # Analyser l'extension
        content, findings, injection_point = self.analyze_extension_js()
        if content is None:
            return False
        
        print(f"📍 Point d'injection trouvé à la position: {injection_point}")
        
        # Injecter le code
        modified_content = self.inject_logging_code(content, injection_point)
        
        # Écrire le fichier modifié
        try:
            with open(self.extension_js, 'w', encoding='utf-8') as f:
                f.write(modified_content)
            
            print(f"✅ Extension modifiée avec succès!")
            print(f"📁 Fichier: {self.extension_js}")
            print(f"📊 Taille originale: {len(content)} caractères")
            print(f"📊 Taille modifiée: {len(modified_content)} caractères")
            print(f"📊 Code ajouté: {len(modified_content) - len(content)} caractères")
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur écriture fichier modifié: {e}")
            return False
    
    def create_restoration_script(self):
        """Crée un script de restauration de l'extension"""
        restore_script = self.backup_dir / f"restore_extension_{self.timestamp}.py"
        
        script_content = f'''#!/usr/bin/env python3
"""
SCRIPT DE RESTAURATION EXTENSION AUGMENT
Généré le: {datetime.datetime.now().isoformat()}
"""

import shutil
from pathlib import Path

def restore_extension():
    print("🔄 RESTAURATION EXTENSION AUGMENT")
    print("=" * 50)
    
    # Chemins
    backup_js = Path("{self.backup_dir / f'extension_js_backup_{self.timestamp}.js'}")
    original_js = Path("{self.extension_js}")
    
    try:
        if backup_js.exists():
            shutil.copy2(backup_js, original_js)
            print("✅ extension.js restauré")
        else:
            print("❌ Fichier de sauvegarde non trouvé")
            
    except Exception as e:
        print(f"❌ Erreur restauration: {{e}}")

if __name__ == "__main__":
    restore_extension()
'''
        
        try:
            with open(restore_script, 'w', encoding='utf-8') as f:
                f.write(script_content)
            
            print(f"✅ Script de restauration créé: {restore_script}")
            return True
            
        except Exception as e:
            print(f"❌ Erreur création script restauration: {e}")
            return False
    
    def run_modification(self):
        """Exécute la modification complète"""
        print(f"🎯 MODIFICATION EXTENSION AUGMENT POUR LOGGING UNIVERSEL")
        print(f"=" * 70)
        print(f"Extension: {self.extension_path}")
        print(f"Fichier principal: {self.extension_js}")
        print()
        
        # Modifier l'extension
        if self.modify_extension():
            # Créer script de restauration
            self.create_restoration_script()
            
            print(f"\n🎉 MODIFICATION RÉUSSIE!")
            print(f"✅ L'extension Augment intègre maintenant le logging automatique")
            print(f"✅ Tous les nouveaux projets auront automatiquement le logging")
            print(f"✅ Fichiers de sauvegarde créés dans: {self.backup_dir}")
            print(f"\n💡 REDÉMARREZ VSCODE pour activer les modifications")
            
            return True
        else:
            print(f"\n❌ ÉCHEC DE LA MODIFICATION")
            return False

def main():
    """Fonction principale"""
    modifier = AugmentExtensionModifier()
    modifier.run_modification()

if __name__ == "__main__":
    main()
