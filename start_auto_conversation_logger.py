#!/usr/bin/env python3
"""
DÉMARREUR AUTOMATIQUE DU SYSTÈME DE LOGGING CONVERSATION
========================================================

Ce script démarre le système de logging automatique en arrière-plan
et crée les fichiers de conversation en temps réel.

Usage: python start_auto_conversation_logger.py
"""

import subprocess
import sys
import time
import datetime
from pathlib import Path
import threading
import os

class AutoLoggerStarter:
    def __init__(self):
        self.project_dir = Path("C:/Users/<USER>/Desktop/Travail/Projet7")
        self.logger_script = self.project_dir / "file_watcher_auto_logger.py"
        self.log_file = self.project_dir / "conversation_live_auto.txt"
        self.json_log = self.project_dir / "conversation_live_auto.json"
        self.status_file = self.project_dir / "auto_logger_status.txt"
        
        self.process = None
        self.running = False
    
    def check_requirements(self):
        """Vérifie que tous les fichiers requis existent"""
        if not self.logger_script.exists():
            print(f"❌ Script logger non trouvé: {self.logger_script}")
            return False
        
        try:
            import watchdog
            print(f"✅ Module watchdog disponible")
        except ImportError:
            print(f"❌ Module watchdog manquant. Installation: pip install watchdog")
            return False
        
        return True
    
    def create_status_file(self):
        """Crée un fichier de statut"""
        with open(self.status_file, 'w', encoding='utf-8') as f:
            f.write("STATUT SYSTÈME DE LOGGING AUTOMATIQUE AUGMENT\n")
            f.write("=" * 50 + "\n")
            f.write(f"Démarré: {datetime.datetime.now().isoformat()}\n")
            f.write(f"Script: {self.logger_script}\n")
            f.write(f"Log texte: {self.log_file}\n")
            f.write(f"Log JSON: {self.json_log}\n")
            f.write("=" * 50 + "\n\n")
    
    def start_background_logger(self):
        """Démarre le logger en arrière-plan"""
        try:
            # Importer et démarrer le logger directement
            sys.path.insert(0, str(self.project_dir))
            from file_watcher_auto_logger import AutoConversationLogger
            
            self.logger = AutoConversationLogger()
            
            if self.logger.start_watching():
                self.running = True
                
                # Mettre à jour le statut
                with open(self.status_file, 'a', encoding='utf-8') as f:
                    f.write(f"[{datetime.datetime.now().isoformat()}] ✅ Logger démarré avec succès\n")
                
                print(f"🚀 SYSTÈME DE LOGGING AUTOMATIQUE DÉMARRÉ")
                print(f"📝 Fichier de log: {self.log_file}")
                print(f"📊 Fichier JSON: {self.json_log}")
                print(f"📋 Statut: {self.status_file}")
                print(f"💬 Le système capture maintenant automatiquement vos conversations Augment")
                
                return True
            else:
                print(f"❌ Échec du démarrage du logger")
                return False
                
        except Exception as e:
            print(f"❌ Erreur démarrage: {e}")
            return False
    
    def monitor_system(self):
        """Surveille le système en arrière-plan"""
        while self.running:
            try:
                # Vérifier que les fichiers de log existent et sont accessibles
                if self.log_file.exists() and self.json_log.exists():
                    # Mettre à jour le statut périodiquement
                    with open(self.status_file, 'a', encoding='utf-8') as f:
                        f.write(f"[{datetime.datetime.now().isoformat()}] ✅ Système actif\n")
                
                time.sleep(60)  # Vérifier chaque minute
                
            except Exception as e:
                print(f"⚠️ Erreur monitoring: {e}")
                time.sleep(10)
    
    def stop_logger(self):
        """Arrête le logger"""
        if hasattr(self, 'logger') and self.logger:
            self.logger.stop_watching()
            self.running = False
            
            with open(self.status_file, 'a', encoding='utf-8') as f:
                f.write(f"[{datetime.datetime.now().isoformat()}] 🛑 Logger arrêté\n")
            
            print(f"🛑 Système de logging arrêté")
    
    def start(self):
        """Démarre le système complet"""
        print(f"🎯 DÉMARRAGE SYSTÈME DE LOGGING AUTOMATIQUE")
        print(f"=" * 60)
        
        # Vérifier les prérequis
        if not self.check_requirements():
            return False
        
        # Créer le fichier de statut
        self.create_status_file()
        
        # Démarrer le logger
        if not self.start_background_logger():
            return False
        
        # Démarrer le monitoring en arrière-plan
        monitor_thread = threading.Thread(target=self.monitor_system, daemon=True)
        monitor_thread.start()
        
        return True

def main():
    """Fonction principale"""
    starter = AutoLoggerStarter()
    
    if starter.start():
        try:
            print(f"\n🔄 Système actif - Surveillance en cours...")
            print(f"💡 Tapez quelque chose dans Augment pour voir le logging en action")
            print(f"⏹️  Appuyez sur Ctrl+C pour arrêter")
            
            # Garder le script actif
            while True:
                time.sleep(1)
                
        except KeyboardInterrupt:
            print(f"\n🛑 Arrêt demandé...")
            starter.stop_logger()
    else:
        print(f"❌ Impossible de démarrer le système")

if __name__ == "__main__":
    main()
