#!/usr/bin/env python3
"""
SCRIPT POUR IDENTIFIER LES 29 MÉTHODES SANS PARAMÈTRES
======================================================

Objectif : Identifier les méthodes des 108 qui n'ont aucun paramètre codé en dur
"""

import json
import re

def extract_all_methods_from_class_txt():
    """Extrait toutes les méthodes du fichier class.txt"""
    try:
        with open('centralisation_methodes/class.txt', 'r', encoding='utf-8') as f:
            content = f.read()
        
        methods = []
        lines = content.split('\n')
        
        for line_num, line in enumerate(lines, 1):
            if 'def ' in line and '(' in line:
                # Extraction du nom de méthode
                match = re.search(r'def ([a-zA-Z_][a-zA-Z0-9_]*)', line)
                if match:
                    method_name = match.group(1)
                    methods.append({
                        'name': method_name,
                        'line_number': line_num,
                        'signature': line.strip()
                    })
        
        return methods
    except Exception as e:
        print(f"Erreur lecture class.txt: {e}")
        return []

def load_excluded_methods():
    """Charge les méthodes exclues (31 universelles + 16 spécifiques)"""
    excluded_methods = {
        # 31 méthodes universelles centralisées
        '_rollout_analyzer', '_analyze_impair_consecutive_bias', '_analyze_pair_priority_2_autonomous',
        '_analyze_sync_alternation_bias', '_correlate_impair_with_sync', '_correlate_impair_with_combined',
        '_correlate_impair_with_pb', '_correlate_impair_with_so', '_correlate_bias_to_pb_variations',
        '_correlate_bias_to_so_variations', '_analyze_combined_structural_bias',
        '_generate_priority_based_synthesis_autonomous', '_generate_bias_signals_summary',
        '_generate_bias_generation_guidance', '_generate_bias_quick_access', '_rollout_generator',
        '_define_optimized_generation_space', '_generate_sequences_from_signals',
        '_generate_fallback_sequences', '_enrich_sequences_with_complete_indexes',
        '_rollout_predictor', '_evaluate_sequence_quality', '_evaluate_signal_alignment',
        '_evaluate_fallback_alignment', '_analyze_sequence_consistency', '_assess_risk_reward_ratio',
        '_validate_sequence_logic', '_calculate_sequence_score', '_select_best_sequence',
        '_calculate_cluster_confidence_azr_calibrated', '_convert_pb_sequence_to_so',
        
        # 16 méthodes spécifiques clusters
        '_rollout_analyzer_c2_patterns_courts', '_rollout_analyzer_c3_patterns_moyens',
        '_analyze_impair_consecutive_bias_c2_specialized', '_analyze_sync_alternation_bias_c2_specialized',
        '_apply_c2_short_patterns_specialization', '_generate_bias_signals_summary_c2',
        '_generate_bias_generation_guidance_c2', '_generate_bias_quick_access_c2',
        '_analyze_impair_consecutive_bias_c3_specialized', '_analyze_sync_alternation_bias_c3_specialized',
        '_apply_c3_medium_patterns_specialization', '_get_cluster_specialization_params',
        '_create_generic_cluster_analyzer', '_analyze_impair_bias_specialized',
        '_analyze_sync_bias_specialized', '_apply_cluster_specialization'
    }
    return excluded_methods

def load_methods_with_parameters():
    """Charge les 79 méthodes qui ont des paramètres"""
    try:
        with open('centralisation_methodes/analyse_108_methodes_uniquement.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Extraction des méthodes avec paramètres
        methods_with_params = set()
        for param in data['extracted_parameters']:
            method_name = param.get('method_name')
            if method_name and method_name != 'unknown':
                methods_with_params.add(method_name)
        
        return methods_with_params
    except Exception as e:
        print(f"Erreur lecture analyse: {e}")
        return set()

def identify_methods_without_parameters():
    """Identifie les 29 méthodes sans paramètres"""
    
    # 1. Toutes les méthodes du fichier
    all_methods = extract_all_methods_from_class_txt()
    print(f"Total méthodes dans class.txt: {len(all_methods)}")
    
    # 2. Méthodes exclues (31 + 16)
    excluded_methods = load_excluded_methods()
    print(f"Méthodes exclues: {len(excluded_methods)}")
    
    # 3. Méthodes avec paramètres (79)
    methods_with_params = load_methods_with_parameters()
    print(f"Méthodes avec paramètres: {len(methods_with_params)}")
    
    # 4. Filtrage pour obtenir les 108 méthodes cibles
    target_methods = []
    for method in all_methods:
        method_name = method['name']
        # Exclure les méthodes centralisées et spécifiques
        if not any(excluded in method_name for excluded in excluded_methods):
            target_methods.append(method)
    
    print(f"Méthodes cibles (108): {len(target_methods)}")
    
    # 5. Identifier celles sans paramètres
    methods_without_params = []
    methods_with_params_found = []
    
    for method in target_methods:
        method_name = method['name']
        if method_name in methods_with_params:
            methods_with_params_found.append(method)
        else:
            methods_without_params.append(method)
    
    print(f"Méthodes avec paramètres trouvées: {len(methods_with_params_found)}")
    print(f"Méthodes sans paramètres: {len(methods_without_params)}")
    
    return {
        'methods_without_parameters': methods_without_params,
        'methods_with_parameters_found': methods_with_params_found,
        'total_target_methods': len(target_methods),
        'excluded_methods_count': len(excluded_methods)
    }

def main():
    """Fonction principale"""
    results = identify_methods_without_parameters()
    
    # Sauvegarde des résultats
    output_file = 'centralisation_methodes/liste_29_methodes_sans_parametres.txt'
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("LISTE DES 29 MÉTHODES SANS PARAMÈTRES CODÉS EN DUR\n")
        f.write("=" * 55 + "\n\n")
        
        f.write("ANALYSE COMPARATIVE :\n")
        f.write("=" * 20 + "\n")
        f.write(f"Total méthodes cibles (108) : {results['total_target_methods']}\n")
        f.write(f"Méthodes avec paramètres (79) : {len(results['methods_with_parameters_found'])}\n")
        f.write(f"Méthodes sans paramètres (29) : {len(results['methods_without_parameters'])}\n")
        f.write(f"Méthodes exclues (47) : {results['excluded_methods_count']}\n\n")
        
        f.write("LISTE DES 29 MÉTHODES SANS PARAMÈTRES :\n")
        f.write("=" * 40 + "\n\n")
        
        for i, method in enumerate(results['methods_without_parameters'], 1):
            f.write(f"{i:2d}. {method['name']} - Ligne {method['line_number']}\n")
            f.write(f"    Signature: {method['signature']}\n\n")
        
        f.write("\nCARACTÉRISTIQUES DE CES MÉTHODES :\n")
        f.write("=" * 35 + "\n")
        f.write("Ces méthodes sont probablement :\n")
        f.write("- Méthodes utilitaires pures (sans valeurs fixes)\n")
        f.write("- Méthodes de classification logique\n")
        f.write("- Getters/setters simples\n")
        f.write("- Méthodes de validation sans seuils\n")
        f.write("- Méthodes de transformation pure\n\n")
        
        f.write("IMPACT SUR LA CENTRALISATION :\n")
        f.write("=" * 30 + "\n")
        f.write("✅ Ces 29 méthodes n'ont PAS besoin de centralisation\n")
        f.write("✅ Elles sont déjà compatibles avec l'architecture universelle\n")
        f.write("✅ Focus sur les 79 méthodes avec 785 paramètres à centraliser\n")
    
    print(f"Liste sauvée dans {output_file}")
    print(f"29 méthodes sans paramètres identifiées")

if __name__ == "__main__":
    main()
