#!/usr/bin/env python3
"""
INJECTION DIRECTE DANS L'EXTENSION AUGMENT
==========================================

Script Python pour injecter directement le code de logging
dans l'extension Augment maintenant que les permissions sont correctes.
"""

import shutil
from pathlib import Path
import datetime

def injection_directe():
    """Effectue l'injection directe du code"""
    
    extension_js = Path("C:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.470.1/out/extension.js")
    injection_code_file = Path("C:/Users/<USER>/Desktop/Travail/Projet7/injection_code_manual.js")
    backup_dir = Path("C:/Users/<USER>/Desktop/Travail/Projet7/BACKUPS_EXTENSION")
    
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    
    print(f"🔧 INJECTION DIRECTE DANS L'EXTENSION")
    print(f"=" * 50)
    
    try:
        # 1. Vérifier que les fichiers existent
        if not extension_js.exists():
            print(f"❌ Fichier extension.js non trouvé: {extension_js}")
            return False
        
        if not injection_code_file.exists():
            print(f"❌ Code d'injection non trouvé: {injection_code_file}")
            return False
        
        # 2. Créer une sauvegarde
        backup_dir.mkdir(parents=True, exist_ok=True)
        backup_file = backup_dir / f"extension_js_direct_backup_{timestamp}.js"
        
        shutil.copy2(extension_js, backup_file)
        print(f"✅ Sauvegarde créée: {backup_file}")
        
        # 3. Lire le fichier original
        with open(extension_js, 'r', encoding='utf-8') as f:
            original_content = f.read()
        
        print(f"📊 Fichier original: {len(original_content)} caractères")
        
        # 4. Lire le code d'injection
        with open(injection_code_file, 'r', encoding='utf-8') as f:
            injection_code = f.read()
        
        print(f"📊 Code d'injection: {len(injection_code)} caractères")
        
        # 5. Créer le contenu modifié
        modified_content = injection_code + "\n" + original_content
        
        print(f"📊 Contenu modifié: {len(modified_content)} caractères")
        
        # 6. Écrire le fichier modifié
        with open(extension_js, 'w', encoding='utf-8') as f:
            f.write(modified_content)
        
        print(f"✅ Injection réussie!")
        print(f"📁 Fichier modifié: {extension_js}")
        print(f"📊 Code ajouté: {len(injection_code)} caractères")
        
        # 7. Vérifier l'injection
        with open(extension_js, 'r', encoding='utf-8') as f:
            verification_content = f.read()
        
        if "AUGMENT AUTO LOGGING SYSTEM" in verification_content:
            print(f"✅ Vérification: Injection confirmée!")
            return True
        else:
            print(f"❌ Vérification: Injection non détectée")
            return False
        
    except PermissionError as e:
        print(f"❌ Erreur de permission: {e}")
        print(f"💡 Assurez-vous que VSCode est fermé et que vous avez les droits administrateur")
        return False
    
    except Exception as e:
        print(f"❌ Erreur inattendue: {e}")
        return False

def verifier_injection():
    """Vérifie que l'injection a bien fonctionné"""
    
    extension_js = Path("C:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.470.1/out/extension.js")
    
    print(f"\n🔍 VÉRIFICATION DE L'INJECTION")
    print(f"=" * 40)
    
    try:
        with open(extension_js, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Chercher nos marqueurs
        markers = [
            "AUGMENT AUTO LOGGING SYSTEM",
            "AugmentAutoLogger",
            "augment_conversation_auto.txt"
        ]
        
        found_markers = 0
        for marker in markers:
            if marker in content:
                print(f"✅ Marqueur trouvé: {marker}")
                found_markers += 1
            else:
                print(f"❌ Marqueur manquant: {marker}")
        
        if found_markers == len(markers):
            print(f"\n🎉 INJECTION CONFIRMÉE!")
            print(f"✅ Tous les marqueurs sont présents")
            
            # Afficher le début du fichier
            print(f"\n📝 Début du fichier modifié:")
            print(f"{'='*50}")
            print(content[:300])
            print(f"{'='*50}")
            
            return True
        else:
            print(f"\n❌ INJECTION INCOMPLÈTE")
            print(f"⚠️ {found_markers}/{len(markers)} marqueurs trouvés")
            return False
    
    except Exception as e:
        print(f"❌ Erreur vérification: {e}")
        return False

def main():
    """Fonction principale"""
    
    print(f"🎯 INJECTION DIRECTE EXTENSION AUGMENT")
    print(f"=" * 60)
    
    if injection_directe():
        if verifier_injection():
            print(f"\n🎉 SUCCÈS COMPLET!")
            print(f"✅ L'extension Augment a été modifiée avec succès")
            print(f"✅ Le code de logging automatique est injecté")
            print(f"✅ Tous les nouveaux projets auront le logging automatique")
            print(f"\n💡 REDÉMARREZ VSCODE pour activer les modifications")
            print(f"\n🧪 POUR TESTER:")
            print(f"   1. Redémarrez VSCode")
            print(f"   2. Ouvrez un nouveau projet")
            print(f"   3. Démarrez une conversation Augment")
            print(f"   4. Vérifiez qu'un fichier 'augment_conversation_auto.txt' est créé")
        else:
            print(f"\n⚠️ INJECTION PARTIELLE")
            print(f"Le fichier a été modifié mais la vérification a échoué")
    else:
        print(f"\n❌ ÉCHEC DE L'INJECTION")
        print(f"Impossible de modifier l'extension")

if __name__ == "__main__":
    main()
