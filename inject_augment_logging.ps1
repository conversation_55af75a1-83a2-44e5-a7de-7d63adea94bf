# Script PowerShell pour injecter le logging Augment
$extensionFile = "C:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.470.1\out\extension.js"
$injectionCode = Get-Content "C:\Users\<USER>\Desktop\Travail\Projet7\augment_auto_injector.js" -Raw

# Lire le fichier original
$originalContent = Get-Content $extensionFile -Raw

# Créer le contenu modifié
$modifiedContent = $injectionCode + $originalContent

# Sauvegarder l'original
$backupFile = "C:\Users\<USER>\Desktop\Travail\Projet7\BACKUPS_EXTENSION\extension_js_ps_backup_20250604_130030.js"
Copy-Item $extensionFile $backupFile

# Écrire le fichier modifié
$modifiedContent | Out-File $extensionFile -Encoding UTF8

Write-Host "✅ Extension modifiée via PowerShell"
