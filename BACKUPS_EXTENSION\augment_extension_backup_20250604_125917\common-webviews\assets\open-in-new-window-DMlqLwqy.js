var le=Object.defineProperty;var he=(a,e,t)=>e in a?le(a,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[e]=t;var c=(a,e,t)=>he(a,typeof e!="symbol"?e+"":e,t);import{b as z,A as O,R as w,a as ue}from"./types-LfaCSdmF.js";import{W as d}from"./BaseButton-C6Dhmpxa.js";import{a as B,C as ne,P as q}from"./chat-types-NgqNgjwU.js";import{c as pe,f as me,A as _e,a as fe,p as ye,T,W as ve,b as S,d as V,g as Se,r as J,s as Ae,e as X,S as we,h as Ce}from"./test_service_pb-B6vKXZrG.js";import{n as Re}from"./file-paths-BcSg4gks.js";import{S as be,b as Me,c as Ie}from"./types-a569v5Ol.js";import{ah as K,ae as Ee,ao as xe,S as ae,i as re,s as ie,a as N,b as $,I as Pe,J as Te,K as Fe,L as ke,h as W,d as Z,M as qe,g as Le,n as L,j as Q,c as C,e as De,f as Oe}from"./SpinnerAugment-BJ4-L7QR.js";var I;function Y(a){const e=I[a];return typeof e!="string"?a.toString():e[0].toLowerCase()+e.substring(1).replace(/[A-Z]/g,t=>"_"+t.toLowerCase())}(function(a){a[a.Canceled=1]="Canceled",a[a.Unknown=2]="Unknown",a[a.InvalidArgument=3]="InvalidArgument",a[a.DeadlineExceeded=4]="DeadlineExceeded",a[a.NotFound=5]="NotFound",a[a.AlreadyExists=6]="AlreadyExists",a[a.PermissionDenied=7]="PermissionDenied",a[a.ResourceExhausted=8]="ResourceExhausted",a[a.FailedPrecondition=9]="FailedPrecondition",a[a.Aborted=10]="Aborted",a[a.OutOfRange=11]="OutOfRange",a[a.Unimplemented=12]="Unimplemented",a[a.Internal=13]="Internal",a[a.Unavailable=14]="Unavailable",a[a.DataLoss=15]="DataLoss",a[a.Unauthenticated=16]="Unauthenticated"})(I||(I={}));class M extends Error{constructor(e,t=I.Unknown,s,n,i){super(function(r,o){return r.length?`[${Y(o)}] ${r}`:`[${Y(o)}]`}(e,t)),this.name="ConnectError",Object.setPrototypeOf(this,new.target.prototype),this.rawMessage=e,this.code=t,this.metadata=new Headers(s??{}),this.details=n??[],this.cause=i}static from(e,t=I.Unknown){return e instanceof M?e:e instanceof Error?e.name=="AbortError"?new M(e.message,I.Canceled):new M(e.message,t,void 0,void 0,e):new M(String(e),t,void 0,void 0,e)}static[Symbol.hasInstance](e){return e instanceof Error&&(Object.getPrototypeOf(e)===M.prototype||e.name==="ConnectError"&&"code"in e&&typeof e.code=="number"&&"metadata"in e&&"details"in e&&Array.isArray(e.details)&&"rawMessage"in e&&typeof e.rawMessage=="string"&&"cause"in e)}findDetails(e){const t=e.kind==="message"?{getMessage:n=>n===e.typeName?e:void 0}:e,s=[];for(const n of this.details){if("desc"in n){t.getMessage(n.desc.typeName)&&s.push(pe(n.desc,n.value));continue}const i=t.getMessage(n.type);if(i)try{s.push(me(i,n.value))}catch{}}return s}}var Ue=function(a){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e,t=a[Symbol.asyncIterator];return t?t.call(a):(a=typeof __values=="function"?__values(a):a[Symbol.iterator](),e={},s("next"),s("throw"),s("return"),e[Symbol.asyncIterator]=function(){return this},e);function s(n){e[n]=a[n]&&function(i){return new Promise(function(r,o){(function(g,h,u,l){Promise.resolve(l).then(function(p){g({value:p,done:u})},h)})(r,o,(i=a[n](i)).done,i.value)})}}},k=function(a){return this instanceof k?(this.v=a,this):new k(a)},He=function(a,e,t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var s,n=t.apply(a,e||[]),i=[];return s=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),r("next"),r("throw"),r("return",function(l){return function(p){return Promise.resolve(p).then(l,h)}}),s[Symbol.asyncIterator]=function(){return this},s;function r(l,p){n[l]&&(s[l]=function(m){return new Promise(function(y,R){i.push([l,m,y,R])>1||o(l,m)})},p&&(s[l]=p(s[l])))}function o(l,p){try{(m=n[l](p)).value instanceof k?Promise.resolve(m.value.v).then(g,h):u(i[0][2],m)}catch(y){u(i[0][3],y)}var m}function g(l){o("next",l)}function h(l){o("throw",l)}function u(l,p){l(p),i.shift(),i.length&&o(i[0][0],i[0][1])}},Be=function(a){var e,t;return e={},s("next"),s("throw",function(n){throw n}),s("return"),e[Symbol.iterator]=function(){return this},e;function s(n,i){e[n]=a[n]?function(r){return(t=!t)?{value:k(a[n](r)),done:!1}:i?i(r):r}:i}},oe=function(a){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e,t=a[Symbol.asyncIterator];return t?t.call(a):(a=typeof __values=="function"?__values(a):a[Symbol.iterator](),e={},s("next"),s("throw"),s("return"),e[Symbol.asyncIterator]=function(){return this},e);function s(n){e[n]=a[n]&&function(i){return new Promise(function(r,o){(function(g,h,u,l){Promise.resolve(l).then(function(p){g({value:p,done:u})},h)})(r,o,(i=a[n](i)).done,i.value)})}}},x=function(a){return this instanceof x?(this.v=a,this):new x(a)},Ne=function(a){var e,t;return e={},s("next"),s("throw",function(n){throw n}),s("return"),e[Symbol.iterator]=function(){return this},e;function s(n,i){e[n]=a[n]?function(r){return(t=!t)?{value:x(a[n](r)),done:!1}:i?i(r):r}:i}},$e=function(a,e,t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var s,n=t.apply(a,e||[]),i=[];return s=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),r("next"),r("throw"),r("return",function(l){return function(p){return Promise.resolve(p).then(l,h)}}),s[Symbol.asyncIterator]=function(){return this},s;function r(l,p){n[l]&&(s[l]=function(m){return new Promise(function(y,R){i.push([l,m,y,R])>1||o(l,m)})},p&&(s[l]=p(s[l])))}function o(l,p){try{(m=n[l](p)).value instanceof x?Promise.resolve(m.value.v).then(g,h):u(i[0][2],m)}catch(y){u(i[0][3],y)}var m}function g(l){o("next",l)}function h(l){o("throw",l)}function u(l,p){l(p),i.shift(),i.length&&o(i[0][0],i[0][1])}};function We(a,e){return function(t,s){const n={};for(const i of t.methods){const r=s(i);r!=null&&(n[i.localName]=r)}return n}(a,t=>{switch(t.methodKind){case"unary":return function(s,n){return async function(i,r){var o,g;const h=await s.unary(n,r==null?void 0:r.signal,r==null?void 0:r.timeoutMs,r==null?void 0:r.headers,i,r==null?void 0:r.contextValues);return(o=r==null?void 0:r.onHeader)===null||o===void 0||o.call(r,h.header),(g=r==null?void 0:r.onTrailer)===null||g===void 0||g.call(r,h.trailer),h.message}}(e,t);case"server_streaming":return function(s,n){return function(i,r){return ee(s.stream(n,r==null?void 0:r.signal,r==null?void 0:r.timeoutMs,r==null?void 0:r.headers,function(o){return He(this,arguments,function*(){yield k(yield*Be(Ue(o)))})}([i]),r==null?void 0:r.contextValues),r)}}(e,t);case"client_streaming":return function(s,n){return async function(i,r){var o,g,h,u,l,p;const m=await s.stream(n,r==null?void 0:r.signal,r==null?void 0:r.timeoutMs,r==null?void 0:r.headers,i,r==null?void 0:r.contextValues);let y;(l=r==null?void 0:r.onHeader)===null||l===void 0||l.call(r,m.header);let R=0;try{for(var _,P=!0,D=oe(m.message);!(o=(_=await D.next()).done);P=!0)u=_.value,P=!1,y=u,R++}catch(ge){g={error:ge}}finally{try{P||o||!(h=D.return)||await h.call(D)}finally{if(g)throw g.error}}if(!y)throw new M("protocol error: missing response message",I.Unimplemented);if(R>1)throw new M("protocol error: received extra messages for client streaming method",I.Unimplemented);return(p=r==null?void 0:r.onTrailer)===null||p===void 0||p.call(r,m.trailer),y}}(e,t);case"bidi_streaming":return function(s,n){return function(i,r){return ee(s.stream(n,r==null?void 0:r.signal,r==null?void 0:r.timeoutMs,r==null?void 0:r.headers,i,r==null?void 0:r.contextValues),r)}}(e,t);default:return null}})}function ee(a,e){const t=function(){return $e(this,arguments,function*(){var s,n;const i=yield x(a);(s=e==null?void 0:e.onHeader)===null||s===void 0||s.call(e,i.header),yield x(yield*Ne(oe(i.message))),(n=e==null?void 0:e.onTrailer)===null||n===void 0||n.call(e,i.trailer)})}()[Symbol.asyncIterator]();return{[Symbol.asyncIterator]:()=>({next:()=>t.next()})}}const Ct="augment-welcome";var f=(a=>(a.draft="draft",a.sent="sent",a.failed="failed",a.success="success",a.cancelled="cancelled",a))(f||{}),Ge=(a=>(a.running="running",a.awaitingUserAction="awaiting-user-action",a.notRunning="not-running",a))(Ge||{}),A=(a=>(a.seen="seen",a.unseen="unseen",a))(A||{}),je=(a=>(a.signInWelcome="sign-in-welcome",a.generateCommitMessage="generate-commit-message",a.summaryResponse="summary-response",a.summaryTitle="summary-title",a.educateFeatures="educate-features",a.autofixMessage="autofix-message",a.autofixSteeringMessage="autofix-steering-message",a.autofixStage="autofix-stage",a.agentOnboarding="agent-onboarding",a.agenticTurnDelimiter="agentic-turn-delimiter",a.agenticRevertDelimiter="agentic-revert-delimiter",a.agenticCheckpointDelimiter="agentic-checkpoint-delimiter",a.exchange="exchange",a))(je||{});function ze(a){return!!a&&(a.chatItemType===void 0||a.chatItemType==="agent-onboarding")}function Rt(a){return ze(a)&&a.status==="success"}function bt(a){return a.chatItemType==="autofix-message"}function Mt(a){return a.chatItemType==="autofix-steering-message"}function It(a){return a.chatItemType==="autofix-stage"}function Et(a){return a.chatItemType==="sign-in-welcome"}function xt(a){return a.chatItemType==="generate-commit-message"}function Pt(a){return a.chatItemType==="summary-response"}function Tt(a){return a.chatItemType==="educate-features"}function Ft(a){return a.chatItemType==="agent-onboarding"}function kt(a){return a.chatItemType==="agentic-turn-delimiter"}function qt(a){return a.chatItemType==="agentic-checkpoint-delimiter"}function Lt(a){return a.revertTarget!==void 0}function Dt(a){var e;return((e=a.structured_output_nodes)==null?void 0:e.some(t=>t.type===ne.TOOL_USE))??!1}function Ot(a){var e;return((e=a.structured_request_nodes)==null?void 0:e.some(t=>t.type===B.TOOL_RESULT))??!1}function Ut(a){return!(!a||typeof a!="object")&&(!("request_id"in a)||typeof a.request_id=="string")&&(!("seen_state"in a)||a.seen_state==="seen"||a.seen_state==="unseen")}async function*Ve(a,e=1e3){for(;a>0;)yield a,await new Promise(t=>setTimeout(t,Math.min(e,a))),a-=e}class Je{constructor(e,t,s,n=5,i=4e3,r){c(this,"_isCancelled",!1);this.requestId=e,this.chatMessage=t,this.startStreamFn=s,this.maxRetries=n,this.baseDelay=i,this.flags=r}cancel(){this._isCancelled=!0}async*getStream(){let e=0,t=!1;try{for(;!this._isCancelled;){const s=this.startStreamFn({...this.chatMessage,createdTimestamp:Date.now()},this.flags?{flags:this.flags}:void 0);let n,i=!1,r="";for await(const g of s){if(g.status===f.failed){if(g.isRetriable!==!0||t)return yield g;i=!0,r=g.display_error_message||"Service is currently unavailable",n=g.request_id;break}t=!0,yield g}if(!i)return;if(this._isCancelled)return yield this.createCancelledStatus();if(e++,e>this.maxRetries)return void(yield{request_id:n??this.requestId,seen_state:A.unseen,status:f.failed,display_error_message:r,isRetriable:!1});const o=this.baseDelay*2**(e-1);for await(const g of Ve(o))yield{request_id:this.requestId,status:f.sent,display_error_message:`Service temporarily unavailable. Retrying in ${Math.floor(g/1e3)} seconds... (Attempt ${e} of ${this.maxRetries})`,isRetriable:!0};yield{request_id:this.requestId,status:f.sent,display_error_message:"Generating response...",isRetriable:!0}}this._isCancelled&&(yield this.createCancelledStatus())}catch(s){yield{request_id:this.requestId,seen_state:A.unseen,status:f.failed,display_error_message:s instanceof Error?s.message:String(s)}}}createCancelledStatus(){return{request_id:this.requestId,seen_state:A.unseen,status:f.cancelled}}}function v(a,e){return e in a&&a[e]!==void 0}function Xe(a){return v(a,"file")}function Ke(a){return v(a,"recentFile")}function Ze(a){return v(a,"folder")}function Qe(a){return v(a,"sourceFolder")}function Ht(a){return v(a,"sourceFolderGroup")}function Bt(a){return v(a,"selection")}function Ye(a){return v(a,"externalSource")}function Nt(a){return v(a,"allDefaultContext")}function $t(a){return v(a,"clearContext")}function Wt(a){return v(a,"userGuidelines")}function Gt(a){return v(a,"agentMemories")}function et(a){return v(a,"personality")}function tt(a){return v(a,"rule")}const jt={allDefaultContext:!0,label:"Default Context",id:"allDefaultContext"},zt={clearContext:!0,label:"Clear Context",id:"clearContext"},Vt={userGuidelines:{enabled:!1,overLimit:!1,contents:"",lengthLimit:2e3},label:"User Guidelines",id:"userGuidelines"},Jt={agentMemories:{},label:"Agent Memories",id:"agentMemories"},te=[{personality:{type:q.DEFAULT,description:"Expert software engineer - trusted coding agent, at your service!"},label:"Agent Auggie",name:"auggie-personality-agent-default",id:"auggie-personality-agent-default"},{personality:{type:q.PROTOTYPER,description:"Fast and loose - let's get it done, boss!"},label:"Prototyper Auggie",name:"auggie-personality-prototyper",id:"auggie-personality-prototyper"},{personality:{type:q.BRAINSTORM,description:"Thoughtful and creative - thinking through all possibilities..."},label:"Brainstorm Auggie",name:"auggie-personality-brainstorm",id:"auggie-personality-brainstorm"},{personality:{type:q.REVIEWER,description:"Code detective - finding issues and analyzing implications"},label:"Reviewer Auggie",name:"auggie-personality-reviewer",id:"auggie-personality-reviewer"}];function Xt(a){return v(a,"group")}function Kt(a){const e=new Map;return a.forEach(t=>{Xe(t)?e.set("file",[...e.get("file")??[],t]):Ke(t)?e.set("recentFile",[...e.get("recentFile")??[],t]):Ze(t)?e.set("folder",[...e.get("folder")??[],t]):Ye(t)?e.set("externalSource",[...e.get("externalSource")??[],t]):Qe(t)?e.set("sourceFolder",[...e.get("sourceFolder")??[],t]):et(t)?e.set("personality",[...e.get("personality")??[],t]):tt(t)&&e.set("rule",[...e.get("rule")??[],t])}),[{label:"Personalities",id:"personalities",group:{type:"personality",materialIcon:"person",items:e.get("personality")??[]}},{label:"Files",id:"files",group:{type:"file",materialIcon:"insert_drive_file",items:e.get("file")??[]}},{label:"Folders",id:"folders",group:{type:"folder",materialIcon:"folder",items:e.get("folder")??[]}},{label:"Source Folders",id:"sourceFolders",group:{type:"sourceFolder",materialIcon:"folder_managed",items:e.get("sourceFolder")??[]}},{label:"Recently Opened Files",id:"recentlyOpenedFiles",group:{type:"recentFile",materialIcon:"insert_drive_file",items:e.get("recentFile")??[]}},{label:"Documentation",id:"externalSources",group:{type:"externalSource",materialIcon:"link",items:e.get("externalSource")??[]}},{label:"Rules",id:"rules",group:{type:"rule",materialIcon:"rule",items:e.get("rule")??[]}}].filter(t=>t.group.items.length>0)}function st(a){const e={label:Re(a.pathName).split("/").filter(t=>t.trim()!=="").pop()||"",name:a.pathName,id:ye({rootPath:a.repoRoot,relPath:a.pathName})};if(a.fullRange){const t=`:L${a.fullRange.startLineNumber}-${a.fullRange.endLineNumber}`;e.label+=t,e.name+=t,e.id+=t}else if(a.range){const t=`:L${a.range.start}-${a.range.stop}`;e.label+=t,e.name+=t,e.id+=t}return e}function nt(a){const e=a.path.split("/"),t=e[e.length-1],s=t.endsWith(".md")?t.slice(0,-3):t,n=`${_e}/${fe}/${a.path}`;return{label:s,name:n,id:n}}class at{constructor(e){c(this,"getHydratedTask",async e=>{const t={type:T.getHydratedTaskRequest,data:{uuid:e}};return(await this._asyncMsgSender.sendToSidecar(t,3e4)).data.task});c(this,"createTask",async(e,t,s)=>{const n={type:T.createTaskRequest,data:{name:e,description:t,parentTaskUuid:s}};return(await this._asyncMsgSender.sendToSidecar(n,3e4)).data.uuid});c(this,"updateTask",async(e,t,s)=>{const n={type:T.updateTaskRequest,data:{uuid:e,updates:t,updatedBy:s}};await this._asyncMsgSender.sendToSidecar(n,3e4)});c(this,"setCurrentRootTaskUuid",e=>{const t={type:T.setCurrentRootTaskUuid,data:{uuid:e}};this._asyncMsgSender.sendToSidecar(t)});c(this,"updateHydratedTask",async(e,t)=>{const s={type:T.updateHydratedTaskRequest,data:{task:e,updatedBy:t}};return(await this._asyncMsgSender.sendToSidecar(s,3e4)).data});this._asyncMsgSender=e}}class rt{constructor(e,t,s){c(this,"_taskClient");c(this,"getChatInitData",async()=>{const e=await this._asyncMsgSender.send({type:d.chatLoaded},3e4);if(e.data.enableDebugFeatures)try{console.log("Running hello world test...");const t=await async function(s){return(await We(Ce,new we({sendMessage:i=>{s.postMessage(i)},onReceiveMessage:i=>{const r=o=>{i(o.data)};return window.addEventListener("message",r),()=>{window.removeEventListener("message",r)}}})).testMethod({foo:"bar"},{timeoutMs:1e3})).result}(this._host);console.log("Hello world result:",t)}catch(t){console.error("Hello world error:",t)}return e.data});c(this,"reportWebviewClientEvent",e=>{this._asyncMsgSender.send({type:d.reportWebviewClientMetric,data:{webviewName:ve.chat,client_metric:e,value:1}})});c(this,"reportAgentSessionEvent",e=>{this._asyncMsgSender.sendToSidecar({type:S.reportAgentSessionEvent,data:e})});c(this,"reportAgentRequestEvent",e=>{this._asyncMsgSender.sendToSidecar({type:S.reportAgentRequestEvent,data:e})});c(this,"getSuggestions",async(e,t=!1)=>{const s={rootPath:"",relPath:e},n=this.findFiles(s,6),i=this.findRecentlyOpenedFiles(s,6),r=this.findFolders(s,3),o=this.findExternalSources(e,t),g=this.findRules(e,6),[h,u,l,p,m]=await Promise.all([F(n,[]),F(i,[]),F(r,[]),F(o,[]),F(g,[])]),y=(_,P)=>({...st(_),[P]:_}),R=[...h.map(_=>y(_,"file")),...l.map(_=>y(_,"folder")),...u.map(_=>y(_,"recentFile")),...p.map(_=>({label:_.name,name:_.name,id:_.id,externalSource:_})),...m.map(_=>({...nt(_),rule:_}))];if(this._flags.enablePersonalities){const _=this.getPersonalities(e);_.length>0&&R.push(..._)}return R});c(this,"getPersonalities",e=>{if(!this._flags.enablePersonalities)return[];if(e==="")return te;const t=e.toLowerCase();return te.filter(s=>{const n=s.personality.description.toLowerCase(),i=s.label.toLowerCase();return n.includes(t)||i.includes(t)})});c(this,"sendAction",e=>{this._host.postMessage({type:d.mainPanelPerformAction,data:e})});c(this,"showAugmentPanel",()=>{this._asyncMsgSender.send({type:d.showAugmentPanel})});c(this,"openConfirmationModal",async e=>(await this._asyncMsgSender.send({type:d.openConfirmationModal,data:e},1e9)).data.ok);c(this,"clearMetadataFor",e=>{this._host.postMessage({type:d.chatClearMetadata,data:e})});c(this,"resolvePath",async(e,t=void 0)=>{const s=await this._asyncMsgSender.send({type:d.resolveFileRequest,data:{...e,exactMatch:!0,maxResults:1,searchScope:t}},5e3);if(s.data)return s.data});c(this,"resolveSymbols",async(e,t)=>(await this._asyncMsgSender.send({type:d.findSymbolRequest,data:{query:e,searchScope:t}},3e4)).data);c(this,"getDiagnostics",async()=>(await this._asyncMsgSender.send({type:d.getDiagnosticsRequest},1e3)).data);c(this,"findFiles",async(e,t=12)=>(await this._asyncMsgSender.send({type:d.findFileRequest,data:{...e,maxResults:t}},5e3)).data);c(this,"findFolders",async(e,t=12)=>(await this._asyncMsgSender.send({type:d.findFolderRequest,data:{...e,maxResults:t}},5e3)).data);c(this,"findRecentlyOpenedFiles",async(e,t=12)=>(await this._asyncMsgSender.send({type:d.findRecentlyOpenedFilesRequest,data:{...e,maxResults:t}},5e3)).data);c(this,"findExternalSources",async(e,t=!1)=>this._flags.enableExternalSourcesInChat?t?[]:(await this._asyncMsgSender.send({type:d.findExternalSourcesRequest,data:{query:e,source_types:[]}},5e3)).data.sources??[]:[]);c(this,"findRules",async(e,t=12)=>this._flags.enableRules?(await this._asyncMsgSender.send({type:d.getRulesListRequest,data:{query:e,maxResults:t}},5e3)).data:[]);c(this,"openFile",e=>{this._host.postMessage({type:d.openFile,data:e})});c(this,"saveFile",e=>this._host.postMessage({type:d.saveFile,data:e}));c(this,"loadFile",e=>this._host.postMessage({type:d.loadFile,data:e}));c(this,"openMemoriesFile",()=>{this._host.postMessage({type:d.openMemoriesFile})});c(this,"createFile",(e,t)=>{this._host.postMessage({type:d.chatCreateFile,data:{code:e,relPath:t}})});c(this,"openScratchFile",async(e,t="shellscript")=>{await this._asyncMsgSender.send({type:d.openScratchFileRequest,data:{content:e,language:t}},1e4)});c(this,"resolveWorkspaceFileChunk",async e=>{try{return(await this._asyncMsgSender.send({type:d.resolveWorkspaceFileChunkRequest,data:e},5e3)).data}catch{return}});c(this,"smartPaste",e=>{this._host.postMessage({type:d.chatSmartPaste,data:e})});c(this,"getHydratedTask",async e=>this._taskClient.getHydratedTask(e));c(this,"updateHydratedTask",async(e,t)=>this._taskClient.updateHydratedTask(e,t));c(this,"setCurrentRootTaskUuid",e=>{this._taskClient.setCurrentRootTaskUuid(e)});c(this,"createTask",async(e,t,s)=>this._taskClient.createTask(e,t,s));c(this,"updateTask",async(e,t,s)=>this._taskClient.updateTask(e,t,s));c(this,"saveChat",async(e,t,s)=>this._asyncMsgSender.send({type:d.saveChat,data:{conversationId:e,chatHistory:t,title:s}}));c(this,"launchAutofixPanel",async(e,t,s)=>this._asyncMsgSender.send({type:d.chatLaunchAutofixPanel,data:{conversationId:e,iterationId:t,stage:s}}));c(this,"updateUserGuidelines",e=>{this._host.postMessage({type:d.updateUserGuidelines,data:e})});c(this,"updateWorkspaceGuidelines",e=>{this._host.postMessage({type:d.updateWorkspaceGuidelines,data:e})});c(this,"updateRuleFile",(e,t)=>{this._host.postMessage({type:d.updateRuleFile,data:{rulePath:e,content:t}})});c(this,"openSettingsPage",e=>{this._host.postMessage({type:d.openSettingsPage,data:e})});c(this,"_activeRetryStreams",new Map);c(this,"cancelChatStream",async e=>{var t;(t=this._activeRetryStreams.get(e))==null||t.cancel(),await this._asyncMsgSender.send({type:d.chatUserCancel,data:{requestId:e}},1e4)});c(this,"sendUserRating",async(e,t,s,n="")=>{const i={requestId:e,rating:s,note:n,mode:t},r={type:d.chatRating,data:i};return(await this._asyncMsgSender.send(r,3e4)).data});c(this,"triggerUsedChatMetric",()=>{this._host.postMessage({type:d.usedChat})});c(this,"createProject",e=>{this._host.postMessage({type:d.mainPanelCreateProject,data:{name:e}})});c(this,"openProjectFolder",()=>{this._host.postMessage({type:d.mainPanelPerformAction,data:"open-folder"})});c(this,"closeProjectFolder",()=>{this._host.postMessage({type:d.mainPanelPerformAction,data:"close-folder"})});c(this,"cloneRepository",()=>{this._host.postMessage({type:d.mainPanelPerformAction,data:"clone-repository"})});c(this,"grantSyncPermission",()=>{this._host.postMessage({type:d.mainPanelPerformAction,data:"grant-sync-permission"})});c(this,"callTool",async(e,t,s,n,i,r)=>{const o={type:d.callTool,data:{chatRequestId:e,toolUseId:t,name:s,input:n,chatHistory:i,conversationId:r}};return(await this._asyncMsgSender.send(o,0)).data});c(this,"cancelToolRun",async(e,t)=>{const s={type:d.cancelToolRun,data:{requestId:e,toolUseId:t}};await this._asyncMsgSender.send(s,0)});c(this,"checkSafe",async(e,t)=>{const s={type:d.toolCheckSafe,data:{name:e,input:t}};return(await this._asyncMsgSender.send(s,0)).data.isSafe});c(this,"closeAllToolProcesses",async()=>{await this._asyncMsgSender.sendToSidecar({type:V.closeAllToolProcesses},0)});c(this,"getToolIdentifier",async e=>{const t={type:V.getToolIdentifierRequest,data:{toolName:e}};return(await this._asyncMsgSender.sendToSidecar(t,0)).data});c(this,"executeCommand",async(e,t,s)=>{try{const n=await this._asyncMsgSender.send({type:d.chatAutofixExecuteCommandRequest,data:{iterationId:e,command:t,args:s}},6e5);return{output:n.data.output,returnCode:n.data.returnCode}}catch(n){throw console.error("[ExtensionClient] Execute command failed:",n),n}});c(this,"sendAutofixStateUpdate",async e=>{await this._asyncMsgSender.send({type:d.chatAutofixStateUpdate,data:e})});c(this,"autofixPlan",async(e,t)=>(await this._asyncMsgSender.send({type:d.chatAutofixPlanRequest,data:{command:e,steeringHistory:t}},6e4)).data.plan);c(this,"setChatMode",e=>{this._asyncMsgSender.send({type:d.chatModeChanged,data:{mode:e}})});c(this,"getAgentEditList",async(e,t)=>{const s={type:S.getEditListRequest,data:{fromTimestamp:e,toTimestamp:t}};return(await this._asyncMsgSender.sendToSidecar(s,3e4)).data});c(this,"hasChangesSince",async e=>{const t={type:S.getEditListRequest,data:{fromTimestamp:e,toTimestamp:Number.MAX_SAFE_INTEGER}};return(await this._asyncMsgSender.sendToSidecar(t,3e4)).data.edits.filter(s=>{var n,i;return((n=s.changesSummary)==null?void 0:n.totalAddedLines)||((i=s.changesSummary)==null?void 0:i.totalRemovedLines)}).length>0});c(this,"getToolCallCheckpoint",async e=>{const t={type:d.getToolCallCheckpoint,data:{requestId:e}};return(await this._asyncMsgSender.send(t,3e4)).data.checkpointNumber});c(this,"setCurrentConversation",e=>{this._asyncMsgSender.sendToSidecar({type:S.setCurrentConversation,data:{conversationId:e}})});c(this,"migrateConversationId",async(e,t)=>{await this._asyncMsgSender.sendToSidecar({type:S.migrateConversationId,data:{oldConversationId:e,newConversationId:t}},3e4)});c(this,"showAgentReview",(e,t,s,n=!0)=>{this._asyncMsgSender.sendToSidecar({type:S.chatReviewAgentFile,data:{qualifiedPathName:e,fromTimestamp:t,toTimestamp:s,retainFocus:n}})});c(this,"acceptAllAgentEdits",async()=>(await this._asyncMsgSender.sendToSidecar({type:S.chatAgentEditAcceptAll}),!0));c(this,"revertToTimestamp",async(e,t)=>(await this._asyncMsgSender.sendToSidecar({type:S.revertToTimestamp,data:{timestamp:e,qualifiedPathNames:t}}),!0));c(this,"getAgentOnboardingPrompt",async()=>(await this._asyncMsgSender.send({type:d.chatGetAgentOnboardingPromptRequest,data:{}},3e4)).data.prompt);c(this,"getAgentEditChangesByRequestId",async e=>{const t={type:S.getEditChangesByRequestIdRequest,data:{requestId:e}};return(await this._asyncMsgSender.sendToSidecar(t,3e4)).data});c(this,"getAgentEditContentsByRequestId",async e=>{const t={type:S.getAgentEditContentsByRequestId,data:{requestId:e}};return(await this._asyncMsgSender.sendToSidecar(t,3e4)).data});c(this,"triggerInitialOrientation",()=>{this._host.postMessage({type:d.triggerInitialOrientation})});c(this,"getWorkspaceInfo",async()=>{try{return(await this._asyncMsgSender.send({type:d.getWorkspaceInfoRequest},5e3)).data}catch(e){return console.error("Error getting workspace info:",e),{}}});c(this,"getRemoteAgentStatus",async()=>{try{return(await this._asyncMsgSender.send({type:d.getRemoteAgentStatus},5e3)).data}catch(e){return console.error("Error getting remote agent status:",e),{isRemoteAgentWindow:!1}}});c(this,"toggleCollapseUnchangedRegions",()=>{this._host.postMessage({type:d.toggleCollapseUnchangedRegions})});c(this,"checkAgentAutoModeApproval",async()=>(await this._asyncMsgSender.send({type:d.checkAgentAutoModeApproval},5e3)).data);c(this,"setAgentAutoModeApproved",async e=>{await this._asyncMsgSender.send({type:d.setAgentAutoModeApproved,data:e},5e3)});c(this,"checkHasEverUsedAgent",async()=>(await this._asyncMsgSender.sendToSidecar({type:S.checkHasEverUsedAgent},5e3)).data);c(this,"setHasEverUsedAgent",async e=>{await this._asyncMsgSender.sendToSidecar({type:S.setHasEverUsedAgent,data:e},5e3)});c(this,"getChatRequestIdeState",async()=>{const e={type:d.getChatRequestIdeStateRequest};return(await this._asyncMsgSender.send(e,3e4)).data});c(this,"reportError",e=>{this._host.postMessage({type:d.reportError,data:e})});this._host=e,this._asyncMsgSender=t,this._flags=s,this._taskClient=new at(t)}async*generateCommitMessage(){const e={type:d.generateCommitMessage},t=this._asyncMsgSender.stream(e,3e4,6e4);yield*U(t)}async*sendInstructionMessage(e,t){const s={instruction:e.request_message??"",selectedCodeDetails:t,requestId:e.request_id},n={type:d.chatInstructionMessage,data:s},i=this._asyncMsgSender.stream(n,3e4,6e4);yield*async function*(r){let o;try{for await(const g of r)o=g.data.requestId,yield{request_id:o,response_text:g.data.text,seen_state:A.unseen,status:f.sent};yield{request_id:o,seen_state:A.unseen,status:f.success}}catch{yield{request_id:o,seen_state:A.unseen,status:f.failed}}}(i)}async openGuidelines(e){this._host.postMessage({type:d.openGuidelines,data:e})}async*getExistingChatStream(e,t){if(!e.request_id)return;const s=t==null?void 0:t.flags.enablePreferenceCollection,n=s?1e9:6e4,i=s?1e9:3e5,r={type:d.chatGetStreamRequest,data:{requestId:e.request_id}},o=this._asyncMsgSender.stream(r,n,i);yield*U(o,this.reportError)}async*startChatStream(e,t){const s=t==null?void 0:t.flags.enablePreferenceCollection,n=s?1e9:6e4,i=s?1e9:3e5,r={type:d.chatUserMessage,data:e},o=this._asyncMsgSender.stream(r,n,i);yield*U(o,this.reportError)}async checkToolExists(e){return(await this._asyncMsgSender.send({type:d.checkToolExists,toolName:e},0)).exists}async saveImage(e,t){const s=Se(await J(e)),n=t??`${await Ae(await X(s))}.${e.name.split(".").at(-1)}`;return(await this._asyncMsgSender.send({type:d.chatSaveImageRequest,data:{filename:n,data:s}},1e4)).data}async loadImage(e){const t=await this._asyncMsgSender.send({type:d.chatLoadImageRequest,data:e},1e4),s=t.data?await X(t.data):void 0;if(!s)return;let n="application/octet-stream";const i=e.split(".").at(-1);i==="png"?n="image/png":i!=="jpg"&&i!=="jpeg"||(n="image/jpeg");const r=new File([s],e,{type:n});return await J(r)}async deleteImage(e){await this._asyncMsgSender.send({type:d.chatDeleteImageRequest,data:e},1e4)}async*startChatStreamWithRetry(e,t,s){const n=new Je(e,t,(i,r)=>this.startChatStream(i,r),(s==null?void 0:s.maxRetries)??5,4e3,s==null?void 0:s.flags);this._activeRetryStreams.set(e,n);try{yield*n.getStream()}finally{this._activeRetryStreams.delete(e)}}async getSubscriptionInfo(){return await this._asyncMsgSender.send({type:d.getSubscriptionInfo},5e3)}}async function*U(a,e=()=>{}){let t;try{for await(const s of a){if(t=s.data.requestId,s.data.error)return yield{request_id:t,seen_state:A.unseen,status:f.failed,display_error_message:s.data.error.displayErrorMessage,isRetriable:s.data.error.isRetriable};yield{request_id:t,response_text:s.data.text,workspace_file_chunks:s.data.workspaceFileChunks,structured_output_nodes:it(s.data.nodes),seen_state:A.unseen,status:f.sent}}yield{request_id:t,seen_state:A.unseen,status:f.success}}catch(s){e({originalRequestId:t||"",sanitizedMessage:s instanceof Error?s.message:String(s),stackTrace:s instanceof Error&&s.stack||"",diagnostics:[{key:"error_class",value:"Extension-WebView Error"}]}),yield{request_id:t,seen_state:A.unseen,status:f.failed}}}async function F(a,e){try{return await a}catch(t){return console.warn(`Error while resolving promise: ${t}`),e}}function it(a){if(!a)return a;let e=!1;return a.filter(t=>t.type!==ne.TOOL_USE||!e&&(e=!0,!0))}var b=(a=>(a[a.unknown=0]="unknown",a[a.new=1]="new",a[a.checkingSafety=2]="checkingSafety",a[a.runnable=3]="runnable",a[a.running=4]="running",a[a.completed=5]="completed",a[a.error=6]="error",a[a.cancelling=7]="cancelling",a[a.cancelled=8]="cancelled",a))(b||{});function Zt(a){return a.requestId+";"+a.toolUseId}function Qt(a){const[e,t]=a.split(";");return{requestId:e,toolUseId:t}}function Yt(a,e){return a==null?e:typeof a=="string"?a:e}class ce{constructor(e){c(this,"_applyingFilePaths",K([]));c(this,"_appliedFilePaths",K([]));this._asyncMsgSender=e}get applyingFilePaths(){let e=[];return this._applyingFilePaths.subscribe(t=>{e=t})(),e}get appliedFilePaths(){let e=[];return this._appliedFilePaths.subscribe(t=>{e=t})(),e}async getDiffExplanation(e,t,s=3e4){try{return(await this._asyncMsgSender.send({type:d.diffExplanationRequest,data:{changedFiles:e,apikey:t}},s)).data.explanation}catch(n){return console.error("Failed to get diff explanation:",n),[]}}async groupChanges(e,t=!1,s){try{return(await this._asyncMsgSender.send({type:d.diffGroupChangesRequest,data:{changedFiles:e,changesById:t,apikey:s}})).data.groupedChanges}catch(n){return console.error("Failed to group changes:",n),[]}}async getDescriptions(e,t){try{return(await this._asyncMsgSender.send({type:d.diffDescriptionsRequest,data:{groupedChanges:e,apikey:t}})).data.explanation}catch(s){return console.error("Failed to get descriptions:",s),[]}}async applyChanges(e,t,s){this._applyingFilePaths.update(n=>[...n.filter(i=>i!==e),e]);try{(await this._asyncMsgSender.send({type:d.applyChangesRequest,data:{path:e,originalCode:t,newCode:s}},3e4)).data.success&&this._appliedFilePaths.update(n=>[...n.filter(i=>i!==e),e])}catch(n){console.error("applyChanges error",n)}finally{this._applyingFilePaths.update(n=>n.filter(i=>i!==e))}}}c(ce,"key","remoteAgentsDiffOpsModel");async function es(a,e){a.length&&e&&await Promise.all(a.map(async t=>await e(t.path,t.originalCode,t.newCode)))}function G(a){return a.sort((e,t)=>{const s=new Date(e.updated_at||e.started_at);return new Date(t.updated_at||t.started_at).getTime()-s.getTime()})}class j extends Error{constructor(e){super(e),this.name="StreamRetryExhaustedError"}}class ot{constructor(e,t,s,n,i=5,r=4e3){c(this,"_isCancelled",!1);c(this,"streamId");this.agentId=e,this.lastProcessedSequenceId=t,this.startStreamFn=s,this.cancelStreamFn=n,this.maxRetries=i,this.baseDelay=r,this.streamId=crypto.randomUUID()}get isCancelled(){return this._isCancelled}async cancel(){this._isCancelled=!0,await this.cancelStreamFn(this.streamId)}async*getStream(){let e=0;for(;!this._isCancelled;){const t=this.startStreamFn(this.agentId,this.streamId,this.lastProcessedSequenceId);try{for await(const s of t){if(this._isCancelled)return;e=0,yield s}return}catch(s){const n=s instanceof Error?s.message:String(s);if(n===be&&(this._isCancelled=!0),this._isCancelled)return;if(e++,e>this.maxRetries)throw new j(`Failed after ${this.maxRetries} attempts: ${n}`);let i=this.baseDelay*2**(e-1);n===Me?i=0:yield{errorMessage:"There was an error connecting to the remote agent.",retryAt:new Date(Date.now()+i)},console.warn(`Retrying remote agent history stream in ${i/1e3} seconds... (Attempt ${e} of ${this.maxRetries})`),await new Promise(r=>setTimeout(r,i));continue}}}}class de{constructor(e){c(this,"_msgBroker");c(this,"_activeRetryStreams",new Map);this._msgBroker=e}hasActiveHistoryStream(e){return this._activeRetryStreams.has(e)}getActiveHistoryStream(e){return this._activeRetryStreams.get(e)}get activeHistoryStreams(){return this._activeRetryStreams}async sshToRemoteAgent(e){const t=await this._msgBroker.send({type:d.remoteAgentSshRequest,data:{agentId:e}},1e4);return!!t.data.success||(console.error("Failed to connect to remote agent:",t.data.error),!1)}async deleteRemoteAgent(e){return(await this._msgBroker.send({type:d.deleteRemoteAgentRequest,data:{agentId:e}},1e4)).data.success}showRemoteAgentHomePanel(){this._msgBroker.postMessage({type:d.showRemoteAgentHomePanel})}closeRemoteAgentHomePanel(){this._msgBroker.postMessage({type:d.closeRemoteAgentHomePanel})}async getRemoteAgentNotificationEnabled(e){return(await this._msgBroker.send({type:d.getRemoteAgentNotificationEnabledRequest,data:{agentIds:e}})).data}async setRemoteAgentNotificationEnabled(e,t){await this._msgBroker.send({type:d.setRemoteAgentNotificationEnabled,data:{agentId:e,enabled:t}})}async deleteRemoteAgentNotificationEnabled(e){await this._msgBroker.send({type:d.deleteRemoteAgentNotificationEnabled,data:{agentId:e}})}async notifyRemoteAgentReady(e){await this._msgBroker.send({type:d.remoteAgentNotifyReady,data:{agentId:e}})}showRemoteAgentDiffPanel(e){this._msgBroker.postMessage({type:d.showRemoteAgentDiffPanel,data:e})}closeRemoteAgentDiffPanel(){this._msgBroker.postMessage({type:d.closeRemoteAgentDiffPanel})}async getRemoteAgentChatHistory(e,t,s=1e4){return await this._msgBroker.send({type:d.getRemoteAgentChatHistoryRequest,data:{agentId:e,lastProcessedSequenceId:t}},s)}async sendRemoteAgentChatRequest(e,t,s=9e4){return this._msgBroker.send({type:d.remoteAgentChatRequest,data:{agentId:e,requestDetails:t,timeoutMs:s}},s)}async interruptRemoteAgent(e,t=1e4){return await this._msgBroker.send({type:d.remoteAgentInterruptRequest,data:{agentId:e}},t)}async createRemoteAgent(e,t,s,n,i,r,o=1e4){return await this._msgBroker.send({type:d.createRemoteAgentRequest,data:{prompt:e,workspaceSetup:t,setupScript:s,isSetupScriptAgent:n,modelId:i,remoteAgentCreationMetrics:r}},o)}async getRemoteAgentOverviews(e=1e4){return await this._msgBroker.send({type:d.getRemoteAgentOverviewsRequest},e)}async listSetupScripts(e=5e3){return await this._msgBroker.send({type:d.listSetupScriptsRequest},e)}async saveSetupScript(e,t,s,n=5e3){return await this._msgBroker.send({type:d.saveSetupScriptRequest,data:{name:e,content:t,location:s}},n)}async deleteSetupScript(e,t,s=5e3){return await this._msgBroker.send({type:d.deleteSetupScriptRequest,data:{name:e,location:t}},s)}async renameSetupScript(e,t,s,n=5e3){return await this._msgBroker.send({type:d.renameSetupScriptRequest,data:{oldName:e,newName:t,location:s}},n)}async getRemoteAgentWorkspaceLogs(e,t,s,n=1e4){return await this._msgBroker.send({type:d.remoteAgentWorkspaceLogsRequest,data:{agentId:e,lastProcessedStep:t,lastProcessedSequenceId:s}},n)}async saveLastRemoteAgentSetup(e,t,s){return await this._msgBroker.send({type:d.saveLastRemoteAgentSetupRequest,data:{lastRemoteAgentGitRepoUrl:e,lastRemoteAgentGitBranch:t,lastRemoteAgentSetupScript:s}})}async getLastRemoteAgentSetup(){return await this._msgBroker.send({type:d.getLastRemoteAgentSetupRequest})}async*startRemoteAgentHistoryStream(e,t,s,n=6e4,i=3e5){const r={type:d.remoteAgentHistoryStreamRequest,data:{streamId:t,agentId:e,lastProcessedSequenceId:s}},o=this._msgBroker.stream(r,n,i);for await(const g of o)yield g.data}async*startRemoteAgentHistoryStreamWithRetry(e,t,s=5,n=4e3){var r;const i=new ot(e,t,(o,g,h)=>this.startRemoteAgentHistoryStream(o,g,h),o=>this._closeRemoteAgentHistoryStream(o),s,n);(r=this._activeRetryStreams.get(e))==null||r.cancel(),this._activeRetryStreams.set(e,i);try{yield*i.getStream()}finally{i.isCancelled||this._activeRetryStreams.delete(e)}}cancelRemoteAgentHistoryStream(e){const t=this._activeRetryStreams.get(e);t&&(t.cancel(),this._activeRetryStreams.delete(e))}async _closeRemoteAgentHistoryStream(e){await this._msgBroker.send({type:d.cancelRemoteAgentHistoryStreamRequest,data:{streamId:e}})}cancelAllRemoteAgentHistoryStreams(){this._activeRetryStreams.forEach(e=>{e.cancel()}),this._activeRetryStreams.clear()}dispose(){this.cancelAllRemoteAgentHistoryStreams()}async getPinnedAgentsFromStore(){try{return(await this._msgBroker.send({type:d.getRemoteAgentPinnedStatusRequest,data:{}})).data}catch(e){return console.error("Failed to get pinned agents from store:",e),{}}}async savePinnedAgentToStore(e,t){try{await this._msgBroker.send({type:d.setRemoteAgentPinnedStatus,data:{agentId:e,isPinned:t}})}catch(s){console.error("Failed to save pinned agent to store:",s)}}async deletePinnedAgentFromStore(e){try{await this._msgBroker.send({type:d.deleteRemoteAgentPinnedStatus,data:{agentId:e}})}catch(t){console.error("Failed to delete pinned agent from store:",t)}}async openDiffInBuffer(e,t,s){return await this._msgBroker.send({type:d.openDiffInBuffer,data:{oldContents:e,newContents:t,filePath:s}})}async pauseRemoteAgentWorkspace(e){return await this._msgBroker.send({type:d.remoteAgentPauseRequest,data:{agentId:e}},3e4)}async resumeRemoteAgentWorkspace(e){return await this._msgBroker.send({type:d.remoteAgentResumeRequest,data:{agentId:e}},9e4)}async reportRemoteAgentEvent(e){await this._msgBroker.send({type:d.reportRemoteAgentEvent,data:e})}}c(de,"key","remoteAgentsClient");function ct(a,e){if(a.length===0)return e;if(e.length===0)return a;const t=[];let s=0,n=0;for(;s<a.length&&n<e.length;){const i=a[s].sequence_id,r=e[n].sequence_id;r!==void 0?i!==void 0?i<r?(t.push(a[s]),s++):i>r?(t.push(e[n]),n++):(t.push(e[n]),s++,n++):(console.warn("Existing history has an exchange with an undefined sequence ID"),s++):(console.warn("New history has an exchange with an undefined sequence ID"),n++)}for(;s<a.length;)t.push(a[s]),s++;for(;n<e.length;)t.push(e[n]),n++;return t}class se{constructor(e){c(this,"_pollingTimers",new Map);c(this,"_pollingInterval");c(this,"_failedAttempts",0);c(this,"_lastSuccessfulFetch",0);this._config=e,this._pollingInterval=e.defaultInterval}start(e){e&&this._pollingTimers.has(e)?this.stop(e):!e&&this._pollingTimers.has("global")&&this.stop("global"),this.refresh(e);const t=setInterval(()=>{this.refresh(e)},this._pollingInterval);e?this._pollingTimers.set(e,t):this._pollingTimers.set("global",t)}stop(e){if(e){const t=this._pollingTimers.get(e);t&&(clearInterval(t),this._pollingTimers.delete(e))}else for(const[t,s]of this._pollingTimers.entries())clearInterval(s),this._pollingTimers.delete(t)}async refresh(e){try{const t=await this._config.refreshFn(e);return this._failedAttempts=0,this._lastSuccessfulFetch=Date.now(),this._pollingInterval=this._config.defaultInterval,this._config.stopCondition&&e&&this._config.stopCondition(t,e)&&this.stop(e),t}catch{return this._failedAttempts++,this._failedAttempts>3?this._pollingInterval=1e4:this._pollingInterval=Math.min(1e3*Math.pow(2,this._failedAttempts),1e4),null}}isPolling(e){return e?this._pollingTimers.has(e):this._pollingTimers.size>0}get timeSinceLastSuccessfulFetch(){return Date.now()-this._lastSuccessfulFetch}get failedAttempts(){return this._failedAttempts}resetFailedAttempts(){this._failedAttempts=0}}class dt{constructor(e,t){c(this,"_state",{agentOverviews:[],agentConversations:new Map,agentLogs:new Map,maxRemoteAgents:0,maxActiveRemoteAgents:0,overviewError:void 0,conversationError:void 0,logsError:void 0,isOverviewsLoading:!1,isConversationLoading:!1,isLogsLoading:!1,logPollFailedCount:0});c(this,"_loggingMaxRetries",8);c(this,"_overviewsPollingManager");c(this,"_logsPollingManager");c(this,"_isInitialOverviewFetch",!0);c(this,"_stateUpdateSubscribers",new Set);this._flagsModel=e,this._remoteAgentsClient=t,this._overviewsPollingManager=new se({defaultInterval:5e3,refreshFn:async()=>this.refreshAgentOverviews()}),this._logsPollingManager=new se({defaultInterval:1e3,refreshFn:async s=>{if(!s)throw new Error("Agent ID is required for logs polling");return this.refreshAgentLogs(s)},stopCondition:(s,n)=>{if(!n)return!0;if(!this._state.agentOverviews.find(o=>o.remote_agent_id===n))return this._state.logPollFailedCount++,this._state.logPollFailedCount>this._loggingMaxRetries&&(this._state.logPollFailedCount=0,!0);const i=this.state.agentLogs.get(n),r=i==null?void 0:i.steps.at(-1);return(r==null?void 0:r.step_description)==="Indexing"&&r.status===z.success}}),this._flagsModel.subscribe(s=>{const n=this._overviewsPollingManager.isPolling()||this._remoteAgentsClient.activeHistoryStreams.size>0||this._logsPollingManager.isPolling(),i=s.enableBackgroundAgents;i&&!n?this.startStateUpdates():!i&&n&&this.stopStateUpdates()})}get state(){return this._state}startStateUpdates(e){var t,s;this._flagsModel.enableBackgroundAgents&&(e?(e.overviews&&this._overviewsPollingManager.start(),(t=e.conversation)!=null&&t.agentId&&this.startConversationStream(e.conversation.agentId),(s=e.logs)!=null&&s.agentId&&(this._state.logPollFailedCount=0,this._logsPollingManager.start(e.logs.agentId))):this._overviewsPollingManager.start())}stopStateUpdates(e){var t,s;if(!e)return this._overviewsPollingManager.stop(),this._logsPollingManager.stop(),void this.stopAllConversationStreams();e.overviews&&this._overviewsPollingManager.stop(),(t=e.conversation)!=null&&t.agentId&&this.stopConversationStream(e.conversation.agentId),(s=e.logs)!=null&&s.agentId&&this._logsPollingManager.stop(e.logs.agentId)}async refreshCurrentAgent(e){this.startConversationStream(e)}async refreshAgentOverviews(){this._isInitialOverviewFetch&&(this._state.overviewError=void 0,this._state.isOverviewsLoading=!0,this._isInitialOverviewFetch=!1);try{const e=await this._remoteAgentsClient.getRemoteAgentOverviews();if(e.data.error)throw new Error(e.data.error);e.data.maxRemoteAgents!==void 0&&(this._state.maxRemoteAgents=e.data.maxRemoteAgents),e.data.maxActiveRemoteAgents!==void 0&&(this._state.maxActiveRemoteAgents=e.data.maxActiveRemoteAgents);const t=G(e.data.overviews);return this._state.agentOverviews=t,this._state.overviewError=void 0,this._state.isOverviewsLoading=!1,this.notifySubscribers({type:"overviews",data:t}),t}catch(e){this._state.isOverviewsLoading=!1;const t=e instanceof Error?e.message:String(e);return this._isInitialOverviewFetch||this._state.agentOverviews.length===0?this._state.overviewError={errorMessage:t}:(console.warn("Background refresh failed:",e),this._overviewsPollingManager.timeSinceLastSuccessfulFetch>3e4&&this._overviewsPollingManager.failedAttempts>1&&(this._state.overviewError||(this._state.overviewError={errorMessage:`Using cached data. Refresh failed: ${t}`}))),this.notifySubscribers({type:"overviews",data:this._state.agentOverviews,error:this._state.overviewError}),this._state.agentOverviews}}async refreshAgentLogs(e){try{const t=this.state.agentLogs.get(e);let s,n;const i=t==null?void 0:t.steps.at(-1);i?(s=i.step_number,n=i.step_number===0?0:i.sequence_id+1):(s=0,n=0);const r=await this._remoteAgentsClient.getRemoteAgentWorkspaceLogs(e,s,n);if(!r.data.workspaceSetupStatus)return;const o=r.data.workspaceSetupStatus;if(o.steps.length===0)return t;const g=function(u,l){return{steps:[...u.steps,...l.steps].sort((p,m)=>p.step_number!==m.step_number?p.step_number-m.step_number:p.sequence_id-m.sequence_id)}}(t??{steps:[]},o),h={steps:g.steps.reduce((u,l)=>{const p=u[u.length-1];return p&&p.step_number===l.step_number?(p.status!==z.success&&(p.status=l.status),p.step_number===0?p.logs=l.logs:p.sequence_id<l.sequence_id&&(p.logs+=`
${l.logs}`,p.sequence_id=l.sequence_id)):u.push(l),u},[])};return this._state.agentLogs.set(e,h),this._state.logsError=void 0,this.notifySubscribers({type:"logs",agentId:e,data:h}),h}catch(t){const s=t instanceof Error?t.message:String(t);return this._state.logsError={errorMessage:s},this.notifySubscribers({type:"logs",agentId:e,data:this._state.agentLogs.get(e)||{steps:[]},error:this._state.logsError}),this._state.agentLogs.get(e)}}onStateUpdate(e){return this._stateUpdateSubscribers.add(e),e({type:"all",data:this._state}),()=>{this._stateUpdateSubscribers.delete(e)}}dispose(){this.stopStateUpdates(),this._stateUpdateSubscribers.clear()}notifySubscribers(e){this._stateUpdateSubscribers.forEach(t=>t(e))}async startConversationStream(e){this._remoteAgentsClient.hasActiveHistoryStream(e)&&this.stopConversationStream(e);const t=this._state.agentConversations.get(e)||[];let s=0;t.length>0&&(s=Math.max(...t.filter(n=>n.sequence_id!==void 0).map(n=>n.sequence_id||0))-1,s<0&&(s=0)),this._state.isConversationLoading=!0,this._state.conversationError=void 0,this.notifySubscribers({type:"conversation",agentId:e,data:t,error:this._state.conversationError});try{const n=this._remoteAgentsClient.startRemoteAgentHistoryStreamWithRetry(e,s);(async()=>{var i;try{for await(const r of n){if(!this._remoteAgentsClient.hasActiveHistoryStream(e)||(i=this._remoteAgentsClient.getActiveHistoryStream(e))!=null&&i.isCancelled)break;this.processHistoryStreamUpdate(e,r)}}catch(r){if(this._remoteAgentsClient.hasActiveHistoryStream(e)){let o;r instanceof j?(o=`Failed to connect: ${r.message}`,console.error(`Stream retry exhausted for agent ${e}: ${r.message}`)):(o=r instanceof Error?r.message:String(r),console.error(`Stream error for agent ${e}: ${o}`)),this._state.conversationError={errorMessage:o},this._state.isConversationLoading=!1;const g=this._state.agentConversations.get(e)||[];this.notifySubscribers({type:"conversation",agentId:e,data:g,error:this._state.conversationError})}}finally{this._state.isConversationLoading=!1}})()}catch(n){let i;i=n instanceof j?`Failed to connect: ${n.message}`:n instanceof Error?n.message:String(n),this._state.conversationError={errorMessage:i},this._state.isConversationLoading=!1,this.notifySubscribers({type:"conversation",agentId:e,data:t,error:this._state.conversationError})}}stopConversationStream(e){this._remoteAgentsClient.cancelRemoteAgentHistoryStream(e)}stopAllConversationStreams(){this._remoteAgentsClient.cancelAllRemoteAgentHistoryStreams()}processHistoryStreamUpdate(e,t){var s;if((n=>n.updates!==void 0)(t)){this._state.conversationError=void 0;for(const n of t.updates){const i=this._state.agentConversations.get(e)||[];switch(n.type){case O.AGENT_HISTORY_EXCHANGE:if(n.exchange){const r=ct(i,[n.exchange]);this._state.agentConversations.set(e,r)}break;case O.AGENT_HISTORY_EXCHANGE_UPDATE:if(n.exchange_update){const r=n.exchange_update.sequence_id,o=i.findIndex(g=>g.sequence_id===r);if(o>=0){const g=i[o],h=((s=g.exchange)==null?void 0:s.response_text)||"";g.exchange.response_text=h+n.exchange_update.appended_text;const u=n.exchange_update.appended_nodes;if(u&&u.length>0){const p=g.exchange.response_nodes??[];g.exchange.response_nodes=[...p,...u]}const l=n.exchange_update.appended_changed_files;if(l&&l.length>0){const p=g.changed_files??[];g.changed_files=[...p,...l]}}}break;case O.AGENT_HISTORY_AGENT_STATUS:if(n.agent){const r=this._state.agentOverviews.findIndex(o=>o.remote_agent_id===e);r>=0?this._state.agentOverviews[r]=n.agent:(this._state.agentOverviews.push(n.agent),this._state.agentOverviews=G(this._state.agentOverviews)),this.notifySubscribers({type:"overviews",data:this._state.agentOverviews,error:this._state.overviewError})}}}this._state.isConversationLoading=!1,this.notifySubscribers({type:"conversation",agentId:e,data:this._state.agentConversations.get(e)||[],error:this._state.conversationError})}else{this.state.conversationError=t;const n=this._state.agentConversations.get(e)||[];this.notifySubscribers({type:"conversation",agentId:e,data:n,error:this._state.conversationError})}}}var E=(a=>(a.chatRequestFailed="chat_request_failed",a.messageTimeout="message_timeout",a.agentFailed="agent_failed",a))(E||{});const H="This agent is in a failed state and can no longer accept messages";class gt{constructor({msgBroker:e,isActive:t,flagsModel:s,host:n,stateModel:i,chatModel:r}){c(this,"_state",{isActive:!1,isPanelFocused:!1,currentAgentId:void 0,currentConversation:void 0,currentAgent:void 0,agentOverviews:[],chatConversations:[],localAgentConversations:[],isLoading:!1,isCurrentAgentDetailsLoading:!1,lastSuccessfulOverviewFetch:0,failedRefreshAttempts:0,maxRemoteAgents:0,maxActiveRemoteAgents:0,isDiffPanelOpen:!1,diffPanelAgentId:void 0,focusedFilePath:null,isCreatingAgent:!1,error:void 0,agentThreadsError:void 0,agentLogsError:void 0,agentChatHistoryError:void 0,remoteAgentCreationError:null,newAgentDraft:null,notificationSettings:{},pinnedAgents:{},setCurrentAgent:this.setCurrentAgent.bind(this),clearCurrentAgent:this.clearCurrentAgent.bind(this),sendMessage:this.sendMessage.bind(this),interruptAgent:this.interruptAgent.bind(this),createRemoteAgent:this.createRemoteAgent.bind(this),createRemoteAgentFromDraft:this.createRemoteAgentFromDraft.bind(this),deleteAgent:this.deleteAgent.bind(this),setNewAgentDraft:this.setNewAgentDraft.bind(this),setRemoteAgentCreationError:this.setRemoteAgentCreationError.bind(this),hasFetchedOnce:!1,showRemoteAgentDiffPanel:this.showRemoteAgentDiffPanel.bind(this),closeRemoteAgentDiffPanel:this.closeRemoteAgentDiffPanel.bind(this),setIsCreatingAgent:this.setIsCreatingAgent.bind(this),toggleAgentPinned:this.toggleAgentPinned.bind(this),setPinnedAgents:this.setPinnedAgents.bind(this),pauseRemoteAgentWorkspace:this.pauseRemoteAgentWorkspace.bind(this),resumeRemoteAgentWorkspace:this.resumeRemoteAgentWorkspace.bind(this)});c(this,"_agentConversations",new Map);c(this,"_initialPrompts",new Map);c(this,"_agentSetupLogsCache",new Map);c(this,"_creationMetrics");c(this,"_preloadedDiffExplanations",new Map);c(this,"maxCacheEntries",10);c(this,"maxCacheSizeBytes",10485760);c(this,"_diffOpsModel");c(this,"subscribers",new Set);c(this,"agentSetupLogs");c(this,"_remoteAgentsClient");c(this,"_stateModel");c(this,"_extensionClient");c(this,"_flagsModel");c(this,"_cachedUrls",new Map);c(this,"_externalRefCount",0);c(this,"_chatModel");c(this,"_pendingMessageTracking",new Map);c(this,"_agentSendMessageErrors",new Map);c(this,"sendMessageTimeoutMs",9e4);c(this,"dispose",()=>{this._stateModel.dispose(),this._remoteAgentsClient.dispose(),this._agentConversations.clear(),this._agentSetupLogsCache.clear(),this._preloadedDiffExplanations.clear(),this._cachedUrls.clear(),this._pendingMessageTracking.forEach(e=>{e.forEach(t=>clearTimeout(t.timeout))}),this._pendingMessageTracking.clear(),this._agentSendMessageErrors.clear(),this.subscribers.clear()});this._state.isActive=t,this._flagsModel=s,this._diffOpsModel=new ce(e),this._remoteAgentsClient=new de(e),this._chatModel=r,this._extensionClient=new rt(n,e,s),this._stateModel=i||new dt(this._flagsModel,this._remoteAgentsClient),this._stateModel.onStateUpdate(this.handleStateUpdate.bind(this)),t&&this._stateModel.startStateUpdates(),this.loadPinnedAgentsFromStore()}_setChatModel(e){this._chatModel=e}handleOverviewsUpdate(e){var r,o;const t=e.data,s=this._state.agentOverviews,n=t;if(this._state.currentAgentId&&(this._state.currentAgent=this._state.agentOverviews.find(g=>g.remote_agent_id===this._state.currentAgentId)),this._state.currentAgentId){const g=this._state.currentAgentId,h=g?(r=s.find(l=>l.remote_agent_id===g))==null?void 0:r.status:void 0,u=g?(o=n.find(l=>l.remote_agent_id===g))==null?void 0:o.status:void 0;if(u!==h){if(u===w.agentRunning)this._agentSendMessageErrors.delete(g);else if(u===w.agentFailed){const l={type:E.agentFailed,errorMessage:H,canRetry:!1};this._agentSendMessageErrors.set(g,l)}}}this.maybeSendNotifications(n,s);const i=G(n);this._state.agentOverviews=i,this._state.hasFetchedOnce=!0,this._state.agentThreadsError=e.error,this._state.lastSuccessfulOverviewFetch=e.error?this._state.lastSuccessfulOverviewFetch:Date.now(),i.findIndex(g=>g.remote_agent_id===this._state.currentAgentId)===-1&&this.clearCurrentAgent()}handleConversationUpdate(e){var t;if(e.agentId===this._state.currentAgentId){const s={exchanges:e.data,lastFetched:new Date};this._agentConversations.set(e.agentId,s),this._state.currentConversation=s,this._state.agentChatHistoryError=e.error;const n=this._agentSendMessageErrors.get(e.agentId);if(n!=null&&n.failedExchangeId){const i=n.failedExchangeId;((t=this._state.currentConversation)==null?void 0:t.exchanges.some(o=>o.exchange.request_id===i))||this._agentSendMessageErrors.delete(e.agentId)}this._state.isCurrentAgentDetailsLoading=!1}}handleLogsUpdate(e){e.agentId===this._state.currentAgentId&&(this.agentSetupLogs=e.data,this._agentSetupLogsCache.set(e.agentId,e.data))}handleStateUpdate(e){switch(this._state.maxRemoteAgents=this._stateModel.state.maxRemoteAgents,this._state.maxActiveRemoteAgents=this._stateModel.state.maxActiveRemoteAgents,e.type){case"overviews":this.handleOverviewsUpdate(e);break;case"conversation":this.handleConversationUpdate(e);break;case"logs":this.handleLogsUpdate(e);break;case"all":this.handleOverviewsUpdate({type:"overviews",data:e.data.agentOverviews,error:e.data.overviewError}),e.data.agentConversations.forEach((t,s)=>{this._agentConversations.set(s,{exchanges:t,lastFetched:new Date})}),e.data.agentLogs.forEach((t,s)=>{t&&(this._agentSetupLogsCache.set(s,t),s===this._state.currentAgentId&&(this.agentSetupLogs=t))}),this._state.hasFetchedOnce=!0,this._state.agentThreadsError=e.data.overviewError,this._state.agentChatHistoryError=e.data.conversationError,this._state.agentLogsError=e.data.logsError}this.currentAgentId&&this.checkForHistoryErrors(this.currentAgentId),this.notifySubscribers()}subscribe(e){return this.subscribers.add(e),e(this),()=>{this.subscribers.delete(e)}}notifySubscribers(){this.subscribers.forEach(e=>e(this))}showRemoteAgentDiffPanel(e){const t=this._state.currentAgentId;if(t&&e.changedFiles.length>0&&e.turnIdx===-1&&e.isShowingAggregateChanges){const s=`${t}-${this.generateChangedFilesHash(e.changedFiles)}`,n=this._preloadedDiffExplanations.get(s);if(n)return n.lastAccessed=Date.now(),this._preloadedDiffExplanations.set(s,n),this._remoteAgentsClient.showRemoteAgentDiffPanel({...e,preloadedExplanation:n.explanation}),this._state.isDiffPanelOpen=!0,this._state.diffPanelAgentId=t,void this.notifySubscribers()}this._remoteAgentsClient.showRemoteAgentDiffPanel(e),this._state.isDiffPanelOpen=!0,this._state.diffPanelAgentId=t,this.notifySubscribers()}closeRemoteAgentDiffPanel(){this._remoteAgentsClient.closeRemoteAgentDiffPanel(),this._state.isDiffPanelOpen=!1,this._state.diffPanelAgentId=void 0,this.notifySubscribers()}get flagsModel(){return this._flagsModel}_getChatHistory(e){const t=this._agentConversations.get(e);if(!t)return[];const s=this.isAgentRunning(e);return t.exchanges.map(({exchange:n},i)=>{const r=n.request_id.startsWith("pending-");return{seen_state:A.seen,structured_request_nodes:n.request_nodes??[],status:r||i===t.exchanges.length-1&&s?f.sent:f.success,request_message:n.request_message,response_text:r?"":n.response_text,structured_output_nodes:n.response_nodes??[],request_id:n.request_id??`remote-agent-${i}`}})}getCurrentChatHistory(){const e=this.agentSetupLogs;return this.currentAgentId&&!e&&(this.agentSetupLogs={steps:[]},this._stateModel.startStateUpdates({logs:{agentId:this.currentAgentId}})),this._getChatHistory(this.currentAgentId??"")}getToolStates(){var o,g,h;const e=new Map,t=new Set,s=new Map;(o=this.currentConversation)==null||o.exchanges.forEach(u=>{var l,p;(l=u.exchange.response_nodes)==null||l.forEach(m=>{m.tool_use&&t.add(m.tool_use.tool_use_id)}),(p=u.exchange.request_nodes)==null||p.forEach(m=>{m.type===B.TOOL_RESULT&&m.tool_result_node&&s.set(m.tool_result_node.tool_use_id,m.tool_result_node)})});const n=(g=this.currentConversation)==null?void 0:g.exchanges[this.currentConversation.exchanges.length-1];let i=0,r=null;return(h=n==null?void 0:n.exchange.response_nodes)==null||h.forEach(u=>{var l;u.id>i&&(i=u.id,r=(l=u.tool_use)!=null&&l.tool_use_id?u.tool_use.tool_use_id:null)}),t.forEach(u=>{const l=s.get(u);if(l)e.set(u,{phase:l.is_error?b.error:b.completed,result:{isError:l.is_error,text:l.content},requestId:"",toolUseId:u});else{const p=this.isCurrentAgentRunning;u===r?e.set(u,{phase:p?b.running:b.cancelled,requestId:"",toolUseId:u}):e.set(u,{phase:b.cancelled,requestId:"",toolUseId:u})}}),e}getLastToolUseState(){const e=this.getToolStates(),t=[...e.keys()].pop();return e.get(t??"")??{phase:b.unknown}}getToolUseState(e){const t=e;return this.getToolStates().get(t)??{phase:b.completed,requestId:"",toolUseId:e}}async setCurrentAgent(e){this._state.currentAgentId&&this._stateModel.stopStateUpdates({conversation:{agentId:this._state.currentAgentId},logs:{agentId:this._state.currentAgentId}}),this._state.currentAgentId=e,this._state.isCurrentAgentDetailsLoading=!!e,e&&this._agentSetupLogsCache.has(e)?this.agentSetupLogs=this._agentSetupLogsCache.get(e):this.agentSetupLogs=void 0,e&&this.checkForHistoryErrors(e),this.notifySubscribers(),e&&(this._stateModel.startStateUpdates({conversation:{agentId:e},logs:{agentId:e}}),this.preloadDiffExplanation(e))}clearCurrentAgent(){this._state.currentAgentId&&this._stateModel.stopStateUpdates({conversation:{agentId:this._state.currentAgentId},logs:{agentId:this._state.currentAgentId}}),this._state.currentAgentId=void 0,this.agentSetupLogs=void 0,this.notifySubscribers()}async preloadDiffExplanation(e){const t=this._agentConversations.get(e);if(!t||t.exchanges.length===0)return;const s=Ie(t.exchanges);if(s.length===0)return;const n=`${e}-${this.generateChangedFilesHash(s)}`;if(this._preloadedDiffExplanations.get(n)||s.length>12)return;let i=0;if(s.forEach(r=>{var o,g;i+=(((o=r.old_contents)==null?void 0:o.length)||0)+(((g=r.new_contents)==null?void 0:g.length)||0)}),!(i>512e3))try{const r=await this._diffOpsModel.getDiffExplanation(s,void 0,6e4);if(r&&r.length>0){const o=this.generateChangedFilesHash(s),g=`${e}-${o}`;this._preloadedDiffExplanations.set(g,{explanation:r,changedFiles:s,userPrompt:this.getUserMessagePrecedingTurn(t.exchanges,0),timestamp:Date.now(),lastAccessed:Date.now(),changedFilesHash:o,turnIdx:-1}),this.manageCacheSize()}}catch(r){console.error("Failed to preload diff explanation:",r)}}getUserMessagePrecedingTurn(e,t){return e.length===0||t<0||t>=e.length?"":e[t].exchange.request_message||""}generateChangedFilesHash(e){const t=e.map(s=>{var n,i;return{oldPath:s.old_path,newPath:s.new_path,oldSize:((n=s.old_contents)==null?void 0:n.length)||0,newSize:((i=s.new_contents)==null?void 0:i.length)||0,oldHash:this.simpleHash(s.old_contents||""),newHash:this.simpleHash(s.new_contents||"")}});return this.simpleHash(JSON.stringify(t))}simpleHash(e){let t=0;for(let s=0;s<e.length;s++)t=(t<<5)-t+e.charCodeAt(s),t|=0;return t.toString(36)}manageCacheSize(){if(this._preloadedDiffExplanations.size<=this.maxCacheEntries)return;const e=Array.from(this._preloadedDiffExplanations.entries()).map(([s,n])=>({key:s,value:n,accessTime:n.lastAccessed||n.timestamp})).sort((s,n)=>s.accessTime-n.accessTime);let t=0;for(e.forEach(s=>{const n=JSON.stringify(s.value.explanation).length,i=s.value.changedFiles.reduce((r,o)=>{var g,h;return r+(((g=o.old_contents)==null?void 0:g.length)||0)+(((h=o.new_contents)==null?void 0:h.length)||0)},0);t+=n+i});e.length>0&&(e.length>this.maxCacheEntries||t>this.maxCacheSizeBytes);){const s=e.shift();if(s){this._preloadedDiffExplanations.delete(s.key);const n=JSON.stringify(s.value.explanation).length,i=s.value.changedFiles.reduce((r,o)=>{var g,h;return r+(((g=o.old_contents)==null?void 0:g.length)||0)+(((h=o.new_contents)==null?void 0:h.length)||0)},0);t-=n+i}}}async sendMessage(e,t){const s=this._state.currentAgentId;if(!s)return this._state.error="No active remote agent",this.notifySubscribers(),!1;const n=this._state.agentOverviews.find(r=>r.remote_agent_id===s);if((n==null?void 0:n.status)===w.agentFailed){const r={type:E.agentFailed,errorMessage:H,canRetry:!1};return this._agentSendMessageErrors.set(s,r),this.notifySubscribers(),!1}let i;this._state.isLoading=!0,this._state.error=void 0,this.notifySubscribers();try{const r=this._agentConversations.get(s)||{exchanges:[],lastFetched:new Date},o=(this.getfinalSequenceId(s)||0)+1;i="pending-"+Date.now();const g={exchange:{request_message:e,response_text:"",request_id:i,response_nodes:[],request_nodes:[]},changed_files:[],sequence_id:o};r.exchanges.push(g),this._agentConversations.set(s,r),this._state.currentConversation=r,this.notifySubscribers(),this.setupMessageTimeout(s,i);const h={request_nodes:[{id:1,type:B.TEXT,text_node:{content:e}}],model_id:t},u=await this._remoteAgentsClient.sendRemoteAgentChatRequest(s,h,this.sendMessageTimeoutMs);if(u.data.error)throw new Error(u.data.error);return this.clearMessageTimeout(s,i),this._state.currentAgentId&&setTimeout(()=>{this.preloadDiffExplanation(this._state.currentAgentId)},0),await this._stateModel.refreshCurrentAgent(s),await this._stateModel.refreshAgentOverviews(),this.preloadDiffExplanation(s),!0}catch(r){i&&this.clearMessageTimeout(s,i);const o={type:E.chatRequestFailed,errorMessage:`There was an error sending your message. Please try again. Agent ID: ${s}. ${r}`,canRetry:!0,failedExchangeId:i};return this._agentSendMessageErrors.set(s,o),console.error("Failed to send message:",r),this.notifySubscribers(),!1}finally{this._state.isLoading=!1,this.notifySubscribers()}}setupMessageTimeout(e,t){const s=setTimeout(()=>{this.handleMessageTimeout(e,t)},this.sendMessageTimeoutMs);this._pendingMessageTracking.has(e)||this._pendingMessageTracking.set(e,new Map),this._pendingMessageTracking.get(e).set(t,{timeout:s,timestamp:Date.now()})}clearMessageTimeout(e,t){const s=this._pendingMessageTracking.get(e);if(s){const n=s.get(t);n&&(clearTimeout(n.timeout),s.delete(t),s.size===0&&this._pendingMessageTracking.delete(e))}}async handleMessageTimeout(e,t){const s=this._pendingMessageTracking.get(e);s&&(s.delete(t),s.size===0&&this._pendingMessageTracking.delete(e));const n=this._state.agentOverviews.find(o=>o.remote_agent_id===e);if((n==null?void 0:n.status)===w.agentRunning)return;const i=this._agentConversations.get(e);if(!i||!i.exchanges.find(o=>o.exchange.request_id===t))return;const r={type:E.messageTimeout,errorMessage:`There was an error sending your message. Please try again. Agent ID: ${e}`,canRetry:!0,failedExchangeId:t};this._agentSendMessageErrors.set(e,r);try{await this._remoteAgentsClient.interruptRemoteAgent(e),await this._stateModel.refreshAgentOverviews()}catch(o){console.error("Failed to interrupt agent after timeout:",o)}this.notifySubscribers()}removeOptimisticExchange(e,t){const s=this._agentConversations.get(e);s&&(s.exchanges=s.exchanges.filter(n=>n.exchange.request_id!==t),this._agentConversations.set(e,s),e===this._state.currentAgentId&&(this._state.currentConversation=s))}async retryFailedMessage(e,t){const s=this._agentConversations.get(e);if(!s)return!1;const n=s.exchanges.find(i=>i.exchange.request_id===t);return!!n&&(this.removeOptimisticExchange(e,t),this._agentSendMessageErrors.delete(e),this.notifySubscribers(),this.sendMessage(n.exchange.request_message))}async interruptAgent(){const e=this._state.currentAgentId;if(!e)return this._state.error="No active remote agent",void this.notifySubscribers();this._state.isLoading=!0,this._state.error=void 0,this.notifySubscribers();try{await this._remoteAgentsClient.interruptRemoteAgent(e),await this._stateModel.refreshCurrentAgent(e),await this._stateModel.refreshAgentOverviews()}catch(t){this._state.error=t instanceof Error?t.message:String(t)}finally{this._state.isLoading=!1,this.notifySubscribers()}}async createRemoteAgent(e,t,s,n,i){var r;if(!e||!e.trim())return this._state.error="Cannot create a remote agent with an empty prompt",void this.notifySubscribers();this.agentSetupLogs=void 0,this._state.isLoading=!0,this._state.error=void 0,this.notifySubscribers();try{const o=await this._remoteAgentsClient.createRemoteAgent(e,t,s,n,i,this._creationMetrics);if(o.data.error)throw new Error(o.data.error);if(o.data.agentId&&o.data.success)return this._initialPrompts.set(o.data.agentId,e),await this.setNotificationEnabled(o.data.agentId,((r=this.newAgentDraft)==null?void 0:r.enableNotification)??!0),await this.setCurrentAgent(o.data.agentId),o.data.agentId;throw new Error("Failed to create remote agent: No agent ID returned")}catch(o){throw this._state.error=o instanceof Error?o.message:String(o),this.notifySubscribers(),o}finally{this._state.isLoading=!1,this.notifySubscribers()}}async createRemoteAgentFromDraft(e,t){var i,r;if(this.setRemoteAgentCreationError(null),this.agentSetupLogs=void 0,!e||!e.trim())return void this.setRemoteAgentCreationError("Cannot create a remote agent with an empty prompt");const s=this._state.newAgentDraft;if(!s)return void this.setRemoteAgentCreationError("No workspace selected. Please select a workspace first.");if(s.isDisabled)return void this.setRemoteAgentCreationError("Cannot create agent with current workspace selection. Please resolve the issues with your workspace selection.");if(!s.commitRef||!s.selectedBranch)return void this.setRemoteAgentCreationError("No workspace selected. Please select a workspace first.");const n={starting_files:s.commitRef};this._state.isLoading=!0,this.notifySubscribers();try{const o=s.isSetupScriptAgent||((i=s.setupScript)==null?void 0:i.isGenerateOption)===!0;let g=o||(r=s.setupScript)==null?void 0:r.content;if(s.setupScript&&!o){const h=(await this.listSetupScripts()).find(u=>u.path===s.setupScript.path);h&&(g=h.content)}try{return await this.createRemoteAgent(e,n,g,o,t)}catch(h){let u="Failed to create remote agent. Please try again.";return h instanceof Error&&(h.message.includes("too large")||h.message.includes("413")?u="Repository or selected files are too large. Please select a smaller repository or branch.":h.message.includes("timeout")||h.message.includes("504")?u="Request timed out. The repository might be too large or the server is busy.":h.message.includes("rate limit")||h.message.includes("429")?u="Rate limit exceeded. Please try again later.":h.message.includes("unauthorized")||h.message.includes("401")?u="Authentication failed. Please check your GitHub credentials.":h.message.includes("not found")||h.message.includes("404")?u="Repository or branch not found. Please check your selection.":h.message.includes("bad request")||h.message.includes("400")?u="Invalid request. Please check your workspace setup and try again.":h.message.length>0&&(u=`Failed to create remote agent: ${h.message}`)),void this.setRemoteAgentCreationError(u)}}finally{this._state.isLoading=!1,this.notifySubscribers()}}async deleteAgent(e,t=!1){if(!(this._chatModel&&!t&&!await this._chatModel.extensionClient.openConfirmationModal({title:"Delete Remote Agent",message:"Are you sure you want to delete this remote agent?",confirmButtonText:"Delete",cancelButtonText:"Cancel"}))){this._state.isLoading=!0,this._state.error=void 0,this.notifySubscribers();try{if(!await this._remoteAgentsClient.deleteRemoteAgent(e))return this._state.error="Failed to delete remote agent",void this.notifySubscribers();this._agentConversations.delete(e),this._agentSetupLogsCache.delete(e),this._state.agentOverviews=this._state.agentOverviews.filter(s=>s.remote_agent_id!==e),this.removeNotificationEnabled(e),this._state.currentAgentId===e&&this.clearCurrentAgent()}catch(s){this._state.error=s instanceof Error?s.message:String(s)}finally{this._state.isLoading=!1,this.notifySubscribers()}}}async sshToRemoteAgent(e){this._state.isLoading=!0,this._state.error=void 0,this.notifySubscribers();try{return e.workspace_status!==ue.workspaceRunning&&(await this._remoteAgentsClient.resumeRemoteAgentWorkspace(e.remote_agent_id),await new Promise(t=>setTimeout(t,5e3))),await this._remoteAgentsClient.sshToRemoteAgent(e.remote_agent_id)}catch(t){return this._state.error=t instanceof Error?t.message:String(t),this.notifySubscribers(),!1}finally{this._state.isLoading=!1,this.notifySubscribers()}}async maybeSendNotifications(e,t){const s=new Map(t.map(i=>[i.remote_agent_id,i])),n=await this._remoteAgentsClient.getRemoteAgentNotificationEnabled(e.map(i=>i.remote_agent_id));e.forEach(i=>{const r=s.get(i.remote_agent_id),o=n[i.remote_agent_id],g=(r==null?void 0:r.status)===w.agentRunning,h=i.status===w.agentIdle||i.status===w.agentFailed,u=i.remote_agent_id!==this._state.currentAgentId,l=this._state.isPanelFocused;o&&g&&h&&(u||!l)&&this._remoteAgentsClient.notifyRemoteAgentReady(i.remote_agent_id)})}async setNotificationEnabled(e,t){await this._remoteAgentsClient.setRemoteAgentNotificationEnabled(e,t),this._state={...this._state,notificationSettings:{...this._state.notificationSettings,[e]:t}},this.notifySubscribers()}async removeNotificationEnabled(e){await this._remoteAgentsClient.deleteRemoteAgentNotificationEnabled(e);const{[e]:t,...s}=this._state.notificationSettings;this._state={...this._state,notificationSettings:s},this.notifySubscribers()}get hasFetchedOnce(){return this._state.hasFetchedOnce}get focusedFilePath(){return this._state.focusedFilePath}setFocusedFilePath(e){this._state.focusedFilePath=e,this.notifySubscribers()}handleMessageFromExtension(e){switch(e.data.type){case d.diffViewFileFocus:return this.setFocusedFilePath(e.data.data.filePath.replace(/^\/+/,"")),!0;case d.showRemoteAgentDiffPanel:return this._state.isDiffPanelOpen=!0,!0;case d.closeRemoteAgentDiffPanel:return this._state.isDiffPanelOpen=!1,!0;default:return!1}}get currentAgentId(){return this._state.currentAgentId}get currentConversation(){return this._agentConversations.get(this._state.currentAgentId??"")??void 0}_getAgentExchanges(e){var t;return((t=this._agentConversations.get(e))==null?void 0:t.exchanges)||[]}get currentExchanges(){const e=this._state.currentAgentId;return e?this._getAgentExchanges(e):[]}get currentStatus(){var t;const e=this._state.currentAgentId;return e&&((t=this._state.agentOverviews.find(s=>s.remote_agent_id===e))==null?void 0:t.status)||w.agentIdle}get currentAgent(){const e=this._state.currentAgentId;return e?this._state.agentOverviews.find(t=>t.remote_agent_id===e):void 0}get agentOverviews(){return this._state.agentOverviews}get isLoading(){return this._state.isLoading}get isCurrentAgentDetailsLoading(){return this._state.isCurrentAgentDetailsLoading}get lastSuccessfulOverviewFetch(){return this._state.lastSuccessfulOverviewFetch}get error(){return this._state.error}get agentThreadsError(){return this._state.agentThreadsError}get agentChatHistoryError(){return this._state.agentChatHistoryError}clearSendMessageError(){this._state.currentAgentId&&(this._agentSendMessageErrors.delete(this._state.currentAgentId),this.notifySubscribers())}get sendMessageError(){return this._agentSendMessageErrors.get(this._state.currentAgentId??"")??void 0}checkForHistoryErrors(e){var i;const t=this._getAgentExchanges(e);if(((i=this._state.agentOverviews.find(r=>r.remote_agent_id===e))==null?void 0:i.status)===w.agentFailed){const r={type:E.agentFailed,errorMessage:H,canRetry:!1};return this._agentSendMessageErrors.set(e,r),void this.notifySubscribers()}const s=t.length>0&&t[t.length-1].exchange.request_id.startsWith("pending-"),n=this._agentSendMessageErrors.get(e);if(s&&!n){const r=t[t.length-1].exchange.request_id,o=this._pendingMessageTracking.get(e),g=o==null?void 0:o.get(r);if(g&&Date.now()-g.timestamp>this.sendMessageTimeoutMs){const h={type:E.messageTimeout,errorMessage:`There was an error sending your message. Please try again. Agent ID: ${e}`,canRetry:!0,failedExchangeId:r};this._agentSendMessageErrors.set(e,h),this.clearMessageTimeout(e,r),this.notifySubscribers()}}}isAgentRunning(e){const t=this._state.agentOverviews.find(o=>o.remote_agent_id===e),s=!(!t||t.status!==w.agentRunning&&t.status!==w.agentStarting),n=this._getAgentExchanges(e),i=this._agentSendMessageErrors.get(e),r=n.length>0&&n[n.length-1].exchange.request_id.startsWith("pending-")&&!i;return s||r}get isCurrentAgentRunning(){return!!this._state.currentAgentId&&this.isAgentRunning(this._state.currentAgentId)}get maxRemoteAgents(){return this._state.maxRemoteAgents}get maxActiveRemoteAgents(){return this._state.maxActiveRemoteAgents}getInitialPrompt(e){return this._initialPrompts.get(e)}clearInitialPrompt(e){this._initialPrompts.delete(e)}get notificationSettings(){return this._state.notificationSettings}get pinnedAgents(){return this._state.pinnedAgents}getfinalSequenceId(e){var n;const t=this._agentConversations.get(e),s=t==null?void 0:t.exchanges;if(s)return((n=s[s.length-1])==null?void 0:n.sequence_id)??void 0}async listSetupScripts(){try{return(await this._remoteAgentsClient.listSetupScripts()).data.scripts}catch(e){return this._state.error=e instanceof Error?e.message:String(e),this.notifySubscribers(),[]}}async saveSetupScript(e,t,s){try{const n=await this._remoteAgentsClient.saveSetupScript(e,t,s);return n.data.success||(this._state.error=n.data.error||"Failed to save setup script",this.notifySubscribers()),n.data}catch(n){return this._state.error=n instanceof Error?n.message:String(n),this.notifySubscribers(),{success:!1,error:n instanceof Error?n.message:String(n)}}}async deleteSetupScript(e,t){try{const s=await this._remoteAgentsClient.deleteSetupScript(e,t);return s.data.success||(this._state.error=s.data.error||"Failed to delete setup script",this.notifySubscribers()),s.data}catch(s){return this._state.error=s instanceof Error?s.message:String(s),this.notifySubscribers(),{success:!1,error:s instanceof Error?s.message:String(s)}}}async renameSetupScript(e,t,s){try{const n=await this._remoteAgentsClient.renameSetupScript(e,t,s);return n.data.success||(this._state.error=n.data.error||"Failed to rename setup script",this.notifySubscribers()),n.data}catch(n){return this._state.error=n instanceof Error?n.message:String(n),this.notifySubscribers(),{success:!1,error:n instanceof Error?n.message:String(n)}}}get isActive(){return this._state.isActive}setIsActive(e){this._state.isActive=e,e?this._stateModel.startStateUpdates():this._externalRefCount===0?this._stateModel.stopStateUpdates():this._externalRefCount>0&&this._stateModel.startStateUpdates(),this.notifySubscribers()}setExternalRefCount(e){e!==this._externalRefCount&&(this._externalRefCount=e,e!==0||this._state.isActive?e>0&&!this._state.isActive&&this._stateModel.startStateUpdates():this._stateModel.stopStateUpdates())}get isPanelFocused(){return this._state.isPanelFocused}setIsPanelFocused(e){this._state.isPanelFocused=e,this.notifySubscribers()}optimisticallyClearAgentUpdates(e){var s;const t=this._state.agentOverviews.findIndex(n=>n.remote_agent_id===e);if(t!==-1&&this._state.agentOverviews[t].has_updates){const n=[...this._state.agentOverviews];n[t]={...n[t],has_updates:!1},this._state.agentOverviews=n,((s=this._state.currentAgent)==null?void 0:s.remote_agent_id)===e&&(this._state.currentAgent={...this._state.currentAgent,has_updates:!1}),this.notifySubscribers()}}setRemoteAgentCreationError(e){this._state.remoteAgentCreationError=e,this.notifySubscribers()}get isDiffPanelOpen(){return this._state.isDiffPanelOpen}get diffPanelAgentId(){return this._state.diffPanelAgentId}get remoteAgentCreationError(){return this._state.remoteAgentCreationError}setNewAgentDraft(e){this._state.newAgentDraft=e,this.notifySubscribers()}setCreationMetrics(e){this._creationMetrics=e}get creationMetrics(){return this._creationMetrics}refreshAgentChatHistory(e){this._stateModel.refreshCurrentAgent(e)}get newAgentDraft(){return this._state.newAgentDraft}setIsCreatingAgent(e){this._state.isCreatingAgent=e,this.notifySubscribers()}get isCreatingAgent(){return this._state.isCreatingAgent}async showRemoteAgentHomePanel(){await this._remoteAgentsClient.showRemoteAgentHomePanel()}async closeRemoteAgentHomePanel(){await this._remoteAgentsClient.closeRemoteAgentHomePanel()}async saveLastRemoteAgentSetup(e,t,s){try{await this._remoteAgentsClient.saveLastRemoteAgentSetup(e,t,s)}catch(n){console.error("Failed to save last remote agent setup:",n)}}async getLastRemoteAgentSetup(){try{return(await this._remoteAgentsClient.getLastRemoteAgentSetup()).data}catch(e){return console.error("Failed to get last remote agent setup:",e),{lastRemoteAgentGitRepoUrl:null,lastRemoteAgentGitBranch:null,lastRemoteAgentSetupScript:null}}}async loadPinnedAgentsFromStore(){try{const e=await this._remoteAgentsClient.getPinnedAgentsFromStore();this._state.pinnedAgents=e,this.notifySubscribers()}catch(e){console.error("Failed to load pinned agents from store:",e)}}async toggleAgentPinned(e,t){if(!e)return this._state.pinnedAgents;t=t??!1;try{if(this._state.pinnedAgents={...this._state.pinnedAgents,[e]:!t},t){const{[e]:s,...n}=this._state.pinnedAgents;this._state.pinnedAgents=n,await this._remoteAgentsClient.deletePinnedAgentFromStore(e)}else await this._remoteAgentsClient.savePinnedAgentToStore(e,!0);return this.notifySubscribers(),await this._remoteAgentsClient.getPinnedAgentsFromStore()}catch(s){return console.error("Failed to toggle pinned status for remote agent:",s),this._state.pinnedAgents}}async getConversationUrl(e){var g;const t=this._cachedUrls.get(e),s=this._agentConversations.get(e),n=(s==null?void 0:s.exchanges.length)??0;if(t&&s&&t[0]===n)return t[1];const i=this._getChatHistory(e).map(h=>({...h,request_id:h.request_id||"",request_message:h.request_message,response_text:h.response_text||""}));if(i.length===0)throw new Error("No chat history to share");const r=await this._extensionClient.saveChat(e,i,`Remote Agent ${e}`);if(!r.data)throw new Error("Failed to create URL");const o=(g=r.data)==null?void 0:g.url;return o&&this._cachedUrls.set(e,[n,o]),o}async refreshAgentThreads(){this._state.agentThreadsError=void 0,this._state.isLoading=!0,this.notifySubscribers();try{await this._stateModel.refreshAgentOverviews()}catch(e){console.error("Failed to refresh agent threads:",e)}finally{this._state.isLoading=!1,this.notifySubscribers()}}async openDiffInBuffer(e,t,s){await this._remoteAgentsClient.openDiffInBuffer(e,t,s)}async pauseRemoteAgentWorkspace(e){await this._remoteAgentsClient.pauseRemoteAgentWorkspace(e)}async resumeRemoteAgentWorkspace(e){await this._remoteAgentsClient.resumeRemoteAgentWorkspace(e)}async reportRemoteAgentEvent(e){await this._remoteAgentsClient.reportRemoteAgentEvent(e)}setPinnedAgents(e){this._state.pinnedAgents={...e},this.notifySubscribers()}}c(gt,"key","remoteAgentsModel");function ts(){const a=Ee("chatModel");return a||console.warn("ChatModel not found in context"),a}function ss(a){return xe("chatModel",a),a}function lt(a){let e,t,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},a[0]],n={};for(let i=0;i<s.length;i+=1)n=N(n,s[i]);return{c(){e=$("svg"),t=new Pe(!0),this.h()},l(i){e=Te(i,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var r=Fe(e);t=ke(r,!0),r.forEach(W),this.h()},h(){t.a=null,Z(e,n)},m(i,r){qe(i,e,r),t.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M441 103c9.4 9.4 9.4 24.6 0 33.9L177 401c-9.4 9.4-24.6 9.4-33.9 0L7 265c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.4 33.9 0l119 119L407 103c9.4-9.4 24.6-9.4 33.9 0z"/>',e)},p(i,[r]){Z(e,n=Le(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},1&r&&i[0]]))},i:L,o:L,d(i){i&&W(e)}}}function ht(a,e,t){return a.$$set=s=>{t(0,e=N(N({},e),Q(s)))},[e=Q(e)]}class ns extends ae{constructor(e){super(),re(this,e,ht,lt,ie,{})}}function ut(a){let e,t;return{c(){e=$("svg"),t=$("path"),C(t,"fill-rule","evenodd"),C(t,"clip-rule","evenodd"),C(t,"d","M12 13C12.5523 13 13 12.5523 13 12V3C13 2.44771 12.5523 2 12 2H3C2.44771 2 2 2.44771 2 3V6.5C2 6.77614 2.22386 7 2.5 7C2.77614 7 3 6.77614 3 6.5V3H12V12H8.5C8.22386 12 8 12.2239 8 12.5C8 12.7761 8.22386 13 8.5 13H12ZM9 6.5C9 6.5001 9 6.50021 9 6.50031V6.50035V9.5C9 9.77614 8.77614 10 8.5 10C8.22386 10 8 9.77614 8 9.5V7.70711L2.85355 12.8536C2.65829 13.0488 2.34171 13.0488 2.14645 12.8536C1.95118 12.6583 1.95118 12.3417 2.14645 12.1464L7.29289 7H5.5C5.22386 7 5 6.77614 5 6.5C5 6.22386 5.22386 6 5.5 6H8.5C8.56779 6 8.63244 6.01349 8.69139 6.03794C8.74949 6.06198 8.80398 6.09744 8.85143 6.14433C8.94251 6.23434 8.9992 6.35909 8.99999 6.49708L8.99999 6.49738"),C(t,"fill","currentColor"),C(e,"class",a[0]),C(e,"width","15"),C(e,"height","15"),C(e,"viewBox","0 0 15 15"),C(e,"fill","none"),C(e,"xmlns","http://www.w3.org/2000/svg")},m(s,n){De(s,e,n),Oe(e,t)},p(s,[n]){1&n&&C(e,"class",s[0])},i:L,o:L,d(s){s&&W(e)}}}function pt(a,e,t){let{class:s=""}=e;return a.$$set=n=>{"class"in n&&t(0,s=n.class)},[s]}class as extends ae{constructor(e){super(),re(this,e,pt,ut,ie,{class:0})}}export{Yt as $,Ge as A,Bt as B,ns as C,Ze as D,f as E,Qe as F,Ye as G,Wt as H,Jt as I,tt as J,st as K,nt as L,kt as M,Ht as N,as as O,Gt as P,Kt as Q,gt as R,A as S,b as T,Xt as U,Nt as V,jt as W,zt as X,Vt as Y,$t as Z,ce as _,Et as a,es as a0,Tt as b,Pt as c,bt as d,Mt as e,It as f,ze as g,xt as h,Ut as i,Ft as j,qt as k,ts as l,Ot as m,E as n,Dt as o,Lt as p,Qt as q,Zt as r,ss as s,Rt as t,je as u,et as v,rt as w,Ct as x,Xe as y,Ke as z};
