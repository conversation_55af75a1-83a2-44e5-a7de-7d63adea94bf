#!/usr/bin/env python3
"""
EXTRACTEUR SYNCHRONISÉ AUGMENT - BASÉ SUR LES VRAIS TIMINGS
Utilise les paramètres de configuration réels d'Augment :
- timeoutMs: 800ms
- maxWaitMs: 1600ms
"""

import os
import time
import threading
from datetime import datetime
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import json
import sqlite3

class ExtracteurSynchroniseAugment(FileSystemEventHandler):
    def __init__(self):
        self.workspace_path = r"C:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\a35ba43ef26792e61fe787a234121806"
        self.db_path = os.path.join(self.workspace_path, "state.vscdb")
        self.wal_path = self.db_path + "-wal"
        self.output_file = "messages_synchronises_augment.txt"
        
        self.running = True
        self.observer = None
        self.lock = threading.Lock()
        self.dernier_nombre_messages = 0
        self.numero_message_global = 1
        
        # TIMINGS BASÉS SUR LA CONFIGURATION AUGMENT
        self.AUGMENT_TIMEOUT_MS = 800      # timeoutMs par défaut
        self.AUGMENT_MAX_WAIT_MS = 1600    # maxWaitMs avec retry
        self.LECTURE_OPTIMALE_MS = 900     # 100ms après timeout
        self.LECTURE_FINALE_MS = 1700      # 100ms après maxWait
        
        self.init_output_file()
        self.charger_etat_initial()
    
    def init_output_file(self):
        """Initialise le fichier de sortie synchronisé"""
        with open(self.output_file, 'w', encoding='utf-8') as f:
            f.write("EXTRACTEUR SYNCHRONISÉ AUGMENT - TIMINGS RÉELS\n")
            f.write(f"Démarré: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("=" * 80 + "\n")
            f.write("🎯 Basé sur la configuration réelle d'Augment :\n")
            f.write(f"   • timeoutMs: {self.AUGMENT_TIMEOUT_MS}ms\n")
            f.write(f"   • maxWaitMs: {self.AUGMENT_MAX_WAIT_MS}ms\n")
            f.write(f"   • Lecture optimale: {self.LECTURE_OPTIMALE_MS}ms\n")
            f.write(f"   • Lecture finale: {self.LECTURE_FINALE_MS}ms\n")
            f.write("=" * 80 + "\n\n")
    
    def charger_etat_initial(self):
        """Charge l'état initial avec extraction respectueuse"""
        try:
            messages_existants = self.extraire_messages_respectueux()
            self.dernier_nombre_messages = len(messages_existants)
            self.numero_message_global = self.dernier_nombre_messages + 1
            
            print(f"📊 État initial: {self.dernier_nombre_messages} messages existants")
            print(f"🔢 Prochain numéro: #{self.numero_message_global}")
            
        except Exception as e:
            print(f"❌ Erreur chargement: {e}")
            self.dernier_nombre_messages = 0
            self.numero_message_global = 1
    
    def extraire_messages_respectueux(self):
        """Extraction respectueuse sans perturber Augment"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=2.0)
            
            # Configuration respectueuse (pas de forçage)
            conn.execute("PRAGMA journal_mode=WAL")
            conn.execute("PRAGMA synchronous=NORMAL")  # Pas FULL
            
            cursor = conn.cursor()
            
            cursor.execute("SELECT key, value FROM ItemTable WHERE key LIKE '%augment-chat%'")
            rows = cursor.fetchall()
            
            for key, value in rows:
                if 'webviewView.augment-chat' in key:
                    try:
                        if isinstance(value, bytes):
                            value_str = value.decode('utf-8')
                        else:
                            value_str = str(value)
                        
                        main_data = json.loads(value_str)
                        webview_data = json.loads(main_data['webviewState'])
                        
                        conversations = webview_data.get('conversations', {})
                        current_conv_id = webview_data.get('currentConversationId')
                        
                        if current_conv_id and current_conv_id in conversations:
                            chat_history = conversations[current_conv_id].get('chatHistory', [])
                            conn.close()
                            return chat_history
                            
                    except Exception as e:
                        print(f"   ⚠️ Erreur parsing: {e}")
                        continue
            
            conn.close()
            return []
            
        except Exception as e:
            print(f"❌ Erreur extraction respectueuse: {e}")
            return []
    
    def on_modified(self, event):
        """Détection WAL + synchronisation avec les timings Augment"""
        if event.is_directory:
            return
        
        if os.path.basename(event.src_path) == "state.vscdb-wal":
            timestamp_detection = datetime.now().strftime('%H:%M:%S.%f')[:-3]
            print(f"⚡ [{timestamp_detection}] WAL MODIFIÉ - SYNCHRONISATION AUGMENT...")
            
            # Lancer l'extraction synchronisée
            threading.Thread(target=self.extraction_synchronisee, args=(timestamp_detection,)).start()
    
    def extraction_synchronisee(self, timestamp_detection):
        """Extraction synchronisée avec les timings réels d'Augment"""
        try:
            print(f"   ⏳ Attente {self.LECTURE_OPTIMALE_MS}ms (après timeout Augment)...")
            time.sleep(self.LECTURE_OPTIMALE_MS / 1000.0)  # 900ms
            
            # Première tentative après timeout
            messages_actuels = self.extraire_messages_respectueux()
            nombre_actuel = len(messages_actuels)
            
            timestamp_lecture1 = datetime.now().strftime('%H:%M:%S.%f')[:-3]
            delai1 = (datetime.strptime(timestamp_lecture1, '%H:%M:%S.%f') - 
                     datetime.strptime(timestamp_detection, '%H:%M:%S.%f')).total_seconds() * 1000
            
            print(f"   📊 Lecture 1 ({delai1:.0f}ms): {self.dernier_nombre_messages} → {nombre_actuel} messages")
            
            if nombre_actuel > self.dernier_nombre_messages:
                # SUCCESS à la première lecture !
                nouveaux_count = nombre_actuel - self.dernier_nombre_messages
                print(f"🎯 [{timestamp_lecture1}] {nouveaux_count} MESSAGES SYNCHRONISÉS (lecture optimale) !")
                
                nouveaux_messages = messages_actuels[self.dernier_nombre_messages:]
                self.ecrire_messages_synchronises(nouveaux_messages, timestamp_lecture1, delai1, "OPTIMALE")
                self.dernier_nombre_messages = nombre_actuel
                return
            
            # Si pas de nouveaux messages, attendre jusqu'au maxWait
            delai_supplementaire = (self.LECTURE_FINALE_MS - self.LECTURE_OPTIMALE_MS) / 1000.0  # 800ms
            print(f"   ⏳ Attente supplémentaire {delai_supplementaire*1000:.0f}ms (jusqu'à maxWait)...")
            time.sleep(delai_supplementaire)
            
            # Deuxième tentative après maxWait
            messages_actuels = self.extraire_messages_respectueux()
            nombre_actuel = len(messages_actuels)
            
            timestamp_lecture2 = datetime.now().strftime('%H:%M:%S.%f')[:-3]
            delai2 = (datetime.strptime(timestamp_lecture2, '%H:%M:%S.%f') - 
                     datetime.strptime(timestamp_detection, '%H:%M:%S.%f')).total_seconds() * 1000
            
            print(f"   📊 Lecture 2 ({delai2:.0f}ms): {self.dernier_nombre_messages} → {nombre_actuel} messages")
            
            if nombre_actuel > self.dernier_nombre_messages:
                # SUCCESS à la deuxième lecture
                nouveaux_count = nombre_actuel - self.dernier_nombre_messages
                print(f"🎯 [{timestamp_lecture2}] {nouveaux_count} MESSAGES SYNCHRONISÉS (lecture finale) !")
                
                nouveaux_messages = messages_actuels[self.dernier_nombre_messages:]
                self.ecrire_messages_synchronises(nouveaux_messages, timestamp_lecture2, delai2, "FINALE")
                self.dernier_nombre_messages = nombre_actuel
            else:
                print(f"   📝 Aucun nouveau message après {delai2:.0f}ms (stable à {nombre_actuel})")
                
        except Exception as e:
            print(f"   ❌ Erreur extraction synchronisée: {e}")
    
    def ecrire_messages_synchronises(self, nouveaux_messages, timestamp, delai, type_lecture):
        """Écriture des messages avec métadonnées de synchronisation"""
        with self.lock:
            try:
                with open(self.output_file, 'a', encoding='utf-8') as f:
                    for message in nouveaux_messages:
                        role = message.get('role', 'unknown')
                        content = message.get('content', '')
                        
                        # Formatage du rôle avec emojis
                        if role == 'user':
                            role_display = '👤 UTILISATEUR'
                        elif role == 'assistant':
                            role_display = '🤖 ASSISTANT'
                        elif role == 'tool':
                            role_display = '🔧 OUTIL'
                        else:
                            role_display = f'❓ {role.upper()}'
                        
                        # En-tête avec métadonnées de synchronisation
                        f.write(f"[{timestamp}] MESSAGE #{self.numero_message_global} - {role_display}\n")
                        f.write(f"Synchronisation: {type_lecture} | Délai: {delai:.0f}ms\n")
                        f.write(f"Timing Augment: {self.AUGMENT_TIMEOUT_MS}ms → {self.AUGMENT_MAX_WAIT_MS}ms\n")
                        f.write("-" * 60 + "\n")
                        
                        # Contenu du message
                        if content and content.strip():
                            content_clean = self.nettoyer_contenu(content)
                            f.write(f"{content_clean}\n")
                        else:
                            f.write("[Message vide ou en cours de génération]\n")
                        
                        f.write("=" * 80 + "\n\n")
                        
                        # Log console avec aperçu
                        content_preview = content[:80].replace('\n', ' ') if content else '[vide]'
                        print(f"   ✅ MESSAGE #{self.numero_message_global} ({role}): {content_preview}...")
                        self.numero_message_global += 1
                    
                    f.flush()
                    os.fsync(f.fileno())
                    
            except Exception as e:
                print(f"   ❌ Erreur écriture synchronisée: {e}")
    
    def nettoyer_contenu(self, content):
        """Nettoyage avancé du contenu"""
        if not content:
            return "[Contenu vide]"
        
        import re
        
        # Supprimer les caractères de contrôle
        content = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', '', content)
        
        # Limiter la longueur
        if len(content) > 10000:
            content = content[:10000] + "\n[... contenu tronqué ...]"
        
        return content.strip()
    
    def demarrer_surveillance(self):
        """Démarre la surveillance synchronisée avec Augment"""
        print("🚀 EXTRACTEUR SYNCHRONISÉ AUGMENT DÉMARRÉ")
        print("=" * 60)
        print(f"📁 Workspace: a35ba43ef26792e6...")
        print(f"📄 Sortie: {self.output_file}")
        print(f"🔢 Messages existants: {self.dernier_nombre_messages}")
        print(f"🔢 Prochain numéro: #{self.numero_message_global}")
        print("⏱️  TIMINGS SYNCHRONISÉS AVEC AUGMENT :")
        print(f"   • Timeout Augment: {self.AUGMENT_TIMEOUT_MS}ms")
        print(f"   • MaxWait Augment: {self.AUGMENT_MAX_WAIT_MS}ms")
        print(f"   • Lecture optimale: {self.LECTURE_OPTIMALE_MS}ms")
        print(f"   • Lecture finale: {self.LECTURE_FINALE_MS}ms")
        print("💬 Écrivez dans Augment pour voir l'extraction synchronisée...")
        print("⏹️  Ctrl+C pour arrêter")
        print("=" * 60)
        
        # Surveillance filesystem
        self.observer = Observer()
        self.observer.schedule(self, self.workspace_path, recursive=False)
        self.observer.start()
        
        try:
            while self.running:
                threading.Event().wait(1)
                
        except KeyboardInterrupt:
            print("\n🛑 Arrêt de l'extracteur synchronisé...")
            self.running = False
            if self.observer:
                self.observer.stop()
                self.observer.join()

if __name__ == "__main__":
    extracteur = ExtracteurSynchroniseAugment()
    extracteur.demarrer_surveillance()
