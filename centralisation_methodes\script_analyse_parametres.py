#!/usr/bin/env python3
"""
SCRIPT D'ANALYSE COMPLÈTE DES PARAMÈTRES DES 108 MÉTHODES
=========================================================

Objectif : Extraire, catégoriser et analyser tous les paramètres codés en dur
dans les 108 méthodes non centralisées pour préparer leur centralisation.

Méthodologie :
1. Extraction automatique des valeurs numériques
2. Catégorisation par type et contexte
3. Analyse de fréquence et usage
4. Comparaison avec AZRConfig existant
5. Préparation centralisation par catégories
"""

import re
import json
from collections import defaultdict, Counter
from typing import Dict, List, Tuple, Set

class ParameterAnalyzer:
    """Analyseur de paramètres pour les 108 méthodes non centralisées"""
    
    def __init__(self):
        self.parameters = defaultdict(list)
        self.categories = {
            'COEFFICIENTS': [],
            'SEUILS_DETECTION': [],
            'VALEURS_CONFIANCE': [],
            'RATIOS_POURCENTAGES': [],
            'LONGUEURS_TAILLES': [],
            'INDICES_POSITIONS': [],
            'VALEURS_INITIALISATION': [],
            'SEUILS_QUALITE': [],
            'FACTEURS_BOOST': [],
            'IDENTIFIANTS_CLUSTER': [],
            'TIMING_PERFORMANCE': [],
            'PROBABILITES': [],
            'MULTIPLICATEURS': [],
            'CONSTANTES_MATHEMATIQUES': [],
            'VALEURS_LIMITES': []
        }
        
        # Patterns de détection
        self.patterns = {
            'float_values': r'([0-9]+\.[0-9]+)',
            'int_values': r'= ([0-9]+)(?![0-9\.])',
            'comparisons': r'(>= |<= |== |!= )([0-9]+\.?[0-9]*)',
            'cluster_ids': r'cluster_id == ([0-9]+)',
            'percentages': r'([0-9]+\.?[0-9]*)%',
            'ratios': r'([0-9]+\.?[0-9]*) / ([0-9]+\.?[0-9]*)',
            'modulo': r'% ([0-9]+)',
            'array_access': r'\[([0-9]+)\]',
            'range_values': r'range\(([0-9]+)(?:, ([0-9]+))?\)',
            'min_max': r'(min|max)\([^,]+, ([0-9]+\.?[0-9]*)\)',
            'thresholds': r'threshold[^=]*= ([0-9]+\.?[0-9]*)',
            'confidence': r'confidence[^=]*= ([0-9]+\.?[0-9]*)',
            'multiplier': r'multiplier[^=]*= ([0-9]+\.?[0-9]*)',
            'bonus': r'bonus[^=]*= ([0-9]+\.?[0-9]*)',
            'weight': r'weight[^=]*= ([0-9]+\.?[0-9]*)'
        }
        
        # Contextes pour catégorisation
        self.context_keywords = {
            'COEFFICIENTS': ['coefficient', 'factor', 'multiplier', 'ratio'],
            'SEUILS_DETECTION': ['threshold', 'limit', 'minimum', 'maximum'],
            'VALEURS_CONFIANCE': ['confidence', 'probability', 'certainty'],
            'RATIOS_POURCENTAGES': ['ratio', 'percentage', 'percent'],
            'LONGUEURS_TAILLES': ['length', 'size', 'count', 'window'],
            'INDICES_POSITIONS': ['index', 'position', 'offset'],
            'VALEURS_INITIALISATION': ['init', 'default', 'start', 'begin'],
            'SEUILS_QUALITE': ['quality', 'score', 'grade'],
            'FACTEURS_BOOST': ['boost', 'bonus', 'enhancement'],
            'IDENTIFIANTS_CLUSTER': ['cluster_id', 'cluster'],
            'TIMING_PERFORMANCE': ['time', 'ms', 'duration'],
            'PROBABILITES': ['probability', 'chance', 'likelihood'],
            'MULTIPLICATEURS': ['multiplier', 'factor', 'scale'],
            'CONSTANTES_MATHEMATIQUES': ['pi', 'e', 'sqrt', 'log'],
            'VALEURS_LIMITES': ['min', 'max', 'limit', 'bound']
        }
    
    def analyze_file(self, file_path: str) -> Dict:
        """Analyse complète du fichier class.txt"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Extraction des paramètres
            extracted_params = self._extract_parameters(content)
            
            # Catégorisation
            categorized_params = self._categorize_parameters(extracted_params)
            
            # Analyse de fréquence
            frequency_analysis = self._analyze_frequency(extracted_params)
            
            # Analyse contextuelle
            context_analysis = self._analyze_context(content, extracted_params)
            
            return {
                'total_parameters': len(extracted_params),
                'extracted_parameters': extracted_params,
                'categorized_parameters': categorized_params,
                'frequency_analysis': frequency_analysis,
                'context_analysis': context_analysis,
                'recommendations': self._generate_recommendations(categorized_params)
            }
            
        except Exception as e:
            return {'error': f"Erreur analyse fichier: {e}"}
    
    def _extract_parameters(self, content: str) -> List[Dict]:
        """Extraction de tous les paramètres numériques"""
        parameters = []
        lines = content.split('\n')
        
        for line_num, line in enumerate(lines, 1):
            # Ignorer les commentaires
            if line.strip().startswith('#') or line.strip().startswith('//'):
                continue
            
            # Extraction selon différents patterns
            for pattern_name, pattern in self.patterns.items():
                matches = re.finditer(pattern, line)
                for match in matches:
                    param = {
                        'value': match.group(1) if match.groups() else match.group(0),
                        'line_number': line_num,
                        'line_content': line.strip(),
                        'pattern_type': pattern_name,
                        'context': self._extract_context(line),
                        'variable_name': self._extract_variable_name(line)
                    }
                    parameters.append(param)
        
        return parameters
    
    def _extract_context(self, line: str) -> str:
        """Extraction du contexte d'une ligne"""
        # Recherche de mots-clés contextuels
        line_lower = line.lower()
        for category, keywords in self.context_keywords.items():
            for keyword in keywords:
                if keyword in line_lower:
                    return category
        return 'UNKNOWN'
    
    def _extract_variable_name(self, line: str) -> str:
        """Extraction du nom de variable si possible"""
        # Pattern pour variable = valeur
        var_pattern = r'([a-zA-Z_][a-zA-Z0-9_]*)\s*[=:]'
        match = re.search(var_pattern, line)
        return match.group(1) if match else 'unknown'
    
    def _categorize_parameters(self, parameters: List[Dict]) -> Dict:
        """Catégorisation des paramètres par type"""
        categorized = defaultdict(list)
        
        for param in parameters:
            context = param['context']
            if context != 'UNKNOWN':
                categorized[context].append(param)
            else:
                # Catégorisation par valeur
                value = param['value']
                try:
                    num_value = float(value)
                    if num_value == 0.0:
                        categorized['VALEURS_INITIALISATION'].append(param)
                    elif num_value == 1.0:
                        categorized['CONSTANTES_MATHEMATIQUES'].append(param)
                    elif 0.0 < num_value < 1.0:
                        categorized['COEFFICIENTS'].append(param)
                    elif num_value >= 1.0 and num_value == int(num_value):
                        categorized['LONGUEURS_TAILLES'].append(param)
                    else:
                        categorized['VALEURS_LIMITES'].append(param)
                except ValueError:
                    categorized['UNKNOWN'].append(param)
        
        return dict(categorized)
    
    def _analyze_frequency(self, parameters: List[Dict]) -> Dict:
        """Analyse de fréquence des valeurs"""
        values = [param['value'] for param in parameters]
        frequency = Counter(values)
        
        return {
            'most_common': frequency.most_common(20),
            'unique_values': len(frequency),
            'total_occurrences': len(values),
            'duplicates': {k: v for k, v in frequency.items() if v > 1}
        }
    
    def _analyze_context(self, content: str, parameters: List[Dict]) -> Dict:
        """Analyse contextuelle approfondie"""
        context_analysis = {
            'method_distribution': defaultdict(int),
            'cluster_specific': defaultdict(list),
            'hardcoded_clusters': [],
            'potential_azrconfig_matches': []
        }
        
        # Analyse par méthode
        current_method = None
        for line in content.split('\n'):
            if 'def ' in line and '(' in line:
                current_method = line.strip()
            
            for param in parameters:
                if param['line_content'] in line:
                    context_analysis['method_distribution'][current_method] += 1
        
        # Détection clusters codés en dur
        for param in parameters:
            if 'cluster_id' in param['line_content'].lower():
                context_analysis['hardcoded_clusters'].append(param)
        
        return context_analysis
    
    def _generate_recommendations(self, categorized_params: Dict) -> Dict:
        """Génération de recommandations pour centralisation"""
        recommendations = {
            'high_priority': [],
            'medium_priority': [],
            'low_priority': [],
            'azrconfig_additions': {}
        }
        
        # Priorités selon fréquence et impact
        for category, params in categorized_params.items():
            if len(params) > 20:  # Haute fréquence
                recommendations['high_priority'].append({
                    'category': category,
                    'count': len(params),
                    'reason': 'Très haute fréquence - impact majeur'
                })
            elif len(params) > 5:  # Fréquence moyenne
                recommendations['medium_priority'].append({
                    'category': category,
                    'count': len(params),
                    'reason': 'Fréquence moyenne - impact modéré'
                })
            else:  # Faible fréquence
                recommendations['low_priority'].append({
                    'category': category,
                    'count': len(params),
                    'reason': 'Faible fréquence - impact limité'
                })
        
        return recommendations

def main():
    """Fonction principale d'analyse"""
    analyzer = ParameterAnalyzer()
    
    # Analyse du fichier class.txt
    results = analyzer.analyze_file('centralisation_methodes/class.txt')
    
    if 'error' in results:
        print(f"Erreur: {results['error']}")
        return
    
    # Sauvegarde des résultats
    output_file = 'centralisation_methodes/analyse_parametres_complete.json'
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"Analyse complète terminée. Résultats sauvés dans {output_file}")
    print(f"Total paramètres trouvés: {results['total_parameters']}")
    print(f"Catégories identifiées: {len(results['categorized_parameters'])}")

if __name__ == "__main__":
    main()
