#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script final pour convertir la conversation JSON en format lisible
"""

import json
from pathlib import Path
from datetime import datetime

def convertir_conversation_finale():
    """Convertit la conversation JSON en format parfaitement lisible"""
    
    json_file = "C:/temp_conversation/augment_data_complete.json"
    
    print("🔧 CONVERSION FINALE DE LA CONVERSATION")
    print("=" * 60)
    
    try:
        # Lire le fichier JSON complet
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Extraire webviewState
        webview_state = json.loads(data['webviewState'])
        conversations = webview_state['conversations']
        
        # Trouver la conversation avec "tu avais raison finalement"
        target_conversation = None
        for conv_id, conv_data in conversations.items():
            if 'chatHistory' in conv_data:
                chat_history = conv_data['chatHistory']
                for message in chat_history:
                    if isinstance(message, dict) and 'request_message' in message:
                        if "tu avais raison finalement" in message['request_message'].lower():
                            target_conversation = conv_data
                            print(f"🎯 Conversation trouvée: {conv_id}")
                            break
                if target_conversation:
                    break
        
        if not target_conversation:
            print("❌ Conversation avec la phrase non trouvée")
            return None
        
        # Extraire et formater la conversation
        chat_history = target_conversation.get('chatHistory', [])
        
        print(f"📊 Messages dans la conversation: {len(chat_history)}")
        
        # Créer le contenu formaté
        formatted_lines = []
        formatted_lines.append("=" * 80)
        formatted_lines.append("CONVERSATION AUGMENT - FORMAT FINAL LISIBLE")
        formatted_lines.append("=" * 80)
        formatted_lines.append("")
        formatted_lines.append(f"💬 ID Conversation: {target_conversation.get('id', 'N/A')}")
        formatted_lines.append(f"📅 Créée le: {target_conversation.get('createdAtIso', 'N/A')}")
        formatted_lines.append(f"🕒 Dernière interaction: {target_conversation.get('lastInteractedAtIso', 'N/A')}")
        formatted_lines.append(f"📊 Nombre de messages: {len(chat_history)}")
        formatted_lines.append("")
        
        # Traiter chaque message
        message_count = 0
        for i, message in enumerate(chat_history, 1):
            if isinstance(message, dict):
                
                # Messages utilisateur
                if 'request_message' in message and message['request_message'].strip():
                    message_count += 1
                    formatted_lines.append(f"\n--- MESSAGE {message_count} ---")
                    formatted_lines.append("👤 UTILISATEUR")
                    formatted_lines.append(f"🆔 Request ID: {message.get('request_id', 'N/A')}")
                    formatted_lines.append("-" * 60)
                    formatted_lines.append(message['request_message'])
                    formatted_lines.append("-" * 60)
                
                # Réponses de l'assistant
                if 'structured_output_nodes' in message:
                    nodes = message['structured_output_nodes']
                    if nodes:
                        message_count += 1
                        formatted_lines.append(f"\n--- MESSAGE {message_count} ---")
                        formatted_lines.append("🤖 ASSISTANT")
                        formatted_lines.append(f"🆔 Request ID: {message.get('request_id', 'N/A')}")
                        formatted_lines.append(f"📊 Status: {message.get('status', 'N/A')}")
                        formatted_lines.append("-" * 60)
                        
                        # Combiner tous les nœuds de contenu
                        for node in nodes:
                            if isinstance(node, dict) and 'content' in node:
                                content = node['content']
                                # Nettoyer les caractères d'échappement Unicode
                                content = content.replace('\\n', '\n')
                                content = content.replace('\\u00e9', 'é')
                                content = content.replace('\\u00e0', 'à')
                                content = content.replace('\\u00e8', 'è')
                                content = content.replace('\\u00ea', 'ê')
                                content = content.replace('\\u00f4', 'ô')
                                content = content.replace('\\u00e7', 'ç')
                                content = content.replace('\\u00fb', 'û')
                                content = content.replace('\\ud83c\\udf81', '🎁')
                                content = content.replace('\\ud83c\\udfaf', '🎯')
                                content = content.replace('\\ud83d\\udcca', '📊')
                                content = content.replace('\\ud83d\\udd0d', '🔍')
                                content = content.replace('\\u2705', '✅')
                                content = content.replace('\\u274c', '❌')
                                
                                formatted_lines.append(content)
                                formatted_lines.append("")
                        
                        formatted_lines.append("-" * 60)
        
        # Pied de page
        formatted_lines.append("\n" + "=" * 80)
        formatted_lines.append("FIN DE LA CONVERSATION")
        formatted_lines.append(f"📅 Conversion effectuée le: {datetime.now()}")
        formatted_lines.append("🔧 Script: script_convertisseur_conversation_final.py")
        formatted_lines.append("=" * 80)
        
        # Sauvegarder la conversation finale
        output_file = "C:/temp_conversation/CONVERSATION_FINALE_LISIBLE.txt"
        formatted_content = "\n".join(formatted_lines)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(formatted_content)
        
        print(f"✅ Conversation finale sauvée: {output_file}")
        print(f"📏 Taille: {len(formatted_content)} caractères")
        print(f"📊 Messages traités: {message_count}")
        
        # Afficher un aperçu
        print(f"\n📋 APERÇU DE LA CONVERSATION FINALE:")
        print("-" * 60)
        lines = formatted_content.split('\n')
        for line in lines[:30]:
            print(line)
        if len(lines) > 30:
            print("...")
            print(f"(et {len(lines) - 30} lignes supplémentaires)")
        print("-" * 60)
        
        return output_file, formatted_content
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return None, None

if __name__ == "__main__":
    print("🚀 CONVERSION FINALE DE LA CONVERSATION")
    print("=" * 60)
    
    result_file, content = convertir_conversation_finale()
    
    if result_file:
        print(f"\n🎉 CONVERSION RÉUSSIE!")
        print(f"📄 Fichier final: {result_file}")
        print(f"🎯 La conversation est maintenant parfaitement lisible et exploitable!")
    else:
        print(f"\n❌ ÉCHEC DE LA CONVERSION")
