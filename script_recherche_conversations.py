#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour rechercher les conversations JSON dans les répertoires Augment
"""

import os
import json
from pathlib import Path

def explore_task_storage():
    """Explore le répertoire task-storage pour les conversations"""
    
    base_path = Path("C:/Users/<USER>/AppData/Roaming/Code/User/workspaceStorage")
    
    print("🔍 RECHERCHE DANS TASK-STORAGE")
    print("=" * 50)
    
    # Chercher tous les répertoires task-storage
    for workspace in base_path.glob("*/Augment.vscode-augment/augment-user-assets/task-storage/tasks"):
        if workspace.exists():
            print(f"\n📁 Workspace trouvé: {workspace}")
            
            # Explorer chaque tâche
            for task_dir in workspace.iterdir():
                if task_dir.is_dir():
                    print(f"\n🎯 Tâche: {task_dir.name}")
                    
                    # Chercher les fichiers dans la tâche
                    for file_path in task_dir.iterdir():
                        print(f"  📄 Fichier: {file_path.name}")
                        
                        try:
                            # E<PERSON>yer de lire le fichier
                            with open(file_path, 'r', encoding='utf-8') as f:
                                content = f.read()
                            
                            print(f"  📏 Taille: {len(content)} caractères")
                            
                            # Vérifier si c'est du JSON
                            try:
                                data = json.loads(content)
                                print(f"  ✅ JSON valide détecté!")
                                
                                # Chercher des indices de conversation
                                if isinstance(data, dict):
                                    keys = list(data.keys())
                                    print(f"  🔑 Clés principales: {keys[:5]}")
                                    
                                    # Rechercher des mots-clés de conversation
                                    content_lower = content.lower()
                                    conversation_keywords = [
                                        "tu avais raison",
                                        "finalement", 
                                        "conversation",
                                        "message",
                                        "assistant",
                                        "user",
                                        "human"
                                    ]
                                    
                                    found_keywords = []
                                    for keyword in conversation_keywords:
                                        if keyword in content_lower:
                                            found_keywords.append(keyword)
                                    
                                    if found_keywords:
                                        print(f"  🎉 CONVERSATION POTENTIELLE! Mots-clés: {found_keywords}")
                                        print(f"  📝 Aperçu: {content[:300]}")
                                        return file_path, content
                                    
                            except json.JSONDecodeError:
                                print(f"  ⚠️ Pas du JSON valide")
                                # Afficher un aperçu quand même
                                print(f"  📝 Aperçu: {content[:200]}")
                                
                        except Exception as e:
                            print(f"  ❌ Erreur lecture: {e}")
    
    return None, None

def search_all_augment_json():
    """Recherche dans tous les répertoires Augment pour les JSON de conversations"""
    
    print("\n🔍 RECHERCHE GLOBALE DANS TOUS LES RÉPERTOIRES AUGMENT")
    print("=" * 60)
    
    base_path = Path("C:/Users/<USER>/AppData/Roaming/Code")
    
    conversation_files = []
    
    # Rechercher récursivement tous les fichiers JSON dans les répertoires Augment
    for json_file in base_path.rglob("*.json"):
        if "augment" in str(json_file).lower():
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Chercher des mots-clés de conversation
                content_lower = content.lower()
                if any(keyword in content_lower for keyword in ["tu avais raison", "conversation", "message"]):
                    conversation_files.append({
                        'path': str(json_file),
                        'size': len(content),
                        'content': content[:500]
                    })
                    print(f"✅ Conversation trouvée: {json_file}")
                    
            except Exception as e:
                pass  # Ignorer les erreurs
    
    return conversation_files

if __name__ == "__main__":
    print("🚀 RECHERCHE CIBLÉE DES CONVERSATIONS JSON AUGMENT")
    print("=" * 60)
    
    # Recherche dans task-storage
    task_file, task_content = explore_task_storage()
    
    if task_file:
        print(f"\n🎉 CONVERSATION TROUVÉE DANS TASK-STORAGE!")
        print(f"📁 Fichier: {task_file}")
        print(f"📝 Contenu complet:")
        print(task_content)
    else:
        print("\n❌ Aucune conversation trouvée dans task-storage")
    
    # Recherche globale
    all_conversations = search_all_augment_json()
    
    if all_conversations:
        print(f"\n🎉 {len(all_conversations)} CONVERSATIONS TROUVÉES AU TOTAL!")
        for i, conv in enumerate(all_conversations, 1):
            print(f"\n--- CONVERSATION {i} ---")
            print(f"📁 Fichier: {conv['path']}")
            print(f"📏 Taille: {conv['size']} caractères")
            print(f"📝 Aperçu: {conv['content']}")
    else:
        print("\n❌ Aucune conversation trouvée")
