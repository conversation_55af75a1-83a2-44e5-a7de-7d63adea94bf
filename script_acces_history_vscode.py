#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour accéder au répertoire History de VSCode
Contourne les restrictions d'accès PowerShell
"""

import os
import json
import sys
from pathlib import Path
from datetime import datetime
import stat

def get_history_path():
    """Obtient le chemin vers le répertoire History de VSCode"""
    username = os.getenv('USERNAME', 'Administrateur')
    history_path = Path(f"C:/Users/<USER>/AppData/Roaming/Code/User/History")
    return history_path

def list_history_files(history_path, limit=10):
    """Liste les fichiers d'historique triés par date"""
    try:
        if not history_path.exists():
            print(f"❌ Répertoire non trouvé: {history_path}")
            return []

        print(f"📁 Répertoire trouvé: {history_path}")
        print(f"🔍 Tentative de listage des fichiers...")

        files = []
        file_count = 0

        try:
            # Tentative de listage avec os.listdir (plus permissif)
            file_names = os.listdir(str(history_path))
            print(f"📋 {len(file_names)} entrées trouvées avec os.listdir")

            for file_name in file_names:
                file_count += 1
                file_path = history_path / file_name

                try:
                    if file_path.is_file():
                        stat_info = file_path.stat()
                        files.append({
                            'name': file_path.name,
                            'path': str(file_path),
                            'size': stat_info.st_size,
                            'modified': datetime.fromtimestamp(stat_info.st_mtime),
                            'created': datetime.fromtimestamp(stat_info.st_ctime)
                        })
                        print(f"✅ Fichier {file_count}: {file_name} ({stat_info.st_size} octets)")
                    else:
                        print(f"📁 Répertoire: {file_name}")

                except Exception as e:
                    print(f"⚠️ Erreur stat pour {file_name}: {e}")
                    # Ajouter quand même le fichier avec infos minimales
                    files.append({
                        'name': file_name,
                        'path': str(file_path),
                        'size': 0,
                        'modified': datetime.now(),
                        'created': datetime.now()
                    })

        except PermissionError as e:
            print(f"❌ Permission refusée pour lister le répertoire: {e}")
            return []
        except Exception as e:
            print(f"❌ Erreur lors du listage: {e}")
            return []

        # Trier par date de modification (plus récent en premier)
        files.sort(key=lambda x: x['modified'], reverse=True)
        print(f"📊 {len(files)} fichiers traités avec succès")
        return files[:limit]

    except Exception as e:
        print(f"❌ Erreur générale lors de la lecture du répertoire: {e}")
        return []

def read_history_file(file_path, max_chars=1000):
    """Tente de lire un fichier d'historique avec différentes méthodes"""
    file_path = Path(file_path)
    
    print(f"\n🔍 Analyse du fichier: {file_path.name}")
    print(f"📁 Chemin: {file_path}")
    
    # Méthode 1: Lecture binaire puis décodage
    try:
        with open(file_path, 'rb') as f:
            raw_data = f.read(max_chars)
            
        print(f"📊 Taille lue: {len(raw_data)} octets")
        
        # Tentative de décodage UTF-8
        try:
            content = raw_data.decode('utf-8')
            print("✅ Décodage UTF-8 réussi")
            return content
        except UnicodeDecodeError:
            print("⚠️ Échec décodage UTF-8, tentative UTF-16")
            
        # Tentative de décodage UTF-16
        try:
            content = raw_data.decode('utf-16')
            print("✅ Décodage UTF-16 réussi")
            return content
        except UnicodeDecodeError:
            print("⚠️ Échec décodage UTF-16, affichage hexadécimal")
            
        # Affichage hexadécimal des premiers octets
        hex_preview = ' '.join(f'{b:02x}' for b in raw_data[:50])
        print(f"🔢 Aperçu hex: {hex_preview}")
        
        # Tentative d'extraction de texte ASCII
        ascii_chars = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in raw_data[:200])
        print(f"📝 Aperçu ASCII: {ascii_chars}")
        
        return f"Données binaires ({len(raw_data)} octets)"
        
    except PermissionError:
        print("❌ Accès refusé - Permissions insuffisantes")
        return None
    except Exception as e:
        print(f"❌ Erreur de lecture: {e}")
        return None

def explore_history_directory(dir_path, max_files=5):
    """Explore un répertoire d'historique spécifique"""
    try:
        print(f"\n🔍 Exploration du répertoire: {dir_path.name}")

        if not dir_path.is_dir():
            print("❌ Ce n'est pas un répertoire")
            return []

        files_found = []
        try:
            for item in dir_path.iterdir():
                if item.is_file():
                    try:
                        stat_info = item.stat()
                        files_found.append({
                            'name': item.name,
                            'path': str(item),
                            'size': stat_info.st_size,
                            'modified': datetime.fromtimestamp(stat_info.st_mtime)
                        })
                        print(f"  📄 Fichier: {item.name} ({stat_info.st_size} octets)")
                    except Exception as e:
                        print(f"  ⚠️ Erreur fichier {item.name}: {e}")
                elif item.is_dir():
                    print(f"  📁 Sous-répertoire: {item.name}")

        except Exception as e:
            print(f"❌ Erreur exploration: {e}")

        return files_found[:max_files]

    except Exception as e:
        print(f"❌ Erreur générale exploration: {e}")
        return []

def analyze_history_structure():
    """Analyse la structure du répertoire History"""
    history_path = get_history_path()

    print("🔍 ANALYSE DU RÉPERTOIRE HISTORY VSCODE")
    print("=" * 50)
    print(f"📁 Chemin: {history_path}")
    print(f"📊 Existe: {history_path.exists()}")

    if not history_path.exists():
        print("❌ Répertoire History non trouvé")
        return

    # Liste des répertoires (pas fichiers)
    directories = []
    try:
        for item in history_path.iterdir():
            if item.is_dir():
                try:
                    stat_info = item.stat()
                    directories.append({
                        'name': item.name,
                        'path': item,
                        'modified': datetime.fromtimestamp(stat_info.st_mtime)
                    })
                except Exception as e:
                    print(f"⚠️ Erreur répertoire {item.name}: {e}")
    except Exception as e:
        print(f"❌ Erreur listage répertoires: {e}")
        return

    # Trier par date de modification
    directories.sort(key=lambda x: x['modified'], reverse=True)

    print(f"\n📋 Nombre de répertoires trouvés: {len(directories)}")

    if not directories:
        print("❌ Aucun répertoire accessible")
        return

    print("\n📅 RÉPERTOIRES LES PLUS RÉCENTS:")
    print("-" * 80)
    for i, dir_info in enumerate(directories[:10], 1):
        print(f"{i:2d}. {dir_info['name']} - {dir_info['modified']}")

    # Exploration détaillée des 3 répertoires les plus récents
    print("\n🔬 EXPLORATION DÉTAILLÉE DES RÉPERTOIRES RÉCENTS:")
    print("=" * 50)

    for i, dir_info in enumerate(directories[:3], 1):
        print(f"\n--- RÉPERTOIRE {i}: {dir_info['name']} ---")
        files = explore_history_directory(dir_info['path'])

        # Lire le contenu des fichiers trouvés
        for file_info in files:
            content = read_history_file(file_info['path'])
            if content and len(content) > 0:
                print(f"\n📝 Contenu de {file_info['name']} (premiers 500 caractères):")
                print(content[:500])
                if len(content) > 500:
                    print("... (tronqué)")
                print("-" * 40)

def search_json_files_for_conversations():
    """Recherche spécifique dans tous les fichiers JSON pour les conversations"""
    history_path = get_history_path()

    print("\n🔍 RECHERCHE INTENSIVE DANS TOUS LES FICHIERS JSON")
    print("=" * 60)

    conversation_keywords = [
        "tu avais raison",
        "finalement",
        "conversation",
        "chat",
        "augment",
        "message",
        "réponse",
        "assistant",
        "user",
        "human",
        "claude"
    ]

    found_conversations = []
    total_json_files = 0

    try:
        # Explorer tous les répertoires d'historique
        for item in history_path.iterdir():
            if item.is_dir():
                print(f"\n📁 Exploration du répertoire: {item.name}")

                try:
                    for file_path in item.iterdir():
                        if file_path.is_file():
                            total_json_files += 1

                            # Lire le fichier
                            try:
                                with open(file_path, 'r', encoding='utf-8') as f:
                                    content = f.read()

                                # Vérifier si c'est du JSON
                                if file_path.name.endswith('.json') or content.strip().startswith('{'):
                                    print(f"  📄 JSON trouvé: {file_path.name} ({len(content)} caractères)")

                                    # Rechercher les mots-clés de conversation
                                    content_lower = content.lower()
                                    for keyword in conversation_keywords:
                                        if keyword in content_lower:
                                            found_conversations.append({
                                                'directory': item.name,
                                                'file': file_path.name,
                                                'keyword': keyword,
                                                'full_path': str(file_path),
                                                'content_size': len(content),
                                                'content_preview': content[:500]
                                            })
                                            print(f"    ✅ CONVERSATION TROUVÉE! Mot-clé: {keyword}")
                                            break

                                    # Recherche spéciale pour structures de conversation
                                    if any(pattern in content_lower for pattern in ['messages', 'role', 'content']):
                                        try:
                                            import json
                                            data = json.loads(content)
                                            if isinstance(data, dict) and ('messages' in data or 'conversation' in data):
                                                found_conversations.append({
                                                    'directory': item.name,
                                                    'file': file_path.name,
                                                    'keyword': 'STRUCTURE_CONVERSATION',
                                                    'full_path': str(file_path),
                                                    'content_size': len(content),
                                                    'content_preview': content[:500]
                                                })
                                                print(f"    ✅ STRUCTURE CONVERSATION DÉTECTÉE!")
                                        except:
                                            pass

                            except Exception as e:
                                print(f"    ⚠️ Erreur lecture {file_path.name}: {e}")

                except Exception as e:
                    print(f"  ❌ Erreur exploration répertoire {item.name}: {e}")

    except Exception as e:
        print(f"❌ Erreur générale: {e}")

    print(f"\n📊 RÉSULTATS DE LA RECHERCHE:")
    print(f"📋 Total fichiers analysés: {total_json_files}")
    print(f"💬 Conversations trouvées: {len(found_conversations)}")

    if found_conversations:
        print(f"\n✅ CONVERSATIONS DÉTECTÉES:")
        for i, conv in enumerate(found_conversations, 1):
            print(f"\n--- CONVERSATION {i} ---")
            print(f"📁 Répertoire: {conv['directory']}")
            print(f"📄 Fichier: {conv['file']}")
            print(f"🔑 Mot-clé: {conv['keyword']}")
            print(f"📏 Taille: {conv['content_size']} caractères")
            print(f"📝 Aperçu:")
            print(conv['content_preview'])
            print("-" * 50)
    else:
        print("❌ Aucune conversation trouvée dans les fichiers JSON")

def search_for_conversations():
    """Recherche spécifique de traces de conversations - VERSION ÉTENDUE"""
    print("\n🔍 RECHERCHE ÉTENDUE DE CONVERSATIONS")
    print("=" * 50)

    # Recherche dans l'historique VSCode
    search_json_files_for_conversations()

    # Recherche dans d'autres emplacements potentiels
    other_locations = [
        Path("C:/Users/<USER>/AppData/Roaming/Code"),
        Path("C:/Users/<USER>/AppData/Local"),
        Path("C:/Users/<USER>/Desktop/Travail/Projet6")
    ]

    print(f"\n🔍 RECHERCHE DANS D'AUTRES EMPLACEMENTS:")
    for location in other_locations:
        if location.exists():
            print(f"\n📁 Recherche dans: {location}")
            try:
                for json_file in location.rglob("*.json"):
                    if json_file.is_file() and json_file.stat().st_size > 100:  # Fichiers > 100 octets
                        try:
                            with open(json_file, 'r', encoding='utf-8') as f:
                                content = f.read()

                            if any(keyword in content.lower() for keyword in ["tu avais raison", "conversation", "augment"]):
                                print(f"  ✅ Conversation potentielle: {json_file}")
                                print(f"     Aperçu: {content[:200]}")

                        except Exception as e:
                            pass  # Ignorer les erreurs de lecture
            except Exception as e:
                print(f"  ❌ Erreur recherche dans {location}: {e}")

if __name__ == "__main__":
    print("🚀 SCRIPT D'ACCÈS AU RÉPERTOIRE HISTORY VSCODE")
    print("=" * 60)
    
    try:
        # Analyse générale
        analyze_history_structure()
        
        # Recherche spécifique
        search_for_conversations()
        
    except Exception as e:
        print(f"❌ Erreur générale: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n✅ Analyse terminée")
