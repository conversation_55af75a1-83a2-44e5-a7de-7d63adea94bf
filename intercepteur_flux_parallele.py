#!/usr/bin/env python3
"""
INTERCEPTEUR DE FLUX PARALLÈLE AUGMENT
======================================

Système qui reproduit exactement le même flux de données qu'Augment
mais écrit directement dans notre fichier texte au lieu d'attendre
la sauvegarde dans state.vscdb.

CONCEPT:
Interface → Backend → [NOTRE INTERCEPTEUR] → Fichier texte
                   ↓
            state.vscdb (flux original)
"""

import os
import sys
import time
import json
import sqlite3
import threading
import datetime
from pathlib import Path
import subprocess
import psutil

class AugmentFlowInterceptor:
    def __init__(self):
        self.project_dir = Path(__file__).parent.absolute()
        self.output_file = self.project_dir / "augment_conversation_realtime.txt"
        self.state_file = self.find_state_file()
        
        # Surveillance des processus
        self.vscode_processes = []
        self.augment_processes = []
        
        # État de la conversation
        self.last_message_count = 0
        self.conversation_buffer = []
        
        print(f"🎯 INTERCEPTEUR DE FLUX PARALLÈLE AUGMENT")
        print(f"=" * 60)
        print(f"📁 Projet: {self.project_dir}")
        print(f"📝 Sortie: {self.output_file}")
        print(f"📄 Source: {self.state_file}")
        
    def find_state_file(self):
        """Trouve le fichier state.vscdb actuel"""
        base_path = Path("C:/Users/<USER>/AppData/Roaming/Code/User/workspaceStorage")
        
        for workspace_dir in base_path.iterdir():
            if workspace_dir.is_dir():
                state_file = workspace_dir / "state.vscdb"
                if state_file.exists():
                    # Vérifier s'il contient des conversations Augment
                    try:
                        conn = sqlite3.connect(str(state_file))
                        cursor = conn.cursor()
                        cursor.execute("SELECT key FROM ItemTable WHERE key = 'memento/webviewView.augment-chat'")
                        result = cursor.fetchone()
                        conn.close()
                        if result:
                            return state_file
                    except:
                        continue
        return None
    
    def identify_augment_processes(self):
        """Identifie tous les processus liés à Augment et VSCode"""
        processes = {
            'vscode': [],
            'augment': [],
            'node': [],
            'electron': []
        }
        
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                name = proc.info['name'].lower()
                cmdline = ' '.join(proc.info['cmdline'] or []).lower()
                
                if 'code' in name or 'vscode' in cmdline:
                    processes['vscode'].append(proc)
                elif 'augment' in name or 'augment' in cmdline:
                    processes['augment'].append(proc)
                elif 'node' in name and ('augment' in cmdline or 'vscode' in cmdline):
                    processes['node'].append(proc)
                elif 'electron' in name and 'vscode' in cmdline:
                    processes['electron'].append(proc)
                    
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        return processes
    
    def monitor_file_system_changes(self):
        """Surveille les changements du système de fichiers"""
        print(f"🔍 SURVEILLANCE DU SYSTÈME DE FICHIERS")
        
        # Surveiller les répertoires critiques
        watch_dirs = [
            self.state_file.parent,
            Path("C:/Users/<USER>/AppData/Roaming/Code/User/workspaceStorage"),
            Path("C:/Users/<USER>/.vscode/extensions")
        ]
        
        for watch_dir in watch_dirs:
            if watch_dir.exists():
                print(f"📁 Surveillance: {watch_dir}")
    
    def intercept_network_traffic(self):
        """Intercepte le trafic réseau d'Augment (concept)"""
        print(f"🌐 INTERCEPTION RÉSEAU (concept)")
        
        # Identifier les ports utilisés par Augment
        processes = self.identify_augment_processes()
        
        for category, proc_list in processes.items():
            print(f"📊 {category}: {len(proc_list)} processus")
            for proc in proc_list[:3]:  # Limiter l'affichage
                try:
                    connections = proc.connections()
                    if connections:
                        print(f"   PID {proc.pid}: {len(connections)} connexions")
                except:
                    pass
    
    def create_memory_interceptor(self):
        """Crée un intercepteur de mémoire pour capturer les données"""
        print(f"🧠 INTERCEPTEUR MÉMOIRE")
        
        # Concept: Hook dans la mémoire des processus Augment
        # pour capturer les données avant qu'elles soient écrites
        
        processes = self.identify_augment_processes()
        
        for category, proc_list in processes.items():
            for proc in proc_list:
                try:
                    # Analyser l'utilisation mémoire
                    memory_info = proc.memory_info()
                    print(f"📊 {category} PID {proc.pid}: {memory_info.rss / 1024 / 1024:.1f} MB")
                except:
                    pass
    
    def create_api_interceptor(self):
        """Crée un intercepteur d'API pour capturer les appels"""
        print(f"🔌 INTERCEPTEUR API")
        
        # Concept: Intercepter les appels API d'Augment
        # Identifier les endpoints utilisés
        
        # Ports communs pour les extensions VSCode
        common_ports = [3000, 3001, 8080, 8081, 9000, 9001]
        
        for port in common_ports:
            # Vérifier si le port est utilisé
            try:
                import socket
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                result = sock.connect_ex(('localhost', port))
                sock.close()
                
                if result == 0:
                    print(f"🌐 Port actif détecté: {port}")
            except:
                pass
    
    def create_ipc_interceptor(self):
        """Crée un intercepteur IPC (Inter-Process Communication)"""
        print(f"📡 INTERCEPTEUR IPC")
        
        # VSCode utilise IPC pour communiquer avec les extensions
        # Concept: Intercepter les messages IPC
        
        # Rechercher les pipes nommés ou sockets
        if os.name == 'nt':  # Windows
            # Rechercher les pipes nommés
            pipe_pattern = r"\\.\pipe\vscode*"
            print(f"🔍 Recherche pipes: {pipe_pattern}")
    
    def create_database_interceptor(self):
        """Crée un intercepteur de base de données"""
        print(f"🗄️ INTERCEPTEUR BASE DE DONNÉES")
        
        if not self.state_file:
            print(f"❌ Fichier state.vscdb non trouvé")
            return
        
        # Surveiller les écritures dans state.vscdb en temps réel
        # Concept: Hook les opérations SQLite
        
        try:
            # Analyser la structure de la base
            conn = sqlite3.connect(str(self.state_file))
            cursor = conn.cursor()
            
            # Lister toutes les tables
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            
            print(f"📊 Tables dans state.vscdb:")
            for table in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table[0]}")
                count = cursor.fetchone()[0]
                print(f"   {table[0]}: {count} entrées")
            
            conn.close()
            
        except Exception as e:
            print(f"❌ Erreur analyse base: {e}")
    
    def create_file_watcher_advanced(self):
        """Crée un surveillant de fichier avancé"""
        print(f"👁️ SURVEILLANT DE FICHIER AVANCÉ")
        
        if not self.state_file:
            return
        
        # Surveiller avec une fréquence très élevée
        last_mtime = 0
        last_size = 0
        
        def watch_loop():
            nonlocal last_mtime, last_size
            
            while True:
                try:
                    stat = self.state_file.stat()
                    current_mtime = stat.st_mtime
                    current_size = stat.st_size
                    
                    if current_mtime > last_mtime or current_size != last_size:
                        print(f"🔄 [{datetime.datetime.now().strftime('%H:%M:%S.%f')[:-3]}] "
                              f"Fichier modifié: {current_size} bytes")
                        
                        # Traitement immédiat
                        self.process_immediate_change()
                        
                        last_mtime = current_mtime
                        last_size = current_size
                    
                    time.sleep(0.01)  # 10ms - très fréquent
                    
                except Exception as e:
                    time.sleep(0.1)
        
        # Démarrer la surveillance en arrière-plan
        thread = threading.Thread(target=watch_loop, daemon=True)
        thread.start()
        
        return thread
    
    def process_immediate_change(self):
        """Traite immédiatement un changement détecté"""
        try:
            # Lecture immédiate de la base
            conn = sqlite3.connect(str(self.state_file))
            cursor = conn.cursor()
            
            cursor.execute("SELECT value FROM ItemTable WHERE key = 'memento/webviewView.augment-chat'")
            result = cursor.fetchone()
            
            if result:
                value = result[0]
                if isinstance(value, bytes):
                    value_str = value.decode('utf-8')
                else:
                    value_str = str(value)
                
                main_data = json.loads(value_str)
                webview_data = json.loads(main_data['webviewState'])
                
                # Extraire les conversations
                conversations = webview_data.get('conversations', {})
                current_conv_id = webview_data.get('currentConversationId')
                
                if current_conv_id and current_conv_id in conversations:
                    chat_history = conversations[current_conv_id].get('chatHistory', [])
                    
                    if len(chat_history) > self.last_message_count:
                        # Nouveaux messages détectés
                        new_messages = chat_history[self.last_message_count:]
                        self.write_immediate_to_file(new_messages)
                        self.last_message_count = len(chat_history)
            
            conn.close()
            
        except Exception as e:
            pass  # Ignorer les erreurs de lecture temporaires
    
    def write_immediate_to_file(self, new_messages):
        """Écrit immédiatement dans le fichier de sortie"""
        timestamp = datetime.datetime.now().isoformat()
        
        with open(self.output_file, 'a', encoding='utf-8') as f:
            for i, message_data in enumerate(new_messages):
                msg_index = self.last_message_count + i + 1
                
                f.write(f"[{timestamp}] MESSAGE #{msg_index} - TEMPS RÉEL\n")
                f.write("-" * 60 + "\n")
                
                # Message utilisateur
                if 'request_message' in message_data and message_data['request_message']:
                    f.write(f"👤 UTILISATEUR:\n{message_data['request_message']}\n\n")
                
                # Réponse assistant (même partielle)
                if 'structured_output_nodes' in message_data:
                    for node in message_data['structured_output_nodes']:
                        if node.get('type') == 0 and node.get('content'):
                            f.write(f"🤖 ASSISTANT:\n{node['content']}\n\n")
                            break
                
                f.write("=" * 80 + "\n\n")
            
            f.flush()
            os.fsync(f.fileno())
        
        print(f"⚡ {len(new_messages)} messages écrits en TEMPS RÉEL")
    
    def initialize_output_file(self):
        """Initialise le fichier de sortie"""
        timestamp = datetime.datetime.now().isoformat()
        
        with open(self.output_file, 'w', encoding='utf-8') as f:
            f.write("AUGMENT CONVERSATION - FLUX PARALLÈLE TEMPS RÉEL\n")
            f.write(f"Démarré: {timestamp}\n")
            f.write(f"Projet: {self.project_dir}\n")
            f.write(f"Source: {self.state_file}\n")
            f.write("=" * 80 + "\n\n")
    
    def start_parallel_flow(self):
        """Démarre le système de flux parallèle"""
        print(f"\n🚀 DÉMARRAGE DU FLUX PARALLÈLE")
        print(f"=" * 50)
        
        # Initialiser le fichier de sortie
        self.initialize_output_file()
        
        # Identifier les processus
        processes = self.identify_augment_processes()
        
        # Créer les intercepteurs
        self.monitor_file_system_changes()
        self.intercept_network_traffic()
        self.create_memory_interceptor()
        self.create_api_interceptor()
        self.create_ipc_interceptor()
        self.create_database_interceptor()
        
        # Démarrer la surveillance avancée
        watcher_thread = self.create_file_watcher_advanced()
        
        print(f"\n✅ SYSTÈME DE FLUX PARALLÈLE ACTIF")
        print(f"📝 Fichier de sortie: {self.output_file}")
        print(f"⚡ Écriture en temps réel activée")
        print(f"💬 Écrivez dans Augment pour voir l'interception...")
        
        return watcher_thread

def main():
    """Fonction principale"""
    interceptor = AugmentFlowInterceptor()
    
    watcher = interceptor.start_parallel_flow()
    
    try:
        print(f"\n🔄 Système actif - Interception en cours...")
        print(f"⏹️  Ctrl+C pour arrêter")
        
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print(f"\n🛑 Arrêt du système de flux parallèle")

if __name__ == "__main__":
    main()
