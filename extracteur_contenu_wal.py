#!/usr/bin/env python3
"""
EXTRACTEUR CONTENU WAL - MESSAGES NUMÉROTÉS
Extraction complète du contenu des messages depuis WAL avec numérotation
"""

import os
import time
import threading
import struct
import re
from datetime import datetime
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import json
import sqlite3

class ExtracteurContenuWAL(FileSystemEventHandler):
    def __init__(self):
        self.workspace_path = r"C:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\a35ba43ef26792e61fe787a234121806"
        self.db_path = os.path.join(self.workspace_path, "state.vscdb")
        self.wal_path = self.db_path + "-wal"
        self.output_file = "messages_extraits_numerotes.txt"
        
        self.running = True
        self.observer = None
        self.lock = threading.Lock()
        self.derniere_taille_wal = 0
        self.numero_message = 1
        self.derniers_messages_ids = set()
        
        self.init_output_file()
        self.init_wal_monitoring()
        self.charger_etat_initial()
    
    def init_output_file(self):
        """Initialise le fichier de sortie avec format numéroté"""
        with open(self.output_file, 'w', encoding='utf-8') as f:
            f.write("EXTRACTEUR CONTENU WAL - MESSAGES NUMÉROTÉS\n")
            f.write(f"Démarré: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("=" * 80 + "\n")
            f.write("Format: [TIMESTAMP] MESSAGE #N - ROLE\n")
            f.write("Contenu: [Contenu du message]\n")
            f.write("=" * 80 + "\n\n")
    
    def init_wal_monitoring(self):
        """Initialise la surveillance WAL"""
        try:
            if os.path.exists(self.wal_path):
                self.derniere_taille_wal = os.path.getsize(self.wal_path)
                print(f"📊 WAL initial: {self.derniere_taille_wal} bytes")
            else:
                print("⚠️ Fichier WAL non trouvé")
                self.derniere_taille_wal = 0
        except Exception as e:
            print(f"❌ Erreur init WAL: {e}")
            self.derniere_taille_wal = 0
    
    def charger_etat_initial(self):
        """Charge l'état initial pour éviter les doublons"""
        try:
            messages_existants = self.extraire_messages_db_principale()
            self.numero_message = len(messages_existants) + 1
            
            # Mémoriser les IDs des messages existants
            for msg in messages_existants:
                msg_id = self.generer_id_message(msg)
                self.derniers_messages_ids.add(msg_id)
            
            print(f"📊 État initial: {len(messages_existants)} messages existants")
            print(f"🔢 Prochain numéro: {self.numero_message}")
            
        except Exception as e:
            print(f"❌ Erreur chargement état: {e}")
    
    def extraire_messages_db_principale(self):
        """Extrait les messages de la DB principale pour l'état initial"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=1.0)
            cursor = conn.cursor()
            
            cursor.execute("SELECT value FROM ItemTable WHERE key = 'memento/webviewView.augment-chat' LIMIT 1")
            row = cursor.fetchone()
            
            if not row:
                conn.close()
                return []
            
            value = row[0]
            if isinstance(value, bytes):
                value_str = value.decode('utf-8')
            else:
                value_str = str(value)
            
            main_data = json.loads(value_str)
            webview_data = json.loads(main_data['webviewState'])
            
            conversations = webview_data.get('conversations', {})
            current_conv_id = webview_data.get('currentConversationId')
            
            if current_conv_id and current_conv_id in conversations:
                chat_history = conversations[current_conv_id].get('chatHistory', [])
                conn.close()
                return chat_history
            
            conn.close()
            return []
            
        except Exception as e:
            print(f"❌ Erreur extraction DB: {e}")
            return []
    
    def generer_id_message(self, message):
        """Génère un ID unique pour un message"""
        content = message.get('content', '')
        role = message.get('role', '')
        # Utiliser les premiers 50 caractères + role comme ID
        return f"{role}:{content[:50]}"
    
    def on_modified(self, event):
        """Détection instantanée des modifications WAL"""
        if event.is_directory:
            return
        
        if os.path.basename(event.src_path) == "state.vscdb-wal":
            timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
            print(f"⚡ [{timestamp}] WAL MODIFIÉ - EXTRACTION EN COURS...")
            threading.Thread(target=self.extraire_nouveaux_messages, args=(timestamp,)).start()
    
    def extraire_nouveaux_messages(self, timestamp_detection):
        """Extrait les nouveaux messages depuis WAL et DB"""
        try:
            # Attendre un petit délai pour que les données soient disponibles
            time.sleep(0.05)  # 50ms
            
            # Extraire depuis la DB principale (plus fiable que parsing WAL brut)
            messages_actuels = self.extraire_messages_db_principale()
            
            if not messages_actuels:
                print(f"   ⚠️ Aucun message trouvé")
                return
            
            # Identifier les nouveaux messages
            nouveaux_messages = []
            for msg in messages_actuels:
                msg_id = self.generer_id_message(msg)
                if msg_id not in self.derniers_messages_ids:
                    nouveaux_messages.append(msg)
                    self.derniers_messages_ids.add(msg_id)
            
            if nouveaux_messages:
                timestamp_extraction = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                delai = (datetime.strptime(timestamp_extraction, '%H:%M:%S.%f') - 
                        datetime.strptime(timestamp_detection, '%H:%M:%S.%f')).total_seconds() * 1000
                
                print(f"🔥 [{timestamp_extraction}] {len(nouveaux_messages)} NOUVEAUX MESSAGES EXTRAITS !")
                print(f"   ⏱️ Délai extraction: {delai:.1f}ms")
                
                self.ecrire_messages_numerotes(nouveaux_messages, timestamp_extraction)
            else:
                print(f"   📝 Aucun nouveau message (total: {len(messages_actuels)})")
                
        except Exception as e:
            print(f"   ❌ Erreur extraction: {e}")
    
    def ecrire_messages_numerotes(self, nouveaux_messages, timestamp):
        """Écrit les nouveaux messages avec numérotation"""
        with self.lock:
            try:
                with open(self.output_file, 'a', encoding='utf-8') as f:
                    for message in nouveaux_messages:
                        role = message.get('role', 'unknown')
                        content = message.get('content', '')
                        
                        # Formatage du rôle
                        if role == 'user':
                            role_display = '👤 UTILISATEUR'
                        elif role == 'assistant':
                            role_display = '🤖 ASSISTANT'
                        else:
                            role_display = f'❓ {role.upper()}'
                        
                        # Écriture du message numéroté
                        f.write(f"[{timestamp}] MESSAGE #{self.numero_message} - {role_display}\n")
                        f.write("-" * 60 + "\n")
                        
                        if content.strip():
                            # Nettoyer et formater le contenu
                            content_clean = self.nettoyer_contenu(content)
                            f.write(f"{content_clean}\n")
                        else:
                            f.write("[Message vide ou en cours de génération]\n")
                        
                        f.write("=" * 80 + "\n\n")
                        
                        print(f"   ✅ MESSAGE #{self.numero_message} écrit ({role})")
                        self.numero_message += 1
                    
                    f.flush()
                    os.fsync(f.fileno())
                    
            except Exception as e:
                print(f"   ❌ Erreur écriture: {e}")
    
    def nettoyer_contenu(self, content):
        """Nettoie et formate le contenu du message"""
        if not content:
            return "[Contenu vide]"
        
        # Supprimer les caractères de contrôle
        content = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', '', content)
        
        # Limiter la longueur si nécessaire (optionnel)
        if len(content) > 5000:
            content = content[:5000] + "\n[... contenu tronqué ...]"
        
        return content.strip()
    
    def demarrer_surveillance(self):
        """Démarre la surveillance WAL avec extraction de contenu"""
        print("🚀 EXTRACTEUR CONTENU WAL DÉMARRÉ")
        print("=" * 60)
        print(f"📁 Workspace: a35ba43ef26792e6...")
        print(f"📝 WAL: {self.wal_path}")
        print(f"📄 Sortie: {self.output_file}")
        print(f"🔢 Prochain message: #{self.numero_message}")
        print("💬 Écrivez dans Augment pour voir l'extraction numérotée...")
        print("⏹️  Ctrl+C pour arrêter")
        print("=" * 60)
        
        # Surveillance du répertoire workspace
        self.observer = Observer()
        self.observer.schedule(self, self.workspace_path, recursive=False)
        self.observer.start()
        
        try:
            while self.running:
                threading.Event().wait(1)
                
        except KeyboardInterrupt:
            print("\n🛑 Arrêt de l'extracteur...")
            self.running = False
            if self.observer:
                self.observer.stop()
                self.observer.join()

if __name__ == "__main__":
    extracteur = ExtracteurContenuWAL()
    extracteur.demarrer_surveillance()
