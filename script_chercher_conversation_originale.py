#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour chercher la vraie conversation originale (pas nos scripts)
"""

import os
import json
import re
from pathlib import Path
from datetime import datetime

def chercher_conversation_originale():
    """Cherche la vraie conversation originale"""
    
    base_path = Path("C:/Users/<USER>/AppData/Roaming/Code/User/workspaceStorage")
    
    print("🔍 RECHERCHE DE LA CONVERSATION ORIGINALE")
    print("=" * 60)
    
    found_conversations = []
    
    # Explorer tous les workspaces
    for workspace_dir in base_path.iterdir():
        if workspace_dir.is_dir():
            print(f"\n📁 Workspace: {workspace_dir.name}")
            
            conversations_path = workspace_dir / "Augment.vscode-augment" / "augment-user-assets" / "checkpoint-documents"
            
            if conversations_path.exists():
                for conversation_dir in conversations_path.iterdir():
                    if conversation_dir.is_dir():
                        for file_path in conversation_dir.iterdir():
                            if file_path.is_file() and file_path.name.startswith("document-c__"):
                                
                                # EXCLURE nos scripts de recherche
                                exclude_patterns = [
                                    "script_conversation_chronologique",
                                    "script_examiner",
                                    "script_chercher",
                                    "script_extraire",
                                    "script_lire"
                                ]
                                
                                should_exclude = False
                                for pattern in exclude_patterns:
                                    if pattern in file_path.name:
                                        should_exclude = True
                                        break
                                
                                if should_exclude:
                                    continue
                                
                                try:
                                    with open(file_path, 'r', encoding='utf-8') as f:
                                        content = f.read()
                                    
                                    # Parser JSON
                                    try:
                                        data = json.loads(content)
                                        original_code = data.get('originalCode', '')
                                        modified_code = data.get('modifiedCode', '')
                                        full_content = original_code + "\n" + modified_code
                                        
                                        # Chercher des indicateurs de VRAIE conversation
                                        real_conversation_indicators = [
                                            "<<HUMAN_CONVERSATION_START>>",
                                            "Human:",
                                            "Assistant:",
                                            "Answer the user's request"
                                        ]
                                        
                                        found_indicators = []
                                        for indicator in real_conversation_indicators:
                                            if indicator in full_content:
                                                count = full_content.count(indicator)
                                                found_indicators.append(f"{indicator} ({count}x)")
                                        
                                        # Si on trouve des indicateurs de vraie conversation
                                        if found_indicators:
                                            timestamp_match = re.search(r'-(\d+)-', file_path.name)
                                            timestamp = None
                                            if timestamp_match:
                                                timestamp_ms = int(timestamp_match.group(1))
                                                timestamp = datetime.fromtimestamp(timestamp_ms / 1000)
                                            
                                            # Vérifier si c'est vraiment une conversation (pas du code)
                                            is_code = any(code_indicator in full_content for code_indicator in [
                                                "#!/usr/bin/env python3",
                                                "import os",
                                                "def ",
                                                "class ",
                                                "if __name__ == \"__main__\":"
                                            ])
                                            
                                            if not is_code:
                                                found_conversations.append({
                                                    'workspace_id': workspace_dir.name,
                                                    'conversation_id': conversation_dir.name,
                                                    'file_name': file_path.name,
                                                    'file_path': str(file_path),
                                                    'timestamp': timestamp,
                                                    'indicators': found_indicators,
                                                    'content_size': len(full_content),
                                                    'preview': full_content[:500],
                                                    'has_target_phrase': "tu avais raison finalement" in full_content.lower()
                                                })
                                                
                                                print(f"  ✅ VRAIE CONVERSATION TROUVÉE!")
                                                print(f"     📅 Date: {timestamp}")
                                                print(f"     📄 Fichier: {file_path.name[:60]}...")
                                                print(f"     🔑 Indicateurs: {', '.join(found_indicators)}")
                                                print(f"     📏 Taille: {len(full_content)} caractères")
                                                print(f"     🎯 Phrase cible: {'OUI' if 'tu avais raison finalement' in full_content.lower() else 'NON'}")
                                                
                                                # Afficher un aperçu
                                                lines = full_content.split('\n')[:10]
                                                print(f"     📝 Aperçu:")
                                                for line in lines:
                                                    if line.strip():
                                                        print(f"        {line[:60]}...")
                                                        break
                                        
                                    except json.JSONDecodeError:
                                        pass
                                        
                                except Exception as e:
                                    pass
    
    # Trier par timestamp (plus récent en premier)
    found_conversations.sort(key=lambda x: x['timestamp'] if x['timestamp'] else datetime.min, reverse=True)
    
    print(f"\n📊 RÉSUMÉ:")
    print(f"💬 Vraies conversations trouvées: {len(found_conversations)}")
    
    # Chercher spécifiquement celle avec "tu avais raison finalement"
    target_conversations = [conv for conv in found_conversations if conv['has_target_phrase']]
    
    if target_conversations:
        print(f"\n🎯 CONVERSATIONS AVEC 'TU AVAIS RAISON FINALEMENT': {len(target_conversations)}")
        for i, conv in enumerate(target_conversations, 1):
            print(f"\n--- CONVERSATION CIBLE {i} ---")
            print(f"📅 Date: {conv['timestamp']}")
            print(f"📄 Fichier: {conv['file_name']}")
            print(f"📁 Chemin: {conv['file_path']}")
            print(f"🔑 Indicateurs: {', '.join(conv['indicators'])}")
            print(f"📏 Taille: {conv['content_size']} caractères")
            
            return conv
    
    return found_conversations[0] if found_conversations else None

if __name__ == "__main__":
    print("🚀 RECHERCHE DE LA CONVERSATION ORIGINALE")
    print("=" * 60)
    
    result = chercher_conversation_originale()
    
    if result:
        print(f"\n🎉 CONVERSATION ORIGINALE TROUVÉE!")
        print(f"📁 Chemin: {result['file_path']}")
        
        if result['has_target_phrase']:
            print(f"🎯 CONTIENT 'TU AVAIS RAISON FINALEMENT'!")
        else:
            print(f"ℹ️ Ne contient pas la phrase cible, mais c'est une vraie conversation")
    else:
        print(f"\n❌ AUCUNE VRAIE CONVERSATION TROUVÉE")
        print("🤔 La conversation pourrait être dans un autre format ou emplacement")
