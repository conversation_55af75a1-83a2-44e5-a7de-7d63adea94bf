#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour rechercher dans les checkpoint-documents de la conversation spécifique
"""

import os
from pathlib import Path

def search_in_checkpoint_documents():
    """Recherche dans les checkpoint-documents de la conversation"""
    
    conv_path = Path("C:/Users/<USER>/AppData/Roaming/Code/User/workspaceStorage/0274dc7166a66c69494b9273936046e7/Augment.vscode-augment/augment-user-assets/checkpoint-documents/2bcca0ab-285d-410b-b63e-7d5f054030d9")
    
    print("🔍 RECHERCHE DANS CHECKPOINT-DOCUMENTS")
    print("=" * 60)
    print(f"📁 Répertoire: {conv_path}")
    
    if not conv_path.exists():
        print("❌ Répertoire non trouvé")
        return []
    
    found_files = []
    total_files = 0
    
    try:
        # Explorer tous les fichiers du répertoire
        for file_path in conv_path.iterdir():
            if file_path.is_file():
                total_files += 1
                
                try:
                    # Lire le fichier
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Chercher "tu avais raison"
                    content_lower = content.lower()
                    if "tu avais raison" in content_lower:
                        print(f"✅ TROUVÉ dans: {file_path.name}")
                        
                        # Extraire le contexte
                        start_pos = content_lower.find("tu avais raison")
                        context_start = max(0, start_pos - 300)
                        context_end = min(len(content), start_pos + 500)
                        context = content[context_start:context_end]
                        
                        found_files.append({
                            'file': file_path.name,
                            'full_path': str(file_path),
                            'context': context,
                            'full_content': content
                        })
                        
                        print(f"📝 Contexte:")
                        print("-" * 50)
                        print(context)
                        print("-" * 50)
                        print()
                    
                    # Chercher aussi "finalement" seul
                    elif "finalement" in content_lower:
                        print(f"⚠️ 'finalement' trouvé dans: {file_path.name}")
                        
                        # Extraire le contexte
                        start_pos = content_lower.find("finalement")
                        context_start = max(0, start_pos - 200)
                        context_end = min(len(content), start_pos + 300)
                        context = content[context_start:context_end]
                        
                        print(f"📝 Contexte 'finalement':")
                        print("-" * 30)
                        print(context)
                        print("-" * 30)
                        print()
                        
                except Exception as e:
                    print(f"⚠️ Erreur lecture {file_path.name}: {e}")
                    
        print(f"\n📊 RÉSUMÉ:")
        print(f"📋 Total fichiers analysés: {total_files}")
        print(f"💬 Fichiers avec 'tu avais raison': {len(found_files)}")
        
        return found_files
        
    except Exception as e:
        print(f"❌ Erreur générale: {e}")
        return []

def search_in_task_storage():
    """Recherche dans task-storage pour cette conversation"""
    
    print(f"\n🔍 RECHERCHE DANS TASK-STORAGE")
    print("=" * 60)
    
    # Chercher dans tous les workspaces
    base_path = Path("C:/Users/<USER>/AppData/Roaming/Code/User/workspaceStorage")
    conversation_id = "2bcca0ab-285d-410b-b63e-7d5f054030d9"
    
    for workspace in base_path.iterdir():
        if workspace.is_dir():
            task_path = workspace / "Augment.vscode-augment" / "augment-user-assets" / "task-storage" / "tasks" / conversation_id
            
            if task_path.exists():
                print(f"✅ Task trouvé dans: {workspace.name}")
                print(f"📁 Chemin: {task_path}")
                
                # Explorer le contenu
                for file_path in task_path.iterdir():
                    if file_path.is_file():
                        print(f"📄 Fichier: {file_path.name}")
                        
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                content = f.read()
                            
                            print(f"📏 Taille: {len(content)} caractères")
                            
                            # Chercher "tu avais raison"
                            if "tu avais raison" in content.lower():
                                print(f"🎉 'TU AVAIS RAISON' TROUVÉ!")
                                print(f"📝 Contenu complet:")
                                print("-" * 60)
                                print(content)
                                print("-" * 60)
                                return content
                            else:
                                print(f"📝 Aperçu (premiers 500 caractères):")
                                print(content[:500])
                                print("...")
                                
                        except Exception as e:
                            print(f"❌ Erreur lecture: {e}")
    
    print("❌ Aucun task-storage trouvé pour cette conversation")
    return None

if __name__ == "__main__":
    print("🚀 RECHERCHE DANS LES RÉPERTOIRES CONVERSATION")
    print("=" * 60)
    
    # Recherche dans checkpoint-documents
    checkpoint_results = search_in_checkpoint_documents()
    
    # Recherche dans task-storage
    task_result = search_in_task_storage()
    
    print(f"\n🎯 RÉSULTATS FINAUX:")
    if checkpoint_results:
        print(f"✅ {len(checkpoint_results)} fichiers trouvés dans checkpoint-documents")
    else:
        print("❌ Rien trouvé dans checkpoint-documents")
    
    if task_result:
        print("✅ Conversation trouvée dans task-storage")
    else:
        print("❌ Rien trouvé dans task-storage")
