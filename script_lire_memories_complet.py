#!/usr/bin/env python3
"""
Script pour lire complètement les Augment-Memories les plus importantes
"""

import os
from pathlib import Path

def lire_memories_complet():
    """Lit et affiche le contenu complet des mémoires importantes"""
    
    base_path = Path("C:/Users/<USER>/AppData/Roaming/Code/User/workspaceStorage")
    
    # Workspaces d'intérêt avec leurs tailles
    workspaces_importants = [
        "591be8b7f6bd4169258c0affc2eaa1fc",  # 4595 caractères - Framework épistémologique
        "f1b95ebcbba3cf8f700cb9ccd0a04fcf",   # 30547 caractères - Le plus volumineux
        "0274dc7166a66c69494b9273936046e7",   # 8110 caractères - AZR Model Architecture
        "85957ecfc79cd3f9a290bec200e7168c",   # 7183 caractères - Analyse comportement
        "ae986251edd6627681670aed67ef0194",   # 4681 caractères - Project Goal
    ]
    
    for workspace_id in workspaces_importants:
        memory_path = base_path / workspace_id / "Augment.vscode-augment" / "Augment-Memories"
        
        if memory_path.exists():
            print(f"\n{'='*80}")
            print(f"WORKSPACE: {workspace_id}")
            print(f"{'='*80}")
            
            try:
                with open(memory_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    print(content)
                    print(f"\n[Fin du contenu - {len(content)} caractères]")
                    
            except Exception as e:
                print(f"Erreur lecture: {e}")
        else:
            print(f"Fichier non trouvé: {memory_path}")

if __name__ == "__main__":
    lire_memories_complet()
