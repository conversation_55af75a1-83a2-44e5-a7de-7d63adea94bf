#!/usr/bin/env python3
"""
SCRIPT DE SAUVEGARDE DES FICHIERS SYSTÈME CRITIQUES
===================================================

Ce script crée des copies de sauvegarde de tous les fichiers système
qui pourraient être modifiés par notre solution de logging automatique.

Sécurité maximale avant toute intervention.
"""

import shutil
import datetime
from pathlib import Path
import os

class SystemBackupManager:
    def __init__(self):
        self.backup_dir = Path("C:/Users/<USER>/Desktop/Travail/Projet7/BACKUPS_SYSTEME")
        self.timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Fichiers critiques à sauvegarder
        self.critical_files = {
            "state_vscdb_actuel": Path("C:/Users/<USER>/AppData/Roaming/Code/User/workspaceStorage/a35ba43ef26792e61fe787a234121806/state.vscdb"),
            "workspace_complet": Path("C:/Users/<USER>/AppData/Roaming/Code/User/workspaceStorage/a35ba43ef26792e61fe787a234121806"),
        }
        
        # Autres workspaces Augment à sauvegarder
        self.augment_workspaces = [
            "591be8b7f6bd4169258c0affc2eaa1fc",
            "f1b95ebcbba3cf8f700cb9ccd0a04fcf", 
            "0274dc7166a66c69494b9273936046e7",
            "85957ecfc79cd3f9a290bec200e7168c",
            "ae986251edd6627681670aed67ef0194"
        ]
        
        self.backup_log = self.backup_dir / f"backup_log_{self.timestamp}.txt"
        
    def create_backup_directory(self):
        """Crée le répertoire de sauvegarde"""
        try:
            self.backup_dir.mkdir(parents=True, exist_ok=True)
            print(f"✅ Répertoire de sauvegarde créé: {self.backup_dir}")
            return True
        except Exception as e:
            print(f"❌ Erreur création répertoire: {e}")
            return False
    
    def log_backup_action(self, message):
        """Log les actions de sauvegarde"""
        try:
            with open(self.backup_log, 'a', encoding='utf-8') as f:
                timestamp = datetime.datetime.now().isoformat()
                f.write(f"[{timestamp}] {message}\n")
        except Exception as e:
            print(f"⚠️ Erreur log: {e}")
    
    def backup_file(self, source_path, backup_name):
        """Sauvegarde un fichier spécifique"""
        try:
            if not source_path.exists():
                message = f"❌ Fichier source non trouvé: {source_path}"
                print(message)
                self.log_backup_action(message)
                return False
            
            # Nom de sauvegarde avec timestamp
            backup_filename = f"{backup_name}_{self.timestamp}{source_path.suffix}"
            backup_path = self.backup_dir / backup_filename
            
            # Copie du fichier
            shutil.copy2(source_path, backup_path)
            
            # Vérification
            if backup_path.exists():
                size_original = source_path.stat().st_size
                size_backup = backup_path.stat().st_size
                
                if size_original == size_backup:
                    message = f"✅ Sauvegarde réussie: {backup_name} ({size_original} bytes)"
                    print(message)
                    self.log_backup_action(message)
                    return True
                else:
                    message = f"❌ Tailles différentes: {backup_name} (original: {size_original}, backup: {size_backup})"
                    print(message)
                    self.log_backup_action(message)
                    return False
            else:
                message = f"❌ Fichier de sauvegarde non créé: {backup_name}"
                print(message)
                self.log_backup_action(message)
                return False
                
        except Exception as e:
            message = f"❌ Erreur sauvegarde {backup_name}: {e}"
            print(message)
            self.log_backup_action(message)
            return False
    
    def backup_directory(self, source_path, backup_name):
        """Sauvegarde un répertoire complet"""
        try:
            if not source_path.exists():
                message = f"❌ Répertoire source non trouvé: {source_path}"
                print(message)
                self.log_backup_action(message)
                return False
            
            # Nom de sauvegarde avec timestamp
            backup_dirname = f"{backup_name}_{self.timestamp}"
            backup_path = self.backup_dir / backup_dirname
            
            # Copie récursive du répertoire
            shutil.copytree(source_path, backup_path)
            
            # Vérification
            if backup_path.exists():
                # Compter les fichiers
                original_files = list(source_path.rglob("*"))
                backup_files = list(backup_path.rglob("*"))
                
                message = f"✅ Sauvegarde répertoire réussie: {backup_name} ({len(original_files)} fichiers)"
                print(message)
                self.log_backup_action(message)
                return True
            else:
                message = f"❌ Répertoire de sauvegarde non créé: {backup_name}"
                print(message)
                self.log_backup_action(message)
                return False
                
        except Exception as e:
            message = f"❌ Erreur sauvegarde répertoire {backup_name}: {e}"
            print(message)
            self.log_backup_action(message)
            return False
    
    def backup_augment_workspaces(self):
        """Sauvegarde tous les workspaces Augment"""
        base_path = Path("C:/Users/<USER>/AppData/Roaming/Code/User/workspaceStorage")
        success_count = 0
        
        print(f"\n🔄 Sauvegarde des workspaces Augment...")
        
        for workspace_id in self.augment_workspaces:
            workspace_path = base_path / workspace_id
            
            if workspace_path.exists():
                # Sauvegarder le state.vscdb s'il existe
                state_file = workspace_path / "state.vscdb"
                if state_file.exists():
                    if self.backup_file(state_file, f"state_vscdb_{workspace_id}"):
                        success_count += 1
                
                # Sauvegarder les Augment-Memories s'ils existent
                augment_dir = workspace_path / "Augment.vscode-augment"
                if augment_dir.exists():
                    memories_file = augment_dir / "Augment-Memories"
                    if memories_file.exists():
                        self.backup_file(memories_file, f"augment_memories_{workspace_id}")
            else:
                message = f"⚠️ Workspace non trouvé: {workspace_id}"
                print(message)
                self.log_backup_action(message)
        
        return success_count
    
    def create_restoration_script(self):
        """Crée un script de restauration"""
        restore_script = self.backup_dir / f"RESTORE_SCRIPT_{self.timestamp}.py"
        
        script_content = f'''#!/usr/bin/env python3
"""
SCRIPT DE RESTAURATION AUTOMATIQUE
Généré le: {datetime.datetime.now().isoformat()}
"""

import shutil
from pathlib import Path

def restore_files():
    backup_dir = Path("{self.backup_dir}")
    
    print("🔄 RESTAURATION DES FICHIERS SYSTÈME")
    print("=" * 50)
    
    # Restaurer state.vscdb principal
    state_backup = backup_dir / "state_vscdb_actuel_{self.timestamp}.vscdb"
    state_original = Path("C:/Users/<USER>/AppData/Roaming/Code/User/workspaceStorage/a35ba43ef26792e61fe787a234121806/state.vscdb")
    
    if state_backup.exists():
        try:
            shutil.copy2(state_backup, state_original)
            print("✅ state.vscdb restauré")
        except Exception as e:
            print(f"❌ Erreur restauration state.vscdb: {{e}}")
    
    # Restaurer autres workspaces si nécessaire
    # [Ajouter ici d'autres restaurations selon les besoins]
    
    print("🏁 Restauration terminée")

if __name__ == "__main__":
    restore_files()
'''
        
        try:
            with open(restore_script, 'w', encoding='utf-8') as f:
                f.write(script_content)
            
            message = f"✅ Script de restauration créé: {restore_script.name}"
            print(message)
            self.log_backup_action(message)
            return True
            
        except Exception as e:
            message = f"❌ Erreur création script restauration: {e}"
            print(message)
            self.log_backup_action(message)
            return False
    
    def run_complete_backup(self):
        """Exécute une sauvegarde complète"""
        print(f"🛡️ SAUVEGARDE COMPLÈTE DES FICHIERS SYSTÈME")
        print(f"=" * 60)
        print(f"Timestamp: {self.timestamp}")
        print(f"Répertoire de sauvegarde: {self.backup_dir}")
        print()
        
        # Initialiser le log
        self.log_backup_action("=== DÉBUT SAUVEGARDE COMPLÈTE ===")
        
        # Créer répertoire de sauvegarde
        if not self.create_backup_directory():
            return False
        
        success_count = 0
        total_operations = 0
        
        # Sauvegarder le fichier state.vscdb principal
        print("🔄 Sauvegarde du fichier state.vscdb principal...")
        if self.backup_file(self.critical_files["state_vscdb_actuel"], "state_vscdb_actuel"):
            success_count += 1
        total_operations += 1
        
        # Sauvegarder les workspaces Augment
        workspace_successes = self.backup_augment_workspaces()
        success_count += workspace_successes
        total_operations += len(self.augment_workspaces)
        
        # Créer script de restauration
        print(f"\n🔄 Création du script de restauration...")
        if self.create_restoration_script():
            success_count += 1
        total_operations += 1
        
        # Résumé final
        print(f"\n📊 RÉSUMÉ DE LA SAUVEGARDE")
        print(f"=" * 40)
        print(f"Opérations réussies: {success_count}/{total_operations}")
        print(f"Répertoire de sauvegarde: {self.backup_dir}")
        print(f"Log détaillé: {self.backup_log}")
        
        self.log_backup_action(f"=== FIN SAUVEGARDE - {success_count}/{total_operations} réussies ===")
        
        if success_count == total_operations:
            print(f"✅ SAUVEGARDE COMPLÈTE RÉUSSIE !")
            return True
        else:
            print(f"⚠️ SAUVEGARDE PARTIELLE - Vérifiez le log")
            return False

def main():
    """Fonction principale"""
    backup_manager = SystemBackupManager()
    backup_manager.run_complete_backup()

if __name__ == "__main__":
    main()
