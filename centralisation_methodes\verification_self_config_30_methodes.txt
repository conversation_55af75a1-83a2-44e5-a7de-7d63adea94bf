RAPPORT DE VÉRIFICATION SELF.CONFIG - 30 MÉTHODES
=====================================================

RÉSULTATS GLOBAUX :
==================
Total méthodes vérifiées : 30
Méthodes utilisant self.config : 22
Méthodes sans self.config : 8
Méthodes avec valeurs codées : 21

MÉTHODES CENTRALISÉES (22) :
========================================
✅ __init__ - Ligne 1
   self.config utilisé : 6 fois
   ⚠️ Valeurs codées trouvées : ['17', '60', '110', '7']

✅ execute_cluster_pipeline - Ligne 29
   self.config utilisé : 5 fois
   ⚠️ Valeurs codées trouvées : ['1.', '2.', '60', '3.', '110', '7', '4', '5', '6', '7', '60', '110', '7']

✅ _calculate_cluster_confidence - Ligne 3788
   self.config utilisé : 13 fois
   ⚠️ Valeurs codées trouvées : ['1.', '2.', '3.', '4.', '5.', '6.']

✅ _calculate_confidence_risk_factors - Ligne 3916
   self.config utilisé : 25 fois
   ⚠️ Valeurs codées trouvées : ['1.', '2.', '3.', '4.']

✅ _calculate_epistemic_uncertainty - Ligne 3975
   self.config utilisé : 12 fois
   ⚠️ Valeurs codées trouvées : ['1.', '2.', '3.']

✅ _calculate_rollout_consensus - Ligne 4013
   self.config utilisé : 15 fois
   ⚠️ Valeurs codées trouvées : ['1.', '2.', '3.']

✅ calculate_rollout2_diversity_score - Ligne 4288
   self.config utilisé : 4 fois

✅ calculate_rollout3_reward - Ligne 4326
   self.config utilisé : 19 fois
   ⚠️ Valeurs codées trouvées : ['1.', '2.', '20', '10', '3.', '10', '4.', '10', '5.']

✅ calculate_rollout3_risk_factor - Ligne 4414
   self.config utilisé : 14 fois
   ⚠️ Valeurs codées trouvées : ['1.', '2.', '3.', '4.']

✅ _generate_sequence_from_signal - Ligne 4620
   self.config utilisé : 11 fois
   ⚠️ Valeurs codées trouvées : ['4']

✅ _classify_confidence_level - Ligne 4700
   self.config utilisé : 7 fois

✅ _generate_so_based_sequence - Ligne 4719
   self.config utilisé : 1 fois
   ⚠️ Valeurs codées trouvées : ['4', '4', '4']

✅ _generate_impair_sync_sequence - Ligne 5089
   self.config utilisé : 2 fois
   ⚠️ Valeurs codées trouvées : ['4', '4', '4']

✅ _calculate_asymmetric_impair_alert_level - Ligne 6023
   self.config utilisé : 3 fois
   ⚠️ Valeurs codées trouvées : ['4', '4']

✅ _calculate_asymmetric_pair_alert_level - Ligne 6041
   self.config utilisé : 3 fois
   ⚠️ Valeurs codées trouvées : ['6', '9', '12', '12', '9', '6']

✅ _generate_exploitation_recommendation - Ligne 8125
   self.config utilisé : 3 fois

✅ _identify_best_prediction_context - Ligne 8143
   self.config utilisé : 1 fois

✅ _calculate_statistical_significance - Ligne 8229
   self.config utilisé : 2 fois
   ⚠️ Valeurs codées trouvées : ['60', '40']

✅ get_max_sequence_length - Ligne 10328
   self.config utilisé : 2 fois
   ⚠️ Valeurs codées trouvées : ['60', '60']

✅ get_max_so_conversions - Ligne 10343
   self.config utilisé : 2 fois
   ⚠️ Valeurs codées trouvées : ['59', '59']

✅ _calculate_rupture_probability - Ligne 10738
   self.config utilisé : 15 fois
   ⚠️ Valeurs codées trouvées : ['9', '6']

✅ _calculate_cluster_confidence - Ligne 10906
   self.config utilisé : 8 fois
   ⚠️ Valeurs codées trouvées : ['95']


MÉTHODES NON CENTRALISÉES (8) :
=============================================
❌ _get_last_historical_pb_result - Ligne 4146
   self.config utilisé : 0 fois

❌ _analyze_complete_pbt_index - Ligne 5346
   self.config utilisé : 0 fois
   ⚠️ Valeurs codées trouvées : ['4']

❌ _analyze_complete_so_index - Ligne 5394
   self.config utilisé : 0 fois
   ⚠️ Valeurs codées trouvées : ['5']

❌ _analyze_desync_sync_to_pbt_impact - Ligne 5620
   self.config utilisé : 0 fois

❌ _analyze_tri_dimensional_impacts - Ligne 5786
   self.config utilisé : 0 fois

❌ _analyze_variations_impact_on_outcomes - Ligne 5828
   self.config utilisé : 0 fois
   ⚠️ Valeurs codées trouvées : ['1.', '2.', '3.', '4.', '5.', '6.']

❌ _analyze_consecutive_length_impact - Ligne 5893
   self.config utilisé : 0 fois

❌ is_game_complete - Ligne 10358
   self.config utilisé : 0 fois


MÉTHODES AVEC VALEURS CODÉES (21) :
=============================================
⚠️ __init__ - Ligne 1
   Valeurs trouvées : ['17', '60', '110', '7']
   self.config : OUI

⚠️ execute_cluster_pipeline - Ligne 29
   Valeurs trouvées : ['1.', '2.', '60', '3.', '110', '7', '4', '5', '6', '7', '60', '110', '7']
   self.config : OUI

⚠️ _calculate_cluster_confidence - Ligne 3788
   Valeurs trouvées : ['1.', '2.', '3.', '4.', '5.', '6.']
   self.config : OUI

⚠️ _calculate_confidence_risk_factors - Ligne 3916
   Valeurs trouvées : ['1.', '2.', '3.', '4.']
   self.config : OUI

⚠️ _calculate_epistemic_uncertainty - Ligne 3975
   Valeurs trouvées : ['1.', '2.', '3.']
   self.config : OUI

⚠️ _calculate_rollout_consensus - Ligne 4013
   Valeurs trouvées : ['1.', '2.', '3.']
   self.config : OUI

⚠️ calculate_rollout3_reward - Ligne 4326
   Valeurs trouvées : ['1.', '2.', '20', '10', '3.', '10', '4.', '10', '5.']
   self.config : OUI

⚠️ calculate_rollout3_risk_factor - Ligne 4414
   Valeurs trouvées : ['1.', '2.', '3.', '4.']
   self.config : OUI

⚠️ _generate_sequence_from_signal - Ligne 4620
   Valeurs trouvées : ['4']
   self.config : OUI

⚠️ _generate_so_based_sequence - Ligne 4719
   Valeurs trouvées : ['4', '4', '4']
   self.config : OUI

⚠️ _generate_impair_sync_sequence - Ligne 5089
   Valeurs trouvées : ['4', '4', '4']
   self.config : OUI

⚠️ _analyze_complete_pbt_index - Ligne 5346
   Valeurs trouvées : ['4']
   self.config : NON

⚠️ _analyze_complete_so_index - Ligne 5394
   Valeurs trouvées : ['5']
   self.config : NON

⚠️ _analyze_variations_impact_on_outcomes - Ligne 5828
   Valeurs trouvées : ['1.', '2.', '3.', '4.', '5.', '6.']
   self.config : NON

⚠️ _calculate_asymmetric_impair_alert_level - Ligne 6023
   Valeurs trouvées : ['4', '4']
   self.config : OUI

⚠️ _calculate_asymmetric_pair_alert_level - Ligne 6041
   Valeurs trouvées : ['6', '9', '12', '12', '9', '6']
   self.config : OUI

⚠️ _calculate_statistical_significance - Ligne 8229
   Valeurs trouvées : ['60', '40']
   self.config : OUI

⚠️ get_max_sequence_length - Ligne 10328
   Valeurs trouvées : ['60', '60']
   self.config : OUI

⚠️ get_max_so_conversions - Ligne 10343
   Valeurs trouvées : ['59', '59']
   self.config : OUI

⚠️ _calculate_rupture_probability - Ligne 10738
   Valeurs trouvées : ['9', '6']
   self.config : OUI

⚠️ _calculate_cluster_confidence - Ligne 10906
   Valeurs trouvées : ['95']
   self.config : OUI


CONCLUSION :
============
⚠️ 8 méthodes ne sont pas centralisées
❌ Centralisation incomplète
⚠️ 21 méthodes ont encore des valeurs codées
🔧 Centralisation supplémentaire nécessaire
