var yi=Object.defineProperty;var _i=(s,e,t)=>e in s?yi(s,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):s[e]=t;var ve=(s,e,t)=>_i(s,typeof e!="symbol"?e+"":e,t);import{ah as Le,ai as wi,aj as dn,S as fe,i as ge,s as he,Q as M,D as E,y as b,G as U,c as $,e as w,f as x,z as S,a4 as ze,ab as jt,q as K,r as W,u as p,t as m,h as y,B as k,a6 as qs,a2 as $e,H as we,T as de,aD as tr,E as Te,n as H,C as Bs,b as Fe,P as Ie,a as Se,a1 as es,V as Pe,W as je,X as Re,g as yt,Z as ts,a9 as Za,j as He,a7 as Oa,aA as kt,af as bt,F as Fn,a5 as xi,Y as nr,I as zn,J as Dn,K as Un,L as Vn,d as lt,M as qn,w as Ke,x as We,A as Ye,al as Ea,aE as bi,ap as Si,a0 as Ia,aq as sr,_ as Pa}from"./SpinnerAugment-BJ4-L7QR.js";import{P as _s,B as ki,T as Ci,F as Ti,L as Mi,U as Ai}from"./layer-group-CZFSGU8L.js";import"./design-system-init-DA68MSAy.js";import{W as ae,a as tt,e as xe,u as Ot,o as Et,h as Ae,g as Ni,H as rr}from"./BaseButton-C6Dhmpxa.js";import{T as ln,M as ja}from"./TextTooltipAugment-Bkzart3o.js";import{G as Zi,S as Oi,a as Ei,C as Ii,N as Pi,J as ji,L as Ri,F as Kn,b as Ve,D as Li,c as Fi,M as zi,d as Di}from"./mcp-logo-B9nTLE-q.js";import{c as Cs,R as mt,G as Ui,L as Me,F as Vi,d as qi,b as Bi,C as Ji,T as Gi}from"./github-C1PQK5DH.js";import{M as qt,R as Hi}from"./magnifying-glass-LWYs47rB.js";import{V as Ra}from"./VSCodeCodicon-CvBJfpPi.js";import{i as Zs,R as Ki,A as Wi,a as Yi}from"./test_service_pb-B6vKXZrG.js";import{I as ws,A as Xi}from"./IconButtonAugment-Certjadv.js";import{o as Qi}from"./keypress-DD1aQVr0.js";import{D as eo}from"./Drawer-CihA8FWD.js";import{B as Xe}from"./ButtonAugment-HnJOGilM.js";import{D as Ce,T as ns}from"./index-C-g0ZorP.js";import{T as to}from"./Content-Czt02SJi.js";import{C as no,P as so}from"./pen-to-square-Bm4lF9Yl.js";import{E as ro}from"./ellipsis-BWy9xWah.js";import{T as ao}from"./TextAreaAugment-Cj5jK817.js";import{M as io}from"./index-MyvMQzjq.js";import{M as oo}from"./MarkdownEditor-DWj1HgDp.js";import{A as co}from"./arrow-up-right-from-square-CuUnyQRL.js";import{C as lo,E as uo}from"./chat-flags-model-IiDhbRsI.js";import{A as Ts,c as Wn}from"./types-BSMhNRWH.js";import{R as ar}from"./chat-types-NgqNgjwU.js";import{R as po}from"./RulesDropdown-XGjfPruR.js";import{C as mo}from"./chevron-down-B88L5wkj.js";import"./index-CGbmuyBX.js";import"./resize-observer-DdAtcrRr.js";import"./CardAugment-BxTO-shY.js";import"./globals-D0QH3NT1.js";import"./lodash-ChYFUhWY.js";import"./file-paths-BcSg4gks.js";const nn={maxMS:9e5,initialMS:6e4,mult:2,maxSteps:4};class ho{constructor(e,t=nn){ve(this,"timerId",null);ve(this,"currentMS");ve(this,"step",0);ve(this,"params");this.callback=e;const n={...t};n.maxMS<0&&(console.warn("PollingManager: Negative maxMS detected, using default value of 15 minutes"),n.maxMS=nn.maxMS),n.initialMS<=0&&(console.warn("PollingManager: Negative or zero initialMS detected, using default value of 1 minute"),n.initialMS=nn.initialMS),n.mult<=0&&(console.warn("PollingManager: Negative or zero multiplier detected, using default value of 2"),n.mult=nn.mult),n.maxSteps!==void 0&&n.maxSteps<0&&(console.warn("PollingManager: Negative maxSteps detected, using default value of 4"),n.maxSteps=nn.maxSteps),this.params=n,this.currentMS=this.params.maxMS}startPolling(){this.stopPolling(),this.currentMS=this.params.initialMS,this.step=0,this.safeExecute(),this.scheduleNext()}stopPolling(){this.timerId!==null&&(window.clearTimeout(this.timerId),this.timerId=null)}dispose(){this.stopPolling()}scheduleNext(){this.timerId=window.setTimeout(()=>{if(this.safeExecute(),this.params.maxMS===0){if(this.step++,this.params.maxSteps!==void 0&&this.step>=this.params.maxSteps)return void this.stopPolling()}else this.currentMS<this.params.maxMS&&(this.step++,this.params.maxSteps!==void 0&&this.step>=this.params.maxSteps?(this.currentMS=this.params.maxMS,this.step=0):this.currentMS=Math.min(this.currentMS*this.params.mult,this.params.maxMS));this.scheduleNext()},this.currentMS)}safeExecute(){try{const e=this.callback();e instanceof Promise&&e.catch(t=>console.error("Error in polling callback:",t))}catch(e){console.error("Error in polling callback:",e)}}}class fo{constructor(e){ve(this,"configs",Le([]));ve(this,"pollingManager");ve(this,"_enableDebugFeatures",Le(!1));ve(this,"_settingsComponentSupported",Le({workspaceContext:!1,mcpServerList:!1,mcpServerImport:!1,orientation:!1,remoteTools:!1,userGuidelines:!1,terminal:!1,rules:!1}));ve(this,"_enableAgentMode",Le(!1));ve(this,"_enableInitialOrientation",Le(!1));ve(this,"_userTier",Le("unknown"));ve(this,"_guidelines",Le({}));this._host=e,this.pollingManager=new ho(()=>this.requestToolStatus(!1),{maxMS:0,initialMS:2e3,mult:1,maxSteps:150}),this.requestToolStatus(!1)}transformToolDisplay(e){const t=!e.isConfigured,n=e.oauthUrl;if(e.identifier.hostName===Cs.remoteToolHost){let r=e.identifier.toolId;switch(typeof r=="string"&&/^\d+$/.test(r)&&(r=Number(r)),r){case mt.GitHubApi:return{displayName:"GitHub",description:"Configure GitHub API access for repository operations",icon:Ui,requiresAuthentication:t,authUrl:n};case mt.Linear:return{displayName:"Linear",description:"Configure Linear API access for issue tracking",icon:Ri,requiresAuthentication:t,authUrl:n};case mt.Jira:return{displayName:"Jira",description:"Configure Jira API access for issue tracking",icon:ji,requiresAuthentication:t,authUrl:n};case mt.Notion:return{displayName:"Notion",description:"Configure Notion API access",icon:Pi,requiresAuthentication:t,authUrl:n};case mt.Confluence:return{displayName:"Confluence",description:"Configure Confluence API access",icon:Ii,requiresAuthentication:t,authUrl:n};case mt.WebSearch:return{displayName:"Web Search",description:"Configure web search capabilities",icon:Ei,requiresAuthentication:t,authUrl:n};case mt.Supabase:return{displayName:"Supabase",description:"Configure Supabase API access",icon:Oi,requiresAuthentication:t,authUrl:n};case mt.Glean:return{displayName:"Glean",description:"Configure Glean API access",icon:Zi,requiresAuthentication:t,authUrl:n};case mt.Unknown:return{displayName:"Unknown",description:"Unknown tool",requiresAuthentication:t,authUrl:n};default:throw new Error(`Unhandled RemoteToolId: ${r}`)}}else if(e.identifier.hostName===Cs.localToolHost){const r=e.identifier.toolId;switch(r){case Me.readFile:case Me.editFile:case Me.saveFile:case Me.launchProcess:case Me.killProcess:case Me.readProcess:case Me.writeProcess:case Me.listProcesses:case Me.waitProcess:case Me.openBrowser:case Me.clarify:case Me.onboardingSubAgent:case Me.strReplaceEditor:case Me.remember:case Me.diagnostics:case Me.setupScript:case Me.readTerminal:case Me.gitCommitRetrieval:return{displayName:e.definition.name.toString(),description:"Local tool",icon:Kn,requiresAuthentication:t,authUrl:n};default:throw new Error(`Unhandled LocalToolType: ${r}`)}}else if(e.identifier.hostName===Cs.sidecarToolHost){const r=e.identifier.toolId;switch(r){case Ve.codebaseRetrieval:return{displayName:"Code Search",description:"Configure codebase search capabilities",icon:qt,requiresAuthentication:t,authUrl:n};case Ve.shell:return{displayName:"Shell",description:"Shell",icon:qt,requiresAuthentication:t,authUrl:n};case Ve.strReplaceEditor:return{displayName:"File Edit",description:"File Editor",icon:qt,requiresAuthentication:t,authUrl:n};case Ve.view:return{displayName:"File View",description:"File Viewer",icon:qt,requiresAuthentication:t,authUrl:n};case Ve.webFetch:return{displayName:"Web Fetch",description:"Retrieve information from the web",icon:qt,requiresAuthentication:t,authUrl:n};case Ve.removeFiles:return{displayName:"Remove Files",description:"Remove files from the codebase",icon:Fi,requiresAuthentication:t,authUrl:n};case Ve.remember:return{displayName:e.definition.name.toString(),description:"Remember",icon:Kn,requiresAuthentication:t,authUrl:n};case Ve.saveFile:return{displayName:"Save File",description:"Save a new file",icon:Vi,requiresAuthentication:t,authUrl:n};case Ve.viewTaskList:return{displayName:"View Task List",description:"View the current task list",icon:Kn,requiresAuthentication:t,authUrl:n};case Ve.updateTaskList:return{displayName:"Update Task List",description:"Update the current task list",icon:Kn,requiresAuthentication:t,authUrl:n};case Ve.renderMermaid:return{displayName:"View Mermaid Diagram",description:"View a mermaid diagram",icon:Li,requiresAuthentication:t,authUrl:n};case Ve.grepSearch:return{displayName:"Grep search",description:"Run grep search",icon:qt,requiresAuthentication:t,authUrl:n};default:throw new Error(`Unhandled SidecarToolType: ${r}`)}}return{displayName:e.definition.name.toString(),description:e.definition.description||"",requiresAuthentication:t,authUrl:n}}handleMessageFromExtension(e){const t=e.data;switch(t.type){case ae.toolConfigInitialize:return this.createConfigsFromHostTools(t.data.hostTools,t.data.toolConfigs),t.data&&t.data.enableDebugFeatures!==void 0&&this._enableDebugFeatures.set(t.data.enableDebugFeatures),t.data&&t.data.settingsComponentSupported!==void 0&&this._settingsComponentSupported.set(t.data.settingsComponentSupported),t.data.enableAgentMode!==void 0&&this._enableAgentMode.set(t.data.enableAgentMode),t.data.enableInitialOrientation!==void 0&&this._enableInitialOrientation.set(t.data.enableInitialOrientation),t.data.userTier!==void 0&&this._userTier.set(t.data.userTier),t.data.guidelines!==void 0&&this._guidelines.set(t.data.guidelines),!0;case ae.toolConfigDefinitionsResponse:return this.configs.update(n=>this.createConfigsFromHostTools(t.data.hostTools,[]).map(r=>{const a=n.find(i=>i.name===r.name);return a?{...a,displayName:r.displayName,description:r.description,icon:r.icon,requiresAuthentication:r.requiresAuthentication,authUrl:r.authUrl,isConfigured:r.isConfigured}:r})),!0}return!1}createConfigsFromHostTools(e,t){return e.map(n=>{const r=this.transformToolDisplay(n),a=t.find(o=>o.name===n.definition.name),i=(a==null?void 0:a.isConfigured)??!r.requiresAuthentication;return{config:(a==null?void 0:a.config)??{},configString:JSON.stringify((a==null?void 0:a.config)??{},null,2),isConfigured:i,name:n.definition.name.toString(),displayName:r.displayName,description:r.description,identifier:n.identifier,icon:r.icon,requiresAuthentication:r.requiresAuthentication,authUrl:r.authUrl,showStatus:!1,statusMessage:"",statusType:"info"}})}getConfigs(){return this.configs}isDisplayableTool(e){return["github","linear","notion","jira","confluence","supabase"].includes(e.displayName.toLowerCase())}getDisplayableTools(){return wi(this.configs,e=>{const t=e.filter(r=>this.isDisplayableTool(r)),n=new Map;for(const r of t)n.set(r.displayName,r);return Array.from(n.values()).sort((r,a)=>{const i={GitHub:1,Linear:2,Notion:3},o=Number.MAX_SAFE_INTEGER,c=i[r.displayName]||o,l=i[a.displayName]||o;return c<o&&l<o||c===o&&l===o?c!==l?c-l:r.displayName.localeCompare(a.displayName):c-l})})}saveConfig(e){this.startPolling()}notifyLoaded(){this._host.postMessage({type:ae.toolConfigLoaded})}startPolling(){this.pollingManager.startPolling()}requestToolStatus(e=!0){this._host.postMessage({type:ae.toolConfigGetDefinitions,data:{useCache:e}})}dispose(){this.pollingManager.dispose()}getEnableDebugFeatures(){return this._enableDebugFeatures}getEnableAgentMode(){return this._enableAgentMode}getEnableInitialOrientation(){return this._enableInitialOrientation}getUserTier(){return this._userTier}getGuidelines(){return this._guidelines}updateLocalUserGuidelines(e){this._guidelines.update(t=>t.userGuidelines?{...t,userGuidelines:{...t.userGuidelines,contents:e,enabled:e.length>0}}:t)}getSettingsComponentSupported(){return this._settingsComponentSupported}}var ue,Os;(function(s){s.assertEqual=e=>e,s.assertIs=function(e){},s.assertNever=function(e){throw new Error},s.arrayToEnum=e=>{const t={};for(const n of e)t[n]=n;return t},s.getValidEnumValues=e=>{const t=s.objectKeys(e).filter(r=>typeof e[e[r]]!="number"),n={};for(const r of t)n[r]=e[r];return s.objectValues(n)},s.objectValues=e=>s.objectKeys(e).map(function(t){return e[t]}),s.objectKeys=typeof Object.keys=="function"?e=>Object.keys(e):e=>{const t=[];for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.push(n);return t},s.find=(e,t)=>{for(const n of e)if(t(n))return n},s.isInteger=typeof Number.isInteger=="function"?e=>Number.isInteger(e):e=>typeof e=="number"&&isFinite(e)&&Math.floor(e)===e,s.joinValues=function(e,t=" | "){return e.map(n=>typeof n=="string"?`'${n}'`:n).join(t)},s.jsonStringifyReplacer=(e,t)=>typeof t=="bigint"?t.toString():t})(ue||(ue={})),(Os||(Os={})).mergeShapes=(s,e)=>({...s,...e});const z=ue.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),ht=s=>{switch(typeof s){case"undefined":return z.undefined;case"string":return z.string;case"number":return isNaN(s)?z.nan:z.number;case"boolean":return z.boolean;case"function":return z.function;case"bigint":return z.bigint;case"symbol":return z.symbol;case"object":return Array.isArray(s)?z.array:s===null?z.null:s.then&&typeof s.then=="function"&&s.catch&&typeof s.catch=="function"?z.promise:typeof Map<"u"&&s instanceof Map?z.map:typeof Set<"u"&&s instanceof Set?z.set:typeof Date<"u"&&s instanceof Date?z.date:z.object;default:return z.unknown}},Z=ue.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);let at=class La extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=n=>{this.issues=[...this.issues,n]},this.addIssues=(n=[])=>{this.issues=[...this.issues,...n]};const t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){const t=e||function(a){return a.message},n={_errors:[]},r=a=>{for(const i of a.issues)if(i.code==="invalid_union")i.unionErrors.map(r);else if(i.code==="invalid_return_type")r(i.returnTypeError);else if(i.code==="invalid_arguments")r(i.argumentsError);else if(i.path.length===0)n._errors.push(t(i));else{let o=n,c=0;for(;c<i.path.length;){const l=i.path[c];c===i.path.length-1?(o[l]=o[l]||{_errors:[]},o[l]._errors.push(t(i))):o[l]=o[l]||{_errors:[]},o=o[l],c++}}};return r(this),n}static assert(e){if(!(e instanceof La))throw new Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,ue.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(e=t=>t.message){const t={},n=[];for(const r of this.issues)r.path.length>0?(t[r.path[0]]=t[r.path[0]]||[],t[r.path[0]].push(e(r))):n.push(e(r));return{formErrors:n,fieldErrors:t}}get formErrors(){return this.flatten()}};at.create=s=>new at(s);const Ht=(s,e)=>{let t;switch(s.code){case Z.invalid_type:t=s.received===z.undefined?"Required":`Expected ${s.expected}, received ${s.received}`;break;case Z.invalid_literal:t=`Invalid literal value, expected ${JSON.stringify(s.expected,ue.jsonStringifyReplacer)}`;break;case Z.unrecognized_keys:t=`Unrecognized key(s) in object: ${ue.joinValues(s.keys,", ")}`;break;case Z.invalid_union:t="Invalid input";break;case Z.invalid_union_discriminator:t=`Invalid discriminator value. Expected ${ue.joinValues(s.options)}`;break;case Z.invalid_enum_value:t=`Invalid enum value. Expected ${ue.joinValues(s.options)}, received '${s.received}'`;break;case Z.invalid_arguments:t="Invalid function arguments";break;case Z.invalid_return_type:t="Invalid function return type";break;case Z.invalid_date:t="Invalid date";break;case Z.invalid_string:typeof s.validation=="object"?"includes"in s.validation?(t=`Invalid input: must include "${s.validation.includes}"`,typeof s.validation.position=="number"&&(t=`${t} at one or more positions greater than or equal to ${s.validation.position}`)):"startsWith"in s.validation?t=`Invalid input: must start with "${s.validation.startsWith}"`:"endsWith"in s.validation?t=`Invalid input: must end with "${s.validation.endsWith}"`:ue.assertNever(s.validation):t=s.validation!=="regex"?`Invalid ${s.validation}`:"Invalid";break;case Z.too_small:t=s.type==="array"?`Array must contain ${s.exact?"exactly":s.inclusive?"at least":"more than"} ${s.minimum} element(s)`:s.type==="string"?`String must contain ${s.exact?"exactly":s.inclusive?"at least":"over"} ${s.minimum} character(s)`:s.type==="number"?`Number must be ${s.exact?"exactly equal to ":s.inclusive?"greater than or equal to ":"greater than "}${s.minimum}`:s.type==="date"?`Date must be ${s.exact?"exactly equal to ":s.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(s.minimum))}`:"Invalid input";break;case Z.too_big:t=s.type==="array"?`Array must contain ${s.exact?"exactly":s.inclusive?"at most":"less than"} ${s.maximum} element(s)`:s.type==="string"?`String must contain ${s.exact?"exactly":s.inclusive?"at most":"under"} ${s.maximum} character(s)`:s.type==="number"?`Number must be ${s.exact?"exactly":s.inclusive?"less than or equal to":"less than"} ${s.maximum}`:s.type==="bigint"?`BigInt must be ${s.exact?"exactly":s.inclusive?"less than or equal to":"less than"} ${s.maximum}`:s.type==="date"?`Date must be ${s.exact?"exactly":s.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(s.maximum))}`:"Invalid input";break;case Z.custom:t="Invalid input";break;case Z.invalid_intersection_types:t="Intersection results could not be merged";break;case Z.not_multiple_of:t=`Number must be a multiple of ${s.multipleOf}`;break;case Z.not_finite:t="Number must be finite";break;default:t=e.defaultError,ue.assertNever(s)}return{message:t}};let Fa=Ht;function ss(){return Fa}const rs=s=>{const{data:e,path:t,errorMaps:n,issueData:r}=s,a=[...t,...r.path||[]],i={...r,path:a};if(r.message!==void 0)return{...r,path:a,message:r.message};let o="";const c=n.filter(l=>!!l).slice().reverse();for(const l of c)o=l(i,{data:e,defaultError:o}).message;return{...r,path:a,message:o}};function R(s,e){const t=ss(),n=rs({issueData:e,data:s.data,path:s.path,errorMaps:[s.common.contextualErrorMap,s.schemaErrorMap,t,t===Ht?void 0:Ht].filter(r=>!!r)});s.common.issues.push(n)}let De=class za{constructor(){this.value="valid"}dirty(){this.value==="valid"&&(this.value="dirty")}abort(){this.value!=="aborted"&&(this.value="aborted")}static mergeArray(e,t){const n=[];for(const r of t){if(r.status==="aborted")return ee;r.status==="dirty"&&e.dirty(),n.push(r.value)}return{status:e.value,value:n}}static async mergeObjectAsync(e,t){const n=[];for(const r of t){const a=await r.key,i=await r.value;n.push({key:a,value:i})}return za.mergeObjectSync(e,n)}static mergeObjectSync(e,t){const n={};for(const r of t){const{key:a,value:i}=r;if(a.status==="aborted"||i.status==="aborted")return ee;a.status==="dirty"&&e.dirty(),i.status==="dirty"&&e.dirty(),a.value==="__proto__"||i.value===void 0&&!r.alwaysSet||(n[a.value]=i.value)}return{status:e.value,value:n}}};const ee=Object.freeze({status:"aborted"}),as=s=>({status:"dirty",value:s}),Oe=s=>({status:"valid",value:s}),Es=s=>s.status==="aborted",Is=s=>s.status==="dirty",Rt=s=>s.status==="valid",un=s=>typeof Promise<"u"&&s instanceof Promise;function is(s,e,t,n){if(typeof e=="function"?s!==e||!n:!e.has(s))throw new TypeError("Cannot read private member from an object whose class did not declare it");return e.get(s)}function Da(s,e,t,n,r){if(typeof e=="function"?s!==e||!r:!e.has(s))throw new TypeError("Cannot write private member to an object whose class did not declare it");return e.set(s,t),t}var V,sn,rn;typeof SuppressedError=="function"&&SuppressedError,function(s){s.errToObj=e=>typeof e=="string"?{message:e}:e||{},s.toString=e=>typeof e=="string"?e:e==null?void 0:e.message}(V||(V={}));let dt=class{constructor(s,e,t,n){this._cachedPath=[],this.parent=s,this.data=e,this._path=t,this._key=n}get path(){return this._cachedPath.length||(this._key instanceof Array?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}};const ir=(s,e)=>{if(Rt(e))return{success:!0,data:e.value};if(!s.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const t=new at(s.common.issues);return this._error=t,this._error}}};function ne(s){if(!s)return{};const{errorMap:e,invalid_type_error:t,required_error:n,description:r}=s;if(e&&(t||n))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return e?{errorMap:e,description:r}:{errorMap:(a,i)=>{var o,c;const{message:l}=s;return a.code==="invalid_enum_value"?{message:l??i.defaultError}:i.data===void 0?{message:(o=l??n)!==null&&o!==void 0?o:i.defaultError}:a.code!=="invalid_type"?{message:i.defaultError}:{message:(c=l??t)!==null&&c!==void 0?c:i.defaultError}},description:r}}let ie=class{get description(){return this._def.description}_getType(s){return ht(s.data)}_getOrReturnCtx(s,e){return e||{common:s.parent.common,data:s.data,parsedType:ht(s.data),schemaErrorMap:this._def.errorMap,path:s.path,parent:s.parent}}_processInputParams(s){return{status:new De,ctx:{common:s.parent.common,data:s.data,parsedType:ht(s.data),schemaErrorMap:this._def.errorMap,path:s.path,parent:s.parent}}}_parseSync(s){const e=this._parse(s);if(un(e))throw new Error("Synchronous parse encountered promise.");return e}_parseAsync(s){const e=this._parse(s);return Promise.resolve(e)}parse(s,e){const t=this.safeParse(s,e);if(t.success)return t.data;throw t.error}safeParse(s,e){var t;const n={common:{issues:[],async:(t=e==null?void 0:e.async)!==null&&t!==void 0&&t,contextualErrorMap:e==null?void 0:e.errorMap},path:(e==null?void 0:e.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:s,parsedType:ht(s)},r=this._parseSync({data:s,path:n.path,parent:n});return ir(n,r)}"~validate"(s){var e,t;const n={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:s,parsedType:ht(s)};if(!this["~standard"].async)try{const r=this._parseSync({data:s,path:[],parent:n});return Rt(r)?{value:r.value}:{issues:n.common.issues}}catch(r){!((t=(e=r==null?void 0:r.message)===null||e===void 0?void 0:e.toLowerCase())===null||t===void 0)&&t.includes("encountered")&&(this["~standard"].async=!0),n.common={issues:[],async:!0}}return this._parseAsync({data:s,path:[],parent:n}).then(r=>Rt(r)?{value:r.value}:{issues:n.common.issues})}async parseAsync(s,e){const t=await this.safeParseAsync(s,e);if(t.success)return t.data;throw t.error}async safeParseAsync(s,e){const t={common:{issues:[],contextualErrorMap:e==null?void 0:e.errorMap,async:!0},path:(e==null?void 0:e.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:s,parsedType:ht(s)},n=this._parse({data:s,path:t.path,parent:t}),r=await(un(n)?n:Promise.resolve(n));return ir(t,r)}refine(s,e){const t=n=>typeof e=="string"||e===void 0?{message:e}:typeof e=="function"?e(n):e;return this._refinement((n,r)=>{const a=s(n),i=()=>r.addIssue({code:Z.custom,...t(n)});return typeof Promise<"u"&&a instanceof Promise?a.then(o=>!!o||(i(),!1)):!!a||(i(),!1)})}refinement(s,e){return this._refinement((t,n)=>!!s(t)||(n.addIssue(typeof e=="function"?e(t,n):e),!1))}_refinement(s){return new nt({schema:this,typeName:X.ZodEffects,effect:{type:"refinement",refinement:s}})}superRefine(s){return this._refinement(s)}constructor(s){this.spa=this.safeParseAsync,this._def=s,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:e=>this["~validate"](e)}}optional(){return ot.create(this,this._def)}nullable(){return Tt.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return Lt.create(this)}promise(){return Yt.create(this,this._def)}or(s){return $n.create([this,s],this._def)}and(s){return yn.create(this,s,this._def)}transform(s){return new nt({...ne(this._def),schema:this,typeName:X.ZodEffects,effect:{type:"transform",transform:s}})}default(s){const e=typeof s=="function"?s:()=>s;return new bn({...ne(this._def),innerType:this,defaultValue:e,typeName:X.ZodDefault})}brand(){return new Js({typeName:X.ZodBranded,type:this,...ne(this._def)})}catch(s){const e=typeof s=="function"?s:()=>s;return new Sn({...ne(this._def),innerType:this,catchValue:e,typeName:X.ZodCatch})}describe(s){return new this.constructor({...this._def,description:s})}pipe(s){return Gs.create(this,s)}readonly(){return kn.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}};const go=/^c[^\s-]{8,}$/i,vo=/^[0-9a-z]+$/,$o=/^[0-9A-HJKMNP-TV-Z]{26}$/i,yo=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,_o=/^[a-z0-9_-]{21}$/i,wo=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,xo=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,bo=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i;let Ms;const So=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,ko=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,Co=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,To=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,Mo=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,Ao=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,Ua="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",No=new RegExp(`^${Ua}$`);function Va(s){let e="([01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d";return s.precision?e=`${e}\\.\\d{${s.precision}}`:s.precision==null&&(e=`${e}(\\.\\d+)?`),e}function qa(s){let e=`${Ua}T${Va(s)}`;const t=[];return t.push(s.local?"Z?":"Z"),s.offset&&t.push("([+-]\\d{2}:?\\d{2})"),e=`${e}(${t.join("|")})`,new RegExp(`^${e}$`)}function Zo(s,e){if(!wo.test(s))return!1;try{const[t]=s.split("."),n=t.replace(/-/g,"+").replace(/_/g,"/").padEnd(t.length+(4-t.length%4)%4,"="),r=JSON.parse(atob(n));return typeof r=="object"&&r!==null&&!(!r.typ||!r.alg)&&(!e||r.alg===e)}catch{return!1}}function Oo(s,e){return!(e!=="v4"&&e||!ko.test(s))||!(e!=="v6"&&e||!To.test(s))}let Kt=class an extends ie{_parse(e){if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==z.string){const i=this._getOrReturnCtx(e);return R(i,{code:Z.invalid_type,expected:z.string,received:i.parsedType}),ee}const t=new De;let n;for(const i of this._def.checks)if(i.kind==="min")e.data.length<i.value&&(n=this._getOrReturnCtx(e,n),R(n,{code:Z.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),t.dirty());else if(i.kind==="max")e.data.length>i.value&&(n=this._getOrReturnCtx(e,n),R(n,{code:Z.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),t.dirty());else if(i.kind==="length"){const o=e.data.length>i.value,c=e.data.length<i.value;(o||c)&&(n=this._getOrReturnCtx(e,n),o?R(n,{code:Z.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}):c&&R(n,{code:Z.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}),t.dirty())}else if(i.kind==="email")bo.test(e.data)||(n=this._getOrReturnCtx(e,n),R(n,{validation:"email",code:Z.invalid_string,message:i.message}),t.dirty());else if(i.kind==="emoji")Ms||(Ms=new RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),Ms.test(e.data)||(n=this._getOrReturnCtx(e,n),R(n,{validation:"emoji",code:Z.invalid_string,message:i.message}),t.dirty());else if(i.kind==="uuid")yo.test(e.data)||(n=this._getOrReturnCtx(e,n),R(n,{validation:"uuid",code:Z.invalid_string,message:i.message}),t.dirty());else if(i.kind==="nanoid")_o.test(e.data)||(n=this._getOrReturnCtx(e,n),R(n,{validation:"nanoid",code:Z.invalid_string,message:i.message}),t.dirty());else if(i.kind==="cuid")go.test(e.data)||(n=this._getOrReturnCtx(e,n),R(n,{validation:"cuid",code:Z.invalid_string,message:i.message}),t.dirty());else if(i.kind==="cuid2")vo.test(e.data)||(n=this._getOrReturnCtx(e,n),R(n,{validation:"cuid2",code:Z.invalid_string,message:i.message}),t.dirty());else if(i.kind==="ulid")$o.test(e.data)||(n=this._getOrReturnCtx(e,n),R(n,{validation:"ulid",code:Z.invalid_string,message:i.message}),t.dirty());else if(i.kind==="url")try{new URL(e.data)}catch{n=this._getOrReturnCtx(e,n),R(n,{validation:"url",code:Z.invalid_string,message:i.message}),t.dirty()}else i.kind==="regex"?(i.regex.lastIndex=0,i.regex.test(e.data)||(n=this._getOrReturnCtx(e,n),R(n,{validation:"regex",code:Z.invalid_string,message:i.message}),t.dirty())):i.kind==="trim"?e.data=e.data.trim():i.kind==="includes"?e.data.includes(i.value,i.position)||(n=this._getOrReturnCtx(e,n),R(n,{code:Z.invalid_string,validation:{includes:i.value,position:i.position},message:i.message}),t.dirty()):i.kind==="toLowerCase"?e.data=e.data.toLowerCase():i.kind==="toUpperCase"?e.data=e.data.toUpperCase():i.kind==="startsWith"?e.data.startsWith(i.value)||(n=this._getOrReturnCtx(e,n),R(n,{code:Z.invalid_string,validation:{startsWith:i.value},message:i.message}),t.dirty()):i.kind==="endsWith"?e.data.endsWith(i.value)||(n=this._getOrReturnCtx(e,n),R(n,{code:Z.invalid_string,validation:{endsWith:i.value},message:i.message}),t.dirty()):i.kind==="datetime"?qa(i).test(e.data)||(n=this._getOrReturnCtx(e,n),R(n,{code:Z.invalid_string,validation:"datetime",message:i.message}),t.dirty()):i.kind==="date"?No.test(e.data)||(n=this._getOrReturnCtx(e,n),R(n,{code:Z.invalid_string,validation:"date",message:i.message}),t.dirty()):i.kind==="time"?new RegExp(`^${Va(i)}$`).test(e.data)||(n=this._getOrReturnCtx(e,n),R(n,{code:Z.invalid_string,validation:"time",message:i.message}),t.dirty()):i.kind==="duration"?xo.test(e.data)||(n=this._getOrReturnCtx(e,n),R(n,{validation:"duration",code:Z.invalid_string,message:i.message}),t.dirty()):i.kind==="ip"?(r=e.data,((a=i.version)!=="v4"&&a||!So.test(r))&&(a!=="v6"&&a||!Co.test(r))&&(n=this._getOrReturnCtx(e,n),R(n,{validation:"ip",code:Z.invalid_string,message:i.message}),t.dirty())):i.kind==="jwt"?Zo(e.data,i.alg)||(n=this._getOrReturnCtx(e,n),R(n,{validation:"jwt",code:Z.invalid_string,message:i.message}),t.dirty()):i.kind==="cidr"?Oo(e.data,i.version)||(n=this._getOrReturnCtx(e,n),R(n,{validation:"cidr",code:Z.invalid_string,message:i.message}),t.dirty()):i.kind==="base64"?Mo.test(e.data)||(n=this._getOrReturnCtx(e,n),R(n,{validation:"base64",code:Z.invalid_string,message:i.message}),t.dirty()):i.kind==="base64url"?Ao.test(e.data)||(n=this._getOrReturnCtx(e,n),R(n,{validation:"base64url",code:Z.invalid_string,message:i.message}),t.dirty()):ue.assertNever(i);var r,a;return{status:t.value,value:e.data}}_regex(e,t,n){return this.refinement(r=>e.test(r),{validation:t,code:Z.invalid_string,...V.errToObj(n)})}_addCheck(e){return new an({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...V.errToObj(e)})}url(e){return this._addCheck({kind:"url",...V.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...V.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...V.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...V.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...V.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...V.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...V.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...V.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...V.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...V.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...V.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...V.errToObj(e)})}datetime(e){var t,n;return typeof e=="string"?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:(e==null?void 0:e.precision)===void 0?null:e==null?void 0:e.precision,offset:(t=e==null?void 0:e.offset)!==null&&t!==void 0&&t,local:(n=e==null?void 0:e.local)!==null&&n!==void 0&&n,...V.errToObj(e==null?void 0:e.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return typeof e=="string"?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:(e==null?void 0:e.precision)===void 0?null:e==null?void 0:e.precision,...V.errToObj(e==null?void 0:e.message)})}duration(e){return this._addCheck({kind:"duration",...V.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...V.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:t==null?void 0:t.position,...V.errToObj(t==null?void 0:t.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...V.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...V.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...V.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...V.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...V.errToObj(t)})}nonempty(e){return this.min(1,V.errToObj(e))}trim(){return new an({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new an({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new an({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>e.kind==="datetime")}get isDate(){return!!this._def.checks.find(e=>e.kind==="date")}get isTime(){return!!this._def.checks.find(e=>e.kind==="time")}get isDuration(){return!!this._def.checks.find(e=>e.kind==="duration")}get isEmail(){return!!this._def.checks.find(e=>e.kind==="email")}get isURL(){return!!this._def.checks.find(e=>e.kind==="url")}get isEmoji(){return!!this._def.checks.find(e=>e.kind==="emoji")}get isUUID(){return!!this._def.checks.find(e=>e.kind==="uuid")}get isNANOID(){return!!this._def.checks.find(e=>e.kind==="nanoid")}get isCUID(){return!!this._def.checks.find(e=>e.kind==="cuid")}get isCUID2(){return!!this._def.checks.find(e=>e.kind==="cuid2")}get isULID(){return!!this._def.checks.find(e=>e.kind==="ulid")}get isIP(){return!!this._def.checks.find(e=>e.kind==="ip")}get isCIDR(){return!!this._def.checks.find(e=>e.kind==="cidr")}get isBase64(){return!!this._def.checks.find(e=>e.kind==="base64")}get isBase64url(){return!!this._def.checks.find(e=>e.kind==="base64url")}get minLength(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}};function Eo(s,e){const t=(s.toString().split(".")[1]||"").length,n=(e.toString().split(".")[1]||"").length,r=t>n?t:n;return parseInt(s.toFixed(r).replace(".",""))%parseInt(e.toFixed(r).replace(".",""))/Math.pow(10,r)}Kt.create=s=>{var e;return new Kt({checks:[],typeName:X.ZodString,coerce:(e=s==null?void 0:s.coerce)!==null&&e!==void 0&&e,...ne(s)})};let pn=class Ps extends ie{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==z.number){const r=this._getOrReturnCtx(e);return R(r,{code:Z.invalid_type,expected:z.number,received:r.parsedType}),ee}let t;const n=new De;for(const r of this._def.checks)r.kind==="int"?ue.isInteger(e.data)||(t=this._getOrReturnCtx(e,t),R(t,{code:Z.invalid_type,expected:"integer",received:"float",message:r.message}),n.dirty()):r.kind==="min"?(r.inclusive?e.data<r.value:e.data<=r.value)&&(t=this._getOrReturnCtx(e,t),R(t,{code:Z.too_small,minimum:r.value,type:"number",inclusive:r.inclusive,exact:!1,message:r.message}),n.dirty()):r.kind==="max"?(r.inclusive?e.data>r.value:e.data>=r.value)&&(t=this._getOrReturnCtx(e,t),R(t,{code:Z.too_big,maximum:r.value,type:"number",inclusive:r.inclusive,exact:!1,message:r.message}),n.dirty()):r.kind==="multipleOf"?Eo(e.data,r.value)!==0&&(t=this._getOrReturnCtx(e,t),R(t,{code:Z.not_multiple_of,multipleOf:r.value,message:r.message}),n.dirty()):r.kind==="finite"?Number.isFinite(e.data)||(t=this._getOrReturnCtx(e,t),R(t,{code:Z.not_finite,message:r.message}),n.dirty()):ue.assertNever(r);return{status:n.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,V.toString(t))}gt(e,t){return this.setLimit("min",e,!1,V.toString(t))}lte(e,t){return this.setLimit("max",e,!0,V.toString(t))}lt(e,t){return this.setLimit("max",e,!1,V.toString(t))}setLimit(e,t,n,r){return new Ps({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:n,message:V.toString(r)}]})}_addCheck(e){return new Ps({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:V.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:V.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:V.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:V.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:V.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:V.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:V.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:V.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:V.toString(e)})}get minValue(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>e.kind==="int"||e.kind==="multipleOf"&&ue.isInteger(e.value))}get isFinite(){let e=null,t=null;for(const n of this._def.checks){if(n.kind==="finite"||n.kind==="int"||n.kind==="multipleOf")return!0;n.kind==="min"?(t===null||n.value>t)&&(t=n.value):n.kind==="max"&&(e===null||n.value<e)&&(e=n.value)}return Number.isFinite(t)&&Number.isFinite(e)}};pn.create=s=>new pn({checks:[],typeName:X.ZodNumber,coerce:(s==null?void 0:s.coerce)||!1,...ne(s)});let mn=class js extends ie{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==z.bigint)return this._getInvalidInput(e);let t;const n=new De;for(const r of this._def.checks)r.kind==="min"?(r.inclusive?e.data<r.value:e.data<=r.value)&&(t=this._getOrReturnCtx(e,t),R(t,{code:Z.too_small,type:"bigint",minimum:r.value,inclusive:r.inclusive,message:r.message}),n.dirty()):r.kind==="max"?(r.inclusive?e.data>r.value:e.data>=r.value)&&(t=this._getOrReturnCtx(e,t),R(t,{code:Z.too_big,type:"bigint",maximum:r.value,inclusive:r.inclusive,message:r.message}),n.dirty()):r.kind==="multipleOf"?e.data%r.value!==BigInt(0)&&(t=this._getOrReturnCtx(e,t),R(t,{code:Z.not_multiple_of,multipleOf:r.value,message:r.message}),n.dirty()):ue.assertNever(r);return{status:n.value,value:e.data}}_getInvalidInput(e){const t=this._getOrReturnCtx(e);return R(t,{code:Z.invalid_type,expected:z.bigint,received:t.parsedType}),ee}gte(e,t){return this.setLimit("min",e,!0,V.toString(t))}gt(e,t){return this.setLimit("min",e,!1,V.toString(t))}lte(e,t){return this.setLimit("max",e,!0,V.toString(t))}lt(e,t){return this.setLimit("max",e,!1,V.toString(t))}setLimit(e,t,n,r){return new js({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:n,message:V.toString(r)}]})}_addCheck(e){return new js({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:V.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:V.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:V.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:V.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:V.toString(t)})}get minValue(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}};mn.create=s=>{var e;return new mn({checks:[],typeName:X.ZodBigInt,coerce:(e=s==null?void 0:s.coerce)!==null&&e!==void 0&&e,...ne(s)})};let hn=class extends ie{_parse(s){if(this._def.coerce&&(s.data=!!s.data),this._getType(s)!==z.boolean){const e=this._getOrReturnCtx(s);return R(e,{code:Z.invalid_type,expected:z.boolean,received:e.parsedType}),ee}return Oe(s.data)}};hn.create=s=>new hn({typeName:X.ZodBoolean,coerce:(s==null?void 0:s.coerce)||!1,...ne(s)});let fn=class Ba extends ie{_parse(e){if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==z.date){const r=this._getOrReturnCtx(e);return R(r,{code:Z.invalid_type,expected:z.date,received:r.parsedType}),ee}if(isNaN(e.data.getTime()))return R(this._getOrReturnCtx(e),{code:Z.invalid_date}),ee;const t=new De;let n;for(const r of this._def.checks)r.kind==="min"?e.data.getTime()<r.value&&(n=this._getOrReturnCtx(e,n),R(n,{code:Z.too_small,message:r.message,inclusive:!0,exact:!1,minimum:r.value,type:"date"}),t.dirty()):r.kind==="max"?e.data.getTime()>r.value&&(n=this._getOrReturnCtx(e,n),R(n,{code:Z.too_big,message:r.message,inclusive:!0,exact:!1,maximum:r.value,type:"date"}),t.dirty()):ue.assertNever(r);return{status:t.value,value:new Date(e.data.getTime())}}_addCheck(e){return new Ba({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:V.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:V.toString(t)})}get minDate(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e!=null?new Date(e):null}get maxDate(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e!=null?new Date(e):null}};fn.create=s=>new fn({checks:[],coerce:(s==null?void 0:s.coerce)||!1,typeName:X.ZodDate,...ne(s)});let os=class extends ie{_parse(s){if(this._getType(s)!==z.symbol){const e=this._getOrReturnCtx(s);return R(e,{code:Z.invalid_type,expected:z.symbol,received:e.parsedType}),ee}return Oe(s.data)}};os.create=s=>new os({typeName:X.ZodSymbol,...ne(s)});let gn=class extends ie{_parse(s){if(this._getType(s)!==z.undefined){const e=this._getOrReturnCtx(s);return R(e,{code:Z.invalid_type,expected:z.undefined,received:e.parsedType}),ee}return Oe(s.data)}};gn.create=s=>new gn({typeName:X.ZodUndefined,...ne(s)});let vn=class extends ie{_parse(s){if(this._getType(s)!==z.null){const e=this._getOrReturnCtx(s);return R(e,{code:Z.invalid_type,expected:z.null,received:e.parsedType}),ee}return Oe(s.data)}};vn.create=s=>new vn({typeName:X.ZodNull,...ne(s)});let Wt=class extends ie{constructor(){super(...arguments),this._any=!0}_parse(s){return Oe(s.data)}};Wt.create=s=>new Wt({typeName:X.ZodAny,...ne(s)});let It=class extends ie{constructor(){super(...arguments),this._unknown=!0}_parse(s){return Oe(s.data)}};It.create=s=>new It({typeName:X.ZodUnknown,...ne(s)});let vt=class extends ie{_parse(s){const e=this._getOrReturnCtx(s);return R(e,{code:Z.invalid_type,expected:z.never,received:e.parsedType}),ee}};vt.create=s=>new vt({typeName:X.ZodNever,...ne(s)});let cs=class extends ie{_parse(s){if(this._getType(s)!==z.undefined){const e=this._getOrReturnCtx(s);return R(e,{code:Z.invalid_type,expected:z.void,received:e.parsedType}),ee}return Oe(s.data)}};cs.create=s=>new cs({typeName:X.ZodVoid,...ne(s)});let Lt=class Xn extends ie{_parse(e){const{ctx:t,status:n}=this._processInputParams(e),r=this._def;if(t.parsedType!==z.array)return R(t,{code:Z.invalid_type,expected:z.array,received:t.parsedType}),ee;if(r.exactLength!==null){const i=t.data.length>r.exactLength.value,o=t.data.length<r.exactLength.value;(i||o)&&(R(t,{code:i?Z.too_big:Z.too_small,minimum:o?r.exactLength.value:void 0,maximum:i?r.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:r.exactLength.message}),n.dirty())}if(r.minLength!==null&&t.data.length<r.minLength.value&&(R(t,{code:Z.too_small,minimum:r.minLength.value,type:"array",inclusive:!0,exact:!1,message:r.minLength.message}),n.dirty()),r.maxLength!==null&&t.data.length>r.maxLength.value&&(R(t,{code:Z.too_big,maximum:r.maxLength.value,type:"array",inclusive:!0,exact:!1,message:r.maxLength.message}),n.dirty()),t.common.async)return Promise.all([...t.data].map((i,o)=>r.type._parseAsync(new dt(t,i,t.path,o)))).then(i=>De.mergeArray(n,i));const a=[...t.data].map((i,o)=>r.type._parseSync(new dt(t,i,t.path,o)));return De.mergeArray(n,a)}get element(){return this._def.type}min(e,t){return new Xn({...this._def,minLength:{value:e,message:V.toString(t)}})}max(e,t){return new Xn({...this._def,maxLength:{value:e,message:V.toString(t)}})}length(e,t){return new Xn({...this._def,exactLength:{value:e,message:V.toString(t)}})}nonempty(e){return this.min(1,e)}};function Bt(s){if(s instanceof Be){const e={};for(const t in s.shape){const n=s.shape[t];e[t]=ot.create(Bt(n))}return new Be({...s._def,shape:()=>e})}return s instanceof Lt?new Lt({...s._def,type:Bt(s.element)}):s instanceof ot?ot.create(Bt(s.unwrap())):s instanceof Tt?Tt.create(Bt(s.unwrap())):s instanceof Ct?Ct.create(s.items.map(e=>Bt(e))):s}Lt.create=(s,e)=>new Lt({type:s,minLength:null,maxLength:null,exactLength:null,typeName:X.ZodArray,...ne(e)});let Be=class et extends ie{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;const e=this._def.shape(),t=ue.objectKeys(e);return this._cached={shape:e,keys:t}}_parse(e){if(this._getType(e)!==z.object){const c=this._getOrReturnCtx(e);return R(c,{code:Z.invalid_type,expected:z.object,received:c.parsedType}),ee}const{status:t,ctx:n}=this._processInputParams(e),{shape:r,keys:a}=this._getCached(),i=[];if(!(this._def.catchall instanceof vt&&this._def.unknownKeys==="strip"))for(const c in n.data)a.includes(c)||i.push(c);const o=[];for(const c of a){const l=r[c],d=n.data[c];o.push({key:{status:"valid",value:c},value:l._parse(new dt(n,d,n.path,c)),alwaysSet:c in n.data})}if(this._def.catchall instanceof vt){const c=this._def.unknownKeys;if(c==="passthrough")for(const l of i)o.push({key:{status:"valid",value:l},value:{status:"valid",value:n.data[l]}});else if(c==="strict")i.length>0&&(R(n,{code:Z.unrecognized_keys,keys:i}),t.dirty());else if(c!=="strip")throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const c=this._def.catchall;for(const l of i){const d=n.data[l];o.push({key:{status:"valid",value:l},value:c._parse(new dt(n,d,n.path,l)),alwaysSet:l in n.data})}}return n.common.async?Promise.resolve().then(async()=>{const c=[];for(const l of o){const d=await l.key,u=await l.value;c.push({key:d,value:u,alwaysSet:l.alwaysSet})}return c}).then(c=>De.mergeObjectSync(t,c)):De.mergeObjectSync(t,o)}get shape(){return this._def.shape()}strict(e){return V.errToObj,new et({...this._def,unknownKeys:"strict",...e!==void 0?{errorMap:(t,n)=>{var r,a,i,o;const c=(i=(a=(r=this._def).errorMap)===null||a===void 0?void 0:a.call(r,t,n).message)!==null&&i!==void 0?i:n.defaultError;return t.code==="unrecognized_keys"?{message:(o=V.errToObj(e).message)!==null&&o!==void 0?o:c}:{message:c}}}:{}})}strip(){return new et({...this._def,unknownKeys:"strip"})}passthrough(){return new et({...this._def,unknownKeys:"passthrough"})}extend(e){return new et({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new et({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:X.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new et({...this._def,catchall:e})}pick(e){const t={};return ue.objectKeys(e).forEach(n=>{e[n]&&this.shape[n]&&(t[n]=this.shape[n])}),new et({...this._def,shape:()=>t})}omit(e){const t={};return ue.objectKeys(this.shape).forEach(n=>{e[n]||(t[n]=this.shape[n])}),new et({...this._def,shape:()=>t})}deepPartial(){return Bt(this)}partial(e){const t={};return ue.objectKeys(this.shape).forEach(n=>{const r=this.shape[n];e&&!e[n]?t[n]=r:t[n]=r.optional()}),new et({...this._def,shape:()=>t})}required(e){const t={};return ue.objectKeys(this.shape).forEach(n=>{if(e&&!e[n])t[n]=this.shape[n];else{let r=this.shape[n];for(;r instanceof ot;)r=r._def.innerType;t[n]=r}}),new et({...this._def,shape:()=>t})}keyof(){return Xa(ue.objectKeys(this.shape))}};Be.create=(s,e)=>new Be({shape:()=>s,unknownKeys:"strip",catchall:vt.create(),typeName:X.ZodObject,...ne(e)}),Be.strictCreate=(s,e)=>new Be({shape:()=>s,unknownKeys:"strict",catchall:vt.create(),typeName:X.ZodObject,...ne(e)}),Be.lazycreate=(s,e)=>new Be({shape:s,unknownKeys:"strip",catchall:vt.create(),typeName:X.ZodObject,...ne(e)});let $n=class extends ie{_parse(s){const{ctx:e}=this._processInputParams(s),t=this._def.options;if(e.common.async)return Promise.all(t.map(async n=>{const r={...e,common:{...e.common,issues:[]},parent:null};return{result:await n._parseAsync({data:e.data,path:e.path,parent:r}),ctx:r}})).then(function(n){for(const a of n)if(a.result.status==="valid")return a.result;for(const a of n)if(a.result.status==="dirty")return e.common.issues.push(...a.ctx.common.issues),a.result;const r=n.map(a=>new at(a.ctx.common.issues));return R(e,{code:Z.invalid_union,unionErrors:r}),ee});{let n;const r=[];for(const i of t){const o={...e,common:{...e.common,issues:[]},parent:null},c=i._parseSync({data:e.data,path:e.path,parent:o});if(c.status==="valid")return c;c.status!=="dirty"||n||(n={result:c,ctx:o}),o.common.issues.length&&r.push(o.common.issues)}if(n)return e.common.issues.push(...n.ctx.common.issues),n.result;const a=r.map(i=>new at(i));return R(e,{code:Z.invalid_union,unionErrors:a}),ee}}get options(){return this._def.options}};$n.create=(s,e)=>new $n({options:s,typeName:X.ZodUnion,...ne(e)});const wt=s=>s instanceof _n?wt(s.schema):s instanceof nt?wt(s.innerType()):s instanceof wn?[s.value]:s instanceof Bn?s.options:s instanceof xn?ue.objectValues(s.enum):s instanceof bn?wt(s._def.innerType):s instanceof gn?[void 0]:s instanceof vn?[null]:s instanceof ot?[void 0,...wt(s.unwrap())]:s instanceof Tt?[null,...wt(s.unwrap())]:s instanceof Js||s instanceof kn?wt(s.unwrap()):s instanceof Sn?wt(s._def.innerType):[];let Ja=class Ga extends ie{_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==z.object)return R(t,{code:Z.invalid_type,expected:z.object,received:t.parsedType}),ee;const n=this.discriminator,r=t.data[n],a=this.optionsMap.get(r);return a?t.common.async?a._parseAsync({data:t.data,path:t.path,parent:t}):a._parseSync({data:t.data,path:t.path,parent:t}):(R(t,{code:Z.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[n]}),ee)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,t,n){const r=new Map;for(const a of t){const i=wt(a.shape[e]);if(!i.length)throw new Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(const o of i){if(r.has(o))throw new Error(`Discriminator property ${String(e)} has duplicate value ${String(o)}`);r.set(o,a)}}return new Ga({typeName:X.ZodDiscriminatedUnion,discriminator:e,options:t,optionsMap:r,...ne(n)})}};function Rs(s,e){const t=ht(s),n=ht(e);if(s===e)return{valid:!0,data:s};if(t===z.object&&n===z.object){const r=ue.objectKeys(e),a=ue.objectKeys(s).filter(o=>r.indexOf(o)!==-1),i={...s,...e};for(const o of a){const c=Rs(s[o],e[o]);if(!c.valid)return{valid:!1};i[o]=c.data}return{valid:!0,data:i}}if(t===z.array&&n===z.array){if(s.length!==e.length)return{valid:!1};const r=[];for(let a=0;a<s.length;a++){const i=Rs(s[a],e[a]);if(!i.valid)return{valid:!1};r.push(i.data)}return{valid:!0,data:r}}return t===z.date&&n===z.date&&+s==+e?{valid:!0,data:s}:{valid:!1}}let yn=class extends ie{_parse(s){const{status:e,ctx:t}=this._processInputParams(s),n=(r,a)=>{if(Es(r)||Es(a))return ee;const i=Rs(r.value,a.value);return i.valid?((Is(r)||Is(a))&&e.dirty(),{status:e.value,value:i.data}):(R(t,{code:Z.invalid_intersection_types}),ee)};return t.common.async?Promise.all([this._def.left._parseAsync({data:t.data,path:t.path,parent:t}),this._def.right._parseAsync({data:t.data,path:t.path,parent:t})]).then(([r,a])=>n(r,a)):n(this._def.left._parseSync({data:t.data,path:t.path,parent:t}),this._def.right._parseSync({data:t.data,path:t.path,parent:t}))}};yn.create=(s,e,t)=>new yn({left:s,right:e,typeName:X.ZodIntersection,...ne(t)});let Ct=class Ha extends ie{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==z.array)return R(n,{code:Z.invalid_type,expected:z.array,received:n.parsedType}),ee;if(n.data.length<this._def.items.length)return R(n,{code:Z.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),ee;!this._def.rest&&n.data.length>this._def.items.length&&(R(n,{code:Z.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());const r=[...n.data].map((a,i)=>{const o=this._def.items[i]||this._def.rest;return o?o._parse(new dt(n,a,n.path,i)):null}).filter(a=>!!a);return n.common.async?Promise.all(r).then(a=>De.mergeArray(t,a)):De.mergeArray(t,r)}get items(){return this._def.items}rest(e){return new Ha({...this._def,rest:e})}};Ct.create=(s,e)=>{if(!Array.isArray(s))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new Ct({items:s,typeName:X.ZodTuple,rest:null,...ne(e)})};let Ka=class Wa extends ie{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==z.object)return R(n,{code:Z.invalid_type,expected:z.object,received:n.parsedType}),ee;const r=[],a=this._def.keyType,i=this._def.valueType;for(const o in n.data)r.push({key:a._parse(new dt(n,o,n.path,o)),value:i._parse(new dt(n,n.data[o],n.path,o)),alwaysSet:o in n.data});return n.common.async?De.mergeObjectAsync(t,r):De.mergeObjectSync(t,r)}get element(){return this._def.valueType}static create(e,t,n){return new Wa(t instanceof ie?{keyType:e,valueType:t,typeName:X.ZodRecord,...ne(n)}:{keyType:Kt.create(),valueType:e,typeName:X.ZodRecord,...ne(t)})}},ls=class extends ie{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(s){const{status:e,ctx:t}=this._processInputParams(s);if(t.parsedType!==z.map)return R(t,{code:Z.invalid_type,expected:z.map,received:t.parsedType}),ee;const n=this._def.keyType,r=this._def.valueType,a=[...t.data.entries()].map(([i,o],c)=>({key:n._parse(new dt(t,i,t.path,[c,"key"])),value:r._parse(new dt(t,o,t.path,[c,"value"]))}));if(t.common.async){const i=new Map;return Promise.resolve().then(async()=>{for(const o of a){const c=await o.key,l=await o.value;if(c.status==="aborted"||l.status==="aborted")return ee;c.status!=="dirty"&&l.status!=="dirty"||e.dirty(),i.set(c.value,l.value)}return{status:e.value,value:i}})}{const i=new Map;for(const o of a){const c=o.key,l=o.value;if(c.status==="aborted"||l.status==="aborted")return ee;c.status!=="dirty"&&l.status!=="dirty"||e.dirty(),i.set(c.value,l.value)}return{status:e.value,value:i}}}};ls.create=(s,e,t)=>new ls({valueType:e,keyType:s,typeName:X.ZodMap,...ne(t)});let ds=class Ls extends ie{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==z.set)return R(n,{code:Z.invalid_type,expected:z.set,received:n.parsedType}),ee;const r=this._def;r.minSize!==null&&n.data.size<r.minSize.value&&(R(n,{code:Z.too_small,minimum:r.minSize.value,type:"set",inclusive:!0,exact:!1,message:r.minSize.message}),t.dirty()),r.maxSize!==null&&n.data.size>r.maxSize.value&&(R(n,{code:Z.too_big,maximum:r.maxSize.value,type:"set",inclusive:!0,exact:!1,message:r.maxSize.message}),t.dirty());const a=this._def.valueType;function i(c){const l=new Set;for(const d of c){if(d.status==="aborted")return ee;d.status==="dirty"&&t.dirty(),l.add(d.value)}return{status:t.value,value:l}}const o=[...n.data.values()].map((c,l)=>a._parse(new dt(n,c,n.path,l)));return n.common.async?Promise.all(o).then(c=>i(c)):i(o)}min(e,t){return new Ls({...this._def,minSize:{value:e,message:V.toString(t)}})}max(e,t){return new Ls({...this._def,maxSize:{value:e,message:V.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}};ds.create=(s,e)=>new ds({valueType:s,minSize:null,maxSize:null,typeName:X.ZodSet,...ne(e)});let Ya=class Qn extends ie{constructor(){super(...arguments),this.validate=this.implement}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==z.function)return R(t,{code:Z.invalid_type,expected:z.function,received:t.parsedType}),ee;function n(o,c){return rs({data:o,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,ss(),Ht].filter(l=>!!l),issueData:{code:Z.invalid_arguments,argumentsError:c}})}function r(o,c){return rs({data:o,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,ss(),Ht].filter(l=>!!l),issueData:{code:Z.invalid_return_type,returnTypeError:c}})}const a={errorMap:t.common.contextualErrorMap},i=t.data;if(this._def.returns instanceof Yt){const o=this;return Oe(async function(...c){const l=new at([]),d=await o._def.args.parseAsync(c,a).catch(h=>{throw l.addIssue(n(c,h)),l}),u=await Reflect.apply(i,this,d);return await o._def.returns._def.type.parseAsync(u,a).catch(h=>{throw l.addIssue(r(u,h)),l})})}{const o=this;return Oe(function(...c){const l=o._def.args.safeParse(c,a);if(!l.success)throw new at([n(c,l.error)]);const d=Reflect.apply(i,this,l.data),u=o._def.returns.safeParse(d,a);if(!u.success)throw new at([r(d,u.error)]);return u.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new Qn({...this._def,args:Ct.create(e).rest(It.create())})}returns(e){return new Qn({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,t,n){return new Qn({args:e||Ct.create([]).rest(It.create()),returns:t||It.create(),typeName:X.ZodFunction,...ne(n)})}},_n=class extends ie{get schema(){return this._def.getter()}_parse(s){const{ctx:e}=this._processInputParams(s);return this._def.getter()._parse({data:e.data,path:e.path,parent:e})}};_n.create=(s,e)=>new _n({getter:s,typeName:X.ZodLazy,...ne(e)});let wn=class extends ie{_parse(s){if(s.data!==this._def.value){const e=this._getOrReturnCtx(s);return R(e,{received:e.data,code:Z.invalid_literal,expected:this._def.value}),ee}return{status:"valid",value:s.data}}get value(){return this._def.value}};function Xa(s,e){return new Bn({values:s,typeName:X.ZodEnum,...ne(e)})}wn.create=(s,e)=>new wn({value:s,typeName:X.ZodLiteral,...ne(e)});let Bn=class Fs extends ie{constructor(){super(...arguments),sn.set(this,void 0)}_parse(e){if(typeof e.data!="string"){const t=this._getOrReturnCtx(e),n=this._def.values;return R(t,{expected:ue.joinValues(n),received:t.parsedType,code:Z.invalid_type}),ee}if(is(this,sn)||Da(this,sn,new Set(this._def.values)),!is(this,sn).has(e.data)){const t=this._getOrReturnCtx(e),n=this._def.values;return R(t,{received:t.data,code:Z.invalid_enum_value,options:n}),ee}return Oe(e.data)}get options(){return this._def.values}get enum(){const e={};for(const t of this._def.values)e[t]=t;return e}get Values(){const e={};for(const t of this._def.values)e[t]=t;return e}get Enum(){const e={};for(const t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return Fs.create(e,{...this._def,...t})}exclude(e,t=this._def){return Fs.create(this.options.filter(n=>!e.includes(n)),{...this._def,...t})}};sn=new WeakMap,Bn.create=Xa;let xn=class extends ie{constructor(){super(...arguments),rn.set(this,void 0)}_parse(s){const e=ue.getValidEnumValues(this._def.values),t=this._getOrReturnCtx(s);if(t.parsedType!==z.string&&t.parsedType!==z.number){const n=ue.objectValues(e);return R(t,{expected:ue.joinValues(n),received:t.parsedType,code:Z.invalid_type}),ee}if(is(this,rn)||Da(this,rn,new Set(ue.getValidEnumValues(this._def.values))),!is(this,rn).has(s.data)){const n=ue.objectValues(e);return R(t,{received:t.data,code:Z.invalid_enum_value,options:n}),ee}return Oe(s.data)}get enum(){return this._def.values}};rn=new WeakMap,xn.create=(s,e)=>new xn({values:s,typeName:X.ZodNativeEnum,...ne(e)});let Yt=class extends ie{unwrap(){return this._def.type}_parse(s){const{ctx:e}=this._processInputParams(s);if(e.parsedType!==z.promise&&e.common.async===!1)return R(e,{code:Z.invalid_type,expected:z.promise,received:e.parsedType}),ee;const t=e.parsedType===z.promise?e.data:Promise.resolve(e.data);return Oe(t.then(n=>this._def.type.parseAsync(n,{path:e.path,errorMap:e.common.contextualErrorMap})))}};Yt.create=(s,e)=>new Yt({type:s,typeName:X.ZodPromise,...ne(e)});let nt=class extends ie{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===X.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(s){const{status:e,ctx:t}=this._processInputParams(s),n=this._def.effect||null,r={addIssue:a=>{R(t,a),a.fatal?e.abort():e.dirty()},get path(){return t.path}};if(r.addIssue=r.addIssue.bind(r),n.type==="preprocess"){const a=n.transform(t.data,r);if(t.common.async)return Promise.resolve(a).then(async i=>{if(e.value==="aborted")return ee;const o=await this._def.schema._parseAsync({data:i,path:t.path,parent:t});return o.status==="aborted"?ee:o.status==="dirty"||e.value==="dirty"?as(o.value):o});{if(e.value==="aborted")return ee;const i=this._def.schema._parseSync({data:a,path:t.path,parent:t});return i.status==="aborted"?ee:i.status==="dirty"||e.value==="dirty"?as(i.value):i}}if(n.type==="refinement"){const a=i=>{const o=n.refinement(i,r);if(t.common.async)return Promise.resolve(o);if(o instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return i};if(t.common.async===!1){const i=this._def.schema._parseSync({data:t.data,path:t.path,parent:t});return i.status==="aborted"?ee:(i.status==="dirty"&&e.dirty(),a(i.value),{status:e.value,value:i.value})}return this._def.schema._parseAsync({data:t.data,path:t.path,parent:t}).then(i=>i.status==="aborted"?ee:(i.status==="dirty"&&e.dirty(),a(i.value).then(()=>({status:e.value,value:i.value}))))}if(n.type==="transform"){if(t.common.async===!1){const a=this._def.schema._parseSync({data:t.data,path:t.path,parent:t});if(!Rt(a))return a;const i=n.transform(a.value,r);if(i instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:e.value,value:i}}return this._def.schema._parseAsync({data:t.data,path:t.path,parent:t}).then(a=>Rt(a)?Promise.resolve(n.transform(a.value,r)).then(i=>({status:e.value,value:i})):a)}ue.assertNever(n)}};nt.create=(s,e,t)=>new nt({schema:s,typeName:X.ZodEffects,effect:e,...ne(t)}),nt.createWithPreprocess=(s,e,t)=>new nt({schema:e,effect:{type:"preprocess",transform:s},typeName:X.ZodEffects,...ne(t)});let ot=class extends ie{_parse(s){return this._getType(s)===z.undefined?Oe(void 0):this._def.innerType._parse(s)}unwrap(){return this._def.innerType}};ot.create=(s,e)=>new ot({innerType:s,typeName:X.ZodOptional,...ne(e)});let Tt=class extends ie{_parse(s){return this._getType(s)===z.null?Oe(null):this._def.innerType._parse(s)}unwrap(){return this._def.innerType}};Tt.create=(s,e)=>new Tt({innerType:s,typeName:X.ZodNullable,...ne(e)});let bn=class extends ie{_parse(s){const{ctx:e}=this._processInputParams(s);let t=e.data;return e.parsedType===z.undefined&&(t=this._def.defaultValue()),this._def.innerType._parse({data:t,path:e.path,parent:e})}removeDefault(){return this._def.innerType}};bn.create=(s,e)=>new bn({innerType:s,typeName:X.ZodDefault,defaultValue:typeof e.default=="function"?e.default:()=>e.default,...ne(e)});let Sn=class extends ie{_parse(s){const{ctx:e}=this._processInputParams(s),t={...e,common:{...e.common,issues:[]}},n=this._def.innerType._parse({data:t.data,path:t.path,parent:{...t}});return un(n)?n.then(r=>({status:"valid",value:r.status==="valid"?r.value:this._def.catchValue({get error(){return new at(t.common.issues)},input:t.data})})):{status:"valid",value:n.status==="valid"?n.value:this._def.catchValue({get error(){return new at(t.common.issues)},input:t.data})}}removeCatch(){return this._def.innerType}};Sn.create=(s,e)=>new Sn({innerType:s,typeName:X.ZodCatch,catchValue:typeof e.catch=="function"?e.catch:()=>e.catch,...ne(e)});let us=class extends ie{_parse(s){if(this._getType(s)!==z.nan){const e=this._getOrReturnCtx(s);return R(e,{code:Z.invalid_type,expected:z.nan,received:e.parsedType}),ee}return{status:"valid",value:s.data}}};us.create=s=>new us({typeName:X.ZodNaN,...ne(s)});const Io=Symbol("zod_brand");let Js=class extends ie{_parse(s){const{ctx:e}=this._processInputParams(s),t=e.data;return this._def.type._parse({data:t,path:e.path,parent:e})}unwrap(){return this._def.type}},Gs=class Qa extends ie{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.common.async)return(async()=>{const r=await this._def.in._parseAsync({data:n.data,path:n.path,parent:n});return r.status==="aborted"?ee:r.status==="dirty"?(t.dirty(),as(r.value)):this._def.out._parseAsync({data:r.value,path:n.path,parent:n})})();{const r=this._def.in._parseSync({data:n.data,path:n.path,parent:n});return r.status==="aborted"?ee:r.status==="dirty"?(t.dirty(),{status:"dirty",value:r.value}):this._def.out._parseSync({data:r.value,path:n.path,parent:n})}}static create(e,t){return new Qa({in:e,out:t,typeName:X.ZodPipeline})}},kn=class extends ie{_parse(s){const e=this._def.innerType._parse(s),t=n=>(Rt(n)&&(n.value=Object.freeze(n.value)),n);return un(e)?e.then(n=>t(n)):t(e)}unwrap(){return this._def.innerType}};function or(s,e={},t){return s?Wt.create().superRefine((n,r)=>{var a,i;if(!s(n)){const o=typeof e=="function"?e(n):typeof e=="string"?{message:e}:e,c=(i=(a=o.fatal)!==null&&a!==void 0?a:t)===null||i===void 0||i,l=typeof o=="string"?{message:o}:o;r.addIssue({code:"custom",...l,fatal:c})}}):Wt.create()}kn.create=(s,e)=>new kn({innerType:s,typeName:X.ZodReadonly,...ne(e)});const Po={object:Be.lazycreate};var X;(function(s){s.ZodString="ZodString",s.ZodNumber="ZodNumber",s.ZodNaN="ZodNaN",s.ZodBigInt="ZodBigInt",s.ZodBoolean="ZodBoolean",s.ZodDate="ZodDate",s.ZodSymbol="ZodSymbol",s.ZodUndefined="ZodUndefined",s.ZodNull="ZodNull",s.ZodAny="ZodAny",s.ZodUnknown="ZodUnknown",s.ZodNever="ZodNever",s.ZodVoid="ZodVoid",s.ZodArray="ZodArray",s.ZodObject="ZodObject",s.ZodUnion="ZodUnion",s.ZodDiscriminatedUnion="ZodDiscriminatedUnion",s.ZodIntersection="ZodIntersection",s.ZodTuple="ZodTuple",s.ZodRecord="ZodRecord",s.ZodMap="ZodMap",s.ZodSet="ZodSet",s.ZodFunction="ZodFunction",s.ZodLazy="ZodLazy",s.ZodLiteral="ZodLiteral",s.ZodEnum="ZodEnum",s.ZodEffects="ZodEffects",s.ZodNativeEnum="ZodNativeEnum",s.ZodOptional="ZodOptional",s.ZodNullable="ZodNullable",s.ZodDefault="ZodDefault",s.ZodCatch="ZodCatch",s.ZodPromise="ZodPromise",s.ZodBranded="ZodBranded",s.ZodPipeline="ZodPipeline",s.ZodReadonly="ZodReadonly"})(X||(X={}));const cr=Kt.create,lr=pn.create,jo=us.create,Ro=mn.create,dr=hn.create,Lo=fn.create,Fo=os.create,zo=gn.create,Do=vn.create,Uo=Wt.create,Vo=It.create,qo=vt.create,Bo=cs.create,Jo=Lt.create,Go=Be.create,Ho=Be.strictCreate,Ko=$n.create,Wo=Ja.create,Yo=yn.create,Xo=Ct.create,Qo=Ka.create,ec=ls.create,tc=ds.create,nc=Ya.create,sc=_n.create,rc=wn.create,ac=Bn.create,ic=xn.create,oc=Yt.create,ur=nt.create,cc=ot.create,lc=Tt.create,dc=nt.createWithPreprocess,uc=Gs.create,pc={string:s=>Kt.create({...s,coerce:!0}),number:s=>pn.create({...s,coerce:!0}),boolean:s=>hn.create({...s,coerce:!0}),bigint:s=>mn.create({...s,coerce:!0}),date:s=>fn.create({...s,coerce:!0})},mc=ee;var ye=Object.freeze({__proto__:null,defaultErrorMap:Ht,setErrorMap:function(s){Fa=s},getErrorMap:ss,makeIssue:rs,EMPTY_PATH:[],addIssueToContext:R,ParseStatus:De,INVALID:ee,DIRTY:as,OK:Oe,isAborted:Es,isDirty:Is,isValid:Rt,isAsync:un,get util(){return ue},get objectUtil(){return Os},ZodParsedType:z,getParsedType:ht,ZodType:ie,datetimeRegex:qa,ZodString:Kt,ZodNumber:pn,ZodBigInt:mn,ZodBoolean:hn,ZodDate:fn,ZodSymbol:os,ZodUndefined:gn,ZodNull:vn,ZodAny:Wt,ZodUnknown:It,ZodNever:vt,ZodVoid:cs,ZodArray:Lt,ZodObject:Be,ZodUnion:$n,ZodDiscriminatedUnion:Ja,ZodIntersection:yn,ZodTuple:Ct,ZodRecord:Ka,ZodMap:ls,ZodSet:ds,ZodFunction:Ya,ZodLazy:_n,ZodLiteral:wn,ZodEnum:Bn,ZodNativeEnum:xn,ZodPromise:Yt,ZodEffects:nt,ZodTransformer:nt,ZodOptional:ot,ZodNullable:Tt,ZodDefault:bn,ZodCatch:Sn,ZodNaN:us,BRAND:Io,ZodBranded:Js,ZodPipeline:Gs,ZodReadonly:kn,custom:or,Schema:ie,ZodSchema:ie,late:Po,get ZodFirstPartyTypeKind(){return X},coerce:pc,any:Uo,array:Jo,bigint:Ro,boolean:dr,date:Lo,discriminatedUnion:Wo,effect:ur,enum:ac,function:nc,instanceof:(s,e={message:`Input not instance of ${s.name}`})=>or(t=>t instanceof s,e),intersection:Yo,lazy:sc,literal:rc,map:ec,nan:jo,nativeEnum:ic,never:qo,null:Do,nullable:lc,number:lr,object:Go,oboolean:()=>dr().optional(),onumber:()=>lr().optional(),optional:cc,ostring:()=>cr().optional(),pipeline:uc,preprocess:dc,promise:oc,record:Qo,set:tc,strictObject:Ho,string:cr,symbol:Fo,transformer:ur,tuple:Xo,undefined:zo,union:Ko,unknown:Vo,void:Bo,NEVER:mc,ZodIssueCode:Z,quotelessJson:s=>JSON.stringify(s,null,2).replace(/"([^"]+)":/g,"$1:"),ZodError:at});const Je=ye.object({name:ye.string().optional(),title:ye.string().optional(),command:ye.string().optional(),args:ye.array(ye.union([ye.string(),ye.number(),ye.boolean()])).optional(),env:ye.record(ye.union([ye.string(),ye.number(),ye.boolean(),ye.null(),ye.undefined()])).optional()}).passthrough(),hc=ye.array(Je),fc=ye.object({servers:ye.array(Je)}).passthrough(),gc=ye.object({mcpServers:ye.array(Je)}).passthrough(),vc=ye.object({servers:ye.record(Je)}).passthrough(),$c=ye.object({mcpServers:ye.record(Je)}).passthrough(),yc=ye.record(Je),_c=Je.refine(s=>s.command!==void 0,{message:"Server must have a 'command' property"}),wc=Symbol("MCPServerError");let qe=class ei extends Error{constructor(e){super(e),this.name="MCPServerError",Object.setPrototypeOf(this,ei.prototype)}};var Aa,Ns;let xc=(Aa=wc,Ns=class{constructor(s){ve(this,"servers",Le([]));this.host=s,this.loadServersFromStorage()}handleMessageFromExtension(s){const e=s.data;if(e.type===ae.getStoredMCPServersResponse){const t=e.data;return Array.isArray(t)&&this.servers.set(t),!0}return!1}async importServersFromJSON(s){return this.importFromJSON(s)}loadServersFromStorage(){try{this.host.postMessage({type:ae.getStoredMCPServers})}catch(s){console.error("Failed to load MCP servers:",s),this.servers.set([])}}saveServers(s){try{this.host.postMessage({type:ae.setStoredMCPServers,data:s})}catch(e){throw console.error("Failed to save MCP servers:",e),new qe("Failed to save MCP servers")}}getServers(){return this.servers}addServer(s){this.checkExistingServerName(s.name),this.servers.update(e=>{const t=[...e,{...s,id:crypto.randomUUID()}];return this.saveServers(t),t})}checkExistingServerName(s,e){const t=dn(this.servers).find(n=>n.name===s);if(t&&(t==null?void 0:t.id)!==e)throw new qe(`Server name '${s}' already exists`)}updateServer(s){this.checkExistingServerName(s.name,s.id),this.servers.update(e=>{const t=e.map(n=>n.id===s.id?s:n);return this.saveServers(t),t})}deleteServer(s){this.servers.update(e=>{const t=e.filter(n=>n.id!==s);return this.saveServers(t),t})}toggleDisabledServer(s){this.servers.update(e=>{const t=e.map(n=>n.id===s?{...n,disabled:!n.disabled}:n);return this.saveServers(t),t})}static convertServerToJSON(s){return JSON.stringify({mcpServers:{[s.name]:{command:s.command.split(" ")[0],args:s.command.split(" ").slice(1),env:s.env}}},null,2)}static parseServerValidationMessages(s){const e=new Map,t=new Map;s.forEach(r=>{var a;r.disabled?e.set(r.id,"MCP server has been manually disabled"):r.tools&&r.tools.length===0?e.set(r.id,"No tools are available for this MCP server"):r.disabledTools&&r.disabledTools.length===((a=r.tools)==null?void 0:a.length)?e.set(r.id,"All tools for this MCP server have validation errors: "+r.disabledTools.join(", ")):r.disabledTools&&r.disabledTools.length>0&&t.set(r.id,"MCP server has validation errors in the following tools which have been disabled: "+r.disabledTools.join(", "))});const n=this.parseDuplicateServerIds(s);return{errors:new Map([...e,...n]),warnings:t}}static parseDuplicateServerIds(s){const e=new Map;for(const n of s)e.has(n.name)||e.set(n.name,[]),e.get(n.name).push(n.id);const t=new Map;for(const[,n]of e)if(n.length>1)for(let r=1;r<n.length;r++)t.set(n[r],"MCP server is disabled due to duplicate server names");return t}parseServerConfigFromJSON(s){try{const e=JSON.parse(s),t=ye.union([hc.transform(n=>n.map(r=>this.normalizeServerConfig(r))),fc.transform(n=>n.servers.map(r=>this.normalizeServerConfig(r))),gc.transform(n=>n.mcpServers.map(r=>this.normalizeServerConfig(r))),vc.transform(n=>Object.entries(n.servers).map(([r,a])=>{const i=Je.parse(a);return this.normalizeServerConfig({...i,name:i.name||r})})),$c.transform(n=>Object.entries(n.mcpServers).map(([r,a])=>{const i=Je.parse(a);return this.normalizeServerConfig({...i,name:i.name||r})})),yc.transform(n=>{if(!Object.values(n).some(r=>{const a=Je.safeParse(r);return a.success&&a.data.command!==void 0}))throw new Error("No command property found in any server config");return Object.entries(n).map(([r,a])=>{const i=Je.parse(a);return this.normalizeServerConfig({...i,name:i.name||r})})}),_c.transform(n=>[this.normalizeServerConfig(n)])]).safeParse(e);if(t.success)return t.data;throw new qe("Invalid JSON format. Expected an array of servers or an object with a 'servers' property.")}catch(e){throw e instanceof qe?e:new qe("Failed to parse MCP servers from JSON. Please check the format.")}}importFromJSON(s){try{const e=this.parseServerConfigFromJSON(s),t=dn(this.servers),n=new Set(t.map(r=>r.name));for(const r of e){if(!r.name)throw new qe("All servers must have a name.");if(n.has(r.name))throw new qe(`A server with the name '${r.name}' already exists.`);n.add(r.name)}return this.servers.update(r=>{const a=[...r,...e.map(i=>({...i,id:crypto.randomUUID()}))];return this.saveServers(a),a}),e.length}catch(e){throw e instanceof qe?e:new qe("Failed to import MCP servers from JSON. Please check the format.")}}normalizeServerConfig(s){try{const e=Je.transform(t=>{const n=t.command||"",r=t.args?t.args.map(c=>String(c)):[];if(!n)throw new Error("Server must have a 'command' property");const a=r.length>0?`${n} ${r.join(" ")}`:n,i=t.name||t.title||(n?n.split(" ")[0]:""),o=t.env?Object.fromEntries(Object.entries(t.env).filter(([c,l])=>l!=null).map(([c,l])=>[c,String(l)])):void 0;return{name:i,command:a,arguments:"",useShellInterpolation:!0,env:Object.keys(o||{}).length>0?o:void 0}}).refine(t=>!!t.name,{message:"Server must have a name",path:["name"]}).refine(t=>!!t.command,{message:"Server must have a command",path:["command"]}).safeParse(s);if(!e.success)throw new qe(e.error.message);return e.data}catch(e){throw e instanceof Error?new qe(`Invalid server configuration: ${e.message}`):new qe("Invalid server configuration")}}},ve(Ns,Aa,"MCPServerError"),Ns);class bc{constructor(e){ve(this,"_terminalSettings",Le({supportedShells:[],selectedShell:void 0,startupScript:void 0}));this._host=e,this.requestTerminalSettings()}handleMessageFromExtension(e){const t=e.data;return t.type===ae.terminalSettingsResponse&&(this._terminalSettings.set(t.data),!0)}getTerminalSettings(){return this._terminalSettings}requestTerminalSettings(){this._host.postMessage({type:ae.getTerminalSettings})}updateSelectedShell(e){this._terminalSettings.update(t=>({...t,selectedShell:e})),this._host.postMessage({type:ae.updateTerminalSettings,data:{selectedShell:e}})}updateStartupScript(e){this._terminalSettings.update(t=>({...t,startupScript:e})),this._host.postMessage({type:ae.updateTerminalSettings,data:{startupScript:e}})}}function Cn(s,e){return t=>!t.shiftKey&&t.key===s&&(e(t),!0)}var gt=(s=>(s.file="file",s.folder="folder",s))(gt||{});class St{constructor(e,t){ve(this,"subscribe");ve(this,"set");ve(this,"update");ve(this,"handleMessageFromExtension",async e=>{const t=e.data;switch(t.type){case ae.wsContextSourceFoldersChanged:case ae.wsContextFolderContentsChanged:this.updateSourceFolders(await this.getSourceFolders());break;case ae.sourceFoldersSyncStatus:this.update(n=>({...n,syncStatus:t.data.status}))}});ve(this,"getSourceFolders",async()=>(await this.asyncMsgSender.send({type:ae.wsContextGetSourceFoldersRequest},1e4)).data.workspaceFolders);ve(this,"getChildren",async e=>(await this.asyncMsgSender.send({type:ae.wsContextGetChildrenRequest,data:{fileId:e}},1e4)).data.children.map(t=>t.type==="folder"?{...t,children:[],expanded:!1}:{...t}).sort((t,n)=>t.type===n.type?t.name.localeCompare(n.name):t.type==="folder"?-1:1));this.host=e,this.asyncMsgSender=t;const{subscribe:n,set:r,update:a}=Le({sourceFolders:[],sourceTree:[],syncStatus:Zs.done});this.subscribe=n,this.set=r,this.update=a,this.getSourceFolders().then(i=>{this.update(o=>({...o,sourceFolders:i,sourceTree:St.sourceFoldersToSourceNodes(i)}))})}async expandNode(e){e.children=await this.getChildren(e.fileId),e.expanded=!0,this.update(t=>t)}collapseNode(e){this.update(t=>(e.children=[],e.expanded=!1,t))}toggleNode(e){e.type==="folder"&&e.inclusionState!==tt.excluded&&(e.expanded?this.collapseNode(e):this.expandNode(e))}addMoreSourceFolders(){this.host.postMessage({type:ae.wsContextAddMoreSourceFolders})}removeSourceFolder(e){this.host.postMessage({type:ae.wsContextRemoveSourceFolder,data:e})}requestRefresh(){this.host.postMessage({type:ae.wsContextUserRequestedRefresh})}async updateSourceFolders(e){let t=dn(this);const n=await this.getRefreshedSourceTree(t.sourceTree,e);this.update(r=>({...r,sourceFolders:e,sourceTree:n}))}async getRefreshedSourceTree(e,t){const n=St.sourceFoldersToSourceNodes(t);return this.getRefreshedSourceTreeRecurse(e,n)}async getRefreshedSourceTreeRecurse(e,t){const n=new Map(e.map(r=>[JSON.stringify([r.fileId.folderRoot,r.fileId.relPath]),r]));for(let r of t){const a=St.fileIdToString(r.fileId);if(r.type==="folder"){const i=n.get(a);i&&(r.expanded=i.type==="folder"&&i.expanded,r.expanded&&(r.children=await this.getChildren(r.fileId),r.children=await this.getRefreshedSourceTreeRecurse(i.children,r.children)))}}return t}static fileIdToString(e){return JSON.stringify([e.folderRoot,e.relPath])}static sourceFoldersToSourceNodes(e){return e.filter(t=>!t.isNestedFolder&&!t.isPending).sort((t,n)=>t.name.localeCompare(n.name)).map(t=>({name:t.name,fileId:t.fileId,children:[],expanded:!1,type:"folder",inclusionState:t.inclusionState,reason:"",trackedFileCount:t.trackedFileCount}))}}function pr(s,e,t){const n=s.slice();return n[6]=e[t],n}function mr(s){let e,t;function n(){return s[5](s[6])}return e=new ws({props:{title:"Remove source folder from Augment context",variant:"ghost",color:"neutral",size:1,class:"source-folder-v-adjust",$$slots:{default:[Sc]},$$scope:{ctx:s}}}),e.$on("click",function(){return s[4](s[6])}),e.$on("keyup",function(){jt(Cn("Enter",n))&&Cn("Enter",n).apply(this,arguments)}),{c(){b(e.$$.fragment)},m(r,a){S(e,r,a),t=!0},p(r,a){s=r;const i={};512&a&&(i.$$scope={dirty:a,ctx:s}),e.$set(i)},i(r){t||(p(e.$$.fragment,r),t=!0)},o(r){m(e.$$.fragment,r),t=!1},d(r){k(e,r)}}}function Sc(s){let e,t;return e=new qi({}),{c(){b(e.$$.fragment)},m(n,r){S(e,n,r),t=!0},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function hr(s){let e,t;return e=new de({props:{size:1,class:"file-count",$$slots:{default:[kc]},$$scope:{ctx:s}}}),{c(){b(e.$$.fragment)},m(n,r){S(e,n,r),t=!0},p(n,r){const a={};513&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function kc(s){let e,t=s[6].trackedFileCount.toLocaleString()+"";return{c(){e=U(t)},m(n,r){w(n,e,r)},p(n,r){1&r&&t!==(t=n[6].trackedFileCount.toLocaleString()+"")&&we(e,t)},d(n){n&&y(e)}}}function fr(s,e){let t,n,r,a,i,o,c,l,d,u,h,g=e[6].name+"",v=(e[6].isPending?"(pending)":e[6].fileId.folderRoot)+"",f=!e[6].isWorkspaceFolder&&mr(e);r=new Ra({props:{class:"source-folder-v-adjust",icon:e[3](e[6])}});let _=e[6].trackedFileCount&&hr(e);return{key:s,first:null,c(){t=M("div"),f&&f.c(),n=E(),b(r.$$.fragment),a=E(),i=M("span"),o=U(g),c=E(),l=M("span"),d=U(v),u=E(),_&&_.c(),$(l,"class","folderRoot svelte-1skknri"),$(i,"class","name svelte-1skknri"),$(t,"class","item svelte-1skknri"),$e(t,"workspace-folder",e[6].isWorkspaceFolder),this.first=t},m(C,P){w(C,t,P),f&&f.m(t,null),x(t,n),S(r,t,null),x(t,a),x(t,i),x(i,o),x(i,c),x(i,l),x(l,d),x(t,u),_&&_.m(t,null),h=!0},p(C,P){(e=C)[6].isWorkspaceFolder?f&&(K(),m(f,1,1,()=>{f=null}),W()):f?(f.p(e,P),1&P&&p(f,1)):(f=mr(e),f.c(),p(f,1),f.m(t,n));const I={};1&P&&(I.icon=e[3](e[6])),r.$set(I),(!h||1&P)&&g!==(g=e[6].name+"")&&we(o,g),(!h||1&P)&&v!==(v=(e[6].isPending?"(pending)":e[6].fileId.folderRoot)+"")&&we(d,v),e[6].trackedFileCount?_?(_.p(e,P),1&P&&p(_,1)):(_=hr(e),_.c(),p(_,1),_.m(t,null)):_&&(K(),m(_,1,1,()=>{_=null}),W()),(!h||1&P)&&$e(t,"workspace-folder",e[6].isWorkspaceFolder)},i(C){h||(p(f),p(r.$$.fragment,C),p(_),h=!0)},o(C){m(f),m(r.$$.fragment,C),m(_),h=!1},d(C){C&&y(t),f&&f.d(),k(r),_&&_.d()}}}function Cc(s){let e,t,n,r,a,i,o,c,l=[],d=new Map,u=xe(s[0]);const h=g=>St.fileIdToString(g[6].fileId);for(let g=0;g<u.length;g+=1){let v=pr(s,u,g),f=h(v);d.set(f,l[g]=fr(f,v))}return r=new _s({}),{c(){e=M("div");for(let g=0;g<l.length;g+=1)l[g].c();t=E(),n=M("div"),b(r.$$.fragment),a=U(" Add more..."),$(n,"role","button"),$(n,"tabindex","0"),$(n,"class","add-more svelte-1skknri"),$(e,"class","source-folder svelte-1skknri")},m(g,v){w(g,e,v);for(let f=0;f<l.length;f+=1)l[f]&&l[f].m(e,null);x(e,t),x(e,n),S(r,n,null),x(n,a),i=!0,o||(c=[ze(n,"keyup",function(){jt(Cn("Enter",s[1]))&&Cn("Enter",s[1]).apply(this,arguments)}),ze(n,"click",function(){jt(s[1])&&s[1].apply(this,arguments)})],o=!0)},p(g,[v]){s=g,13&v&&(u=xe(s[0]),K(),l=Ot(l,v,h,1,s,u,d,e,Et,fr,t,pr),W())},i(g){if(!i){for(let v=0;v<u.length;v+=1)p(l[v]);p(r.$$.fragment,g),i=!0}},o(g){for(let v=0;v<l.length;v+=1)m(l[v]);m(r.$$.fragment,g),i=!1},d(g){g&&y(e);for(let v=0;v<l.length;v+=1)l[v].d();k(r),o=!1,qs(c)}}}function Tc(s,e,t){let{folders:n=[]}=e,{onAddMore:r}=e,{onRemove:a}=e;return s.$$set=i=>{"folders"in i&&t(0,n=i.folders),"onAddMore"in i&&t(1,r=i.onAddMore),"onRemove"in i&&t(2,a=i.onRemove)},[n,r,a,i=>i.isWorkspaceFolder?"root-folder":"folder",i=>a(i.fileId.folderRoot),i=>a(i.fileId.folderRoot)]}class Mc extends fe{constructor(e){super(),ge(this,e,Tc,Cc,he,{folders:0,onAddMore:1,onRemove:2})}}function gr(s,e,t){const n=s.slice();return n[10]=e[t],n}function vr(s){let e,t;return e=new de({props:{size:1,class:"file-count",$$slots:{default:[Ac]},$$scope:{ctx:s}}}),{c(){b(e.$$.fragment)},m(n,r){S(e,n,r),t=!0},p(n,r){const a={};8193&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Ac(s){let e,t=s[0].trackedFileCount.toLocaleString()+"";return{c(){e=U(t)},m(n,r){w(n,e,r)},p(n,r){1&r&&t!==(t=n[0].trackedFileCount.toLocaleString()+"")&&we(e,t)},d(n){n&&y(e)}}}function $r(s){let e,t,n=[],r=new Map,a=xe(s[5].children);const i=o=>St.fileIdToString(o[10].fileId);for(let o=0;o<a.length;o+=1){let c=gr(s,a,o),l=i(c);r.set(l,n[o]=yr(l,c))}return{c(){e=M("div");for(let o=0;o<n.length;o+=1)n[o].c();$(e,"class","children-container")},m(o,c){w(o,e,c);for(let l=0;l<n.length;l+=1)n[l]&&n[l].m(e,null);t=!0},p(o,c){38&c&&(a=xe(o[5].children),K(),n=Ot(n,c,i,1,o,a,r,e,Et,yr,null,gr),W())},i(o){if(!t){for(let c=0;c<a.length;c+=1)p(n[c]);t=!0}},o(o){for(let c=0;c<n.length;c+=1)m(n[c]);t=!1},d(o){o&&y(e);for(let c=0;c<n.length;c+=1)n[c].d()}}}function yr(s,e){let t,n,r;return n=new ti({props:{data:e[10],wsContextModel:e[1],indentLevel:e[2]+1}}),{key:s,first:null,c(){t=Te(),b(n.$$.fragment),this.first=t},m(a,i){w(a,t,i),S(n,a,i),r=!0},p(a,i){e=a;const o={};32&i&&(o.data=e[10]),2&i&&(o.wsContextModel=e[1]),4&i&&(o.indentLevel=e[2]+1),n.$set(o)},i(a){r||(p(n.$$.fragment,a),r=!0)},o(a){m(n.$$.fragment,a),r=!1},d(a){a&&y(t),k(n,a)}}}function Nc(s){let e,t,n,r,a,i,o,c,l,d,u,h,g,v,f,_,C,P,I=s[0].name+"";n=new Ra({props:{icon:s[4]}});let A=s[0].type===gt.folder&&s[0].inclusionState!==tt.excluded&&typeof s[0].trackedFileCount=="number"&&vr(s),T=s[5]&&$r(s);return{c(){e=M("div"),t=M("div"),b(n.$$.fragment),r=E(),a=M("span"),i=U(I),o=E(),A&&A.c(),c=E(),l=M("img"),f=E(),T&&T.c(),$(a,"class","name svelte-sympus"),tr(l.src,d=s[7][s[0].inclusionState])||$(l,"src",d),$(l,"alt",u=s[8][s[0].inclusionState]),$(t,"class","tree-item svelte-sympus"),$(t,"role","treeitem"),$(t,"aria-selected","false"),$(t,"tabindex","0"),$(t,"title",h=s[0].reason),$(t,"aria-expanded",g=s[0].type===gt.folder&&s[0].expanded),$(t,"aria-level",s[2]),$(t,"style",v=`padding-left: ${10*s[2]+20}px;`),$e(t,"included-folder",s[3])},m(N,j){w(N,e,j),x(e,t),S(n,t,null),x(t,r),x(t,a),x(a,i),x(t,o),A&&A.m(t,null),x(t,c),x(t,l),x(e,f),T&&T.m(e,null),_=!0,C||(P=[ze(t,"click",s[6]),ze(t,"keyup",Cn("Enter",s[6]))],C=!0)},p(N,[j]){const B={};16&j&&(B.icon=N[4]),n.$set(B),(!_||1&j)&&I!==(I=N[0].name+"")&&we(i,I),N[0].type===gt.folder&&N[0].inclusionState!==tt.excluded&&typeof N[0].trackedFileCount=="number"?A?(A.p(N,j),1&j&&p(A,1)):(A=vr(N),A.c(),p(A,1),A.m(t,c)):A&&(K(),m(A,1,1,()=>{A=null}),W()),(!_||1&j&&!tr(l.src,d=N[7][N[0].inclusionState]))&&$(l,"src",d),(!_||1&j&&u!==(u=N[8][N[0].inclusionState]))&&$(l,"alt",u),(!_||1&j&&h!==(h=N[0].reason))&&$(t,"title",h),(!_||1&j&&g!==(g=N[0].type===gt.folder&&N[0].expanded))&&$(t,"aria-expanded",g),(!_||4&j)&&$(t,"aria-level",N[2]),(!_||4&j&&v!==(v=`padding-left: ${10*N[2]+20}px;`))&&$(t,"style",v),(!_||8&j)&&$e(t,"included-folder",N[3]),N[5]?T?(T.p(N,j),32&j&&p(T,1)):(T=$r(N),T.c(),p(T,1),T.m(e,null)):T&&(K(),m(T,1,1,()=>{T=null}),W())},i(N){_||(p(n.$$.fragment,N),p(A),p(T),_=!0)},o(N){m(n.$$.fragment,N),m(A),m(T),_=!1},d(N){N&&y(e),k(n),A&&A.d(),T&&T.d(),C=!1,qs(P)}}}function Zc(s,e,t){let{data:n}=e,{wsContextModel:r}=e,{indentLevel:a}=e;const i={[tt.included]:"data:image/svg+xml,%3csvg%20width='12'%20height='12'%20viewBox='0%200%2012%2012'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M2.66658%201.01118C3.65328%200.351894%204.81331%200%206%200C7.5907%200.00195418%209.11569%200.634723%2010.2405%201.75952C11.3653%202.88431%2011.998%204.4093%2012%206C12%207.18669%2011.6481%208.34672%2010.9888%209.33342C10.3295%2010.3201%209.39246%2011.0891%208.2961%2011.5433C7.19975%2011.9974%205.99335%2012.1162%204.82946%2011.8847C3.66557%2011.6532%202.59648%2011.0818%201.75736%2010.2426C0.918247%209.40352%200.346802%208.33443%200.115291%207.17054C-0.11622%206.00666%200.00259969%204.80025%200.456725%203.7039C0.910851%202.60754%201.67989%201.67047%202.66658%201.01118ZM5.86301%208.67273L9.44256%203.9L8.24256%203L5.12729%207.15368L3.17471%205.59162L2.23767%206.76292L4.79449%208.80838L5.86301%208.67273Z'%20fill='%23388A34'/%3e%3c/svg%3e",[tt.excluded]:"data:image/svg+xml,%3csvg%20width='12'%20height='12'%20viewBox='0%200%2012%2012'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M2.66658%201.01119C3.65328%200.351896%204.81332%200%206%200C7.5907%200.00195419%209.11569%200.634726%2010.2405%201.75953C11.3653%202.88433%2011.998%204.40933%2012%206.00003C12%207.18673%2011.6481%208.34677%2010.9888%209.33347C10.3295%2010.3202%209.39246%2011.0892%208.2961%2011.5433C7.19975%2011.9975%205.99335%2012.1163%204.82946%2011.8848C3.66558%2011.6533%202.59648%2011.0818%201.75736%2010.2427C0.918247%209.40358%200.346802%208.33447%200.115291%207.17058C-0.11622%206.00669%200.00259969%204.80028%200.456726%203.70392C0.910851%202.60756%201.67989%201.67048%202.66658%201.01119ZM6.00007%207.07359L8.1213%209.19482L9.18196%208.13416L7.06073%206.01292L9.18198%203.89166L8.12132%202.83099L6.00007%204.95225L3.87866%202.83083L2.818%203.89149L4.93941%206.01292L2.81802%208.13432L3.87868%209.19499L6.00007%207.07359Z'%20fill='%23E51400'/%3e%3c/svg%3e",[tt.partial]:"data:image/svg+xml,%3csvg%20width='12'%20height='12'%20viewBox='0%200%2012%2012'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M2.66658%201.01118C3.65328%200.351894%204.81331%200%206%200C7.5907%200.00195418%209.11569%200.634723%2010.2405%201.75952C11.3653%202.88431%2011.998%204.4093%2012%206C12%207.18669%2011.6481%208.34672%2010.9888%209.33342C10.3295%2010.3201%209.39246%2011.0891%208.2961%2011.5433C7.19975%2011.9974%205.99335%2012.1162%204.82946%2011.8847C3.66557%2011.6532%202.59648%2011.0818%201.75736%2010.2426C0.918247%209.40352%200.346802%208.33443%200.115291%207.17054C-0.11622%206.00666%200.00259969%204.80025%200.456725%203.7039C0.910851%202.60754%201.67989%201.67047%202.66658%201.01118ZM3.66667%205.83333C3.66667%205.99815%203.61779%206.15927%203.52623%206.29631C3.43466%206.43335%203.30451%206.54016%203.15224%206.60323C2.99997%206.66631%202.83241%206.68281%202.67076%206.65065C2.50911%206.6185%202.36062%206.53913%202.24408%206.42259C2.12753%206.30605%202.04817%206.15756%202.01601%205.99591C1.98386%205.83426%202.00036%205.6667%202.06343%205.51443C2.12651%205.36216%202.23332%205.23201%202.37036%205.14044C2.5074%205.04887%202.66852%205%202.83333%205C3.05435%205%203.26631%205.0878%203.42259%205.24408C3.57887%205.40036%203.66667%205.61232%203.66667%205.83333ZM6.83333%205.83333C6.83333%205.99815%206.78446%206.15927%206.69289%206.29631C6.60132%206.43335%206.47117%206.54016%206.3189%206.60323C6.16663%206.66631%205.99908%206.68281%205.83742%206.65065C5.67577%206.6185%205.52729%206.53913%205.41074%206.42259C5.2942%206.30605%205.21483%206.15756%205.18268%205.99591C5.15052%205.83426%205.16703%205.6667%205.2301%205.51443C5.29317%205.36216%205.39998%205.23201%205.53702%205.14044C5.67407%205.04887%205.83518%205%206%205C6.22101%205%206.43297%205.0878%206.58926%205.24408C6.74554%205.40036%206.83333%205.61232%206.83333%205.83333ZM9.85956%206.29631C9.95113%206.15927%2010%205.99815%2010%205.83333C10%205.61232%209.9122%205.40036%209.75592%205.24408C9.59964%205.0878%209.38768%205%209.16667%205C9.00185%205%208.84073%205.04887%208.70369%205.14044C8.56665%205.23201%208.45984%205.36216%208.39677%205.51443C8.33369%205.6667%208.31719%205.83426%208.34935%205.99591C8.3815%206.15756%208.46087%206.30605%208.57741%206.42259C8.69395%206.53913%208.84244%206.6185%209.00409%206.65065C9.16574%206.68281%209.3333%206.66631%209.48557%206.60323C9.63784%206.54016%209.76799%206.43335%209.85956%206.29631Z'%20fill='%23388A34'/%3e%3c/svg%3e"},o={[tt.included]:"included",[tt.excluded]:"excluded",[tt.partial]:"partially included"};let c,l,d;return s.$$set=u=>{"data"in u&&t(0,n=u.data),"wsContextModel"in u&&t(1,r=u.wsContextModel),"indentLevel"in u&&t(2,a=u.indentLevel)},s.$$.update=()=>{var u;1&s.$$.dirty&&t(4,l=(u=n).type===gt.folder&&u.inclusionState!==tt.excluded?u.expanded?"chevron-down":"chevron-right":u.type===gt.folder?"folder":"file"),1&s.$$.dirty&&t(3,c=n.type===gt.folder&&n.inclusionState!==tt.excluded),1&s.$$.dirty&&t(5,d=n.type===gt.folder&&n.expanded&&n.children&&n.children.length>0?n:null)},[n,r,a,c,l,d,()=>{r.toggleNode(n)},i,o]}class ti extends fe{constructor(e){super(),ge(this,e,Zc,Nc,he,{data:0,wsContextModel:1,indentLevel:2})}}function _r(s,e,t){const n=s.slice();return n[3]=e[t],n}function wr(s,e){let t,n,r;return n=new ti({props:{wsContextModel:e[0],data:e[3],indentLevel:0}}),{key:s,first:null,c(){t=Te(),b(n.$$.fragment),this.first=t},m(a,i){w(a,t,i),S(n,a,i),r=!0},p(a,i){e=a;const o={};1&i&&(o.wsContextModel=e[0]),2&i&&(o.data=e[3]),n.$set(o)},i(a){r||(p(n.$$.fragment,a),r=!0)},o(a){m(n.$$.fragment,a),r=!1},d(a){a&&y(t),k(n,a)}}}function Oc(s){let e,t,n=[],r=new Map,a=xe(s[1]);const i=o=>St.fileIdToString(o[3].fileId);for(let o=0;o<a.length;o+=1){let c=_r(s,a,o),l=i(c);r.set(l,n[o]=wr(l,c))}return{c(){e=M("div");for(let o=0;o<n.length;o+=1)n[o].c();$(e,"class","files-container svelte-8hfqhl")},m(o,c){w(o,e,c);for(let l=0;l<n.length;l+=1)n[l]&&n[l].m(e,null);t=!0},p(o,[c]){3&c&&(a=xe(o[1]),K(),n=Ot(n,c,i,1,o,a,r,e,Et,wr,null,_r),W())},i(o){if(!t){for(let c=0;c<a.length;c+=1)p(n[c]);t=!0}},o(o){for(let c=0;c<n.length;c+=1)m(n[c]);t=!1},d(o){o&&y(e);for(let c=0;c<n.length;c+=1)n[c].d()}}}function Ec(s,e,t){let n,r=H,a=()=>(r(),r=Bs(o,c=>t(2,n=c)),o);s.$$.on_destroy.push(()=>r());let i,{wsContextModel:o}=e;return a(),s.$$set=c=>{"wsContextModel"in c&&a(t(0,o=c.wsContextModel))},s.$$.update=()=>{4&s.$$.dirty&&t(1,i=n.sourceTree)},[o,i,n]}class Ic extends fe{constructor(e){super(),ge(this,e,Ec,Oc,he,{wsContextModel:0})}}function Pc(s){let e,t,n;return{c(){e=Fe("svg"),t=Fe("rect"),n=Fe("path"),$(t,"width","16"),$(t,"height","16"),$(t,"transform","matrix(-1 0 0 -1 16 16)"),$(t,"fill","currentColor"),$(t,"fill-opacity","0.01"),$(n,"fill-rule","evenodd"),$(n,"clip-rule","evenodd"),$(n,"d","M13.7075 11.7333C13.7075 12.8236 12.8236 13.7075 11.7333 13.7075C10.643 13.7075 9.75909 12.8236 9.75909 11.7333C9.75909 10.643 10.643 9.75909 11.7333 9.75909C12.8236 9.75909 13.7075 10.643 13.7075 11.7333ZM11.7333 14.6675C13.3538 14.6675 14.6675 13.3538 14.6675 11.7333C14.6675 10.1128 13.3538 8.79909 11.7333 8.79909C10.1128 8.79909 8.79909 10.1128 8.79909 11.7333C8.79909 13.3538 10.1128 14.6675 11.7333 14.6675ZM9.79161 4.26647L13.3333 2.30721V6.22571L9.79161 4.26647ZM13.1852 7.24088C13.6829 7.51617 14.2933 7.15625 14.2933 6.58752V1.9454C14.2933 1.37665 13.6829 1.01676 13.1852 1.29207L8.98946 3.61313C8.47582 3.89729 8.47582 4.63564 8.98946 4.9198L13.1852 7.24088ZM7.14663 6.39988C7.14663 6.81225 6.81233 7.14654 6.39996 7.14654H2.1333C1.72093 7.14654 1.38664 6.81225 1.38664 6.39988V2.13324C1.38664 1.72087 1.72093 1.38657 2.1333 1.38657H6.39996C6.81233 1.38657 7.14663 1.72087 7.14663 2.13324V6.39988ZM6.18663 6.18654V2.34657H2.34664V6.18654H6.18663ZM1.66056 13.6606C1.47314 13.848 1.47314 14.152 1.66056 14.3394C1.84797 14.5269 2.15186 14.5269 2.33938 14.3394L4.26664 12.4121L6.19388 14.3394C6.38133 14.5268 6.68525 14.5268 6.8727 14.3394C7.06015 14.1519 7.06015 13.848 6.8727 13.6606L4.94546 11.7333L6.8727 9.80608C7.06015 9.61863 7.06015 9.31471 6.8727 9.12726C6.68525 8.9398 6.38133 8.9398 6.19388 9.12726L4.26664 11.0545L2.33938 9.12722C2.15186 8.93978 1.84797 8.93978 1.66056 9.12722C1.47314 9.31468 1.47314 9.61861 1.66056 9.80605L3.58781 11.7333L1.66056 13.6606Z"),$(n,"fill","currentColor"),$(e,"width","15"),$(e,"height","15"),$(e,"viewBox","0 0 16 16"),$(e,"fill","none"),$(e,"xmlns","http://www.w3.org/2000/svg")},m(r,a){w(r,e,a),x(e,t),x(e,n)},p:H,i:H,o:H,d(r){r&&y(e)}}}class jc extends fe{constructor(e){super(),ge(this,e,null,Pc,he,{})}}const Rc=s=>({}),xr=s=>({}),Lc=s=>({}),br=s=>({});function Fc(s){let e;const t=s[8]["header-left"],n=Ie(t,s,s[10],br);return{c(){n&&n.c()},m(r,a){n&&n.m(r,a),e=!0},p(r,a){n&&n.p&&(!e||1024&a)&&Pe(n,t,r,r[10],e?Re(t,r[10],a,Lc):je(r[10]),br)},i(r){e||(p(n,r),e=!0)},o(r){m(n,r),e=!1},d(r){n&&n.d(r)}}}function zc(s){let e,t,n,r=s[0]&&Sr(s),a=s[1]&&kr(s);return{c(){r&&r.c(),e=E(),a&&a.c(),t=Te()},m(i,o){r&&r.m(i,o),w(i,e,o),a&&a.m(i,o),w(i,t,o),n=!0},p(i,o){i[0]?r?(r.p(i,o),1&o&&p(r,1)):(r=Sr(i),r.c(),p(r,1),r.m(e.parentNode,e)):r&&(K(),m(r,1,1,()=>{r=null}),W()),i[1]?a?(a.p(i,o),2&o&&p(a,1)):(a=kr(i),a.c(),p(a,1),a.m(t.parentNode,t)):a&&(K(),m(a,1,1,()=>{a=null}),W())},i(i){n||(p(r),p(a),n=!0)},o(i){m(r),m(a),n=!1},d(i){i&&(y(e),y(t)),r&&r.d(i),a&&a.d(i)}}}function Sr(s){let e,t,n;var r=s[0];return r&&(t=kt(r,{})),{c(){e=M("div"),t&&b(t.$$.fragment),$(e,"class","icon-wrapper svelte-13uht7n")},m(a,i){w(a,e,i),t&&S(t,e,null),n=!0},p(a,i){if(1&i&&r!==(r=a[0])){if(t){K();const o=t;m(o.$$.fragment,1,0,()=>{k(o,1)}),W()}r?(t=kt(r,{}),b(t.$$.fragment),p(t.$$.fragment,1),S(t,e,null)):t=null}},i(a){n||(t&&p(t.$$.fragment,a),n=!0)},o(a){t&&m(t.$$.fragment,a),n=!1},d(a){a&&y(e),t&&k(t)}}}function kr(s){let e,t;return e=new de({props:{color:"neutral",size:1,weight:"light",class:"card-title",$$slots:{default:[Dc]},$$scope:{ctx:s}}}),{c(){b(e.$$.fragment)},m(n,r){S(e,n,r),t=!0},p(n,r){const a={};1026&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Dc(s){let e;return{c(){e=U(s[1])},m(t,n){w(t,e,n)},p(t,n){2&n&&we(e,t[1])},d(t){t&&y(e)}}}function Cr(s){let e,t;const n=s[8].default,r=Ie(n,s,s[10],null);return{c(){e=M("div"),r&&r.c(),$(e,"class","settings-card-body")},m(a,i){w(a,e,i),r&&r.m(e,null),t=!0},p(a,i){r&&r.p&&(!t||1024&i)&&Pe(r,n,a,a[10],t?Re(n,a[10],i,null):je(a[10]),null)},i(a){t||(p(r,a),t=!0)},o(a){m(r,a),t=!1},d(a){a&&y(e),r&&r.d(a)}}}function Uc(s){let e,t,n,r,a,i,o,c,l,d,u;const h=[zc,Fc],g=[];function v(A,T){return A[0]||A[1]?0:1}r=v(s),a=g[r]=h[r](s);const f=s[8]["header-right"],_=Ie(f,s,s[10],xr);let C=s[5].default&&Cr(s),P=[{role:"button"},{class:s[3]},s[4]],I={};for(let A=0;A<P.length;A+=1)I=Se(I,P[A]);return{c(){e=M("div"),t=M("div"),n=M("div"),a.c(),i=E(),o=M("div"),_&&_.c(),c=E(),C&&C.c(),$(n,"class","settings-card-left svelte-13uht7n"),$(o,"class","settings-card-right svelte-13uht7n"),$(t,"class","settings-card-content svelte-13uht7n"),es(e,I),$e(e,"clickable",s[2]),$e(e,"svelte-13uht7n",!0)},m(A,T){w(A,e,T),x(e,t),x(t,n),g[r].m(n,null),x(t,i),x(t,o),_&&_.m(o,null),x(e,c),C&&C.m(e,null),l=!0,d||(u=ze(e,"click",s[9]),d=!0)},p(A,[T]){let N=r;r=v(A),r===N?g[r].p(A,T):(K(),m(g[N],1,1,()=>{g[N]=null}),W(),a=g[r],a?a.p(A,T):(a=g[r]=h[r](A),a.c()),p(a,1),a.m(n,null)),_&&_.p&&(!l||1024&T)&&Pe(_,f,A,A[10],l?Re(f,A[10],T,Rc):je(A[10]),xr),A[5].default?C?(C.p(A,T),32&T&&p(C,1)):(C=Cr(A),C.c(),p(C,1),C.m(e,null)):C&&(K(),m(C,1,1,()=>{C=null}),W()),es(e,I=yt(P,[{role:"button"},(!l||8&T)&&{class:A[3]},16&T&&A[4]])),$e(e,"clickable",A[2]),$e(e,"svelte-13uht7n",!0)},i(A){l||(p(a),p(_,A),p(C),l=!0)},o(A){m(a),m(_,A),m(C),l=!1},d(A){A&&y(e),g[r].d(),_&&_.d(A),C&&C.d(),d=!1,u()}}}function Vc(s,e,t){let n,r,a;const i=["class","icon","title","isClickable"];let o=ts(e,i),{$$slots:c={},$$scope:l}=e;const d=Za(c);let{class:u=""}=e,{icon:h}=e,{title:g}=e,{isClickable:v=!1}=e;return s.$$set=f=>{e=Se(Se({},e),He(f)),t(11,o=ts(e,i)),"class"in f&&t(6,u=f.class),"icon"in f&&t(0,h=f.icon),"title"in f&&t(1,g=f.title),"isClickable"in f&&t(2,v=f.isClickable),"$$scope"in f&&t(10,l=f.$$scope)},s.$$.update=()=>{t(7,{class:n,...r}=o,n,(t(4,r),t(11,o))),192&s.$$.dirty&&t(3,a=`settings-card ${u} ${n||""}`)},[h,g,v,a,r,d,u,n,c,function(f){Oa.call(this,s,f)},l]}let ni=class extends fe{constructor(s){super(),ge(this,s,Vc,Uc,he,{class:6,icon:0,title:1,isClickable:2})}};function qc(s){let e;return{c(){e=U("SOURCE FOLDERS")},m(t,n){w(t,e,n)},d(t){t&&y(e)}}}function Bc(s){let e;return{c(){e=U("FILES")},m(t,n){w(t,e,n)},d(t){t&&y(e)}}}function Jc(s){let e,t=s[2].toLocaleString()+"";return{c(){e=U(t)},m(n,r){w(n,e,r)},p(n,r){4&r&&t!==(t=n[2].toLocaleString()+"")&&we(e,t)},d(n){n&&y(e)}}}function Gc(s){let e,t,n,r,a,i,o,c,l,d,u,h,g,v;return n=new de({props:{size:1,weight:"medium",class:"context-section-header",$$slots:{default:[qc]},$$scope:{ctx:s}}}),a=new Mc({props:{folders:s[0],onRemove:s[7],onAddMore:s[8]}}),l=new de({props:{size:1,weight:"medium",class:"context-section-header",$$slots:{default:[Bc]},$$scope:{ctx:s}}}),u=new de({props:{size:1,class:"file-count",$$slots:{default:[Jc]},$$scope:{ctx:s}}}),g=new Ic({props:{wsContextModel:s[3]}}),{c(){e=M("div"),t=M("div"),b(n.$$.fragment),r=E(),b(a.$$.fragment),i=E(),o=M("div"),c=M("div"),b(l.$$.fragment),d=E(),b(u.$$.fragment),h=E(),b(g.$$.fragment),$(c,"class","files-header svelte-qsnirf"),$(e,"class","context-list svelte-qsnirf")},m(f,_){w(f,e,_),x(e,t),S(n,t,null),x(t,r),S(a,t,null),x(e,i),x(e,o),x(o,c),S(l,c,null),x(c,d),S(u,c,null),x(o,h),S(g,o,null),v=!0},p(f,_){const C={};512&_&&(C.$$scope={dirty:_,ctx:f}),n.$set(C);const P={};1&_&&(P.folders=f[0]),a.$set(P);const I={};512&_&&(I.$$scope={dirty:_,ctx:f}),l.$set(I);const A={};516&_&&(A.$$scope={dirty:_,ctx:f}),u.$set(A)},i(f){v||(p(n.$$.fragment,f),p(a.$$.fragment,f),p(l.$$.fragment,f),p(u.$$.fragment,f),p(g.$$.fragment,f),v=!0)},o(f){m(n.$$.fragment,f),m(a.$$.fragment,f),m(l.$$.fragment,f),m(u.$$.fragment,f),m(g.$$.fragment,f),v=!1},d(f){f&&y(e),k(n),k(a),k(l),k(u),k(g)}}}function Tr(s){let e,t;return e=new ws({props:{title:"Refresh",variant:"ghost-block",color:"neutral",size:1,$$slots:{default:[Hc]},$$scope:{ctx:s}}}),e.$on("click",s[5]),e.$on("keyup",Qi("Enter",s[6])),{c(){b(e.$$.fragment)},m(n,r){S(e,n,r),t=!0},p(n,r){const a={};512&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Hc(s){let e,t;return e=new Hi({}),{c(){b(e.$$.fragment)},m(n,r){S(e,n,r),t=!0},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Kc(s){let e,t,n=s[1]===Zs.done&&Tr(s);return{c(){e=M("div"),n&&n.c(),$(e,"slot","header-right")},m(r,a){w(r,e,a),n&&n.m(e,null),t=!0},p(r,a){r[1]===Zs.done?n?(n.p(r,a),2&a&&p(n,1)):(n=Tr(r),n.c(),p(n,1),n.m(e,null)):n&&(K(),m(n,1,1,()=>{n=null}),W())},i(r){t||(p(n),t=!0)},o(r){m(n),t=!1},d(r){r&&y(e),n&&n.d()}}}function Wc(s){let e,t,n,r;return e=new ni({props:{icon:jc,title:"Context",$$slots:{"header-right":[Kc],default:[Gc]},$$scope:{ctx:s}}}),e.$on("contextmenu",Yc),{c(){b(e.$$.fragment)},m(a,i){S(e,a,i),t=!0,n||(r=ze(window,"message",s[3].handleMessageFromExtension),n=!0)},p(a,[i]){const o={};519&i&&(o.$$scope={dirty:i,ctx:a}),e.$set(o)},i(a){t||(p(e.$$.fragment,a),t=!0)},o(a){m(e.$$.fragment,a),t=!1},d(a){k(e,a),n=!1,r()}}}const Yc=s=>s.preventDefault();function Xc(s,e,t){let n,r,a,i,o=new St(Ae,new Xi(Ae.postMessage));return bt(s,o,c=>t(4,r=c)),s.$$.update=()=>{16&s.$$.dirty&&t(0,a=r.sourceFolders.sort((c,l)=>c.isWorkspaceFolder!==l.isWorkspaceFolder?c.isWorkspaceFolder?-1:1:c.fileId.folderRoot.localeCompare(l.fileId.folderRoot))),16&s.$$.dirty&&t(1,i=r.syncStatus),1&s.$$.dirty&&t(2,n=a.reduce((c,l)=>c+(l.trackedFileCount??0),0))},[a,i,n,o,r,()=>o.requestRefresh(),()=>o.requestRefresh(),c=>o.removeSourceFolder(c),()=>o.addMoreSourceFolders()]}class Qc extends fe{constructor(e){super(),ge(this,e,Xc,Wc,he,{})}}function si(s){return function(e){switch(typeof e){case"object":return e!=null;case"function":return!0;default:return!1}}(s)&&"name"in s}function Mr(s){return si(s)&&"component"in s}function el(s){let e,t;return{c(){e=Fe("svg"),t=Fe("path"),$(t,"d","M5.5 1.75V3H10.5V1.75C10.5 1.625 10.375 1.5 10.25 1.5H5.75C5.59375 1.5 5.5 1.625 5.5 1.75ZM4 3V1.75C4 0.8125 4.78125 0 5.75 0H10.25C11.1875 0 12 0.8125 12 1.75V3H14C15.0938 3 16 3.90625 16 5V8.75V13C16 14.125 15.0938 15 14 15H2C0.875 15 0 14.125 0 13V8.75V5C0 3.90625 0.875 3 2 3H4ZM1.5 9.5V13C1.5 13.2812 1.71875 13.5 2 13.5H14C14.25 13.5 14.5 13.2812 14.5 13V9.5H10V10C10 10.5625 9.53125 11 9 11H7C6.4375 11 6 10.5625 6 10V9.5H1.5ZM6 8H10H14.5V5C14.5 4.75 14.25 4.5 14 4.5H11.25H4.75H2C1.71875 4.5 1.5 4.75 1.5 5V8H6Z"),$(t,"fill","currentColor"),$(e,"width","16"),$(e,"height","15"),$(e,"viewBox","0 0 16 15"),$(e,"xmlns","http://www.w3.org/2000/svg")},m(n,r){w(n,e,r),x(e,t)},p:H,i:H,o:H,d(n){n&&y(e)}}}class ri extends fe{constructor(e){super(),ge(this,e,null,el,he,{})}}const tl=s=>({item:1&s}),Ar=s=>({item:s[0]}),nl=s=>({}),Nr=s=>({});function Zr(s){var l;let e,t,n,r,a;e=new de({props:{size:4,weight:"medium",color:"neutral",$$slots:{default:[sl]},$$scope:{ctx:s}}});let i=((l=s[0])==null?void 0:l.description)&&Or(s);const o=s[1].content,c=Ie(o,s,s[2],Ar);return{c(){b(e.$$.fragment),t=E(),i&&i.c(),n=E(),r=M("div"),c&&c.c(),$(r,"class","c-navigation__content-container svelte-z0ijuz")},m(d,u){S(e,d,u),w(d,t,u),i&&i.m(d,u),w(d,n,u),w(d,r,u),c&&c.m(r,null),a=!0},p(d,u){var g;const h={};5&u&&(h.$$scope={dirty:u,ctx:d}),e.$set(h),(g=d[0])!=null&&g.description?i?(i.p(d,u),1&u&&p(i,1)):(i=Or(d),i.c(),p(i,1),i.m(n.parentNode,n)):i&&(K(),m(i,1,1,()=>{i=null}),W()),c&&c.p&&(!a||5&u)&&Pe(c,o,d,d[2],a?Re(o,d[2],u,tl):je(d[2]),Ar)},i(d){a||(p(e.$$.fragment,d),p(i),p(c,d),a=!0)},o(d){m(e.$$.fragment,d),m(i),m(c,d),a=!1},d(d){d&&(y(t),y(n),y(r)),k(e,d),i&&i.d(d),c&&c.d(d)}}}function sl(s){var r;let e,t,n=((r=s[0])==null?void 0:r.name)+"";return{c(){e=M("div"),t=U(n),$(e,"class","c-navigation__content-header svelte-z0ijuz")},m(a,i){w(a,e,i),x(e,t)},p(a,i){var o;1&i&&n!==(n=((o=a[0])==null?void 0:o.name)+"")&&we(t,n)},d(a){a&&y(e)}}}function Or(s){let e,t;return e=new de({props:{color:"secondary",size:1,weight:"light",$$slots:{default:[rl]},$$scope:{ctx:s}}}),{c(){b(e.$$.fragment)},m(n,r){S(e,n,r),t=!0},p(n,r){const a={};5&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function rl(s){var r;let e,t,n=((r=s[0])==null?void 0:r.description)+"";return{c(){e=M("div"),t=U(n),$(e,"class","c-navigation__content-description svelte-z0ijuz")},m(a,i){w(a,e,i),x(e,t)},p(a,i){var o;1&i&&n!==(n=((o=a[0])==null?void 0:o.description)+"")&&we(t,n)},d(a){a&&y(e)}}}function al(s){let e,t,n,r,a;const i=s[1].header,o=Ie(i,s,s[2],Nr);let c=s[0]!=null&&Zr(s);return{c(){var l;e=M("div"),o&&o.c(),t=E(),n=M("div"),c&&c.c(),$(e,"class","c-navigation__content svelte-z0ijuz"),$(e,"id",r=(l=s[0])==null?void 0:l.id)},m(l,d){w(l,e,d),o&&o.m(e,null),x(e,t),x(e,n),c&&c.m(n,null),a=!0},p(l,[d]){var u;o&&o.p&&(!a||4&d)&&Pe(o,i,l,l[2],a?Re(i,l[2],d,nl):je(l[2]),Nr),l[0]!=null?c?(c.p(l,d),1&d&&p(c,1)):(c=Zr(l),c.c(),p(c,1),c.m(n,null)):c&&(K(),m(c,1,1,()=>{c=null}),W()),(!a||1&d&&r!==(r=(u=l[0])==null?void 0:u.id))&&$(e,"id",r)},i(l){a||(p(o,l),p(c),a=!0)},o(l){m(o,l),m(c),a=!1},d(l){l&&y(e),o&&o.d(l),c&&c.d()}}}function il(s,e,t){let{$$slots:n={},$$scope:r}=e,{item:a}=e;return s.$$set=i=>{"item"in i&&t(0,a=i.item),"$$scope"in i&&t(2,r=i.$$scope)},[a,n,r]}class ai extends fe{constructor(e){super(),ge(this,e,il,al,he,{item:0})}}function ol(s,e){let t;function n({scrollTo:r,delay:a,options:i}){clearTimeout(t),r&&(t=setTimeout(()=>{s.scrollIntoView(i)},a))}return n(e),{update:n,destroy(){clearTimeout(t)}}}function Er(s,e,t){const n=s.slice();return n[13]=e[t][0],n[14]=e[t][1],n}function Ir(s,e,t){const n=s.slice();return n[22]=e[t],n}const cl=s=>({item:32&s}),Pr=s=>({slot:"content",item:s[22]}),ll=s=>({label:32&s,mode:4&s}),jr=s=>({label:s[13],mode:s[2]}),dl=s=>({item:1&s}),Rr=s=>({item:s[0]});function Lr(s,e,t){const n=s.slice();return n[13]=e[t][0],n[14]=e[t][1],n}function Fr(s,e,t){const n=s.slice();return n[17]=e[t],n}const ul=s=>({label:32&s,mode:4&s}),zr=s=>({label:s[13],mode:s[2]}),pl=s=>({item:1&s,selectedId:2&s}),Dr=s=>({slot:"header",item:s[0],selectedId:s[1]}),ml=s=>({item:1&s,isSelected:3&s}),Ur=s=>{var e;return{slot:"content",item:s[0],isSelected:((e=s[0])==null?void 0:e.id)===s[1]}};function hl(s){let e,t,n;const r=s[10].header,a=Ie(r,s,s[12],Rr);let i=xe(s[5]),o=[];for(let l=0;l<i.length;l+=1)o[l]=qr(Er(s,i,l));const c=l=>m(o[l],1,1,()=>{o[l]=null});return{c(){e=M("div"),a&&a.c(),t=E();for(let l=0;l<o.length;l+=1)o[l].c();$(e,"class","c-navigation__flat svelte-1u6mxxy")},m(l,d){w(l,e,d),a&&a.m(e,null),x(e,t);for(let u=0;u<o.length;u+=1)o[u]&&o[u].m(e,null);n=!0},p(l,d){if(a&&a.p&&(!n||4097&d)&&Pe(a,r,l,l[12],n?Re(r,l[12],d,dl):je(l[12]),Rr),4134&d){let u;for(i=xe(l[5]),u=0;u<i.length;u+=1){const h=Er(l,i,u);o[u]?(o[u].p(h,d),p(o[u],1)):(o[u]=qr(h),o[u].c(),p(o[u],1),o[u].m(e,null))}for(K(),u=i.length;u<o.length;u+=1)c(u);W()}},i(l){if(!n){p(a,l);for(let d=0;d<i.length;d+=1)p(o[d]);n=!0}},o(l){m(a,l),o=o.filter(Boolean);for(let d=0;d<o.length;d+=1)m(o[d]);n=!1},d(l){l&&y(e),a&&a.d(l),Fn(o,l)}}}function fl(s){let e,t;return e=new eo({props:{initialWidth:200,expandedMinWidth:150,columnLayoutThreshold:0,showButton:s[3],minimized:!1,$$slots:{right:[bl],left:[_l]},$$scope:{ctx:s}}}),{c(){b(e.$$.fragment)},m(n,r){S(e,n,r),t=!0},p(n,r){const a={};8&r&&(a.showButton=n[3]),4135&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function gl(s){let e,t,n,r,a,i,o=s[13]+"";return t=new ri({}),{c(){e=M("span"),b(t.$$.fragment),n=E(),r=M("span"),a=U(o),$(e,"class","c-navigation__head-icon")},m(c,l){w(c,e,l),S(t,e,null),w(c,n,l),w(c,r,l),x(r,a),i=!0},p(c,l){(!i||32&l)&&o!==(o=c[13]+"")&&we(a,o)},i(c){i||(p(t.$$.fragment,c),i=!0)},o(c){m(t.$$.fragment,c),i=!1},d(c){c&&(y(e),y(n),y(r)),k(t)}}}function vl(s){let e;const t=s[10].content,n=Ie(t,s,s[12],Pr);return{c(){n&&n.c()},m(r,a){n&&n.m(r,a),e=!0},p(r,a){n&&n.p&&(!e||4128&a)&&Pe(n,t,r,r[12],e?Re(t,r[12],a,cl):je(r[12]),Pr)},i(r){e||(p(n,r),e=!0)},o(r){m(n,r),e=!1},d(r){n&&n.d(r)}}}function Vr(s){let e,t,n,r,a,i,o;return t=new ai({props:{item:s[22],$$slots:{content:[vl]},$$scope:{ctx:s}}}),{c(){e=M("span"),b(t.$$.fragment),n=E()},m(c,l){w(c,e,l),S(t,e,null),x(e,n),a=!0,i||(o=xi(r=ol.call(null,e,{scrollTo:s[2]==="flat"&&s[22].id===s[1],delay:300,options:{behavior:"smooth"}})),i=!0)},p(c,l){s=c;const d={};32&l&&(d.item=s[22]),4128&l&&(d.$$scope={dirty:l,ctx:s}),t.$set(d),r&&jt(r.update)&&38&l&&r.update.call(null,{scrollTo:s[2]==="flat"&&s[22].id===s[1],delay:300,options:{behavior:"smooth"}})},i(c){a||(p(t.$$.fragment,c),a=!0)},o(c){m(t.$$.fragment,c),a=!1},d(c){c&&y(e),k(t),i=!1,o()}}}function qr(s){let e,t,n,r;const a=s[10].group,i=Ie(a,s,s[12],jr),o=i||function(u){let h,g;return h=new de({props:{color:"secondary",size:2,weight:"medium",$$slots:{default:[gl]},$$scope:{ctx:u}}}),{c(){b(h.$$.fragment)},m(v,f){S(h,v,f),g=!0},p(v,f){const _={};4128&f&&(_.$$scope={dirty:f,ctx:v}),h.$set(_)},i(v){g||(p(h.$$.fragment,v),g=!0)},o(v){m(h.$$.fragment,v),g=!1},d(v){k(h,v)}}}(s);let c=xe(s[14]),l=[];for(let u=0;u<c.length;u+=1)l[u]=Vr(Ir(s,c,u));const d=u=>m(l[u],1,1,()=>{l[u]=null});return{c(){e=M("div"),o&&o.c(),t=E();for(let u=0;u<l.length;u+=1)l[u].c();n=Te(),$(e,"class","c-navigation__head svelte-1u6mxxy")},m(u,h){w(u,e,h),o&&o.m(e,null),w(u,t,h);for(let g=0;g<l.length;g+=1)l[g]&&l[g].m(u,h);w(u,n,h),r=!0},p(u,h){if(i?i.p&&(!r||4132&h)&&Pe(i,a,u,u[12],r?Re(a,u[12],h,ll):je(u[12]),jr):o&&o.p&&(!r||32&h)&&o.p(u,r?h:-1),4134&h){let g;for(c=xe(u[14]),g=0;g<c.length;g+=1){const v=Ir(u,c,g);l[g]?(l[g].p(v,h),p(l[g],1)):(l[g]=Vr(v),l[g].c(),p(l[g],1),l[g].m(n.parentNode,n))}for(K(),g=c.length;g<l.length;g+=1)d(g);W()}},i(u){if(!r){p(o,u);for(let h=0;h<c.length;h+=1)p(l[h]);r=!0}},o(u){m(o,u),l=l.filter(Boolean);for(let h=0;h<l.length;h+=1)m(l[h]);r=!1},d(u){u&&(y(e),y(t),y(n)),o&&o.d(u),Fn(l,u)}}}function $l(s){let e,t=s[13]+"";return{c(){e=U(t)},m(n,r){w(n,e,r)},p(n,r){32&r&&t!==(t=n[13]+"")&&we(e,t)},d(n){n&&y(e)}}}function yl(s){let e,t,n,r,a,i=s[17].name+"";var o=s[17].icon;return o&&(t=kt(o,{})),{c(){e=M("span"),t&&b(t.$$.fragment),n=E(),r=U(i),$(e,"class","c-navigation__head-icon")},m(c,l){w(c,e,l),t&&S(t,e,null),w(c,n,l),w(c,r,l),a=!0},p(c,l){if(32&l&&o!==(o=c[17].icon)){if(t){K();const d=t;m(d.$$.fragment,1,0,()=>{k(d,1)}),W()}o?(t=kt(o,{}),b(t.$$.fragment),p(t.$$.fragment,1),S(t,e,null)):t=null}(!a||32&l)&&i!==(i=c[17].name+"")&&we(r,i)},i(c){a||(t&&p(t.$$.fragment,c),a=!0)},o(c){t&&m(t.$$.fragment,c),a=!1},d(c){c&&(y(e),y(n),y(r)),t&&k(t)}}}function Br(s){let e,t,n,r,a,i;function o(){return s[11](s[17])}return t=new de({props:{size:2,weight:"regular",color:"primary",$$slots:{default:[yl]},$$scope:{ctx:s}}}),{c(){e=M("button"),b(t.$$.fragment),n=E(),$(e,"class","c-navigation__item svelte-1u6mxxy"),$e(e,"is-active",s[17].id===s[1])},m(c,l){w(c,e,l),S(t,e,null),x(e,n),r=!0,a||(i=ze(e,"click",o),a=!0)},p(c,l){s=c;const d={};4128&l&&(d.$$scope={dirty:l,ctx:s}),t.$set(d),(!r||34&l)&&$e(e,"is-active",s[17].id===s[1])},i(c){r||(p(t.$$.fragment,c),r=!0)},o(c){m(t.$$.fragment,c),r=!1},d(c){c&&y(e),k(t),a=!1,i()}}}function Jr(s){let e,t,n,r,a;const i=s[10].group,o=Ie(i,s,s[12],zr),c=o||function(h){let g,v,f,_,C;return v=new ri({}),_=new de({props:{size:2,color:"primary",$$slots:{default:[$l]},$$scope:{ctx:h}}}),{c(){g=M("div"),b(v.$$.fragment),f=E(),b(_.$$.fragment),$(g,"class","c-navigation__head svelte-1u6mxxy")},m(P,I){w(P,g,I),S(v,g,null),x(g,f),S(_,g,null),C=!0},p(P,I){const A={};4128&I&&(A.$$scope={dirty:I,ctx:P}),_.$set(A)},i(P){C||(p(v.$$.fragment,P),p(_.$$.fragment,P),C=!0)},o(P){m(v.$$.fragment,P),m(_.$$.fragment,P),C=!1},d(P){P&&y(g),k(v),k(_)}}}(s);let l=xe(s[14]),d=[];for(let h=0;h<l.length;h+=1)d[h]=Br(Fr(s,l,h));const u=h=>m(d[h],1,1,()=>{d[h]=null});return{c(){e=M("div"),c&&c.c(),t=E(),n=M("div");for(let h=0;h<d.length;h+=1)d[h].c();r=E(),$(n,"class","c-navigation__items svelte-1u6mxxy"),$(e,"class","c-navigation__group")},m(h,g){w(h,e,g),c&&c.m(e,null),x(e,t),x(e,n);for(let v=0;v<d.length;v+=1)d[v]&&d[v].m(n,null);x(e,r),a=!0},p(h,g){if(o?o.p&&(!a||4132&g)&&Pe(o,i,h,h[12],a?Re(i,h[12],g,ul):je(h[12]),zr):c&&c.p&&(!a||32&g)&&c.p(h,a?g:-1),98&g){let v;for(l=xe(h[14]),v=0;v<l.length;v+=1){const f=Fr(h,l,v);d[v]?(d[v].p(f,g),p(d[v],1)):(d[v]=Br(f),d[v].c(),p(d[v],1),d[v].m(n,null))}for(K(),v=l.length;v<d.length;v+=1)u(v);W()}},i(h){if(!a){p(c,h);for(let g=0;g<l.length;g+=1)p(d[g]);a=!0}},o(h){m(c,h),d=d.filter(Boolean);for(let g=0;g<d.length;g+=1)m(d[g]);a=!1},d(h){h&&y(e),c&&c.d(h),Fn(d,h)}}}function Gr(s){let e,t,n=xe(s[5]),r=[];for(let i=0;i<n.length;i+=1)r[i]=Jr(Lr(s,n,i));const a=i=>m(r[i],1,1,()=>{r[i]=null});return{c(){for(let i=0;i<r.length;i+=1)r[i].c();e=Te()},m(i,o){for(let c=0;c<r.length;c+=1)r[c]&&r[c].m(i,o);w(i,e,o),t=!0},p(i,o){if(4198&o){let c;for(n=xe(i[5]),c=0;c<n.length;c+=1){const l=Lr(i,n,c);r[c]?(r[c].p(l,o),p(r[c],1)):(r[c]=Jr(l),r[c].c(),p(r[c],1),r[c].m(e.parentNode,e))}for(K(),c=n.length;c<r.length;c+=1)a(c);W()}},i(i){if(!t){for(let o=0;o<n.length;o+=1)p(r[o]);t=!0}},o(i){r=r.filter(Boolean);for(let o=0;o<r.length;o+=1)m(r[o]);t=!1},d(i){i&&y(e),Fn(r,i)}}}function _l(s){let e,t,n=s[1],r=Gr(s);return{c(){e=M("nav"),r.c(),$(e,"class","c-navigation__nav svelte-1u6mxxy"),$(e,"slot","left")},m(a,i){w(a,e,i),r.m(e,null),t=!0},p(a,i){2&i&&he(n,n=a[1])?(K(),m(r,1,1,H),W(),r=Gr(a),r.c(),p(r,1),r.m(e,null)):r.p(a,i)},i(a){t||(p(r),t=!0)},o(a){m(r),t=!1},d(a){a&&y(e),r.d(a)}}}function wl(s){let e;const t=s[10].header,n=Ie(t,s,s[12],Dr);return{c(){n&&n.c()},m(r,a){n&&n.m(r,a),e=!0},p(r,a){n&&n.p&&(!e||4099&a)&&Pe(n,t,r,r[12],e?Re(t,r[12],a,pl):je(r[12]),Dr)},i(r){e||(p(n,r),e=!0)},o(r){m(n,r),e=!1},d(r){n&&n.d(r)}}}function Hr(s){let e,t,n;const r=[s[0].props];var a=s[0].component;function i(o,c){let l={};for(let d=0;d<r.length;d+=1)l=Se(l,r[d]);return c!==void 0&&1&c&&(l=Se(l,yt(r,[nr(o[0].props)]))),{props:l}}return a&&(e=kt(a,i(s))),{c(){e&&b(e.$$.fragment),t=Te()},m(o,c){e&&S(e,o,c),w(o,t,c),n=!0},p(o,c){if(1&c&&a!==(a=o[0].component)){if(e){K();const l=e;m(l.$$.fragment,1,0,()=>{k(l,1)}),W()}a?(e=kt(a,i(o,c)),b(e.$$.fragment),p(e.$$.fragment,1),S(e,t.parentNode,t)):e=null}else if(a){const l=1&c?yt(r,[nr(o[0].props)]):{};e.$set(l)}},i(o){n||(e&&p(e.$$.fragment,o),n=!0)},o(o){e&&m(e.$$.fragment,o),n=!1},d(o){o&&y(t),e&&k(e,o)}}}function xl(s){let e;const t=s[10].content,n=Ie(t,s,s[12],Ur),r=n||function(a){let i,o,c=Mr(a[0])&&Kr(a[0],a[2],a[1]),l=c&&Hr(a);return{c(){l&&l.c(),i=Te()},m(d,u){l&&l.m(d,u),w(d,i,u),o=!0},p(d,u){7&u&&(c=Mr(d[0])&&Kr(d[0],d[2],d[1])),c?l?(l.p(d,u),7&u&&p(l,1)):(l=Hr(d),l.c(),p(l,1),l.m(i.parentNode,i)):l&&(K(),m(l,1,1,()=>{l=null}),W())},i(d){o||(p(l),o=!0)},o(d){m(l),o=!1},d(d){d&&y(i),l&&l.d(d)}}}(s);return{c(){r&&r.c()},m(a,i){r&&r.m(a,i),e=!0},p(a,i){n?n.p&&(!e||4099&i)&&Pe(n,t,a,a[12],e?Re(t,a[12],i,ml):je(a[12]),Ur):r&&r.p&&(!e||7&i)&&r.p(a,e?i:-1)},i(a){e||(p(r,a),e=!0)},o(a){m(r,a),e=!1},d(a){r&&r.d(a)}}}function bl(s){let e,t;return e=new ai({props:{item:s[0],slot:"right",$$slots:{content:[xl],header:[wl]},$$scope:{ctx:s}}}),{c(){b(e.$$.fragment)},m(n,r){S(e,n,r),t=!0},p(n,r){const a={};1&r&&(a.item=n[0]),4103&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Sl(s){let e,t,n,r,a;const i=[fl,hl],o=[];function c(l,d){return l[2]==="tree"?0:1}return t=c(s),n=o[t]=i[t](s),{c(){e=M("div"),n.c(),$(e,"class",r="c-navigation c-navigation--mode__"+s[2]+" "+s[4]+" svelte-1u6mxxy")},m(l,d){w(l,e,d),o[t].m(e,null),a=!0},p(l,[d]){let u=t;t=c(l),t===u?o[t].p(l,d):(K(),m(o[u],1,1,()=>{o[u]=null}),W(),n=o[t],n?n.p(l,d):(n=o[t]=i[t](l),n.c()),p(n,1),n.m(e,null)),(!a||20&d&&r!==(r="c-navigation c-navigation--mode__"+l[2]+" "+l[4]+" svelte-1u6mxxy"))&&$(e,"class",r)},i(l){a||(p(n),a=!0)},o(l){m(n),a=!1},d(l){l&&y(e),o[t].d()}}}function Yn(s,e,t,n,r,a){return{name:s,description:e,icon:t,id:n}}function Kr(s,e,t){return e!=="tree"||(s==null?void 0:s.id)===t}function kl(s,e,t){let{$$slots:n={},$$scope:r}=e,{group:a="Workspace Settings"}=e,{items:i=[]}=e,{item:o}=e,{mode:c="tree"}=e,{selectedId:l}=e,{onNavigationChangeItem:d=f=>{}}=e,{showButton:u=!0}=e,{class:h=""}=e,g=new Map;function v(f){t(0,o=f),t(1,l=f==null?void 0:f.id)}return s.$$set=f=>{"group"in f&&t(7,a=f.group),"items"in f&&t(8,i=f.items),"item"in f&&t(0,o=f.item),"mode"in f&&t(2,c=f.mode),"selectedId"in f&&t(1,l=f.selectedId),"onNavigationChangeItem"in f&&t(9,d=f.onNavigationChangeItem),"showButton"in f&&t(3,u=f.showButton),"class"in f&&t(4,h=f.class),"$$scope"in f&&t(12,r=f.$$scope)},s.$$.update=()=>{259&s.$$.dirty&&(l?t(0,o=i.find(f=>(f==null?void 0:f.id)===l)):t(1,l=o==null?void 0:o.id)),384&s.$$.dirty&&t(5,g=i.reduce((f,_)=>{if(!_)return f;const C=_.group??a,P=f.get(C)??[];return P.push(_),f.set(C,P),f},new Map)),257&s.$$.dirty&&(o||t(0,o=i[0])),514&s.$$.dirty&&d(l)},[o,l,c,u,h,g,v,a,i,d,n,f=>v(f),r]}class Cl extends fe{constructor(e){super(),ge(this,e,kl,Sl,he,{group:7,items:8,item:0,mode:2,selectedId:1,onNavigationChangeItem:9,showButton:3,class:4})}}function Tl(s){let e,t;return{c(){e=Fe("svg"),t=Fe("path"),$(t,"d","M3.13281 0.886719L5.97656 3.07422C6.14062 3.21094 6.25 3.40234 6.25 3.59375V5.07031L9.23047 8.05078C10.0234 7.66797 11.0078 7.80469 11.6641 8.46094L14.7266 11.5234C15.082 11.8516 15.082 12.4258 14.7266 12.7539L12.9766 14.5039C12.6484 14.8594 12.0742 14.8594 11.7461 14.5039L8.68359 11.4414C8.02734 10.7852 7.89062 9.77344 8.30078 8.98047L5.32031 6H3.81641C3.625 6 3.43359 5.91797 3.29688 5.75391L1.10938 2.91016C0.917969 2.63672 0.945312 2.28125 1.19141 2.03516L2.28516 0.941406C2.50391 0.722656 2.88672 0.695312 3.13281 0.886719ZM1.62891 11.0586L5.375 7.3125L6.30469 8.24219L2.55859 11.9883C2.39453 12.1523 2.3125 12.3711 2.3125 12.5898C2.3125 13.0547 2.69531 13.4375 3.16016 13.4375C3.37891 13.4375 3.59766 13.3555 3.76172 13.1914L7.17969 9.77344C7.15234 10.293 7.26172 10.8125 7.50781 11.3047L4.69141 14.1211C4.28125 14.5312 3.73438 14.75 3.16016 14.75C1.95703 14.75 1 13.793 1 12.5898C1 12.0156 1.21875 11.4688 1.62891 11.0586ZM13.6602 5.23438L12.9766 5.94531C12.6484 6.27344 12.2109 6.46484 11.7461 6.46484H11.0625C10.0781 6.46484 9.3125 5.67188 9.3125 4.71484V4.00391C9.3125 3.53906 9.47656 3.10156 9.80469 2.77344L10.5156 2.08984C8.875 2.14453 7.5625 3.48438 7.5625 5.125V5.15234L7.125 4.71484V3.59375C7.125 3.32031 7.04297 3.04688 6.90625 2.82812C7.67188 1.59766 9.03906 0.75 10.625 0.75C11.2812 0.75 11.9375 0.914062 12.5117 1.1875C12.7578 1.32422 12.7852 1.65234 12.5938 1.84375L10.7344 3.70312C10.6523 3.78516 10.625 3.89453 10.625 4.00391V4.6875C10.625 4.93359 10.8164 5.125 11.0625 5.125L11.7461 5.15234C11.8555 5.15234 11.9648 5.09766 12.0469 5.01562L13.9062 3.15625C14.0977 2.96484 14.4258 2.99219 14.5625 3.23828C14.8359 3.8125 15 4.46875 15 5.15234C15 6.60156 14.2617 7.91406 13.1406 8.70703L12.293 7.83203C12.2656 7.80469 12.2383 7.77734 12.2109 7.75C13.0586 7.23047 13.6328 6.30078 13.6602 5.23438Z"),$(t,"fill","currentColor"),$(e,"width","16"),$(e,"height","16"),$(e,"viewBox","0 0 16 16"),$(e,"xmlns","http://www.w3.org/2000/svg")},m(n,r){w(n,e,r),x(e,t)},p:H,i:H,o:H,d(n){n&&y(e)}}}class Ml extends fe{constructor(e){super(),ge(this,e,null,Tl,he,{})}}function Al(s){let e,t;return{c(){e=Fe("svg"),t=Fe("path"),$(t,"d","M6.25 10.8125H12.375C12.5938 10.8125 12.8125 10.6211 12.8125 10.375V4.25H11.5C11.0078 4.25 10.625 3.86719 10.625 3.375V2.0625H6.25C6.00391 2.0625 5.8125 2.28125 5.8125 2.5V10.375C5.8125 10.6211 6.00391 10.8125 6.25 10.8125ZM12.375 12.125H6.25C5.26562 12.125 4.5 11.3594 4.5 10.375V2.5C4.5 1.54297 5.26562 0.75 6.25 0.75H10.7617C11.2266 0.75 11.6641 0.941406 11.9922 1.26953L13.6055 2.88281C13.9336 3.21094 14.125 3.64844 14.125 4.11328V10.375C14.125 11.3594 13.332 12.125 12.375 12.125ZM2.53125 3.375C2.88672 3.375 3.1875 3.67578 3.1875 4.03125V11.0312C3.1875 12.3711 4.25391 13.4375 5.59375 13.4375H10.8438C11.1992 13.4375 11.5 13.7383 11.5 14.0938C11.5 14.4766 11.1992 14.75 10.8438 14.75H5.59375C3.51562 14.75 1.875 13.1094 1.875 11.0312V4.03125C1.875 3.67578 2.14844 3.375 2.53125 3.375Z"),$(t,"fill","currentColor"),$(e,"width","16"),$(e,"height","16"),$(e,"viewBox","0 0 16 16"),$(e,"xmlns","http://www.w3.org/2000/svg")},m(n,r){w(n,e,r),x(e,t)},p:H,i:H,o:H,d(n){n&&y(e)}}}class Nl extends fe{constructor(e){super(),ge(this,e,null,Al,he,{})}}const Zl=s=>({}),Wr=s=>({}),Ol=s=>({}),Yr=s=>({});function El(s){let e;const t=s[8]["header-left"],n=Ie(t,s,s[10],Yr);return{c(){n&&n.c()},m(r,a){n&&n.m(r,a),e=!0},p(r,a){n&&n.p&&(!e||1024&a)&&Pe(n,t,r,r[10],e?Re(t,r[10],a,Ol):je(r[10]),Yr)},i(r){e||(p(n,r),e=!0)},o(r){m(n,r),e=!1},d(r){n&&n.d(r)}}}function Il(s){let e,t,n,r=s[0]&&Xr(s),a=s[1]&&Qr(s);return{c(){r&&r.c(),e=E(),a&&a.c(),t=Te()},m(i,o){r&&r.m(i,o),w(i,e,o),a&&a.m(i,o),w(i,t,o),n=!0},p(i,o){i[0]?r?(r.p(i,o),1&o&&p(r,1)):(r=Xr(i),r.c(),p(r,1),r.m(e.parentNode,e)):r&&(K(),m(r,1,1,()=>{r=null}),W()),i[1]?a?(a.p(i,o),2&o&&p(a,1)):(a=Qr(i),a.c(),p(a,1),a.m(t.parentNode,t)):a&&(K(),m(a,1,1,()=>{a=null}),W())},i(i){n||(p(r),p(a),n=!0)},o(i){m(r),m(a),n=!1},d(i){i&&(y(e),y(t)),r&&r.d(i),a&&a.d(i)}}}function Xr(s){let e,t,n;var r=s[0];return r&&(t=kt(r,{})),{c(){e=M("div"),t&&b(t.$$.fragment),$(e,"class","icon-wrapper svelte-13uht7n")},m(a,i){w(a,e,i),t&&S(t,e,null),n=!0},p(a,i){if(1&i&&r!==(r=a[0])){if(t){K();const o=t;m(o.$$.fragment,1,0,()=>{k(o,1)}),W()}r?(t=kt(r,{}),b(t.$$.fragment),p(t.$$.fragment,1),S(t,e,null)):t=null}},i(a){n||(t&&p(t.$$.fragment,a),n=!0)},o(a){t&&m(t.$$.fragment,a),n=!1},d(a){a&&y(e),t&&k(t)}}}function Qr(s){let e,t;return e=new de({props:{color:"neutral",size:1,weight:"light",class:"card-title",$$slots:{default:[Pl]},$$scope:{ctx:s}}}),{c(){b(e.$$.fragment)},m(n,r){S(e,n,r),t=!0},p(n,r){const a={};1026&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Pl(s){let e;return{c(){e=U(s[1])},m(t,n){w(t,e,n)},p(t,n){2&n&&we(e,t[1])},d(t){t&&y(e)}}}function ea(s){let e,t;const n=s[8].default,r=Ie(n,s,s[10],null);return{c(){e=M("div"),r&&r.c(),$(e,"class","settings-card-body")},m(a,i){w(a,e,i),r&&r.m(e,null),t=!0},p(a,i){r&&r.p&&(!t||1024&i)&&Pe(r,n,a,a[10],t?Re(n,a[10],i,null):je(a[10]),null)},i(a){t||(p(r,a),t=!0)},o(a){m(r,a),t=!1},d(a){a&&y(e),r&&r.d(a)}}}function jl(s){let e,t,n,r,a,i,o,c,l,d,u;const h=[Il,El],g=[];function v(A,T){return A[0]||A[1]?0:1}r=v(s),a=g[r]=h[r](s);const f=s[8]["header-right"],_=Ie(f,s,s[10],Wr);let C=s[5].default&&ea(s),P=[{role:"button"},{class:s[3]},s[4]],I={};for(let A=0;A<P.length;A+=1)I=Se(I,P[A]);return{c(){e=M("div"),t=M("div"),n=M("div"),a.c(),i=E(),o=M("div"),_&&_.c(),c=E(),C&&C.c(),$(n,"class","settings-card-left svelte-13uht7n"),$(o,"class","settings-card-right svelte-13uht7n"),$(t,"class","settings-card-content svelte-13uht7n"),es(e,I),$e(e,"clickable",s[2]),$e(e,"svelte-13uht7n",!0)},m(A,T){w(A,e,T),x(e,t),x(t,n),g[r].m(n,null),x(t,i),x(t,o),_&&_.m(o,null),x(e,c),C&&C.m(e,null),l=!0,d||(u=ze(e,"click",s[9]),d=!0)},p(A,[T]){let N=r;r=v(A),r===N?g[r].p(A,T):(K(),m(g[N],1,1,()=>{g[N]=null}),W(),a=g[r],a?a.p(A,T):(a=g[r]=h[r](A),a.c()),p(a,1),a.m(n,null)),_&&_.p&&(!l||1024&T)&&Pe(_,f,A,A[10],l?Re(f,A[10],T,Zl):je(A[10]),Wr),A[5].default?C?(C.p(A,T),32&T&&p(C,1)):(C=ea(A),C.c(),p(C,1),C.m(e,null)):C&&(K(),m(C,1,1,()=>{C=null}),W()),es(e,I=yt(P,[{role:"button"},(!l||8&T)&&{class:A[3]},16&T&&A[4]])),$e(e,"clickable",A[2]),$e(e,"svelte-13uht7n",!0)},i(A){l||(p(a),p(_,A),p(C),l=!0)},o(A){m(a),m(_,A),m(C),l=!1},d(A){A&&y(e),g[r].d(),_&&_.d(A),C&&C.d(),d=!1,u()}}}function Rl(s,e,t){let n,r,a;const i=["class","icon","title","isClickable"];let o=ts(e,i),{$$slots:c={},$$scope:l}=e;const d=Za(c);let{class:u=""}=e,{icon:h}=e,{title:g}=e,{isClickable:v=!1}=e;return s.$$set=f=>{e=Se(Se({},e),He(f)),t(11,o=ts(e,i)),"class"in f&&t(6,u=f.class),"icon"in f&&t(0,h=f.icon),"title"in f&&t(1,g=f.title),"isClickable"in f&&t(2,v=f.isClickable),"$$scope"in f&&t(10,l=f.$$scope)},s.$$.update=()=>{t(7,{class:n,...r}=o,n,(t(4,r),t(11,o))),192&s.$$.dirty&&t(3,a=`settings-card ${u} ${n||""}`)},[h,g,v,a,r,d,u,n,c,function(f){Oa.call(this,s,f)},l]}class ii extends fe{constructor(e){super(),ge(this,e,Rl,jl,he,{class:6,icon:0,title:1,isClickable:2})}}function Ll(s){let e,t,n=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 17 16"},s[0]],r={};for(let a=0;a<n.length;a+=1)r=Se(r,n[a]);return{c(){e=Fe("svg"),t=new zn(!0),this.h()},l(a){e=Dn(a,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var i=Un(e);t=Vn(i,!0),i.forEach(y),this.h()},h(){t.a=null,lt(e,r)},m(a,i){qn(a,e,i),t.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M3.552 7.158a.568.568 0 0 1 .804 0l.702.702L6.23 6.688c.2-.2.511-.22.734-.06l.07.06a.568.568 0 0 1 0 .804L5.862 8.664l1.626 1.626 1.173-1.172c.2-.2.511-.22.733-.06l.071.06a.568.568 0 0 1 0 .804l-1.173 1.172.703.703c.2.2.22.51.06.733l-.06.07a.568.568 0 0 1-.804 0l-.041-.039-.812.813a3.226 3.226 0 0 1-4.043.421l-.08-.054-.959.96c-.2.2-.511.22-.733.06l-.071-.06a.568.568 0 0 1 0-.804l.96-.96-.054-.079a3.226 3.226 0 0 1 .294-3.91l.127-.133.811-.813-.038-.04a.567.567 0 0 1-.06-.734zm3.759-3.759a.568.568 0 0 1 .804 0l.038.04.815-.813a3.226 3.226 0 0 1 4.043-.421l.078.054.96-.96c.2-.2.511-.22.734-.06l.07.06a.568.568 0 0 1 0 .804l-.96.96.055.079a3.226 3.226 0 0 1-.295 3.91l-.126.133-.814.813.04.04c.201.2.221.511.06.734l-.06.07a.568.568 0 0 1-.804 0L7.31 4.204a.568.568 0 0 1 0-.805m2.39-.04-.884.884 3.093 3.093.884-.884A2.186 2.186 0 1 0 9.7 3.359M4.396 8.664l-.884.884a2.186 2.186 0 1 0 3.092 3.093l.884-.884z"/>',e)},p(a,[i]){lt(e,r=yt(n,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 17 16"},1&i&&a[0]]))},i:H,o:H,d(a){a&&y(e)}}}function Fl(s,e,t){return s.$$set=n=>{t(0,e=Se(Se({},e),He(n)))},[e=He(e)]}class zl extends fe{constructor(e){super(),ge(this,e,Fl,Ll,he,{})}}function Dl(s){let e,t,n,r,a,i,o;function c(d){s[11](d)}let l={onOpenChange:s[10],$$slots:{default:[Kl]},$$scope:{ctx:s}};return s[3]!==void 0&&(l.requestClose=s[3]),n=new Ce.Root({props:l}),Ke.push(()=>We(n,"requestClose",c)),i=new ki.Root({props:{color:"success",size:1,variant:"soft",$$slots:{default:[Wl]},$$scope:{ctx:s}}}),{c(){e=M("div"),t=M("div"),b(n.$$.fragment),a=E(),b(i.$$.fragment),$(t,"class","icon-container svelte-js5lik"),$(e,"class","status-controls svelte-js5lik")},m(d,u){w(d,e,u),x(e,t),S(n,t,null),x(t,a),S(i,t,null),o=!0},p(d,u){const h={};16&u&&(h.onOpenChange=d[10]),16411&u&&(h.$$scope={dirty:u,ctx:d}),!r&&8&u&&(r=!0,h.requestClose=d[3],Ye(()=>r=!1)),n.$set(h);const g={};16384&u&&(g.$$scope={dirty:u,ctx:d}),i.$set(g)},i(d){o||(p(n.$$.fragment,d),p(i.$$.fragment,d),o=!0)},o(d){m(n.$$.fragment,d),m(i.$$.fragment,d),o=!1},d(d){d&&y(e),k(n),k(i)}}}function Ul(s){let e,t;return e=new Xe({props:{variant:"ghost-block",color:s[2]?"neutral":"accent",size:1,$$slots:{default:[Ql]},$$scope:{ctx:s}}}),e.$on("click",s[5]),{c(){b(e.$$.fragment)},m(n,r){S(e,n,r),t=!0},p(n,r){const a={};4&r&&(a.color=n[2]?"neutral":"accent"),16388&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Vl(s){let e,t;return e=new zl({}),{c(){b(e.$$.fragment)},m(n,r){S(e,n,r),t=!0},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function ql(s){let e,t;return e=new ws({props:{color:"neutral",variant:"ghost",size:1,$$slots:{default:[Vl]},$$scope:{ctx:s}}}),e.$on("click",s[8]),{c(){b(e.$$.fragment)},m(n,r){S(e,n,r),t=!0},p(n,r){const a={};16384&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Bl(s){let e,t,n,r;return n=new ln({props:{triggerOn:[to.Hover],content:"Revoke Access",$$slots:{default:[ql]},$$scope:{ctx:s}}}),{c(){e=M("div"),t=M("div"),b(n.$$.fragment),$(t,"class","icon-button-wrapper svelte-js5lik"),$e(t,"active",s[4]),$(e,"class","connection-status svelte-js5lik")},m(a,i){w(a,e,i),x(e,t),S(n,t,null),r=!0},p(a,i){const o={};16400&i&&(o.$$scope={dirty:i,ctx:a}),n.$set(o),(!r||16&i)&&$e(t,"active",a[4])},i(a){r||(p(n.$$.fragment,a),r=!0)},o(a){m(n.$$.fragment,a),r=!1},d(a){a&&y(e),k(n)}}}function Jl(s){let e;return{c(){e=U("Revoke Access")},m(t,n){w(t,e,n)},d(t){t&&y(e)}}}function Gl(s){let e,t;return e=new de({props:{size:1,weight:"medium",$$slots:{default:[Jl]},$$scope:{ctx:s}}}),{c(){b(e.$$.fragment)},m(n,r){S(e,n,r),t=!0},p(n,r){const a={};16384&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Hl(s){let e,t;return e=new Ce.Item({props:{onSelect:s[9],$$slots:{default:[Gl]},$$scope:{ctx:s}}}),{c(){b(e.$$.fragment)},m(n,r){S(e,n,r),t=!0},p(n,r){const a={};27&r&&(a.onSelect=n[9]),16384&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Kl(s){let e,t,n,r;return e=new Ce.Trigger({props:{$$slots:{default:[Bl]},$$scope:{ctx:s}}}),n=new Ce.Content({props:{size:1,side:"bottom",align:"end",$$slots:{default:[Hl]},$$scope:{ctx:s}}}),{c(){b(e.$$.fragment),t=E(),b(n.$$.fragment)},m(a,i){S(e,a,i),w(a,t,i),S(n,a,i),r=!0},p(a,i){const o={};16400&i&&(o.$$scope={dirty:i,ctx:a}),e.$set(o);const c={};16411&i&&(c.$$scope={dirty:i,ctx:a}),n.$set(c)},i(a){r||(p(e.$$.fragment,a),p(n.$$.fragment,a),r=!0)},o(a){m(e.$$.fragment,a),m(n.$$.fragment,a),r=!1},d(a){a&&y(t),k(e,a),k(n,a)}}}function Wl(s){let e;return{c(){e=U("Connected")},m(t,n){w(t,e,n)},d(t){t&&y(e)}}}function Yl(s){let e;return{c(){e=M("span"),e.textContent="Connect"},m(t,n){w(t,e,n)},i:H,o:H,d(t){t&&y(e)}}}function Xl(s){let e,t,n,r,a;return t=new Ea({props:{size:1,useCurrentColor:!0}}),{c(){e=M("div"),b(t.$$.fragment),n=E(),r=M("span"),r.textContent="Cancel",$(e,"class","connect-button-spinner svelte-js5lik")},m(i,o){w(i,e,o),S(t,e,null),w(i,n,o),w(i,r,o),a=!0},i(i){a||(p(t.$$.fragment,i),a=!0)},o(i){m(t.$$.fragment,i),a=!1},d(i){i&&(y(e),y(n),y(r)),k(t)}}}function Ql(s){let e,t,n,r;const a=[Xl,Yl],i=[];function o(c,l){return c[2]?0:1}return t=o(s),n=i[t]=a[t](s),{c(){e=M("div"),n.c(),$(e,"class","connect-button-content svelte-js5lik")},m(c,l){w(c,e,l),i[t].m(e,null),r=!0},p(c,l){let d=t;t=o(c),t!==d&&(K(),m(i[d],1,1,()=>{i[d]=null}),W(),n=i[t],n||(n=i[t]=a[t](c),n.c()),p(n,1),n.m(e,null))},i(c){r||(p(n),r=!0)},o(c){m(n),r=!1},d(c){c&&y(e),i[t].d()}}}function ed(s){let e,t,n,r;const a=[Ul,Dl],i=[];function o(c,l){return!c[0].isConfigured&&c[0].authUrl?0:c[0].isConfigured?1:-1}return~(t=o(s))&&(n=i[t]=a[t](s)),{c(){e=M("div"),n&&n.c(),$(e,"slot","header-right")},m(c,l){w(c,e,l),~t&&i[t].m(e,null),r=!0},p(c,l){let d=t;t=o(c),t===d?~t&&i[t].p(c,l):(n&&(K(),m(i[d],1,1,()=>{i[d]=null}),W()),~t?(n=i[t],n?n.p(c,l):(n=i[t]=a[t](c),n.c()),p(n,1),n.m(e,null)):n=null)},i(c){r||(p(n),r=!0)},o(c){m(n),r=!1},d(c){c&&y(e),~t&&i[t].d()}}}function ta(s){let e,t,n,r=s[0].statusMessage+"";return{c(){e=M("div"),t=U(r),$(e,"class",n="status-message "+s[0].statusType+" svelte-js5lik")},m(a,i){w(a,e,i),x(e,t)},p(a,i){1&i&&r!==(r=a[0].statusMessage+"")&&we(t,r),1&i&&n!==(n="status-message "+a[0].statusType+" svelte-js5lik")&&$(e,"class",n)},d(a){a&&y(e)}}}function td(s){let e,t,n,r,a,i;t=new ii({props:{icon:s[0].icon,title:s[0].displayName,$$slots:{"header-right":[ed]},$$scope:{ctx:s}}});let o=s[0].showStatus&&ta(s);return{c(){e=M("div"),b(t.$$.fragment),n=E(),o&&o.c(),$(e,"class","config-wrapper"),$(e,"role","group"),$(e,"aria-label","Connection status controls")},m(c,l){w(c,e,l),S(t,e,null),x(e,n),o&&o.m(e,null),r=!0,a||(i=[ze(e,"mouseenter",s[12]),ze(e,"mouseleave",s[13])],a=!0)},p(c,[l]){const d={};1&l&&(d.icon=c[0].icon),1&l&&(d.title=c[0].displayName),16415&l&&(d.$$scope={dirty:l,ctx:c}),t.$set(d),c[0].showStatus?o?o.p(c,l):(o=ta(c),o.c(),o.m(e,null)):o&&(o.d(1),o=null)},i(c){r||(p(t.$$.fragment,c),r=!0)},o(c){m(t.$$.fragment,c),r=!1},d(c){c&&y(e),k(t),o&&o.d(),a=!1,qs(i)}}}function nd(s,e,t){let{config:n}=e,{onAuthenticate:r}=e,{onRevokeAccess:a}=e,i=()=>{t(4,l=!1)},o=!1,c=null,l=!1;return s.$$set=d=>{"config"in d&&t(0,n=d.config),"onAuthenticate"in d&&t(6,r=d.onAuthenticate),"onRevokeAccess"in d&&t(1,a=d.onRevokeAccess)},s.$$.update=()=>{133&s.$$.dirty&&n.isConfigured&&o&&(t(2,o=!1),c&&(clearTimeout(c),t(7,c=null)))},[n,a,o,i,l,function(){if(o)t(2,o=!1),c&&(clearTimeout(c),t(7,c=null));else{t(2,o=!0);const d=n.authUrl||"";r(d),t(7,c=setTimeout(()=>{t(2,o=!1),t(7,c=null)},6e4))}},r,c,()=>t(4,l=!l),()=>{a(n),t(4,l=!1),i()},d=>{d||t(4,l=!1)},function(d){i=d,t(3,i)},()=>t(4,l=!0),()=>t(4,l=!1)]}class sd extends fe{constructor(e){super(),ge(this,e,nd,td,he,{config:0,onAuthenticate:6,onRevokeAccess:1})}}function rd(s){let e;return{c(){e=U(s[0])},m(t,n){w(t,e,n)},p(t,n){1&n&&we(e,t[0])},d(t){t&&y(e)}}}function ad(s){let e,t;const n=s[2].default,r=Ie(n,s,s[3],null);return{c(){e=M("div"),r&&r.c(),$(e,"class","category-content")},m(a,i){w(a,e,i),r&&r.m(e,null),t=!0},p(a,i){r&&r.p&&(!t||8&i)&&Pe(r,n,a,a[3],t?Re(n,a[3],i,null):je(a[3]),null)},i(a){t||(p(r,a),t=!0)},o(a){m(r,a),t=!1},d(a){a&&y(e),r&&r.d(a)}}}function id(s){let e,t,n,r,a;return t=new Ea({props:{size:1}}),r=new de({props:{size:1,color:"secondary",$$slots:{default:[od]},$$scope:{ctx:s}}}),{c(){e=M("div"),b(t.$$.fragment),n=E(),b(r.$$.fragment),$(e,"class","loading-container svelte-2bsejd")},m(i,o){w(i,e,o),S(t,e,null),x(e,n),S(r,e,null),a=!0},p(i,o){const c={};8&o&&(c.$$scope={dirty:o,ctx:i}),r.$set(c)},i(i){a||(p(t.$$.fragment,i),p(r.$$.fragment,i),a=!0)},o(i){m(t.$$.fragment,i),m(r.$$.fragment,i),a=!1},d(i){i&&y(e),k(t),k(r)}}}function od(s){let e;return{c(){e=U("Loading...")},m(t,n){w(t,e,n)},d(t){t&&y(e)}}}function cd(s){let e,t,n,r,a,i,o;n=new de({props:{size:1,color:"secondary",weight:"regular",$$slots:{default:[rd]},$$scope:{ctx:s}}});const c=[id,ad],l=[];function d(u,h){return u[1]?0:1}return a=d(s),i=l[a]=c[a](s),{c(){e=M("div"),t=M("div"),b(n.$$.fragment),r=E(),i.c(),$(t,"class","category-heading"),$(e,"class","category")},m(u,h){w(u,e,h),x(e,t),S(n,t,null),x(e,r),l[a].m(e,null),o=!0},p(u,[h]){const g={};9&h&&(g.$$scope={dirty:h,ctx:u}),n.$set(g);let v=a;a=d(u),a===v?l[a].p(u,h):(K(),m(l[v],1,1,()=>{l[v]=null}),W(),i=l[a],i?i.p(u,h):(i=l[a]=c[a](u),i.c()),p(i,1),i.m(e,null))},i(u){o||(p(n.$$.fragment,u),p(i),o=!0)},o(u){m(n.$$.fragment,u),m(i),o=!1},d(u){u&&y(e),k(n),l[a].d()}}}function ld(s,e,t){let{$$slots:n={},$$scope:r}=e,{title:a}=e,{loading:i=!1}=e;return s.$$set=o=>{"title"in o&&t(0,a=o.title),"loading"in o&&t(1,i=o.loading),"$$scope"in o&&t(3,r=o.$$scope)},[a,i,n,r]}class dd extends fe{constructor(e){super(),ge(this,e,ld,cd,he,{title:0,loading:1})}}function na(s,e,t){const n=s.slice();return n[4]=e[t],n}function sa(s){let e,t;return e=new sd({props:{config:s[4],onAuthenticate:s[2],onRevokeAccess:s[3]}}),{c(){b(e.$$.fragment)},m(n,r){S(e,n,r),t=!0},p(n,r){const a={};2&r&&(a.config=n[4]),4&r&&(a.onAuthenticate=n[2]),8&r&&(a.onRevokeAccess=n[3]),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function ud(s){let e,t,n=xe(s[1]),r=[];for(let i=0;i<n.length;i+=1)r[i]=sa(na(s,n,i));const a=i=>m(r[i],1,1,()=>{r[i]=null});return{c(){e=M("div");for(let i=0;i<r.length;i+=1)r[i].c();$(e,"class","tool-category-list svelte-on3wl5")},m(i,o){w(i,e,o);for(let c=0;c<r.length;c+=1)r[c]&&r[c].m(e,null);t=!0},p(i,o){if(14&o){let c;for(n=xe(i[1]),c=0;c<n.length;c+=1){const l=na(i,n,c);r[c]?(r[c].p(l,o),p(r[c],1)):(r[c]=sa(l),r[c].c(),p(r[c],1),r[c].m(e,null))}for(K(),c=n.length;c<r.length;c+=1)a(c);W()}},i(i){if(!t){for(let o=0;o<n.length;o+=1)p(r[o]);t=!0}},o(i){r=r.filter(Boolean);for(let o=0;o<r.length;o+=1)m(r[o]);t=!1},d(i){i&&y(e),Fn(r,i)}}}function pd(s){let e,t;return e=new dd({props:{title:s[0],loading:s[1].length===0,$$slots:{default:[ud]},$$scope:{ctx:s}}}),{c(){b(e.$$.fragment)},m(n,r){S(e,n,r),t=!0},p(n,[r]){const a={};1&r&&(a.title=n[0]),2&r&&(a.loading=n[1].length===0),142&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function md(s,e,t){let{title:n}=e,{tools:r=[]}=e,{onAuthenticate:a}=e,{onRevokeAccess:i}=e;return s.$$set=o=>{"title"in o&&t(0,n=o.title),"tools"in o&&t(1,r=o.tools),"onAuthenticate"in o&&t(2,a=o.onAuthenticate),"onRevokeAccess"in o&&t(3,i=o.onRevokeAccess)},[n,r,a,i]}class hd extends fe{constructor(e){super(),ge(this,e,md,pd,he,{title:0,tools:1,onAuthenticate:2,onRevokeAccess:3})}}var pe,zs;(function(s){s.assertEqual=e=>e,s.assertIs=function(e){},s.assertNever=function(e){throw new Error},s.arrayToEnum=e=>{const t={};for(const n of e)t[n]=n;return t},s.getValidEnumValues=e=>{const t=s.objectKeys(e).filter(r=>typeof e[e[r]]!="number"),n={};for(const r of t)n[r]=e[r];return s.objectValues(n)},s.objectValues=e=>s.objectKeys(e).map(function(t){return e[t]}),s.objectKeys=typeof Object.keys=="function"?e=>Object.keys(e):e=>{const t=[];for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.push(n);return t},s.find=(e,t)=>{for(const n of e)if(t(n))return n},s.isInteger=typeof Number.isInteger=="function"?e=>Number.isInteger(e):e=>typeof e=="number"&&isFinite(e)&&Math.floor(e)===e,s.joinValues=function(e,t=" | "){return e.map(n=>typeof n=="string"?`'${n}'`:n).join(t)},s.jsonStringifyReplacer=(e,t)=>typeof t=="bigint"?t.toString():t})(pe||(pe={})),function(s){s.mergeShapes=(e,t)=>({...e,...t})}(zs||(zs={}));const D=pe.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),ft=s=>{switch(typeof s){case"undefined":return D.undefined;case"string":return D.string;case"number":return isNaN(s)?D.nan:D.number;case"boolean":return D.boolean;case"function":return D.function;case"bigint":return D.bigint;case"symbol":return D.symbol;case"object":return Array.isArray(s)?D.array:s===null?D.null:s.then&&typeof s.then=="function"&&s.catch&&typeof s.catch=="function"?D.promise:typeof Map<"u"&&s instanceof Map?D.map:typeof Set<"u"&&s instanceof Set?D.set:typeof Date<"u"&&s instanceof Date?D.date:D.object;default:return D.unknown}},O=pe.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class Ue extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=n=>{this.issues=[...this.issues,n]},this.addIssues=(n=[])=>{this.issues=[...this.issues,...n]};const t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){const t=e||function(a){return a.message},n={_errors:[]},r=a=>{for(const i of a.issues)if(i.code==="invalid_union")i.unionErrors.map(r);else if(i.code==="invalid_return_type")r(i.returnTypeError);else if(i.code==="invalid_arguments")r(i.argumentsError);else if(i.path.length===0)n._errors.push(t(i));else{let o=n,c=0;for(;c<i.path.length;){const l=i.path[c];c===i.path.length-1?(o[l]=o[l]||{_errors:[]},o[l]._errors.push(t(i))):o[l]=o[l]||{_errors:[]},o=o[l],c++}}};return r(this),n}static assert(e){if(!(e instanceof Ue))throw new Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,pe.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(e=t=>t.message){const t={},n=[];for(const r of this.issues)r.path.length>0?(t[r.path[0]]=t[r.path[0]]||[],t[r.path[0]].push(e(r))):n.push(e(r));return{formErrors:n,fieldErrors:t}}get formErrors(){return this.flatten()}}Ue.create=s=>new Ue(s);const Xt=(s,e)=>{let t;switch(s.code){case O.invalid_type:t=s.received===D.undefined?"Required":`Expected ${s.expected}, received ${s.received}`;break;case O.invalid_literal:t=`Invalid literal value, expected ${JSON.stringify(s.expected,pe.jsonStringifyReplacer)}`;break;case O.unrecognized_keys:t=`Unrecognized key(s) in object: ${pe.joinValues(s.keys,", ")}`;break;case O.invalid_union:t="Invalid input";break;case O.invalid_union_discriminator:t=`Invalid discriminator value. Expected ${pe.joinValues(s.options)}`;break;case O.invalid_enum_value:t=`Invalid enum value. Expected ${pe.joinValues(s.options)}, received '${s.received}'`;break;case O.invalid_arguments:t="Invalid function arguments";break;case O.invalid_return_type:t="Invalid function return type";break;case O.invalid_date:t="Invalid date";break;case O.invalid_string:typeof s.validation=="object"?"includes"in s.validation?(t=`Invalid input: must include "${s.validation.includes}"`,typeof s.validation.position=="number"&&(t=`${t} at one or more positions greater than or equal to ${s.validation.position}`)):"startsWith"in s.validation?t=`Invalid input: must start with "${s.validation.startsWith}"`:"endsWith"in s.validation?t=`Invalid input: must end with "${s.validation.endsWith}"`:pe.assertNever(s.validation):t=s.validation!=="regex"?`Invalid ${s.validation}`:"Invalid";break;case O.too_small:t=s.type==="array"?`Array must contain ${s.exact?"exactly":s.inclusive?"at least":"more than"} ${s.minimum} element(s)`:s.type==="string"?`String must contain ${s.exact?"exactly":s.inclusive?"at least":"over"} ${s.minimum} character(s)`:s.type==="number"?`Number must be ${s.exact?"exactly equal to ":s.inclusive?"greater than or equal to ":"greater than "}${s.minimum}`:s.type==="date"?`Date must be ${s.exact?"exactly equal to ":s.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(s.minimum))}`:"Invalid input";break;case O.too_big:t=s.type==="array"?`Array must contain ${s.exact?"exactly":s.inclusive?"at most":"less than"} ${s.maximum} element(s)`:s.type==="string"?`String must contain ${s.exact?"exactly":s.inclusive?"at most":"under"} ${s.maximum} character(s)`:s.type==="number"?`Number must be ${s.exact?"exactly":s.inclusive?"less than or equal to":"less than"} ${s.maximum}`:s.type==="bigint"?`BigInt must be ${s.exact?"exactly":s.inclusive?"less than or equal to":"less than"} ${s.maximum}`:s.type==="date"?`Date must be ${s.exact?"exactly":s.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(s.maximum))}`:"Invalid input";break;case O.custom:t="Invalid input";break;case O.invalid_intersection_types:t="Intersection results could not be merged";break;case O.not_multiple_of:t=`Number must be a multiple of ${s.multipleOf}`;break;case O.not_finite:t="Number must be finite";break;default:t=e.defaultError,pe.assertNever(s)}return{message:t}};let oi=Xt;function ps(){return oi}const ms=s=>{const{data:e,path:t,errorMaps:n,issueData:r}=s,a=[...t,...r.path||[]],i={...r,path:a};if(r.message!==void 0)return{...r,path:a,message:r.message};let o="";const c=n.filter(l=>!!l).slice().reverse();for(const l of c)o=l(i,{data:e,defaultError:o}).message;return{...r,path:a,message:o}};function L(s,e){const t=ps(),n=ms({issueData:e,data:s.data,path:s.path,errorMaps:[s.common.contextualErrorMap,s.schemaErrorMap,t,t===Xt?void 0:Xt].filter(r=>!!r)});s.common.issues.push(n)}class Ze{constructor(){this.value="valid"}dirty(){this.value==="valid"&&(this.value="dirty")}abort(){this.value!=="aborted"&&(this.value="aborted")}static mergeArray(e,t){const n=[];for(const r of t){if(r.status==="aborted")return te;r.status==="dirty"&&e.dirty(),n.push(r.value)}return{status:e.value,value:n}}static async mergeObjectAsync(e,t){const n=[];for(const r of t){const a=await r.key,i=await r.value;n.push({key:a,value:i})}return Ze.mergeObjectSync(e,n)}static mergeObjectSync(e,t){const n={};for(const r of t){const{key:a,value:i}=r;if(a.status==="aborted"||i.status==="aborted")return te;a.status==="dirty"&&e.dirty(),i.status==="dirty"&&e.dirty(),a.value==="__proto__"||i.value===void 0&&!r.alwaysSet||(n[a.value]=i.value)}return{status:e.value,value:n}}}const te=Object.freeze({status:"aborted"}),hs=s=>({status:"dirty",value:s}),Ee=s=>({status:"valid",value:s}),Ds=s=>s.status==="aborted",Us=s=>s.status==="dirty",Ft=s=>s.status==="valid",Tn=s=>typeof Promise<"u"&&s instanceof Promise;function fs(s,e,t,n){if(typeof e=="function"?s!==e||!n:!e.has(s))throw new TypeError("Cannot read private member from an object whose class did not declare it");return e.get(s)}function ci(s,e,t,n,r){if(typeof e=="function"?s!==e||!r:!e.has(s))throw new TypeError("Cannot write private member to an object whose class did not declare it");return e.set(s,t),t}var q,on,cn;typeof SuppressedError=="function"&&SuppressedError,function(s){s.errToObj=e=>typeof e=="string"?{message:e}:e||{},s.toString=e=>typeof e=="string"?e:e==null?void 0:e.message}(q||(q={}));class ut{constructor(e,t,n,r){this._cachedPath=[],this.parent=e,this.data=t,this._path=n,this._key=r}get path(){return this._cachedPath.length||(this._key instanceof Array?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const ra=(s,e)=>{if(Ft(e))return{success:!0,data:e.value};if(!s.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const t=new Ue(s.common.issues);return this._error=t,this._error}}};function se(s){if(!s)return{};const{errorMap:e,invalid_type_error:t,required_error:n,description:r}=s;if(e&&(t||n))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return e?{errorMap:e,description:r}:{errorMap:(a,i)=>{var o,c;const{message:l}=s;return a.code==="invalid_enum_value"?{message:l??i.defaultError}:i.data===void 0?{message:(o=l??n)!==null&&o!==void 0?o:i.defaultError}:a.code!=="invalid_type"?{message:i.defaultError}:{message:(c=l??t)!==null&&c!==void 0?c:i.defaultError}},description:r}}class oe{get description(){return this._def.description}_getType(e){return ft(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:ft(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new Ze,ctx:{common:e.parent.common,data:e.data,parsedType:ft(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){const t=this._parse(e);if(Tn(t))throw new Error("Synchronous parse encountered promise.");return t}_parseAsync(e){const t=this._parse(e);return Promise.resolve(t)}parse(e,t){const n=this.safeParse(e,t);if(n.success)return n.data;throw n.error}safeParse(e,t){var n;const r={common:{issues:[],async:(n=t==null?void 0:t.async)!==null&&n!==void 0&&n,contextualErrorMap:t==null?void 0:t.errorMap},path:(t==null?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:ft(e)},a=this._parseSync({data:e,path:r.path,parent:r});return ra(r,a)}"~validate"(e){var t,n;const r={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:ft(e)};if(!this["~standard"].async)try{const a=this._parseSync({data:e,path:[],parent:r});return Ft(a)?{value:a.value}:{issues:r.common.issues}}catch(a){!((n=(t=a==null?void 0:a.message)===null||t===void 0?void 0:t.toLowerCase())===null||n===void 0)&&n.includes("encountered")&&(this["~standard"].async=!0),r.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:r}).then(a=>Ft(a)?{value:a.value}:{issues:r.common.issues})}async parseAsync(e,t){const n=await this.safeParseAsync(e,t);if(n.success)return n.data;throw n.error}async safeParseAsync(e,t){const n={common:{issues:[],contextualErrorMap:t==null?void 0:t.errorMap,async:!0},path:(t==null?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:ft(e)},r=this._parse({data:e,path:n.path,parent:n}),a=await(Tn(r)?r:Promise.resolve(r));return ra(n,a)}refine(e,t){const n=r=>typeof t=="string"||t===void 0?{message:t}:typeof t=="function"?t(r):t;return this._refinement((r,a)=>{const i=e(r),o=()=>a.addIssue({code:O.custom,...n(r)});return typeof Promise<"u"&&i instanceof Promise?i.then(c=>!!c||(o(),!1)):!!i||(o(),!1)})}refinement(e,t){return this._refinement((n,r)=>!!e(n)||(r.addIssue(typeof t=="function"?t(n,r):t),!1))}_refinement(e){return new rt({schema:this,typeName:Q.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:t=>this["~validate"](t)}}optional(){return ct.create(this,this._def)}nullable(){return Zt.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return it.create(this)}promise(){return en.create(this,this._def)}or(e){return Zn.create([this,e],this._def)}and(e){return On.create(this,e,this._def)}transform(e){return new rt({...se(this._def),schema:this,typeName:Q.ZodEffects,effect:{type:"transform",transform:e}})}default(e){const t=typeof e=="function"?e:()=>e;return new jn({...se(this._def),innerType:this,defaultValue:t,typeName:Q.ZodDefault})}brand(){return new Hs({typeName:Q.ZodBranded,type:this,...se(this._def)})}catch(e){const t=typeof e=="function"?e:()=>e;return new Rn({...se(this._def),innerType:this,catchValue:t,typeName:Q.ZodCatch})}describe(e){return new this.constructor({...this._def,description:e})}pipe(e){return Jn.create(this,e)}readonly(){return Ln.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const fd=/^c[^\s-]{8,}$/i,gd=/^[0-9a-z]+$/,vd=/^[0-9A-HJKMNP-TV-Z]{26}$/i,$d=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,yd=/^[a-z0-9_-]{21}$/i,_d=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,wd=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,xd=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i;let As;const bd=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,Sd=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,kd=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,Cd=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,Td=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,Md=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,li="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",Ad=new RegExp(`^${li}$`);function di(s){let e="([01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d";return s.precision?e=`${e}\\.\\d{${s.precision}}`:s.precision==null&&(e=`${e}(\\.\\d+)?`),e}function ui(s){let e=`${li}T${di(s)}`;const t=[];return t.push(s.local?"Z?":"Z"),s.offset&&t.push("([+-]\\d{2}:?\\d{2})"),e=`${e}(${t.join("|")})`,new RegExp(`^${e}$`)}function Nd(s,e){if(!_d.test(s))return!1;try{const[t]=s.split("."),n=t.replace(/-/g,"+").replace(/_/g,"/").padEnd(t.length+(4-t.length%4)%4,"="),r=JSON.parse(atob(n));return typeof r=="object"&&r!==null&&!(!r.typ||!r.alg)&&(!e||r.alg===e)}catch{return!1}}function Zd(s,e){return!(e!=="v4"&&e||!Sd.test(s))||!(e!=="v6"&&e||!Cd.test(s))}class st extends oe{_parse(e){if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==D.string){const i=this._getOrReturnCtx(e);return L(i,{code:O.invalid_type,expected:D.string,received:i.parsedType}),te}const t=new Ze;let n;for(const i of this._def.checks)if(i.kind==="min")e.data.length<i.value&&(n=this._getOrReturnCtx(e,n),L(n,{code:O.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),t.dirty());else if(i.kind==="max")e.data.length>i.value&&(n=this._getOrReturnCtx(e,n),L(n,{code:O.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),t.dirty());else if(i.kind==="length"){const o=e.data.length>i.value,c=e.data.length<i.value;(o||c)&&(n=this._getOrReturnCtx(e,n),o?L(n,{code:O.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}):c&&L(n,{code:O.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}),t.dirty())}else if(i.kind==="email")xd.test(e.data)||(n=this._getOrReturnCtx(e,n),L(n,{validation:"email",code:O.invalid_string,message:i.message}),t.dirty());else if(i.kind==="emoji")As||(As=new RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),As.test(e.data)||(n=this._getOrReturnCtx(e,n),L(n,{validation:"emoji",code:O.invalid_string,message:i.message}),t.dirty());else if(i.kind==="uuid")$d.test(e.data)||(n=this._getOrReturnCtx(e,n),L(n,{validation:"uuid",code:O.invalid_string,message:i.message}),t.dirty());else if(i.kind==="nanoid")yd.test(e.data)||(n=this._getOrReturnCtx(e,n),L(n,{validation:"nanoid",code:O.invalid_string,message:i.message}),t.dirty());else if(i.kind==="cuid")fd.test(e.data)||(n=this._getOrReturnCtx(e,n),L(n,{validation:"cuid",code:O.invalid_string,message:i.message}),t.dirty());else if(i.kind==="cuid2")gd.test(e.data)||(n=this._getOrReturnCtx(e,n),L(n,{validation:"cuid2",code:O.invalid_string,message:i.message}),t.dirty());else if(i.kind==="ulid")vd.test(e.data)||(n=this._getOrReturnCtx(e,n),L(n,{validation:"ulid",code:O.invalid_string,message:i.message}),t.dirty());else if(i.kind==="url")try{new URL(e.data)}catch{n=this._getOrReturnCtx(e,n),L(n,{validation:"url",code:O.invalid_string,message:i.message}),t.dirty()}else i.kind==="regex"?(i.regex.lastIndex=0,i.regex.test(e.data)||(n=this._getOrReturnCtx(e,n),L(n,{validation:"regex",code:O.invalid_string,message:i.message}),t.dirty())):i.kind==="trim"?e.data=e.data.trim():i.kind==="includes"?e.data.includes(i.value,i.position)||(n=this._getOrReturnCtx(e,n),L(n,{code:O.invalid_string,validation:{includes:i.value,position:i.position},message:i.message}),t.dirty()):i.kind==="toLowerCase"?e.data=e.data.toLowerCase():i.kind==="toUpperCase"?e.data=e.data.toUpperCase():i.kind==="startsWith"?e.data.startsWith(i.value)||(n=this._getOrReturnCtx(e,n),L(n,{code:O.invalid_string,validation:{startsWith:i.value},message:i.message}),t.dirty()):i.kind==="endsWith"?e.data.endsWith(i.value)||(n=this._getOrReturnCtx(e,n),L(n,{code:O.invalid_string,validation:{endsWith:i.value},message:i.message}),t.dirty()):i.kind==="datetime"?ui(i).test(e.data)||(n=this._getOrReturnCtx(e,n),L(n,{code:O.invalid_string,validation:"datetime",message:i.message}),t.dirty()):i.kind==="date"?Ad.test(e.data)||(n=this._getOrReturnCtx(e,n),L(n,{code:O.invalid_string,validation:"date",message:i.message}),t.dirty()):i.kind==="time"?new RegExp(`^${di(i)}$`).test(e.data)||(n=this._getOrReturnCtx(e,n),L(n,{code:O.invalid_string,validation:"time",message:i.message}),t.dirty()):i.kind==="duration"?wd.test(e.data)||(n=this._getOrReturnCtx(e,n),L(n,{validation:"duration",code:O.invalid_string,message:i.message}),t.dirty()):i.kind==="ip"?(r=e.data,((a=i.version)!=="v4"&&a||!bd.test(r))&&(a!=="v6"&&a||!kd.test(r))&&(n=this._getOrReturnCtx(e,n),L(n,{validation:"ip",code:O.invalid_string,message:i.message}),t.dirty())):i.kind==="jwt"?Nd(e.data,i.alg)||(n=this._getOrReturnCtx(e,n),L(n,{validation:"jwt",code:O.invalid_string,message:i.message}),t.dirty()):i.kind==="cidr"?Zd(e.data,i.version)||(n=this._getOrReturnCtx(e,n),L(n,{validation:"cidr",code:O.invalid_string,message:i.message}),t.dirty()):i.kind==="base64"?Td.test(e.data)||(n=this._getOrReturnCtx(e,n),L(n,{validation:"base64",code:O.invalid_string,message:i.message}),t.dirty()):i.kind==="base64url"?Md.test(e.data)||(n=this._getOrReturnCtx(e,n),L(n,{validation:"base64url",code:O.invalid_string,message:i.message}),t.dirty()):pe.assertNever(i);var r,a;return{status:t.value,value:e.data}}_regex(e,t,n){return this.refinement(r=>e.test(r),{validation:t,code:O.invalid_string,...q.errToObj(n)})}_addCheck(e){return new st({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...q.errToObj(e)})}url(e){return this._addCheck({kind:"url",...q.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...q.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...q.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...q.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...q.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...q.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...q.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...q.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...q.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...q.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...q.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...q.errToObj(e)})}datetime(e){var t,n;return typeof e=="string"?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:(e==null?void 0:e.precision)===void 0?null:e==null?void 0:e.precision,offset:(t=e==null?void 0:e.offset)!==null&&t!==void 0&&t,local:(n=e==null?void 0:e.local)!==null&&n!==void 0&&n,...q.errToObj(e==null?void 0:e.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return typeof e=="string"?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:(e==null?void 0:e.precision)===void 0?null:e==null?void 0:e.precision,...q.errToObj(e==null?void 0:e.message)})}duration(e){return this._addCheck({kind:"duration",...q.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...q.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:t==null?void 0:t.position,...q.errToObj(t==null?void 0:t.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...q.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...q.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...q.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...q.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...q.errToObj(t)})}nonempty(e){return this.min(1,q.errToObj(e))}trim(){return new st({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new st({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new st({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>e.kind==="datetime")}get isDate(){return!!this._def.checks.find(e=>e.kind==="date")}get isTime(){return!!this._def.checks.find(e=>e.kind==="time")}get isDuration(){return!!this._def.checks.find(e=>e.kind==="duration")}get isEmail(){return!!this._def.checks.find(e=>e.kind==="email")}get isURL(){return!!this._def.checks.find(e=>e.kind==="url")}get isEmoji(){return!!this._def.checks.find(e=>e.kind==="emoji")}get isUUID(){return!!this._def.checks.find(e=>e.kind==="uuid")}get isNANOID(){return!!this._def.checks.find(e=>e.kind==="nanoid")}get isCUID(){return!!this._def.checks.find(e=>e.kind==="cuid")}get isCUID2(){return!!this._def.checks.find(e=>e.kind==="cuid2")}get isULID(){return!!this._def.checks.find(e=>e.kind==="ulid")}get isIP(){return!!this._def.checks.find(e=>e.kind==="ip")}get isCIDR(){return!!this._def.checks.find(e=>e.kind==="cidr")}get isBase64(){return!!this._def.checks.find(e=>e.kind==="base64")}get isBase64url(){return!!this._def.checks.find(e=>e.kind==="base64url")}get minLength(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}}function Od(s,e){const t=(s.toString().split(".")[1]||"").length,n=(e.toString().split(".")[1]||"").length,r=t>n?t:n;return parseInt(s.toFixed(r).replace(".",""))%parseInt(e.toFixed(r).replace(".",""))/Math.pow(10,r)}st.create=s=>{var e;return new st({checks:[],typeName:Q.ZodString,coerce:(e=s==null?void 0:s.coerce)!==null&&e!==void 0&&e,...se(s)})};class Mt extends oe{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==D.number){const r=this._getOrReturnCtx(e);return L(r,{code:O.invalid_type,expected:D.number,received:r.parsedType}),te}let t;const n=new Ze;for(const r of this._def.checks)r.kind==="int"?pe.isInteger(e.data)||(t=this._getOrReturnCtx(e,t),L(t,{code:O.invalid_type,expected:"integer",received:"float",message:r.message}),n.dirty()):r.kind==="min"?(r.inclusive?e.data<r.value:e.data<=r.value)&&(t=this._getOrReturnCtx(e,t),L(t,{code:O.too_small,minimum:r.value,type:"number",inclusive:r.inclusive,exact:!1,message:r.message}),n.dirty()):r.kind==="max"?(r.inclusive?e.data>r.value:e.data>=r.value)&&(t=this._getOrReturnCtx(e,t),L(t,{code:O.too_big,maximum:r.value,type:"number",inclusive:r.inclusive,exact:!1,message:r.message}),n.dirty()):r.kind==="multipleOf"?Od(e.data,r.value)!==0&&(t=this._getOrReturnCtx(e,t),L(t,{code:O.not_multiple_of,multipleOf:r.value,message:r.message}),n.dirty()):r.kind==="finite"?Number.isFinite(e.data)||(t=this._getOrReturnCtx(e,t),L(t,{code:O.not_finite,message:r.message}),n.dirty()):pe.assertNever(r);return{status:n.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,q.toString(t))}gt(e,t){return this.setLimit("min",e,!1,q.toString(t))}lte(e,t){return this.setLimit("max",e,!0,q.toString(t))}lt(e,t){return this.setLimit("max",e,!1,q.toString(t))}setLimit(e,t,n,r){return new Mt({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:n,message:q.toString(r)}]})}_addCheck(e){return new Mt({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:q.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:q.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:q.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:q.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:q.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:q.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:q.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:q.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:q.toString(e)})}get minValue(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>e.kind==="int"||e.kind==="multipleOf"&&pe.isInteger(e.value))}get isFinite(){let e=null,t=null;for(const n of this._def.checks){if(n.kind==="finite"||n.kind==="int"||n.kind==="multipleOf")return!0;n.kind==="min"?(t===null||n.value>t)&&(t=n.value):n.kind==="max"&&(e===null||n.value<e)&&(e=n.value)}return Number.isFinite(t)&&Number.isFinite(e)}}Mt.create=s=>new Mt({checks:[],typeName:Q.ZodNumber,coerce:(s==null?void 0:s.coerce)||!1,...se(s)});class At extends oe{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==D.bigint)return this._getInvalidInput(e);let t;const n=new Ze;for(const r of this._def.checks)r.kind==="min"?(r.inclusive?e.data<r.value:e.data<=r.value)&&(t=this._getOrReturnCtx(e,t),L(t,{code:O.too_small,type:"bigint",minimum:r.value,inclusive:r.inclusive,message:r.message}),n.dirty()):r.kind==="max"?(r.inclusive?e.data>r.value:e.data>=r.value)&&(t=this._getOrReturnCtx(e,t),L(t,{code:O.too_big,type:"bigint",maximum:r.value,inclusive:r.inclusive,message:r.message}),n.dirty()):r.kind==="multipleOf"?e.data%r.value!==BigInt(0)&&(t=this._getOrReturnCtx(e,t),L(t,{code:O.not_multiple_of,multipleOf:r.value,message:r.message}),n.dirty()):pe.assertNever(r);return{status:n.value,value:e.data}}_getInvalidInput(e){const t=this._getOrReturnCtx(e);return L(t,{code:O.invalid_type,expected:D.bigint,received:t.parsedType}),te}gte(e,t){return this.setLimit("min",e,!0,q.toString(t))}gt(e,t){return this.setLimit("min",e,!1,q.toString(t))}lte(e,t){return this.setLimit("max",e,!0,q.toString(t))}lt(e,t){return this.setLimit("max",e,!1,q.toString(t))}setLimit(e,t,n,r){return new At({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:n,message:q.toString(r)}]})}_addCheck(e){return new At({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:q.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:q.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:q.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:q.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:q.toString(t)})}get minValue(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}}At.create=s=>{var e;return new At({checks:[],typeName:Q.ZodBigInt,coerce:(e=s==null?void 0:s.coerce)!==null&&e!==void 0&&e,...se(s)})};class Mn extends oe{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==D.boolean){const t=this._getOrReturnCtx(e);return L(t,{code:O.invalid_type,expected:D.boolean,received:t.parsedType}),te}return Ee(e.data)}}Mn.create=s=>new Mn({typeName:Q.ZodBoolean,coerce:(s==null?void 0:s.coerce)||!1,...se(s)});class zt extends oe{_parse(e){if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==D.date){const r=this._getOrReturnCtx(e);return L(r,{code:O.invalid_type,expected:D.date,received:r.parsedType}),te}if(isNaN(e.data.getTime()))return L(this._getOrReturnCtx(e),{code:O.invalid_date}),te;const t=new Ze;let n;for(const r of this._def.checks)r.kind==="min"?e.data.getTime()<r.value&&(n=this._getOrReturnCtx(e,n),L(n,{code:O.too_small,message:r.message,inclusive:!0,exact:!1,minimum:r.value,type:"date"}),t.dirty()):r.kind==="max"?e.data.getTime()>r.value&&(n=this._getOrReturnCtx(e,n),L(n,{code:O.too_big,message:r.message,inclusive:!0,exact:!1,maximum:r.value,type:"date"}),t.dirty()):pe.assertNever(r);return{status:t.value,value:new Date(e.data.getTime())}}_addCheck(e){return new zt({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:q.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:q.toString(t)})}get minDate(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e!=null?new Date(e):null}get maxDate(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e!=null?new Date(e):null}}zt.create=s=>new zt({checks:[],coerce:(s==null?void 0:s.coerce)||!1,typeName:Q.ZodDate,...se(s)});class gs extends oe{_parse(e){if(this._getType(e)!==D.symbol){const t=this._getOrReturnCtx(e);return L(t,{code:O.invalid_type,expected:D.symbol,received:t.parsedType}),te}return Ee(e.data)}}gs.create=s=>new gs({typeName:Q.ZodSymbol,...se(s)});class An extends oe{_parse(e){if(this._getType(e)!==D.undefined){const t=this._getOrReturnCtx(e);return L(t,{code:O.invalid_type,expected:D.undefined,received:t.parsedType}),te}return Ee(e.data)}}An.create=s=>new An({typeName:Q.ZodUndefined,...se(s)});class Nn extends oe{_parse(e){if(this._getType(e)!==D.null){const t=this._getOrReturnCtx(e);return L(t,{code:O.invalid_type,expected:D.null,received:t.parsedType}),te}return Ee(e.data)}}Nn.create=s=>new Nn({typeName:Q.ZodNull,...se(s)});class Qt extends oe{constructor(){super(...arguments),this._any=!0}_parse(e){return Ee(e.data)}}Qt.create=s=>new Qt({typeName:Q.ZodAny,...se(s)});class Pt extends oe{constructor(){super(...arguments),this._unknown=!0}_parse(e){return Ee(e.data)}}Pt.create=s=>new Pt({typeName:Q.ZodUnknown,...se(s)});class $t extends oe{_parse(e){const t=this._getOrReturnCtx(e);return L(t,{code:O.invalid_type,expected:D.never,received:t.parsedType}),te}}$t.create=s=>new $t({typeName:Q.ZodNever,...se(s)});class vs extends oe{_parse(e){if(this._getType(e)!==D.undefined){const t=this._getOrReturnCtx(e);return L(t,{code:O.invalid_type,expected:D.void,received:t.parsedType}),te}return Ee(e.data)}}vs.create=s=>new vs({typeName:Q.ZodVoid,...se(s)});class it extends oe{_parse(e){const{ctx:t,status:n}=this._processInputParams(e),r=this._def;if(t.parsedType!==D.array)return L(t,{code:O.invalid_type,expected:D.array,received:t.parsedType}),te;if(r.exactLength!==null){const i=t.data.length>r.exactLength.value,o=t.data.length<r.exactLength.value;(i||o)&&(L(t,{code:i?O.too_big:O.too_small,minimum:o?r.exactLength.value:void 0,maximum:i?r.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:r.exactLength.message}),n.dirty())}if(r.minLength!==null&&t.data.length<r.minLength.value&&(L(t,{code:O.too_small,minimum:r.minLength.value,type:"array",inclusive:!0,exact:!1,message:r.minLength.message}),n.dirty()),r.maxLength!==null&&t.data.length>r.maxLength.value&&(L(t,{code:O.too_big,maximum:r.maxLength.value,type:"array",inclusive:!0,exact:!1,message:r.maxLength.message}),n.dirty()),t.common.async)return Promise.all([...t.data].map((i,o)=>r.type._parseAsync(new ut(t,i,t.path,o)))).then(i=>Ze.mergeArray(n,i));const a=[...t.data].map((i,o)=>r.type._parseSync(new ut(t,i,t.path,o)));return Ze.mergeArray(n,a)}get element(){return this._def.type}min(e,t){return new it({...this._def,minLength:{value:e,message:q.toString(t)}})}max(e,t){return new it({...this._def,maxLength:{value:e,message:q.toString(t)}})}length(e,t){return new it({...this._def,exactLength:{value:e,message:q.toString(t)}})}nonempty(e){return this.min(1,e)}}function Jt(s){if(s instanceof be){const e={};for(const t in s.shape){const n=s.shape[t];e[t]=ct.create(Jt(n))}return new be({...s._def,shape:()=>e})}return s instanceof it?new it({...s._def,type:Jt(s.element)}):s instanceof ct?ct.create(Jt(s.unwrap())):s instanceof Zt?Zt.create(Jt(s.unwrap())):s instanceof pt?pt.create(s.items.map(e=>Jt(e))):s}it.create=(s,e)=>new it({type:s,minLength:null,maxLength:null,exactLength:null,typeName:Q.ZodArray,...se(e)});class be extends oe{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;const e=this._def.shape(),t=pe.objectKeys(e);return this._cached={shape:e,keys:t}}_parse(e){if(this._getType(e)!==D.object){const c=this._getOrReturnCtx(e);return L(c,{code:O.invalid_type,expected:D.object,received:c.parsedType}),te}const{status:t,ctx:n}=this._processInputParams(e),{shape:r,keys:a}=this._getCached(),i=[];if(!(this._def.catchall instanceof $t&&this._def.unknownKeys==="strip"))for(const c in n.data)a.includes(c)||i.push(c);const o=[];for(const c of a){const l=r[c],d=n.data[c];o.push({key:{status:"valid",value:c},value:l._parse(new ut(n,d,n.path,c)),alwaysSet:c in n.data})}if(this._def.catchall instanceof $t){const c=this._def.unknownKeys;if(c==="passthrough")for(const l of i)o.push({key:{status:"valid",value:l},value:{status:"valid",value:n.data[l]}});else if(c==="strict")i.length>0&&(L(n,{code:O.unrecognized_keys,keys:i}),t.dirty());else if(c!=="strip")throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const c=this._def.catchall;for(const l of i){const d=n.data[l];o.push({key:{status:"valid",value:l},value:c._parse(new ut(n,d,n.path,l)),alwaysSet:l in n.data})}}return n.common.async?Promise.resolve().then(async()=>{const c=[];for(const l of o){const d=await l.key,u=await l.value;c.push({key:d,value:u,alwaysSet:l.alwaysSet})}return c}).then(c=>Ze.mergeObjectSync(t,c)):Ze.mergeObjectSync(t,o)}get shape(){return this._def.shape()}strict(e){return q.errToObj,new be({...this._def,unknownKeys:"strict",...e!==void 0?{errorMap:(t,n)=>{var r,a,i,o;const c=(i=(a=(r=this._def).errorMap)===null||a===void 0?void 0:a.call(r,t,n).message)!==null&&i!==void 0?i:n.defaultError;return t.code==="unrecognized_keys"?{message:(o=q.errToObj(e).message)!==null&&o!==void 0?o:c}:{message:c}}}:{}})}strip(){return new be({...this._def,unknownKeys:"strip"})}passthrough(){return new be({...this._def,unknownKeys:"passthrough"})}extend(e){return new be({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new be({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:Q.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new be({...this._def,catchall:e})}pick(e){const t={};return pe.objectKeys(e).forEach(n=>{e[n]&&this.shape[n]&&(t[n]=this.shape[n])}),new be({...this._def,shape:()=>t})}omit(e){const t={};return pe.objectKeys(this.shape).forEach(n=>{e[n]||(t[n]=this.shape[n])}),new be({...this._def,shape:()=>t})}deepPartial(){return Jt(this)}partial(e){const t={};return pe.objectKeys(this.shape).forEach(n=>{const r=this.shape[n];e&&!e[n]?t[n]=r:t[n]=r.optional()}),new be({...this._def,shape:()=>t})}required(e){const t={};return pe.objectKeys(this.shape).forEach(n=>{if(e&&!e[n])t[n]=this.shape[n];else{let r=this.shape[n];for(;r instanceof ct;)r=r._def.innerType;t[n]=r}}),new be({...this._def,shape:()=>t})}keyof(){return pi(pe.objectKeys(this.shape))}}be.create=(s,e)=>new be({shape:()=>s,unknownKeys:"strip",catchall:$t.create(),typeName:Q.ZodObject,...se(e)}),be.strictCreate=(s,e)=>new be({shape:()=>s,unknownKeys:"strict",catchall:$t.create(),typeName:Q.ZodObject,...se(e)}),be.lazycreate=(s,e)=>new be({shape:s,unknownKeys:"strip",catchall:$t.create(),typeName:Q.ZodObject,...se(e)});class Zn extends oe{_parse(e){const{ctx:t}=this._processInputParams(e),n=this._def.options;if(t.common.async)return Promise.all(n.map(async r=>{const a={...t,common:{...t.common,issues:[]},parent:null};return{result:await r._parseAsync({data:t.data,path:t.path,parent:a}),ctx:a}})).then(function(r){for(const i of r)if(i.result.status==="valid")return i.result;for(const i of r)if(i.result.status==="dirty")return t.common.issues.push(...i.ctx.common.issues),i.result;const a=r.map(i=>new Ue(i.ctx.common.issues));return L(t,{code:O.invalid_union,unionErrors:a}),te});{let r;const a=[];for(const o of n){const c={...t,common:{...t.common,issues:[]},parent:null},l=o._parseSync({data:t.data,path:t.path,parent:c});if(l.status==="valid")return l;l.status!=="dirty"||r||(r={result:l,ctx:c}),c.common.issues.length&&a.push(c.common.issues)}if(r)return t.common.issues.push(...r.ctx.common.issues),r.result;const i=a.map(o=>new Ue(o));return L(t,{code:O.invalid_union,unionErrors:i}),te}}get options(){return this._def.options}}Zn.create=(s,e)=>new Zn({options:s,typeName:Q.ZodUnion,...se(e)});const xt=s=>s instanceof En?xt(s.schema):s instanceof rt?xt(s.innerType()):s instanceof In?[s.value]:s instanceof Nt?s.options:s instanceof Pn?pe.objectValues(s.enum):s instanceof jn?xt(s._def.innerType):s instanceof An?[void 0]:s instanceof Nn?[null]:s instanceof ct?[void 0,...xt(s.unwrap())]:s instanceof Zt?[null,...xt(s.unwrap())]:s instanceof Hs||s instanceof Ln?xt(s.unwrap()):s instanceof Rn?xt(s._def.innerType):[];class xs extends oe{_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==D.object)return L(t,{code:O.invalid_type,expected:D.object,received:t.parsedType}),te;const n=this.discriminator,r=t.data[n],a=this.optionsMap.get(r);return a?t.common.async?a._parseAsync({data:t.data,path:t.path,parent:t}):a._parseSync({data:t.data,path:t.path,parent:t}):(L(t,{code:O.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[n]}),te)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,t,n){const r=new Map;for(const a of t){const i=xt(a.shape[e]);if(!i.length)throw new Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(const o of i){if(r.has(o))throw new Error(`Discriminator property ${String(e)} has duplicate value ${String(o)}`);r.set(o,a)}}return new xs({typeName:Q.ZodDiscriminatedUnion,discriminator:e,options:t,optionsMap:r,...se(n)})}}function Vs(s,e){const t=ft(s),n=ft(e);if(s===e)return{valid:!0,data:s};if(t===D.object&&n===D.object){const r=pe.objectKeys(e),a=pe.objectKeys(s).filter(o=>r.indexOf(o)!==-1),i={...s,...e};for(const o of a){const c=Vs(s[o],e[o]);if(!c.valid)return{valid:!1};i[o]=c.data}return{valid:!0,data:i}}if(t===D.array&&n===D.array){if(s.length!==e.length)return{valid:!1};const r=[];for(let a=0;a<s.length;a++){const i=Vs(s[a],e[a]);if(!i.valid)return{valid:!1};r.push(i.data)}return{valid:!0,data:r}}return t===D.date&&n===D.date&&+s==+e?{valid:!0,data:s}:{valid:!1}}class On extends oe{_parse(e){const{status:t,ctx:n}=this._processInputParams(e),r=(a,i)=>{if(Ds(a)||Ds(i))return te;const o=Vs(a.value,i.value);return o.valid?((Us(a)||Us(i))&&t.dirty(),{status:t.value,value:o.data}):(L(n,{code:O.invalid_intersection_types}),te)};return n.common.async?Promise.all([this._def.left._parseAsync({data:n.data,path:n.path,parent:n}),this._def.right._parseAsync({data:n.data,path:n.path,parent:n})]).then(([a,i])=>r(a,i)):r(this._def.left._parseSync({data:n.data,path:n.path,parent:n}),this._def.right._parseSync({data:n.data,path:n.path,parent:n}))}}On.create=(s,e,t)=>new On({left:s,right:e,typeName:Q.ZodIntersection,...se(t)});class pt extends oe{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==D.array)return L(n,{code:O.invalid_type,expected:D.array,received:n.parsedType}),te;if(n.data.length<this._def.items.length)return L(n,{code:O.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),te;!this._def.rest&&n.data.length>this._def.items.length&&(L(n,{code:O.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());const r=[...n.data].map((a,i)=>{const o=this._def.items[i]||this._def.rest;return o?o._parse(new ut(n,a,n.path,i)):null}).filter(a=>!!a);return n.common.async?Promise.all(r).then(a=>Ze.mergeArray(t,a)):Ze.mergeArray(t,r)}get items(){return this._def.items}rest(e){return new pt({...this._def,rest:e})}}pt.create=(s,e)=>{if(!Array.isArray(s))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new pt({items:s,typeName:Q.ZodTuple,rest:null,...se(e)})};class bs extends oe{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==D.object)return L(n,{code:O.invalid_type,expected:D.object,received:n.parsedType}),te;const r=[],a=this._def.keyType,i=this._def.valueType;for(const o in n.data)r.push({key:a._parse(new ut(n,o,n.path,o)),value:i._parse(new ut(n,n.data[o],n.path,o)),alwaysSet:o in n.data});return n.common.async?Ze.mergeObjectAsync(t,r):Ze.mergeObjectSync(t,r)}get element(){return this._def.valueType}static create(e,t,n){return new bs(t instanceof oe?{keyType:e,valueType:t,typeName:Q.ZodRecord,...se(n)}:{keyType:st.create(),valueType:e,typeName:Q.ZodRecord,...se(t)})}}class $s extends oe{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==D.map)return L(n,{code:O.invalid_type,expected:D.map,received:n.parsedType}),te;const r=this._def.keyType,a=this._def.valueType,i=[...n.data.entries()].map(([o,c],l)=>({key:r._parse(new ut(n,o,n.path,[l,"key"])),value:a._parse(new ut(n,c,n.path,[l,"value"]))}));if(n.common.async){const o=new Map;return Promise.resolve().then(async()=>{for(const c of i){const l=await c.key,d=await c.value;if(l.status==="aborted"||d.status==="aborted")return te;l.status!=="dirty"&&d.status!=="dirty"||t.dirty(),o.set(l.value,d.value)}return{status:t.value,value:o}})}{const o=new Map;for(const c of i){const l=c.key,d=c.value;if(l.status==="aborted"||d.status==="aborted")return te;l.status!=="dirty"&&d.status!=="dirty"||t.dirty(),o.set(l.value,d.value)}return{status:t.value,value:o}}}}$s.create=(s,e,t)=>new $s({valueType:e,keyType:s,typeName:Q.ZodMap,...se(t)});class Dt extends oe{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==D.set)return L(n,{code:O.invalid_type,expected:D.set,received:n.parsedType}),te;const r=this._def;r.minSize!==null&&n.data.size<r.minSize.value&&(L(n,{code:O.too_small,minimum:r.minSize.value,type:"set",inclusive:!0,exact:!1,message:r.minSize.message}),t.dirty()),r.maxSize!==null&&n.data.size>r.maxSize.value&&(L(n,{code:O.too_big,maximum:r.maxSize.value,type:"set",inclusive:!0,exact:!1,message:r.maxSize.message}),t.dirty());const a=this._def.valueType;function i(c){const l=new Set;for(const d of c){if(d.status==="aborted")return te;d.status==="dirty"&&t.dirty(),l.add(d.value)}return{status:t.value,value:l}}const o=[...n.data.values()].map((c,l)=>a._parse(new ut(n,c,n.path,l)));return n.common.async?Promise.all(o).then(c=>i(c)):i(o)}min(e,t){return new Dt({...this._def,minSize:{value:e,message:q.toString(t)}})}max(e,t){return new Dt({...this._def,maxSize:{value:e,message:q.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}Dt.create=(s,e)=>new Dt({valueType:s,minSize:null,maxSize:null,typeName:Q.ZodSet,...se(e)});class Gt extends oe{constructor(){super(...arguments),this.validate=this.implement}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==D.function)return L(t,{code:O.invalid_type,expected:D.function,received:t.parsedType}),te;function n(o,c){return ms({data:o,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,ps(),Xt].filter(l=>!!l),issueData:{code:O.invalid_arguments,argumentsError:c}})}function r(o,c){return ms({data:o,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,ps(),Xt].filter(l=>!!l),issueData:{code:O.invalid_return_type,returnTypeError:c}})}const a={errorMap:t.common.contextualErrorMap},i=t.data;if(this._def.returns instanceof en){const o=this;return Ee(async function(...c){const l=new Ue([]),d=await o._def.args.parseAsync(c,a).catch(h=>{throw l.addIssue(n(c,h)),l}),u=await Reflect.apply(i,this,d);return await o._def.returns._def.type.parseAsync(u,a).catch(h=>{throw l.addIssue(r(u,h)),l})})}{const o=this;return Ee(function(...c){const l=o._def.args.safeParse(c,a);if(!l.success)throw new Ue([n(c,l.error)]);const d=Reflect.apply(i,this,l.data),u=o._def.returns.safeParse(d,a);if(!u.success)throw new Ue([r(d,u.error)]);return u.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new Gt({...this._def,args:pt.create(e).rest(Pt.create())})}returns(e){return new Gt({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,t,n){return new Gt({args:e||pt.create([]).rest(Pt.create()),returns:t||Pt.create(),typeName:Q.ZodFunction,...se(n)})}}class En extends oe{get schema(){return this._def.getter()}_parse(e){const{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}En.create=(s,e)=>new En({getter:s,typeName:Q.ZodLazy,...se(e)});class In extends oe{_parse(e){if(e.data!==this._def.value){const t=this._getOrReturnCtx(e);return L(t,{received:t.data,code:O.invalid_literal,expected:this._def.value}),te}return{status:"valid",value:e.data}}get value(){return this._def.value}}function pi(s,e){return new Nt({values:s,typeName:Q.ZodEnum,...se(e)})}In.create=(s,e)=>new In({value:s,typeName:Q.ZodLiteral,...se(e)});class Nt extends oe{constructor(){super(...arguments),on.set(this,void 0)}_parse(e){if(typeof e.data!="string"){const t=this._getOrReturnCtx(e),n=this._def.values;return L(t,{expected:pe.joinValues(n),received:t.parsedType,code:O.invalid_type}),te}if(fs(this,on)||ci(this,on,new Set(this._def.values)),!fs(this,on).has(e.data)){const t=this._getOrReturnCtx(e),n=this._def.values;return L(t,{received:t.data,code:O.invalid_enum_value,options:n}),te}return Ee(e.data)}get options(){return this._def.values}get enum(){const e={};for(const t of this._def.values)e[t]=t;return e}get Values(){const e={};for(const t of this._def.values)e[t]=t;return e}get Enum(){const e={};for(const t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return Nt.create(e,{...this._def,...t})}exclude(e,t=this._def){return Nt.create(this.options.filter(n=>!e.includes(n)),{...this._def,...t})}}on=new WeakMap,Nt.create=pi;class Pn extends oe{constructor(){super(...arguments),cn.set(this,void 0)}_parse(e){const t=pe.getValidEnumValues(this._def.values),n=this._getOrReturnCtx(e);if(n.parsedType!==D.string&&n.parsedType!==D.number){const r=pe.objectValues(t);return L(n,{expected:pe.joinValues(r),received:n.parsedType,code:O.invalid_type}),te}if(fs(this,cn)||ci(this,cn,new Set(pe.getValidEnumValues(this._def.values))),!fs(this,cn).has(e.data)){const r=pe.objectValues(t);return L(n,{received:n.data,code:O.invalid_enum_value,options:r}),te}return Ee(e.data)}get enum(){return this._def.values}}cn=new WeakMap,Pn.create=(s,e)=>new Pn({values:s,typeName:Q.ZodNativeEnum,...se(e)});class en extends oe{unwrap(){return this._def.type}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==D.promise&&t.common.async===!1)return L(t,{code:O.invalid_type,expected:D.promise,received:t.parsedType}),te;const n=t.parsedType===D.promise?t.data:Promise.resolve(t.data);return Ee(n.then(r=>this._def.type.parseAsync(r,{path:t.path,errorMap:t.common.contextualErrorMap})))}}en.create=(s,e)=>new en({type:s,typeName:Q.ZodPromise,...se(e)});class rt extends oe{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===Q.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){const{status:t,ctx:n}=this._processInputParams(e),r=this._def.effect||null,a={addIssue:i=>{L(n,i),i.fatal?t.abort():t.dirty()},get path(){return n.path}};if(a.addIssue=a.addIssue.bind(a),r.type==="preprocess"){const i=r.transform(n.data,a);if(n.common.async)return Promise.resolve(i).then(async o=>{if(t.value==="aborted")return te;const c=await this._def.schema._parseAsync({data:o,path:n.path,parent:n});return c.status==="aborted"?te:c.status==="dirty"||t.value==="dirty"?hs(c.value):c});{if(t.value==="aborted")return te;const o=this._def.schema._parseSync({data:i,path:n.path,parent:n});return o.status==="aborted"?te:o.status==="dirty"||t.value==="dirty"?hs(o.value):o}}if(r.type==="refinement"){const i=o=>{const c=r.refinement(o,a);if(n.common.async)return Promise.resolve(c);if(c instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return o};if(n.common.async===!1){const o=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});return o.status==="aborted"?te:(o.status==="dirty"&&t.dirty(),i(o.value),{status:t.value,value:o.value})}return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(o=>o.status==="aborted"?te:(o.status==="dirty"&&t.dirty(),i(o.value).then(()=>({status:t.value,value:o.value}))))}if(r.type==="transform"){if(n.common.async===!1){const i=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});if(!Ft(i))return i;const o=r.transform(i.value,a);if(o instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:o}}return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(i=>Ft(i)?Promise.resolve(r.transform(i.value,a)).then(o=>({status:t.value,value:o})):i)}pe.assertNever(r)}}rt.create=(s,e,t)=>new rt({schema:s,typeName:Q.ZodEffects,effect:e,...se(t)}),rt.createWithPreprocess=(s,e,t)=>new rt({schema:e,effect:{type:"preprocess",transform:s},typeName:Q.ZodEffects,...se(t)});class ct extends oe{_parse(e){return this._getType(e)===D.undefined?Ee(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}ct.create=(s,e)=>new ct({innerType:s,typeName:Q.ZodOptional,...se(e)});class Zt extends oe{_parse(e){return this._getType(e)===D.null?Ee(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}Zt.create=(s,e)=>new Zt({innerType:s,typeName:Q.ZodNullable,...se(e)});class jn extends oe{_parse(e){const{ctx:t}=this._processInputParams(e);let n=t.data;return t.parsedType===D.undefined&&(n=this._def.defaultValue()),this._def.innerType._parse({data:n,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}jn.create=(s,e)=>new jn({innerType:s,typeName:Q.ZodDefault,defaultValue:typeof e.default=="function"?e.default:()=>e.default,...se(e)});class Rn extends oe{_parse(e){const{ctx:t}=this._processInputParams(e),n={...t,common:{...t.common,issues:[]}},r=this._def.innerType._parse({data:n.data,path:n.path,parent:{...n}});return Tn(r)?r.then(a=>({status:"valid",value:a.status==="valid"?a.value:this._def.catchValue({get error(){return new Ue(n.common.issues)},input:n.data})})):{status:"valid",value:r.status==="valid"?r.value:this._def.catchValue({get error(){return new Ue(n.common.issues)},input:n.data})}}removeCatch(){return this._def.innerType}}Rn.create=(s,e)=>new Rn({innerType:s,typeName:Q.ZodCatch,catchValue:typeof e.catch=="function"?e.catch:()=>e.catch,...se(e)});class ys extends oe{_parse(e){if(this._getType(e)!==D.nan){const t=this._getOrReturnCtx(e);return L(t,{code:O.invalid_type,expected:D.nan,received:t.parsedType}),te}return{status:"valid",value:e.data}}}ys.create=s=>new ys({typeName:Q.ZodNaN,...se(s)});const Ed=Symbol("zod_brand");class Hs extends oe{_parse(e){const{ctx:t}=this._processInputParams(e),n=t.data;return this._def.type._parse({data:n,path:t.path,parent:t})}unwrap(){return this._def.type}}class Jn extends oe{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.common.async)return(async()=>{const r=await this._def.in._parseAsync({data:n.data,path:n.path,parent:n});return r.status==="aborted"?te:r.status==="dirty"?(t.dirty(),hs(r.value)):this._def.out._parseAsync({data:r.value,path:n.path,parent:n})})();{const r=this._def.in._parseSync({data:n.data,path:n.path,parent:n});return r.status==="aborted"?te:r.status==="dirty"?(t.dirty(),{status:"dirty",value:r.value}):this._def.out._parseSync({data:r.value,path:n.path,parent:n})}}static create(e,t){return new Jn({in:e,out:t,typeName:Q.ZodPipeline})}}class Ln extends oe{_parse(e){const t=this._def.innerType._parse(e),n=r=>(Ft(r)&&(r.value=Object.freeze(r.value)),r);return Tn(t)?t.then(r=>n(r)):n(t)}unwrap(){return this._def.innerType}}function aa(s,e={},t){return s?Qt.create().superRefine((n,r)=>{var a,i;if(!s(n)){const o=typeof e=="function"?e(n):typeof e=="string"?{message:e}:e,c=(i=(a=o.fatal)!==null&&a!==void 0?a:t)===null||i===void 0||i,l=typeof o=="string"?{message:o}:o;r.addIssue({code:"custom",...l,fatal:c})}}):Qt.create()}Ln.create=(s,e)=>new Ln({innerType:s,typeName:Q.ZodReadonly,...se(e)});const Id={object:be.lazycreate};var Q;(function(s){s.ZodString="ZodString",s.ZodNumber="ZodNumber",s.ZodNaN="ZodNaN",s.ZodBigInt="ZodBigInt",s.ZodBoolean="ZodBoolean",s.ZodDate="ZodDate",s.ZodSymbol="ZodSymbol",s.ZodUndefined="ZodUndefined",s.ZodNull="ZodNull",s.ZodAny="ZodAny",s.ZodUnknown="ZodUnknown",s.ZodNever="ZodNever",s.ZodVoid="ZodVoid",s.ZodArray="ZodArray",s.ZodObject="ZodObject",s.ZodUnion="ZodUnion",s.ZodDiscriminatedUnion="ZodDiscriminatedUnion",s.ZodIntersection="ZodIntersection",s.ZodTuple="ZodTuple",s.ZodRecord="ZodRecord",s.ZodMap="ZodMap",s.ZodSet="ZodSet",s.ZodFunction="ZodFunction",s.ZodLazy="ZodLazy",s.ZodLiteral="ZodLiteral",s.ZodEnum="ZodEnum",s.ZodEffects="ZodEffects",s.ZodNativeEnum="ZodNativeEnum",s.ZodOptional="ZodOptional",s.ZodNullable="ZodNullable",s.ZodDefault="ZodDefault",s.ZodCatch="ZodCatch",s.ZodPromise="ZodPromise",s.ZodBranded="ZodBranded",s.ZodPipeline="ZodPipeline",s.ZodReadonly="ZodReadonly"})(Q||(Q={}));const ia=st.create,oa=Mt.create,Pd=ys.create,jd=At.create,ca=Mn.create,Rd=zt.create,Ld=gs.create,Fd=An.create,zd=Nn.create,Dd=Qt.create,Ud=Pt.create,Vd=$t.create,qd=vs.create,Bd=it.create,Jd=be.create,Gd=be.strictCreate,Hd=Zn.create,Kd=xs.create,Wd=On.create,Yd=pt.create,Xd=bs.create,Qd=$s.create,eu=Dt.create,tu=Gt.create,nu=En.create,su=In.create,ru=Nt.create,au=Pn.create,iu=en.create,la=rt.create,ou=ct.create,cu=Zt.create,lu=rt.createWithPreprocess,du=Jn.create,uu={string:s=>st.create({...s,coerce:!0}),number:s=>Mt.create({...s,coerce:!0}),boolean:s=>Mn.create({...s,coerce:!0}),bigint:s=>At.create({...s,coerce:!0}),date:s=>zt.create({...s,coerce:!0})},pu=te;var _e=Object.freeze({__proto__:null,defaultErrorMap:Xt,setErrorMap:function(s){oi=s},getErrorMap:ps,makeIssue:ms,EMPTY_PATH:[],addIssueToContext:L,ParseStatus:Ze,INVALID:te,DIRTY:hs,OK:Ee,isAborted:Ds,isDirty:Us,isValid:Ft,isAsync:Tn,get util(){return pe},get objectUtil(){return zs},ZodParsedType:D,getParsedType:ft,ZodType:oe,datetimeRegex:ui,ZodString:st,ZodNumber:Mt,ZodBigInt:At,ZodBoolean:Mn,ZodDate:zt,ZodSymbol:gs,ZodUndefined:An,ZodNull:Nn,ZodAny:Qt,ZodUnknown:Pt,ZodNever:$t,ZodVoid:vs,ZodArray:it,ZodObject:be,ZodUnion:Zn,ZodDiscriminatedUnion:xs,ZodIntersection:On,ZodTuple:pt,ZodRecord:bs,ZodMap:$s,ZodSet:Dt,ZodFunction:Gt,ZodLazy:En,ZodLiteral:In,ZodEnum:Nt,ZodNativeEnum:Pn,ZodPromise:en,ZodEffects:rt,ZodTransformer:rt,ZodOptional:ct,ZodNullable:Zt,ZodDefault:jn,ZodCatch:Rn,ZodNaN:ys,BRAND:Ed,ZodBranded:Hs,ZodPipeline:Jn,ZodReadonly:Ln,custom:aa,Schema:oe,ZodSchema:oe,late:Id,get ZodFirstPartyTypeKind(){return Q},coerce:uu,any:Dd,array:Bd,bigint:jd,boolean:ca,date:Rd,discriminatedUnion:Kd,effect:la,enum:ru,function:tu,instanceof:(s,e={message:`Input not instance of ${s.name}`})=>aa(t=>t instanceof s,e),intersection:Wd,lazy:nu,literal:su,map:Qd,nan:Pd,nativeEnum:au,never:Vd,null:zd,nullable:cu,number:oa,object:Jd,oboolean:()=>ca().optional(),onumber:()=>oa().optional(),optional:ou,ostring:()=>ia().optional(),pipeline:du,preprocess:lu,promise:iu,record:Xd,set:eu,strictObject:Gd,string:ia,symbol:Ld,transformer:la,tuple:Yd,undefined:Fd,union:Hd,unknown:Ud,void:qd,NEVER:pu,ZodIssueCode:O,quotelessJson:s=>JSON.stringify(s,null,2).replace(/"([^"]+)":/g,"$1:"),ZodError:Ue});const Ge=_e.object({name:_e.string().optional(),title:_e.string().optional(),command:_e.string().optional(),args:_e.array(_e.union([_e.string(),_e.number(),_e.boolean()])).optional(),env:_e.record(_e.union([_e.string(),_e.number(),_e.boolean(),_e.null(),_e.undefined()])).optional()}).passthrough(),mu=_e.array(Ge),hu=_e.object({servers:_e.array(Ge)}).passthrough(),fu=_e.object({mcpServers:_e.array(Ge)}).passthrough(),gu=_e.object({servers:_e.record(Ge)}).passthrough(),vu=_e.object({mcpServers:_e.record(Ge)}).passthrough(),$u=_e.record(Ge),yu=Ge.refine(s=>s.command!==void 0,{message:"Server must have a 'command' property"}),mi=Symbol("MCPServerError");class Ne extends Error{constructor(e){super(e),this.name="MCPServerError",Object.setPrototypeOf(this,Ne.prototype)}}var Na;Na=mi;class Ks{constructor(e){ve(this,"servers",Le([]));this.host=e,this.loadServersFromStorage()}handleMessageFromExtension(e){const t=e.data;if(t.type===ae.getStoredMCPServersResponse){const n=t.data;return Array.isArray(n)&&this.servers.set(n),!0}return!1}async importServersFromJSON(e){return this.importFromJSON(e)}loadServersFromStorage(){try{this.host.postMessage({type:ae.getStoredMCPServers})}catch(e){console.error("Failed to load MCP servers:",e),this.servers.set([])}}saveServers(e){try{this.host.postMessage({type:ae.setStoredMCPServers,data:e})}catch(t){throw console.error("Failed to save MCP servers:",t),new Ne("Failed to save MCP servers")}}getServers(){return this.servers}addServer(e){this.checkExistingServerName(e.name),this.servers.update(t=>{const n=[...t,{...e,id:crypto.randomUUID()}];return this.saveServers(n),n})}checkExistingServerName(e,t){const n=dn(this.servers).find(r=>r.name===e);if(n&&(n==null?void 0:n.id)!==t)throw new Ne(`Server name '${e}' already exists`)}updateServer(e){this.checkExistingServerName(e.name,e.id),this.servers.update(t=>{const n=t.map(r=>r.id===e.id?e:r);return this.saveServers(n),n})}deleteServer(e){this.servers.update(t=>{const n=t.filter(r=>r.id!==e);return this.saveServers(n),n})}toggleDisabledServer(e){this.servers.update(t=>{const n=t.map(r=>r.id===e?{...r,disabled:!r.disabled}:r);return this.saveServers(n),n})}static convertServerToJSON(e){return JSON.stringify({mcpServers:{[e.name]:{command:e.command.split(" ")[0],args:e.command.split(" ").slice(1),env:e.env}}},null,2)}static parseServerValidationMessages(e){const t=new Map,n=new Map;e.forEach(a=>{var i;a.disabled?t.set(a.id,"MCP server has been manually disabled"):a.tools&&a.tools.length===0?t.set(a.id,"No tools are available for this MCP server"):a.disabledTools&&a.disabledTools.length===((i=a.tools)==null?void 0:i.length)?t.set(a.id,"All tools for this MCP server have validation errors: "+a.disabledTools.join(", ")):a.disabledTools&&a.disabledTools.length>0&&n.set(a.id,"MCP server has validation errors in the following tools which have been disabled: "+a.disabledTools.join(", "))});const r=this.parseDuplicateServerIds(e);return{errors:new Map([...t,...r]),warnings:n}}static parseDuplicateServerIds(e){const t=new Map;for(const r of e)t.has(r.name)||t.set(r.name,[]),t.get(r.name).push(r.id);const n=new Map;for(const[,r]of t)if(r.length>1)for(let a=1;a<r.length;a++)n.set(r[a],"MCP server is disabled due to duplicate server names");return n}parseServerConfigFromJSON(e){try{const t=JSON.parse(e),n=_e.union([mu.transform(r=>r.map(a=>this.normalizeServerConfig(a))),hu.transform(r=>r.servers.map(a=>this.normalizeServerConfig(a))),fu.transform(r=>r.mcpServers.map(a=>this.normalizeServerConfig(a))),gu.transform(r=>Object.entries(r.servers).map(([a,i])=>{const o=Ge.parse(i);return this.normalizeServerConfig({...o,name:o.name||a})})),vu.transform(r=>Object.entries(r.mcpServers).map(([a,i])=>{const o=Ge.parse(i);return this.normalizeServerConfig({...o,name:o.name||a})})),$u.transform(r=>{if(!Object.values(r).some(a=>{const i=Ge.safeParse(a);return i.success&&i.data.command!==void 0}))throw new Error("No command property found in any server config");return Object.entries(r).map(([a,i])=>{const o=Ge.parse(i);return this.normalizeServerConfig({...o,name:o.name||a})})}),yu.transform(r=>[this.normalizeServerConfig(r)])]).safeParse(t);if(n.success)return n.data;throw new Ne("Invalid JSON format. Expected an array of servers or an object with a 'servers' property.")}catch(t){throw t instanceof Ne?t:new Ne("Failed to parse MCP servers from JSON. Please check the format.")}}importFromJSON(e){try{const t=this.parseServerConfigFromJSON(e),n=dn(this.servers),r=new Set(n.map(a=>a.name));for(const a of t){if(!a.name)throw new Ne("All servers must have a name.");if(r.has(a.name))throw new Ne(`A server with the name '${a.name}' already exists.`);r.add(a.name)}return this.servers.update(a=>{const i=[...a,...t.map(o=>({...o,id:crypto.randomUUID()}))];return this.saveServers(i),i}),t.length}catch(t){throw t instanceof Ne?t:new Ne("Failed to import MCP servers from JSON. Please check the format.")}}normalizeServerConfig(e){try{const t=Ge.transform(n=>{const r=n.command||"",a=n.args?n.args.map(l=>String(l)):[];if(!r)throw new Error("Server must have a 'command' property");const i=a.length>0?`${r} ${a.join(" ")}`:r,o=n.name||n.title||(r?r.split(" ")[0]:""),c=n.env?Object.fromEntries(Object.entries(n.env).filter(([l,d])=>d!=null).map(([l,d])=>[l,String(d)])):void 0;return{name:o,command:i,arguments:"",useShellInterpolation:!0,env:Object.keys(c||{}).length>0?c:void 0}}).refine(n=>!!n.name,{message:"Server must have a name",path:["name"]}).refine(n=>!!n.command,{message:"Server must have a command",path:["command"]}).safeParse(e);if(!t.success)throw new Ne(t.error.message);return t.data}catch(t){throw t instanceof Error?new Ne(`Invalid server configuration: ${t.message}`):new Ne("Invalid server configuration")}}}ve(Ks,Na,"MCPServerError");function _u(s){let e,t,n=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},s[0]],r={};for(let a=0;a<n.length;a+=1)r=Se(r,n[a]);return{c(){e=Fe("svg"),t=new zn(!0),this.h()},l(a){e=Dn(a,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var i=Un(e);t=Vn(i,!0),i.forEach(y),this.h()},h(){t.a=null,lt(e,r)},m(a,i){qn(a,e,i),t.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="m170.5 51.6-19 28.4h145l-19-28.4c-1.5-2.2-4-3.6-6.7-3.6h-93.7c-2.7 0-5.2 1.3-6.7 3.6zm147-26.6 36.7 55H424c13.3 0 24 10.7 24 24s-10.7 24-24 24h-8v304c0 44.2-35.8 80-80 80H112c-44.2 0-80-35.8-80-80V128h-8c-13.3 0-24-10.7-24-24s10.7-24 24-24h69.8l36.7-55.1C140.9 9.4 158.4 0 177.1 0h93.7c18.7 0 36.2 9.4 46.6 24.9zM80 128v304c0 17.7 14.3 32 32 32h224c17.7 0 32-14.3 32-32V128zm80 64v208c0 8.8-7.2 16-16 16s-16-7.2-16-16V192c0-8.8 7.2-16 16-16s16 7.2 16 16m80 0v208c0 8.8-7.2 16-16 16s-16-7.2-16-16V192c0-8.8 7.2-16 16-16s16 7.2 16 16m80 0v208c0 8.8-7.2 16-16 16s-16-7.2-16-16V192c0-8.8 7.2-16 16-16s16 7.2 16 16"/>',e)},p(a,[i]){lt(e,r=yt(n,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},1&i&&a[0]]))},i:H,o:H,d(a){a&&y(e)}}}function wu(s,e,t){return s.$$set=n=>{t(0,e=Se(Se({},e),He(n)))},[e=He(e)]}class hi extends fe{constructor(e){super(),ge(this,e,wu,_u,he,{})}}function da(s,e,t){const n=s.slice();return n[11]=e[t],n[12]=e,n[13]=t,n}function xu(s){let e;return{c(){e=U("Environment Variables")},m(t,n){w(t,e,n)},d(t){t&&y(e)}}}function ua(s){let e,t,n=[],r=new Map,a=xe(s[0]);const i=o=>o[11].id;for(let o=0;o<a.length;o+=1){let c=da(s,a,o),l=i(c);r.set(l,n[o]=pa(l,c))}return{c(){for(let o=0;o<n.length;o+=1)n[o].c();e=Te()},m(o,c){for(let l=0;l<n.length;l+=1)n[l]&&n[l].m(o,c);w(o,e,c),t=!0},p(o,c){59&c&&(a=xe(o[0]),K(),n=Ot(n,c,i,1,o,a,r,e.parentNode,Et,pa,e,da),W())},i(o){if(!t){for(let c=0;c<a.length;c+=1)p(n[c]);t=!0}},o(o){for(let c=0;c<n.length;c+=1)m(n[c]);t=!1},d(o){o&&y(e);for(let c=0;c<n.length;c+=1)n[c].d(o)}}}function bu(s){let e,t;return e=new hi({props:{slot:"iconLeft"}}),{c(){b(e.$$.fragment)},m(n,r){S(e,n,r),t=!0},p:H,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Su(s){let e,t;return e=new Xe({props:{variant:"ghost",color:"neutral",type:"button",size:1,$$slots:{iconLeft:[bu]},$$scope:{ctx:s}}}),e.$on("focus",function(){jt(s[1])&&s[1].apply(this,arguments)}),e.$on("click",function(){return s[10](s[11])}),{c(){b(e.$$.fragment)},m(n,r){S(e,n,r),t=!0},p(n,r){s=n;const a={};16384&r&&(a.$$scope={dirty:r,ctx:s}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function pa(s,e){let t,n,r,a,i,o,c,l,d,u,h,g,v;function f(I){e[6](I,e[11])}let _={size:1,placeholder:"Name",class:"full-width"};function C(I){e[8](I,e[11])}e[11].key!==void 0&&(_.value=e[11].key),r=new ns({props:_}),Ke.push(()=>We(r,"value",f)),r.$on("focus",function(){jt(e[1])&&e[1].apply(this,arguments)}),r.$on("change",function(){return e[7](e[11])});let P={size:1,placeholder:"Value",class:"full-width"};return e[11].value!==void 0&&(P.value=e[11].value),c=new ns({props:P}),Ke.push(()=>We(c,"value",C)),c.$on("focus",function(){jt(e[1])&&e[1].apply(this,arguments)}),c.$on("change",function(){return e[9](e[11])}),h=new ln({props:{content:"Remove",$$slots:{default:[Su]},$$scope:{ctx:e}}}),{key:s,first:null,c(){t=M("tr"),n=M("td"),b(r.$$.fragment),i=E(),o=M("td"),b(c.$$.fragment),d=E(),u=M("td"),b(h.$$.fragment),g=E(),$(n,"class","name-cell svelte-1mazg1z"),$(o,"class","value-cell svelte-1mazg1z"),$(u,"class","action-cell svelte-1mazg1z"),$(t,"class","env-var-row svelte-1mazg1z"),this.first=t},m(I,A){w(I,t,A),x(t,n),S(r,n,null),x(t,i),x(t,o),S(c,o,null),x(t,d),x(t,u),S(h,u,null),x(t,g),v=!0},p(I,A){e=I;const T={};!a&&1&A&&(a=!0,T.value=e[11].key,Ye(()=>a=!1)),r.$set(T);const N={};!l&&1&A&&(l=!0,N.value=e[11].value,Ye(()=>l=!1)),c.$set(N);const j={};16387&A&&(j.$$scope={dirty:A,ctx:e}),h.$set(j)},i(I){v||(p(r.$$.fragment,I),p(c.$$.fragment,I),p(h.$$.fragment,I),v=!0)},o(I){m(r.$$.fragment,I),m(c.$$.fragment,I),m(h.$$.fragment,I),v=!1},d(I){I&&y(t),k(r),k(c),k(h)}}}function ku(s){let e;return{c(){e=U("Variable")},m(t,n){w(t,e,n)},d(t){t&&y(e)}}}function Cu(s){let e,t;return e=new _s({props:{slot:"iconLeft"}}),{c(){b(e.$$.fragment)},m(n,r){S(e,n,r),t=!0},p:H,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Tu(s){let e,t,n,r,a,i,o,c;e=new de({props:{size:1,weight:"medium",$$slots:{default:[xu]},$$scope:{ctx:s}}});let l=s[0].length>0&&ua(s);return o=new Xe({props:{size:1,variant:"soft",color:"neutral",type:"button",$$slots:{iconLeft:[Cu],default:[ku]},$$scope:{ctx:s}}}),o.$on("click",s[2]),{c(){b(e.$$.fragment),t=E(),n=M("table"),r=M("tbody"),l&&l.c(),a=E(),i=M("div"),b(o.$$.fragment),$(n,"class","env-vars-table svelte-1mazg1z"),$(i,"class","new-var-button-container svelte-1mazg1z")},m(d,u){S(e,d,u),w(d,t,u),w(d,n,u),x(n,r),l&&l.m(r,null),w(d,a,u),w(d,i,u),S(o,i,null),c=!0},p(d,[u]){const h={};16384&u&&(h.$$scope={dirty:u,ctx:d}),e.$set(h),d[0].length>0?l?(l.p(d,u),1&u&&p(l,1)):(l=ua(d),l.c(),p(l,1),l.m(r,null)):l&&(K(),m(l,1,1,()=>{l=null}),W());const g={};16384&u&&(g.$$scope={dirty:u,ctx:d}),o.$set(g)},i(d){c||(p(e.$$.fragment,d),p(l),p(o.$$.fragment,d),c=!0)},o(d){m(e.$$.fragment,d),m(l),m(o.$$.fragment,d),c=!1},d(d){d&&(y(t),y(n),y(a),y(i)),k(e,d),l&&l.d(),k(o)}}}function Mu(s,e,t){let{handleEnterEditMode:n}=e,{envVarEntries:r=[]}=e;function a(c){n(),t(0,r=r.filter(l=>l.id!==c))}function i(c,l){const d=r.findIndex(u=>u.id===c);d!==-1&&(t(0,r[d].key=l,r),t(0,r))}function o(c,l){const d=r.findIndex(u=>u.id===c);d!==-1&&(t(0,r[d].value=l,r),t(0,r))}return s.$$set=c=>{"handleEnterEditMode"in c&&t(1,n=c.handleEnterEditMode),"envVarEntries"in c&&t(0,r=c.envVarEntries)},[r,n,function(){n(),t(0,r=[...r,{id:crypto.randomUUID(),key:"",value:""}])},a,i,o,function(c,l){s.$$.not_equal(l.key,c)&&(l.key=c,t(0,r))},c=>i(c.id,c.key),function(c,l){s.$$.not_equal(l.value,c)&&(l.value=c,t(0,r))},c=>o(c.id,c.value),c=>a(c.id)]}class Au extends fe{constructor(e){super(),ge(this,e,Mu,Tu,he,{handleEnterEditMode:1,envVarEntries:0})}}function Nu(s){let e,t,n,r,a,i,o,c,l,d,u,h,g,v,f,_,C,P,I,A,T,N,j,B,F;i=new zi({}),c=new de({props:{color:"secondary",size:1,weight:"medium",$$slots:{default:[Ou]},$$scope:{ctx:s}}});const Y=[Iu,Eu],ce=[];function G(J,me){return J[0]==="addJson"?0:J[0]==="add"||J[0]==="edit"?1:-1}~(d=G(s))&&(u=ce[d]=Y[d](s));let le=(s[0]==="add"||s[0]==="edit")&&ma(s);return _=new no({props:{variant:"soft",color:"error",size:1,$$slots:{icon:[Du],default:[zu]},$$scope:{ctx:s}}}),I=new Xe({props:{size:1,variant:"ghost",color:"neutral",type:"button",$$slots:{default:[Uu]},$$scope:{ctx:s}}}),I.$on("click",s[18]),T=new Xe({props:{size:1,variant:"solid",color:"accent",loading:s[2],type:"submit",disabled:s[14],$$slots:{default:[Ju]},$$scope:{ctx:s}}}),{c(){e=M("form"),t=M("div"),n=M("div"),r=M("div"),a=M("div"),b(i.$$.fragment),o=E(),b(c.$$.fragment),l=E(),u&&u.c(),h=E(),le&&le.c(),g=E(),v=M("div"),f=M("div"),b(_.$$.fragment),C=E(),P=M("div"),b(I.$$.fragment),A=E(),b(T.$$.fragment),$(a,"class","server-icon svelte-nlsbjs"),$(r,"class","server-title svelte-nlsbjs"),$(n,"class","server-header svelte-nlsbjs"),$(f,"class","error-container svelte-nlsbjs"),$e(f,"is-error",!!s[1]),$(P,"class","form-actions svelte-nlsbjs"),$(v,"class","form-actions-row svelte-nlsbjs"),$(t,"class","server-edit-form svelte-nlsbjs"),$(e,"class",N="c-mcp-server-card "+(s[0]==="add"||s[0]==="addJson"?"add-server-section":"server-item")+" svelte-nlsbjs")},m(J,me){w(J,e,me),x(e,t),x(t,n),x(n,r),x(r,a),S(i,a,null),x(r,o),S(c,r,null),x(t,l),~d&&ce[d].m(t,null),x(t,h),le&&le.m(t,null),x(t,g),x(t,v),x(v,f),S(_,f,null),x(v,C),x(v,P),S(I,P,null),x(P,A),S(T,P,null),j=!0,B||(F=ze(e,"submit",bi(s[17])),B=!0)},p(J,me){const _t={};8192&me[0]|32&me[1]&&(_t.$$scope={dirty:me,ctx:J}),c.$set(_t);let Ut=d;d=G(J),d===Ut?~d&&ce[d].p(J,me):(u&&(K(),m(ce[Ut],1,1,()=>{ce[Ut]=null}),W()),~d?(u=ce[d],u?u.p(J,me):(u=ce[d]=Y[d](J),u.c()),p(u,1),u.m(t,h)):u=null),J[0]==="add"||J[0]==="edit"?le?(le.p(J,me),1&me[0]&&p(le,1)):(le=ma(J),le.c(),p(le,1),le.m(t,g)):le&&(K(),m(le,1,1,()=>{le=null}),W());const Qe={};2&me[0]|32&me[1]&&(Qe.$$scope={dirty:me,ctx:J}),_.$set(Qe),(!j||2&me[0])&&$e(f,"is-error",!!J[1]);const tn={};32&me[1]&&(tn.$$scope={dirty:me,ctx:J}),I.$set(tn);const Vt={};4&me[0]&&(Vt.loading=J[2]),16384&me[0]&&(Vt.disabled=J[14]),1&me[0]|32&me[1]&&(Vt.$$scope={dirty:me,ctx:J}),T.$set(Vt),(!j||1&me[0]&&N!==(N="c-mcp-server-card "+(J[0]==="add"||J[0]==="addJson"?"add-server-section":"server-item")+" svelte-nlsbjs"))&&$(e,"class",N)},i(J){j||(p(i.$$.fragment,J),p(c.$$.fragment,J),p(u),p(le),p(_.$$.fragment,J),p(I.$$.fragment,J),p(T.$$.fragment,J),j=!0)},o(J){m(i.$$.fragment,J),m(c.$$.fragment,J),m(u),m(le),m(_.$$.fragment,J),m(I.$$.fragment,J),m(T.$$.fragment,J),j=!1},d(J){J&&y(e),k(i),k(c),~d&&ce[d].d(),le&&le.d(),k(_),k(I),k(T),B=!1,F()}}}function Zu(s){let e,t;return e=new ii({props:{$$slots:{"header-right":[lp],"header-left":[Xu]},$$scope:{ctx:s}}}),{c(){b(e.$$.fragment)},m(n,r){S(e,n,r),t=!0},p(n,r){const a={};4344&r[0]|32&r[1]&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Ou(s){let e;return{c(){e=U(s[13])},m(t,n){w(t,e,n)},p(t,n){8192&n[0]&&we(e,t[13])},d(t){t&&y(e)}}}function Eu(s){let e,t,n,r,a,i,o,c,l,d;function u(f){s[32](f)}let h={size:1,placeholder:"Enter a name for your MCP server (e.g., 'Server Memory')",$$slots:{label:[ju]},$$scope:{ctx:s}};function g(f){s[33](f)}s[8]!==void 0&&(h.value=s[8]),n=new ns({props:h}),Ke.push(()=>We(n,"value",u)),n.$on("focus",s[15]);let v={size:1,placeholder:"Enter the MCP command (e.g., 'npx -y @modelcontextprotocol/server-memory')",$$slots:{label:[Lu]},$$scope:{ctx:s}};return s[9]!==void 0&&(v.value=s[9]),c=new ns({props:v}),Ke.push(()=>We(c,"value",g)),c.$on("focus",s[15]),{c(){e=M("div"),t=M("div"),b(n.$$.fragment),a=E(),i=M("div"),o=M("div"),b(c.$$.fragment),$(t,"class","input-field svelte-nlsbjs"),$(e,"class","form-row svelte-nlsbjs"),$(o,"class","input-field svelte-nlsbjs"),$(i,"class","form-row svelte-nlsbjs")},m(f,_){w(f,e,_),x(e,t),S(n,t,null),w(f,a,_),w(f,i,_),x(i,o),S(c,o,null),d=!0},p(f,_){const C={};32&_[1]&&(C.$$scope={dirty:_,ctx:f}),!r&&256&_[0]&&(r=!0,C.value=f[8],Ye(()=>r=!1)),n.$set(C);const P={};32&_[1]&&(P.$$scope={dirty:_,ctx:f}),!l&&512&_[0]&&(l=!0,P.value=f[9],Ye(()=>l=!1)),c.$set(P)},i(f){d||(p(n.$$.fragment,f),p(c.$$.fragment,f),d=!0)},o(f){m(n.$$.fragment,f),m(c.$$.fragment,f),d=!1},d(f){f&&(y(e),y(a),y(i)),k(n),k(c)}}}function Iu(s){let e,t,n,r,a,i,o,c,l;function d(h){s[31](h)}n=new de({props:{size:1,weight:"medium",$$slots:{default:[Fu]},$$scope:{ctx:s}}});let u={size:1,placeholder:"Paste JSON here..."};return s[10]!==void 0&&(u.value=s[10]),o=new ao({props:u}),Ke.push(()=>We(o,"value",d)),{c(){e=M("div"),t=M("div"),b(n.$$.fragment),r=E(),a=M("div"),i=M("div"),b(o.$$.fragment),$(t,"class","input-field svelte-nlsbjs"),$(e,"class","form-row svelte-nlsbjs"),$(i,"class","input-field svelte-nlsbjs"),$(a,"class","form-row svelte-nlsbjs")},m(h,g){w(h,e,g),x(e,t),S(n,t,null),w(h,r,g),w(h,a,g),x(a,i),S(o,i,null),l=!0},p(h,g){const v={};32&g[1]&&(v.$$scope={dirty:g,ctx:h}),n.$set(v);const f={};!c&&1024&g[0]&&(c=!0,f.value=h[10],Ye(()=>c=!1)),o.$set(f)},i(h){l||(p(n.$$.fragment,h),p(o.$$.fragment,h),l=!0)},o(h){m(n.$$.fragment,h),m(o.$$.fragment,h),l=!1},d(h){h&&(y(e),y(r),y(a)),k(n),k(o)}}}function Pu(s){let e;return{c(){e=U("Name")},m(t,n){w(t,e,n)},d(t){t&&y(e)}}}function ju(s){let e,t;return e=new de({props:{slot:"label",size:1,weight:"medium",$$slots:{default:[Pu]},$$scope:{ctx:s}}}),{c(){b(e.$$.fragment)},m(n,r){S(e,n,r),t=!0},p(n,r){const a={};32&r[1]&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Ru(s){let e;return{c(){e=U("Command")},m(t,n){w(t,e,n)},d(t){t&&y(e)}}}function Lu(s){let e,t;return e=new de({props:{slot:"label",size:1,weight:"medium",$$slots:{default:[Ru]},$$scope:{ctx:s}}}),{c(){b(e.$$.fragment)},m(n,r){S(e,n,r),t=!0},p(n,r){const a={};32&r[1]&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Fu(s){let e;return{c(){e=U("Code Snippet")},m(t,n){w(t,e,n)},d(t){t&&y(e)}}}function ma(s){let e,t,n;function r(i){s[34](i)}let a={handleEnterEditMode:s[15]};return s[11]!==void 0&&(a.envVarEntries=s[11]),e=new Au({props:a}),Ke.push(()=>We(e,"envVarEntries",r)),{c(){b(e.$$.fragment)},m(i,o){S(e,i,o),n=!0},p(i,o){const c={};!t&&2048&o[0]&&(t=!0,c.envVarEntries=i[11],Ye(()=>t=!1)),e.$set(c)},i(i){n||(p(e.$$.fragment,i),n=!0)},o(i){m(e.$$.fragment,i),n=!1},d(i){k(e,i)}}}function zu(s){let e;return{c(){e=U(s[1])},m(t,n){w(t,e,n)},p(t,n){2&n[0]&&we(e,t[1])},d(t){t&&y(e)}}}function Du(s){let e,t;return e=new Bi({props:{slot:"icon"}}),{c(){b(e.$$.fragment)},m(n,r){S(e,n,r),t=!0},p:H,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Uu(s){let e;return{c(){e=U("Cancel")},m(t,n){w(t,e,n)},d(t){t&&y(e)}}}function Vu(s){let e;return{c(){e=U("Save")},m(t,n){w(t,e,n)},d(t){t&&y(e)}}}function qu(s){let e;return{c(){e=U("Add")},m(t,n){w(t,e,n)},d(t){t&&y(e)}}}function Bu(s){let e;return{c(){e=U("Import")},m(t,n){w(t,e,n)},d(t){t&&y(e)}}}function Ju(s){let e;function t(a,i){return a[0]==="addJson"?Bu:a[0]==="add"?qu:a[0]==="edit"?Vu:void 0}let n=t(s),r=n&&n(s);return{c(){r&&r.c(),e=Te()},m(a,i){r&&r.m(a,i),w(a,e,i)},p(a,i){n!==(n=t(a))&&(r&&r.d(1),r=n&&n(a),r&&(r.c(),r.m(e.parentNode,e)))},d(a){a&&y(e),r&&r.d(a)}}}function Gu(s){let e;return{c(){e=M("div"),$(e,"class","c-dot svelte-nlsbjs"),$e(e,"c-green",!s[6]),$e(e,"c-warning",!s[6]&&!!s[7]),$e(e,"c-red",!!s[6]),$e(e,"c-disabled",s[3].disabled)},m(t,n){w(t,e,n)},p(t,n){64&n[0]&&$e(e,"c-green",!t[6]),192&n[0]&&$e(e,"c-warning",!t[6]&&!!t[7]),64&n[0]&&$e(e,"c-red",!!t[6]),8&n[0]&&$e(e,"c-disabled",t[3].disabled)},d(t){t&&y(e)}}}function Hu(s){let e,t=s[3].name+"";return{c(){e=U(t)},m(n,r){w(n,e,r)},p(n,r){8&r[0]&&t!==(t=n[3].name+"")&&we(e,t)},d(n){n&&y(e)}}}function Ku(s){let e,t,n;return t=new de({props:{size:1,weight:"medium",$$slots:{default:[Hu]},$$scope:{ctx:s}}}),{c(){e=M("div"),b(t.$$.fragment),$(e,"class","server-name svelte-nlsbjs")},m(r,a){w(r,e,a),S(t,e,null),n=!0},p(r,a){const i={};8&a[0]|32&a[1]&&(i.$$scope={dirty:a,ctx:r}),t.$set(i)},i(r){n||(p(t.$$.fragment,r),n=!0)},o(r){m(t.$$.fragment,r),n=!1},d(r){r&&y(e),k(t)}}}function Wu(s){let e,t=s[3].command+"";return{c(){e=U(t)},m(n,r){w(n,e,r)},p(n,r){8&r[0]&&t!==(t=n[3].command+"")&&we(e,t)},d(n){n&&y(e)}}}function Yu(s){let e,t,n;return t=new de({props:{color:"secondary",size:1,weight:"regular",$$slots:{default:[Wu]},$$scope:{ctx:s}}}),{c(){e=M("div"),b(t.$$.fragment),$(e,"class","command-text svelte-nlsbjs")},m(r,a){w(r,e,a),S(t,e,null),n=!0},p(r,a){const i={};8&a[0]|32&a[1]&&(i.$$scope={dirty:a,ctx:r}),t.$set(i)},i(r){n||(p(t.$$.fragment,r),n=!0)},o(r){m(t.$$.fragment,r),n=!1},d(r){r&&y(e),k(t)}}}function Xu(s){let e,t,n,r,a,i,o;return t=new ln({props:{content:s[6]||s[7],$$slots:{default:[Gu]},$$scope:{ctx:s}}}),r=new ln({props:{content:s[3].name,side:"top",align:"start",$$slots:{default:[Ku]},$$scope:{ctx:s}}}),i=new ln({props:{content:s[3].command,side:"top",align:"start",$$slots:{default:[Yu]},$$scope:{ctx:s}}}),{c(){e=M("div"),b(t.$$.fragment),n=E(),b(r.$$.fragment),a=E(),b(i.$$.fragment),$(e,"slot","header-left"),$(e,"class","l-header svelte-nlsbjs")},m(c,l){w(c,e,l),S(t,e,null),x(e,n),S(r,e,null),x(e,a),S(i,e,null),o=!0},p(c,l){const d={};192&l[0]&&(d.content=c[6]||c[7]),200&l[0]|32&l[1]&&(d.$$scope={dirty:l,ctx:c}),t.$set(d);const u={};8&l[0]&&(u.content=c[3].name),8&l[0]|32&l[1]&&(u.$$scope={dirty:l,ctx:c}),r.$set(u);const h={};8&l[0]&&(h.content=c[3].command),8&l[0]|32&l[1]&&(h.$$scope={dirty:l,ctx:c}),i.$set(h)},i(c){o||(p(t.$$.fragment,c),p(r.$$.fragment,c),p(i.$$.fragment,c),o=!0)},o(c){m(t.$$.fragment,c),m(r.$$.fragment,c),m(i.$$.fragment,c),o=!1},d(c){c&&y(e),k(t),k(r),k(i)}}}function Qu(s){let e,t;return e=new ro({}),{c(){b(e.$$.fragment)},m(n,r){S(e,n,r),t=!0},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function ep(s){let e,t;return e=new ws({props:{size:1,variant:"ghost-block",color:"neutral",$$slots:{default:[Qu]},$$scope:{ctx:s}}}),{c(){b(e.$$.fragment)},m(n,r){S(e,n,r),t=!0},p(n,r){const a={};32&r[1]&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function tp(s){let e;return{c(){e=U("Edit")},m(t,n){w(t,e,n)},d(t){t&&y(e)}}}function np(s){let e,t,n,r,a;return t=new so({}),r=new de({props:{size:1,weight:"medium",$$slots:{default:[tp]},$$scope:{ctx:s}}}),{c(){e=M("div"),b(t.$$.fragment),n=E(),b(r.$$.fragment),$(e,"class","status-controls-button svelte-nlsbjs")},m(i,o){w(i,e,o),S(t,e,null),x(e,n),S(r,e,null),a=!0},p(i,o){const c={};32&o[1]&&(c.$$scope={dirty:o,ctx:i}),r.$set(c)},i(i){a||(p(t.$$.fragment,i),p(r.$$.fragment,i),a=!0)},o(i){m(t.$$.fragment,i),m(r.$$.fragment,i),a=!1},d(i){i&&y(e),k(t),k(r)}}}function sp(s){let e;return{c(){e=U("Copy JSON")},m(t,n){w(t,e,n)},d(t){t&&y(e)}}}function rp(s){let e,t,n,r,a;return t=new Di({}),r=new de({props:{size:1,weight:"medium",$$slots:{default:[sp]},$$scope:{ctx:s}}}),{c(){e=M("div"),b(t.$$.fragment),n=E(),b(r.$$.fragment),$(e,"class","status-controls-button svelte-nlsbjs")},m(i,o){w(i,e,o),S(t,e,null),x(e,n),S(r,e,null),a=!0},p(i,o){const c={};32&o[1]&&(c.$$scope={dirty:o,ctx:i}),r.$set(c)},i(i){a||(p(t.$$.fragment,i),p(r.$$.fragment,i),a=!0)},o(i){m(t.$$.fragment,i),m(r.$$.fragment,i),a=!1},d(i){i&&y(e),k(t),k(r)}}}function ap(s){let e;return{c(){e=U("Delete")},m(t,n){w(t,e,n)},d(t){t&&y(e)}}}function ip(s){let e,t,n,r,a;return t=new hi({}),r=new de({props:{size:1,weight:"medium",$$slots:{default:[ap]},$$scope:{ctx:s}}}),{c(){e=M("div"),b(t.$$.fragment),n=E(),b(r.$$.fragment),$(e,"class","status-controls-button svelte-nlsbjs")},m(i,o){w(i,e,o),S(t,e,null),x(e,n),S(r,e,null),a=!0},p(i,o){const c={};32&o[1]&&(c.$$scope={dirty:o,ctx:i}),r.$set(c)},i(i){a||(p(t.$$.fragment,i),p(r.$$.fragment,i),a=!0)},o(i){m(t.$$.fragment,i),m(r.$$.fragment,i),a=!1},d(i){i&&y(e),k(t),k(r)}}}function op(s){let e,t,n,r,a,i;return e=new Ce.Item({props:{onSelect:s[15],$$slots:{default:[np]},$$scope:{ctx:s}}}),n=new Ce.Item({props:{onSelect:s[28],$$slots:{default:[rp]},$$scope:{ctx:s}}}),a=new Ce.Item({props:{color:"error",onSelect:s[29],$$slots:{default:[ip]},$$scope:{ctx:s}}}),{c(){b(e.$$.fragment),t=E(),b(n.$$.fragment),r=E(),b(a.$$.fragment)},m(o,c){S(e,o,c),w(o,t,c),S(n,o,c),w(o,r,c),S(a,o,c),i=!0},p(o,c){const l={};32&c[1]&&(l.$$scope={dirty:c,ctx:o}),e.$set(l);const d={};4096&c[0]&&(d.onSelect=o[28]),32&c[1]&&(d.$$scope={dirty:c,ctx:o}),n.$set(d);const u={};4120&c[0]&&(u.onSelect=o[29]),32&c[1]&&(u.$$scope={dirty:c,ctx:o}),a.$set(u)},i(o){i||(p(e.$$.fragment,o),p(n.$$.fragment,o),p(a.$$.fragment,o),i=!0)},o(o){m(e.$$.fragment,o),m(n.$$.fragment,o),m(a.$$.fragment,o),i=!1},d(o){o&&(y(t),y(r)),k(e,o),k(n,o),k(a,o)}}}function cp(s){let e,t,n,r;return e=new Ce.Trigger({props:{$$slots:{default:[ep]},$$scope:{ctx:s}}}),n=new Ce.Content({props:{side:"bottom",align:"end",$$slots:{default:[op]},$$scope:{ctx:s}}}),{c(){b(e.$$.fragment),t=E(),b(n.$$.fragment)},m(a,i){S(e,a,i),w(a,t,i),S(n,a,i),r=!0},p(a,i){const o={};32&i[1]&&(o.$$scope={dirty:i,ctx:a}),e.$set(o);const c={};4120&i[0]|32&i[1]&&(c.$$scope={dirty:i,ctx:a}),n.$set(c)},i(a){r||(p(e.$$.fragment,a),p(n.$$.fragment,a),r=!0)},o(a){m(e.$$.fragment,a),m(n.$$.fragment,a),r=!1},d(a){a&&y(t),k(e,a),k(n,a)}}}function lp(s){let e,t,n,r,a,i,o;function c(d){s[30](d)}n=new Ci({props:{size:1,checked:!s[3].disabled}}),n.$on("change",s[27]);let l={$$slots:{default:[cp]},$$scope:{ctx:s}};return s[12]!==void 0&&(l.requestClose=s[12]),a=new Ce.Root({props:l}),Ke.push(()=>We(a,"requestClose",c)),{c(){e=M("div"),t=M("div"),b(n.$$.fragment),r=E(),b(a.$$.fragment),$(t,"class","status-controls svelte-nlsbjs"),$(e,"class","server-actions svelte-nlsbjs"),$(e,"slot","header-right")},m(d,u){w(d,e,u),x(e,t),S(n,t,null),x(t,r),S(a,t,null),o=!0},p(d,u){const h={};8&u[0]&&(h.checked=!d[3].disabled),n.$set(h);const g={};4120&u[0]|32&u[1]&&(g.$$scope={dirty:u,ctx:d}),!i&&4096&u[0]&&(i=!0,g.requestClose=d[12],Ye(()=>i=!1)),a.$set(g)},i(d){o||(p(n.$$.fragment,d),p(a.$$.fragment,d),o=!0)},o(d){m(n.$$.fragment,d),m(a.$$.fragment,d),o=!1},d(d){d&&y(e),k(n),k(a)}}}function dp(s){let e,t,n,r;const a=[Zu,Nu],i=[];function o(c,l){return c[0]==="view"&&c[3]?0:1}return e=o(s),t=i[e]=a[e](s),{c(){t.c(),n=Te()},m(c,l){i[e].m(c,l),w(c,n,l),r=!0},p(c,l){let d=e;e=o(c),e===d?i[e].p(c,l):(K(),m(i[d],1,1,()=>{i[d]=null}),W(),t=i[e],t?t.p(c,l):(t=i[e]=a[e](c),t.c()),p(t,1),t.m(n.parentNode,n))},i(c){r||(p(t),r=!0)},o(c){m(t),r=!1},d(c){c&&y(n),i[e].d(c)}}}function up({key:s,value:e}){return s.trim()&&e.trim()}function pp(s,e,t){let n,r,a,i,{server:o=null}=e,{onDelete:c}=e,{onAdd:l}=e,{onSave:d}=e,{onEdit:u}=e,{onToggleDisableServer:h}=e,{onJSONImport:g}=e,{onCancel:v}=e,{disabledText:f}=e,{warningText:_}=e,{mode:C="view"}=e,{mcpServerError:P=""}=e,I=(o==null?void 0:o.name)??"",A=(o==null?void 0:o.command)??"",T=(o==null?void 0:o.env)??{},N="",j=[];function B(){t(11,j=Object.entries(T).map(([G,le])=>({id:crypto.randomUUID(),key:G,value:le})))}B();let F=()=>{},{busy:Y=!1}=e;function ce(){if(o){const G=Ks.convertServerToJSON(o);navigator.clipboard.writeText(G)}}return s.$$set=G=>{"server"in G&&t(3,o=G.server),"onDelete"in G&&t(4,c=G.onDelete),"onAdd"in G&&t(19,l=G.onAdd),"onSave"in G&&t(20,d=G.onSave),"onEdit"in G&&t(21,u=G.onEdit),"onToggleDisableServer"in G&&t(5,h=G.onToggleDisableServer),"onJSONImport"in G&&t(22,g=G.onJSONImport),"onCancel"in G&&t(23,v=G.onCancel),"disabledText"in G&&t(6,f=G.disabledText),"warningText"in G&&t(7,_=G.warningText),"mode"in G&&t(0,C=G.mode),"mcpServerError"in G&&t(1,P=G.mcpServerError),"busy"in G&&t(2,Y=G.busy)},s.$$.update=()=>{768&s.$$.dirty[0]&&I&&A&&t(1,P=""),769&s.$$.dirty[0]&&t(26,n=!(C!=="add"||I.trim()&&A.trim())),1025&s.$$.dirty[0]&&t(25,r=C==="addJson"&&!N.trim()),100663297&s.$$.dirty[0]&&t(14,a=n||C==="view"||r),1&s.$$.dirty[0]&&t(13,i=C==="add"||C==="addJson"?"New MCP Server":"Edit MCP Server")},[C,P,Y,o,c,h,f,_,I,A,N,j,F,i,a,function(){o&&C==="view"&&(t(0,C="edit"),u(o),F())},ce,async function(){t(1,P=""),t(2,Y=!0);const G=j.filter(up);T=Object.fromEntries(G.map(({key:J,value:me})=>[J.trim(),me.trim()])),B();try{if(C==="add")await l({name:I.trim(),command:A.trim(),arguments:"",useShellInterpolation:!0,env:Object.keys(T).length>0?T:void 0});else if(C==="addJson"){try{JSON.parse(N)}catch(J){const me=J instanceof Error?J.message:String(J);throw new Ne(`Invalid JSON format: ${me}`)}await g(N)}else C==="edit"&&o&&await d({...o,name:I.trim(),command:A.trim(),arguments:"",env:Object.keys(T).length>0?T:void 0})}catch(J){t(1,P=(le=J)!=null&&typeof le=="object"&&(le instanceof Ne||mi in(le.constructor||le))?J.message:"Failed to save server"),console.warn(J)}finally{t(2,Y=!1)}var le},function(){t(2,Y=!1),t(1,P=""),v==null||v(),t(10,N=""),t(8,I=(o==null?void 0:o.name)??""),t(9,A=(o==null?void 0:o.command)??""),T=o!=null&&o.env?{...o.env}:{},B()},l,d,u,g,v,B,r,n,()=>{o&&h(o.id),F()},()=>{ce(),F()},()=>{c(o.id),F()},function(G){F=G,t(12,F)},function(G){N=G,t(10,N)},function(G){I=G,t(8,I)},function(G){A=G,t(9,A)},function(G){j=G,t(11,j)}]}class fi extends fe{constructor(e){super(),ge(this,e,pp,dp,he,{server:3,onDelete:4,onAdd:19,onSave:20,onEdit:21,onToggleDisableServer:5,onJSONImport:22,onCancel:23,disabledText:6,warningText:7,mode:0,mcpServerError:1,setLocalEnvVarFormState:24,busy:2},null,[-1,-1])}get setLocalEnvVarFormState(){return this.$$.ctx[24]}}function mp(s){let e,t,n=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},s[0]],r={};for(let a=0;a<n.length;a+=1)r=Se(r,n[a]);return{c(){e=Fe("svg"),t=new zn(!0),this.h()},l(a){e=Dn(a,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var i=Un(e);t=Vn(i,!0),i.forEach(y),this.h()},h(){t.a=null,lt(e,r)},m(a,i){qn(a,e,i),t.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M280 24c0-13.3-10.7-24-24-24s-24 10.7-24 24v270.1l-95-95c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9L239 369c9.4 9.4 24.6 9.4 33.9 0L409 233c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-95 95zM128.8 304H64c-35.3 0-64 28.7-64 64v80c0 35.3 28.7 64 64 64h384c35.3 0 64-28.7 64-64v-80c0-35.3-28.7-64-64-64h-64.8l-48 48H448c8.8 0 16 7.2 16 16v80c0 8.8-7.2 16-16 16H64c-8.8 0-16-7.2-16-16v-80c0-8.8 7.2-16 16-16h112.8zM432 408a24 24 0 1 0-48 0 24 24 0 1 0 48 0"/>',e)},p(a,[i]){lt(e,r=yt(n,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&i&&a[0]]))},i:H,o:H,d(a){a&&y(e)}}}function hp(s,e,t){return s.$$set=n=>{t(0,e=Se(Se({},e),He(n)))},[e=He(e)]}class gi extends fe{constructor(e){super(),ge(this,e,hp,mp,he,{})}}const fp={mcpDocsURL:"https://docs.augmentcode.com/setup-augment/mcp"},gp={mcpDocsURL:"https://docs.augmentcode.com/jetbrains/setup-augment/mcp"},vp=Ni(),$p=new class{constructor(s){ve(this,"strings");let e={[rr.vscode]:{},[rr.jetbrains]:gp};this.strings={...fp,...e[s]}}get(s){return this.strings[s]}}(vp.clientType);function ha(s,e,t){const n=s.slice();return n[23]=e[t],n}function yp(s){let e;return{c(){e=M("div"),e.textContent="MCP",$(e,"class","section-heading-text")},m(t,n){w(t,e,n)},p:H,d(t){t&&y(e)}}}function fa(s,e){let t,n,r;return n=new fi({props:{mode:e[2]===e[23].id?"edit":"view",server:e[23],onAdd:e[8],onSave:e[9],onDelete:e[11],onToggleDisableServer:e[12],onEdit:e[7],onCancel:e[6],onJSONImport:e[10],disabledText:e[4].errors.get(e[23].id),warningText:e[4].warnings.get(e[23].id)}}),{key:s,first:null,c(){t=Te(),b(n.$$.fragment),this.first=t},m(a,i){w(a,t,i),S(n,a,i),r=!0},p(a,i){e=a;const o={};5&i&&(o.mode=e[2]===e[23].id?"edit":"view"),1&i&&(o.server=e[23]),17&i&&(o.disabledText=e[4].errors.get(e[23].id)),17&i&&(o.warningText=e[4].warnings.get(e[23].id)),n.$set(o)},i(a){r||(p(n.$$.fragment,a),r=!0)},o(a){m(n.$$.fragment,a),r=!1},d(a){a&&y(t),k(n,a)}}}function ga(s){let e,t;return e=new fi({props:{mode:s[3],onAdd:s[8],onSave:s[9],onDelete:s[11],onToggleDisableServer:s[12],onEdit:s[7],onCancel:s[6],onJSONImport:s[10]}}),{c(){b(e.$$.fragment)},m(n,r){S(e,n,r),t=!0},p(n,r){const a={};8&r&&(a.mode=n[3]),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function _p(s){let e;return{c(){e=U("Add MCP")},m(t,n){w(t,e,n)},d(t){t&&y(e)}}}function wp(s){let e,t;return e=new _s({props:{slot:"iconLeft"}}),{c(){b(e.$$.fragment)},m(n,r){S(e,n,r),t=!0},p:H,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function va(s){let e,t;return e=new Xe({props:{disabled:s[5],color:"neutral",variant:"soft",size:1,title:"Add MCP from JSON",$$slots:{iconLeft:[bp],default:[xp]},$$scope:{ctx:s}}}),e.$on("click",s[21]),{c(){b(e.$$.fragment)},m(n,r){S(e,n,r),t=!0},p(n,r){const a={};32&r&&(a.disabled=n[5]),67108864&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function xp(s){let e;return{c(){e=U("Import from JSON")},m(t,n){w(t,e,n)},d(t){t&&y(e)}}}function bp(s){let e,t;return e=new gi({props:{slot:"iconLeft"}}),{c(){b(e.$$.fragment)},m(n,r){S(e,n,r),t=!0},p:H,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Sp(s){let e,t,n,r,a,i,o,c,l,d,u,h,g,v,f,_,C=[],P=new Map;n=new de({props:{size:1,weight:"regular",color:"secondary",$$slots:{default:[yp]},$$scope:{ctx:s}}});let I=xe(s[0]);const A=j=>j[23].id;for(let j=0;j<I.length;j+=1){let B=ha(s,I,j),F=A(B);P.set(F,C[j]=fa(F,B))}let T=(s[3]==="add"||s[3]==="addJson")&&ga(s);v=new Xe({props:{disabled:s[5],color:"neutral",variant:"soft",size:1,$$slots:{iconLeft:[wp],default:[_p]},$$scope:{ctx:s}}}),v.$on("click",s[20]);let N=s[1]&&va(s);return{c(){e=M("div"),t=M("div"),b(n.$$.fragment),r=E(),a=M("div"),i=U(`Configure a new Model Context Protocol server to connect Augment to custom tools. Find out more
    about MCP `),o=M("a"),c=U("in the docs"),l=U("."),d=E();for(let j=0;j<C.length;j+=1)C[j].c();u=E(),T&&T.c(),h=E(),g=M("div"),b(v.$$.fragment),f=E(),N&&N.c(),$(t,"class","section-heading svelte-1vnq4q3"),$(o,"href",s[13]),$(a,"class","description-text svelte-1vnq4q3"),$(e,"class","mcp-servers svelte-1vnq4q3"),$(g,"class","add-mcp-button-container svelte-1vnq4q3")},m(j,B){w(j,e,B),x(e,t),S(n,t,null),x(e,r),x(e,a),x(a,i),x(a,o),x(o,c),x(a,l),x(e,d);for(let F=0;F<C.length;F+=1)C[F]&&C[F].m(e,null);w(j,u,B),T&&T.m(j,B),w(j,h,B),w(j,g,B),S(v,g,null),x(g,f),N&&N.m(g,null),_=!0},p(j,[B]){const F={};67108864&B&&(F.$$scope={dirty:B,ctx:j}),n.$set(F),8149&B&&(I=xe(j[0]),K(),C=Ot(C,B,A,1,j,I,P,e,Et,fa,null,ha),W()),j[3]==="add"||j[3]==="addJson"?T?(T.p(j,B),8&B&&p(T,1)):(T=ga(j),T.c(),p(T,1),T.m(h.parentNode,h)):T&&(K(),m(T,1,1,()=>{T=null}),W());const Y={};32&B&&(Y.disabled=j[5]),67108864&B&&(Y.$$scope={dirty:B,ctx:j}),v.$set(Y),j[1]?N?(N.p(j,B),2&B&&p(N,1)):(N=va(j),N.c(),p(N,1),N.m(g,null)):N&&(K(),m(N,1,1,()=>{N=null}),W())},i(j){if(!_){p(n.$$.fragment,j);for(let B=0;B<I.length;B+=1)p(C[B]);p(T),p(v.$$.fragment,j),p(N),_=!0}},o(j){m(n.$$.fragment,j);for(let B=0;B<C.length;B+=1)m(C[B]);m(T),m(v.$$.fragment,j),m(N),_=!1},d(j){j&&(y(e),y(u),y(h),y(g)),k(n);for(let B=0;B<C.length;B+=1)C[B].d();T&&T.d(j),k(v),N&&N.d()}}}function kp(s,e,t){let n,r,{servers:a}=e,{onMCPServerAdd:i}=e,{onMCPServerSave:o}=e,{onMCPServerDelete:c}=e,{onMCPServerToggleDisable:l}=e,{onCancel:d}=e,{onMCPServerJSONImport:u}=e,{isMCPImportEnabled:h=!0}=e,g=null,v=null;function f(N){return async function(...j){const B=await N(...j);return t(3,v=null),t(2,g=null),B}}const _=f(i),C=f(o),P=f(u),I=f(c),A=f(l),T=$p.get("mcpDocsURL");return s.$$set=N=>{"servers"in N&&t(0,a=N.servers),"onMCPServerAdd"in N&&t(14,i=N.onMCPServerAdd),"onMCPServerSave"in N&&t(15,o=N.onMCPServerSave),"onMCPServerDelete"in N&&t(16,c=N.onMCPServerDelete),"onMCPServerToggleDisable"in N&&t(17,l=N.onMCPServerToggleDisable),"onCancel"in N&&t(18,d=N.onCancel),"onMCPServerJSONImport"in N&&t(19,u=N.onMCPServerJSONImport),"isMCPImportEnabled"in N&&t(1,h=N.isMCPImportEnabled)},s.$$.update=()=>{12&s.$$.dirty&&t(5,n=v==="add"||v==="addJson"||g!==null),1&s.$$.dirty&&t(4,r=Ks.parseServerValidationMessages(a))},[a,h,g,v,r,n,function(){t(2,g=null),t(3,v=null),d==null||d()},function(N){t(2,g=N.id)},_,C,P,I,A,T,i,o,c,l,d,u,()=>{t(3,v="add")},()=>{t(3,v="addJson")}]}class Cp extends fe{constructor(e){super(),ge(this,e,kp,Sp,he,{servers:0,onMCPServerAdd:14,onMCPServerSave:15,onMCPServerDelete:16,onMCPServerToggleDisable:17,onCancel:18,onMCPServerJSONImport:19,isMCPImportEnabled:1})}}function $a(s,e,t){const n=s.slice();return n[10]=e[t],n}function Tp(s){let e;return{c(){e=M("div"),e.textContent="Terminal",$(e,"class","section-heading-text")},m(t,n){w(t,e,n)},p:H,d(t){t&&y(e)}}}function Mp(s){let e;return{c(){e=U("Shell")},m(t,n){w(t,e,n)},d(t){t&&y(e)}}}function Ap(s){let e;return{c(){e=U("Select a shell")},m(t,n){w(t,e,n)},p:H,d(t){t&&y(e)}}}function Np(s){let e;return{c(){e=U("No shells available")},m(t,n){w(t,e,n)},p:H,d(t){t&&y(e)}}}function Zp(s){let e,t;function n(i,o){return 2&o&&(e=null),e==null&&(e=!!i[5](i[1])),e?Ep:Op}let r=n(s,-1),a=r(s);return{c(){a.c(),t=Te()},m(i,o){a.m(i,o),w(i,t,o)},p(i,o){r===(r=n(i,o))&&a?a.p(i,o):(a.d(1),a=r(i),a&&(a.c(),a.m(t.parentNode,t)))},d(i){i&&y(t),a.d(i)}}}function Op(s){let e;return{c(){e=U(s[1])},m(t,n){w(t,e,n)},p(t,n){2&n&&we(e,t[1])},d(t){t&&y(e)}}}function Ep(s){var n;let e,t=(((n=s[5](s[1]))==null?void 0:n.friendlyName)||s[1])+"";return{c(){e=U(t)},m(r,a){w(r,e,a)},p(r,a){var i;2&a&&t!==(t=(((i=r[5](r[1]))==null?void 0:i.friendlyName)||r[1])+"")&&we(e,t)},d(r){r&&y(e)}}}function ya(s){var r;let e,t,n=((r=s[5](s[1]))==null?void 0:r.supportString)+"";return{c(){e=M("div"),t=U(n),$(e,"class","shell-description svelte-zio0bh")},m(a,i){w(a,e,i),x(e,t)},p(a,i){var o;2&i&&n!==(n=((o=a[5](a[1]))==null?void 0:o.supportString)+"")&&we(t,n)},d(a){a&&y(e)}}}function Ip(s){var f;let e,t,n,r,a,i,o,c,l,d=s[1]&&s[0].length>0&&((f=s[5](s[1]))==null?void 0:f.supportString);function u(_,C){return _[1]&&_[0].length>0?Zp:_[0].length===0?Np:Ap}let h=u(s),g=h(s),v=d&&ya(s);return c=new Ji({}),{c(){e=M("div"),t=M("div"),n=M("div"),r=M("div"),g.c(),a=E(),v&&v.c(),i=E(),o=M("div"),b(c.$$.fragment),$(r,"class","shell-name svelte-zio0bh"),$(n,"class","shell-display svelte-zio0bh"),$(t,"class","dropdown-content svelte-zio0bh"),$(o,"class","dropdown-icon svelte-zio0bh"),$(e,"class","shell-dropdown-trigger svelte-zio0bh"),$e(e,"disabled",s[0].length===0)},m(_,C){w(_,e,C),x(e,t),x(t,n),x(n,r),g.m(r,null),x(n,a),v&&v.m(n,null),x(e,i),x(e,o),S(c,o,null),l=!0},p(_,C){var P;h===(h=u(_))&&g?g.p(_,C):(g.d(1),g=h(_),g&&(g.c(),g.m(r,null))),3&C&&(d=_[1]&&_[0].length>0&&((P=_[5](_[1]))==null?void 0:P.supportString)),d?v?v.p(_,C):(v=ya(_),v.c(),v.m(n,null)):v&&(v.d(1),v=null),(!l||1&C)&&$e(e,"disabled",_[0].length===0)},i(_){l||(p(c.$$.fragment,_),l=!0)},o(_){m(c.$$.fragment,_),l=!1},d(_){_&&y(e),g.d(),v&&v.d(),k(c)}}}function Pp(s){let e,t;return e=new Ce.Label({props:{$$slots:{default:[Rp]},$$scope:{ctx:s}}}),{c(){b(e.$$.fragment)},m(n,r){S(e,n,r),t=!0},p(n,r){const a={};8192&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function jp(s){let e,t,n=[],r=new Map,a=xe(s[0]);const i=o=>o[10].friendlyName;for(let o=0;o<a.length;o+=1){let c=$a(s,a,o),l=i(c);r.set(l,n[o]=wa(l,c))}return{c(){for(let o=0;o<n.length;o+=1)n[o].c();e=Te()},m(o,c){for(let l=0;l<n.length;l+=1)n[l]&&n[l].m(o,c);w(o,e,c),t=!0},p(o,c){27&c&&(a=xe(o[0]),K(),n=Ot(n,c,i,1,o,a,r,e.parentNode,Et,wa,e,$a),W())},i(o){if(!t){for(let c=0;c<a.length;c+=1)p(n[c]);t=!0}},o(o){for(let c=0;c<n.length;c+=1)m(n[c]);t=!1},d(o){o&&y(e);for(let c=0;c<n.length;c+=1)n[c].d(o)}}}function Rp(s){let e;return{c(){e=U("No shells available")},m(t,n){w(t,e,n)},d(t){t&&y(e)}}}function _a(s){let e,t,n=s[10].supportString+"";return{c(){e=M("div"),t=U(n),$(e,"class","shell-description svelte-zio0bh")},m(r,a){w(r,e,a),x(e,t)},p(r,a){1&a&&n!==(n=r[10].supportString+"")&&we(t,n)},d(r){r&&y(e)}}}function Lp(s){let e,t,n,r,a,i=s[10].friendlyName+"",o=s[10].supportString&&_a(s);return{c(){e=M("div"),t=M("div"),n=U(i),r=E(),o&&o.c(),a=E(),$(t,"class","shell-name svelte-zio0bh"),$(e,"class","shell-display svelte-zio0bh")},m(c,l){w(c,e,l),x(e,t),x(t,n),x(e,r),o&&o.m(e,null),w(c,a,l)},p(c,l){1&l&&i!==(i=c[10].friendlyName+"")&&we(n,i),c[10].supportString?o?o.p(c,l):(o=_a(c),o.c(),o.m(e,null)):o&&(o.d(1),o=null)},d(c){c&&(y(e),y(a)),o&&o.d()}}}function wa(s,e){let t,n,r;function a(){return e[8](e[10])}return n=new Ce.Item({props:{onSelect:a,highlight:e[1]===e[10].friendlyName,$$slots:{default:[Lp]},$$scope:{ctx:e}}}),{key:s,first:null,c(){t=Te(),b(n.$$.fragment),this.first=t},m(i,o){w(i,t,o),S(n,i,o),r=!0},p(i,o){e=i;const c={};25&o&&(c.onSelect=a),3&o&&(c.highlight=e[1]===e[10].friendlyName),8193&o&&(c.$$scope={dirty:o,ctx:e}),n.$set(c)},i(i){r||(p(n.$$.fragment,i),r=!0)},o(i){m(n.$$.fragment,i),r=!1},d(i){i&&y(t),k(n,i)}}}function Fp(s){let e,t,n,r;const a=[jp,Pp],i=[];function o(c,l){return c[0].length>0?0:1}return e=o(s),t=i[e]=a[e](s),{c(){t.c(),n=Te()},m(c,l){i[e].m(c,l),w(c,n,l),r=!0},p(c,l){let d=e;e=o(c),e===d?i[e].p(c,l):(K(),m(i[d],1,1,()=>{i[d]=null}),W(),t=i[e],t?t.p(c,l):(t=i[e]=a[e](c),t.c()),p(t,1),t.m(n.parentNode,n))},i(c){r||(p(t),r=!0)},o(c){m(t),r=!1},d(c){c&&y(n),i[e].d(c)}}}function zp(s){let e,t,n,r;return e=new Ce.Trigger({props:{$$slots:{default:[Ip]},$$scope:{ctx:s}}}),n=new Ce.Content({props:{side:"bottom",align:"start",$$slots:{default:[Fp]},$$scope:{ctx:s}}}),{c(){b(e.$$.fragment),t=E(),b(n.$$.fragment)},m(a,i){S(e,a,i),w(a,t,i),S(n,a,i),r=!0},p(a,i){const o={};8195&i&&(o.$$scope={dirty:i,ctx:a}),e.$set(o);const c={};8219&i&&(c.$$scope={dirty:i,ctx:a}),n.$set(c)},i(a){r||(p(e.$$.fragment,a),p(n.$$.fragment,a),r=!0)},o(a){m(e.$$.fragment,a),m(n.$$.fragment,a),r=!1},d(a){a&&y(t),k(e,a),k(n,a)}}}function Dp(s){let e;return{c(){e=U("Startup script")},m(t,n){w(t,e,n)},d(t){t&&y(e)}}}function Up(s){let e;return{c(){e=U("Code to run whenever a new terminal is opened")},m(t,n){w(t,e,n)},d(t){t&&y(e)}}}function Vp(s){let e,t,n,r,a,i,o,c,l,d,u,h,g,v,f,_,C,P,I,A,T,N,j;function B(Y){s[9](Y)}n=new de({props:{size:1,weight:"regular",color:"secondary",$$slots:{default:[Tp]},$$scope:{ctx:s}}}),o=new de({props:{size:1,weight:"medium",$$slots:{default:[Mp]},$$scope:{ctx:s}}});let F={$$slots:{default:[zp]},$$scope:{ctx:s}};return s[4]!==void 0&&(F.requestClose=s[4]),d=new Ce.Root({props:F}),Ke.push(()=>We(d,"requestClose",B)),v=new de({props:{size:1,weight:"medium",$$slots:{default:[Dp]},$$scope:{ctx:s}}}),C=new de({props:{size:1,color:"secondary",$$slots:{default:[Up]},$$scope:{ctx:s}}}),{c(){e=M("div"),t=M("div"),b(n.$$.fragment),r=E(),a=M("div"),i=M("div"),b(o.$$.fragment),c=E(),l=M("div"),b(d.$$.fragment),h=E(),g=M("div"),b(v.$$.fragment),f=E(),_=M("div"),b(C.$$.fragment),P=E(),I=M("textarea"),$(t,"class","section-heading svelte-zio0bh"),$(l,"class","dropdown-container svelte-zio0bh"),$(i,"class","shell-selector svelte-zio0bh"),$(_,"class","startup-script-description svelte-zio0bh"),$(I,"class","startup-script-textarea svelte-zio0bh"),$(I,"placeholder","Enter shell commands to run on terminal startup"),I.value=A=s[2]||"",$(g,"class","startup-script-container svelte-zio0bh"),$(a,"class","terminal-content svelte-zio0bh"),$(e,"class","terminal-settings svelte-zio0bh")},m(Y,ce){w(Y,e,ce),x(e,t),S(n,t,null),x(e,r),x(e,a),x(a,i),S(o,i,null),x(i,c),x(i,l),S(d,l,null),x(a,h),x(a,g),S(v,g,null),x(g,f),x(g,_),S(C,_,null),x(g,P),x(g,I),T=!0,N||(j=ze(I,"change",s[6]),N=!0)},p(Y,[ce]){const G={};8192&ce&&(G.$$scope={dirty:ce,ctx:Y}),n.$set(G);const le={};8192&ce&&(le.$$scope={dirty:ce,ctx:Y}),o.$set(le);const J={};8219&ce&&(J.$$scope={dirty:ce,ctx:Y}),!u&&16&ce&&(u=!0,J.requestClose=Y[4],Ye(()=>u=!1)),d.$set(J);const me={};8192&ce&&(me.$$scope={dirty:ce,ctx:Y}),v.$set(me);const _t={};8192&ce&&(_t.$$scope={dirty:ce,ctx:Y}),C.$set(_t),(!T||4&ce&&A!==(A=Y[2]||""))&&(I.value=A)},i(Y){T||(p(n.$$.fragment,Y),p(o.$$.fragment,Y),p(d.$$.fragment,Y),p(v.$$.fragment,Y),p(C.$$.fragment,Y),T=!0)},o(Y){m(n.$$.fragment,Y),m(o.$$.fragment,Y),m(d.$$.fragment,Y),m(v.$$.fragment,Y),m(C.$$.fragment,Y),T=!1},d(Y){Y&&y(e),k(n),k(o),k(d),k(v),k(C),N=!1,j()}}}function qp(s,e,t){let n,{supportedShells:r=[]}=e,{selectedShell:a}=e,{startupScript:i}=e,{onShellSelect:o}=e,{onStartupScriptChange:c}=e;return s.$$set=l=>{"supportedShells"in l&&t(0,r=l.supportedShells),"selectedShell"in l&&t(1,a=l.selectedShell),"startupScript"in l&&t(2,i=l.startupScript),"onShellSelect"in l&&t(3,o=l.onShellSelect),"onStartupScriptChange"in l&&t(7,c=l.onStartupScriptChange)},[r,a,i,o,n,function(l){return r.find(d=>d.friendlyName===l)},function(l){const d=l.target;c(d.value)},c,l=>{o(l.friendlyName),n()},function(l){n=l,t(4,n)}]}class Bp extends fe{constructor(e){super(),ge(this,e,qp,Vp,he,{supportedShells:0,selectedShell:1,startupScript:2,onShellSelect:3,onStartupScriptChange:7})}}function xa(s){let e,t;return e=new Cp({props:{servers:s[1],onMCPServerAdd:s[7],onMCPServerSave:s[8],onMCPServerDelete:s[9],onMCPServerToggleDisable:s[10],onMCPServerJSONImport:s[11],onCancel:s[12],isMCPImportEnabled:s[3]}}),{c(){b(e.$$.fragment)},m(n,r){S(e,n,r),t=!0},p(n,r){const a={};2&r&&(a.servers=n[1]),128&r&&(a.onMCPServerAdd=n[7]),256&r&&(a.onMCPServerSave=n[8]),512&r&&(a.onMCPServerDelete=n[9]),1024&r&&(a.onMCPServerToggleDisable=n[10]),2048&r&&(a.onMCPServerJSONImport=n[11]),4096&r&&(a.onCancel=n[12]),8&r&&(a.isMCPImportEnabled=n[3]),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function ba(s){let e,t;return e=new Bp({props:{supportedShells:s[13],selectedShell:s[14],startupScript:s[15],onShellSelect:s[16],onStartupScriptChange:s[17]}}),{c(){b(e.$$.fragment)},m(n,r){S(e,n,r),t=!0},p(n,r){const a={};8192&r&&(a.supportedShells=n[13]),16384&r&&(a.selectedShell=n[14]),32768&r&&(a.startupScript=n[15]),65536&r&&(a.onShellSelect=n[16]),131072&r&&(a.onStartupScriptChange=n[17]),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Jp(s){let e,t,n,r,a;t=new hd({props:{title:"Services",tools:s[0],onAuthenticate:s[5],onRevokeAccess:s[6]}});let i=s[2]&&xa(s),o=s[4]&&ba(s);return{c(){e=M("div"),b(t.$$.fragment),n=E(),i&&i.c(),r=E(),o&&o.c(),$(e,"class","c-settings-tools svelte-181yusq")},m(c,l){w(c,e,l),S(t,e,null),x(e,n),i&&i.m(e,null),x(e,r),o&&o.m(e,null),a=!0},p(c,[l]){const d={};1&l&&(d.tools=c[0]),32&l&&(d.onAuthenticate=c[5]),64&l&&(d.onRevokeAccess=c[6]),t.$set(d),c[2]?i?(i.p(c,l),4&l&&p(i,1)):(i=xa(c),i.c(),p(i,1),i.m(e,r)):i&&(K(),m(i,1,1,()=>{i=null}),W()),c[4]?o?(o.p(c,l),16&l&&p(o,1)):(o=ba(c),o.c(),p(o,1),o.m(e,null)):o&&(K(),m(o,1,1,()=>{o=null}),W())},i(c){a||(p(t.$$.fragment,c),p(i),p(o),a=!0)},o(c){m(t.$$.fragment,c),m(i),m(o),a=!1},d(c){c&&y(e),k(t),i&&i.d(),o&&o.d()}}}function Gp(s,e,t){let{tools:n=[]}=e,{servers:r=[]}=e,{isMCPEnabled:a=!0}=e,{isMCPImportEnabled:i=!0}=e,{isTerminalEnabled:o=!0}=e,{onAuthenticate:c}=e,{onRevokeAccess:l}=e,{onMCPServerAdd:d}=e,{onMCPServerSave:u}=e,{onMCPServerDelete:h}=e,{onMCPServerToggleDisable:g}=e,{onMCPServerJSONImport:v}=e,{onCancel:f}=e,{supportedShells:_=[]}=e,{selectedShell:C}=e,{startupScript:P}=e,{onShellSelect:I=()=>{}}=e,{onStartupScriptChange:A=()=>{}}=e;return s.$$set=T=>{"tools"in T&&t(0,n=T.tools),"servers"in T&&t(1,r=T.servers),"isMCPEnabled"in T&&t(2,a=T.isMCPEnabled),"isMCPImportEnabled"in T&&t(3,i=T.isMCPImportEnabled),"isTerminalEnabled"in T&&t(4,o=T.isTerminalEnabled),"onAuthenticate"in T&&t(5,c=T.onAuthenticate),"onRevokeAccess"in T&&t(6,l=T.onRevokeAccess),"onMCPServerAdd"in T&&t(7,d=T.onMCPServerAdd),"onMCPServerSave"in T&&t(8,u=T.onMCPServerSave),"onMCPServerDelete"in T&&t(9,h=T.onMCPServerDelete),"onMCPServerToggleDisable"in T&&t(10,g=T.onMCPServerToggleDisable),"onMCPServerJSONImport"in T&&t(11,v=T.onMCPServerJSONImport),"onCancel"in T&&t(12,f=T.onCancel),"supportedShells"in T&&t(13,_=T.supportedShells),"selectedShell"in T&&t(14,C=T.selectedShell),"startupScript"in T&&t(15,P=T.startupScript),"onShellSelect"in T&&t(16,I=T.onShellSelect),"onStartupScriptChange"in T&&t(17,A=T.onStartupScriptChange)},[n,r,a,i,o,c,l,d,u,h,g,v,f,_,C,P,I,A]}class Hp extends fe{constructor(e){super(),ge(this,e,Gp,Jp,he,{tools:0,servers:1,isMCPEnabled:2,isMCPImportEnabled:3,isTerminalEnabled:4,onAuthenticate:5,onRevokeAccess:6,onMCPServerAdd:7,onMCPServerSave:8,onMCPServerDelete:9,onMCPServerToggleDisable:10,onMCPServerJSONImport:11,onCancel:12,supportedShells:13,selectedShell:14,startupScript:15,onShellSelect:16,onStartupScriptChange:17})}}function Kp(s){let e,t,n=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},s[0]],r={};for(let a=0;a<n.length;a+=1)r=Se(r,n[a]);return{c(){e=Fe("svg"),t=new zn(!0),this.h()},l(a){e=Dn(a,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var i=Un(e);t=Vn(i,!0),i.forEach(y),this.h()},h(){t.a=null,lt(e,r)},m(a,i){qn(a,e,i),t.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M63.2 379.3c-6.2-6.2-6.2-16.4 0-22.6l39.4-39.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 41.4-41.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 41.4-41.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 41.4-41.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 39.4-39.4c6.2-6.2 16.4-6.2 22.6 0l69.5 69.5c6.2 6.2 6.2 16.4 0 22.6L155.3 448.8c-6.2 6.2-16.4 6.2-22.6 0zm35.5 103.4c25 25 65.5 25 90.5 0l293.5-293.4c25-25 25-65.5 0-90.5l-69.4-69.5c-25-25-65.5-25-90.5 0L29.3 322.7c-25 25-25 65.5 0 90.5l69.5 69.5z"/>',e)},p(a,[i]){lt(e,r=yt(n,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&i&&a[0]]))},i:H,o:H,d(a){a&&y(e)}}}function Wp(s,e,t){return s.$$set=n=>{t(0,e=Se(Se({},e),He(n)))},[e=He(e)]}class Yp extends fe{constructor(e){super(),ge(this,e,Wp,Kp,he,{})}}function Xp(s){let e,t,n,r;function a(o){s[6](o)}let i={placeholder:"Add your guidelines for Augment Chat...",resize:"vertical",saveFunction:s[3]};return s[0]!==void 0&&(i.value=s[0]),t=new oo({props:i}),Ke.push(()=>We(t,"value",a)),t.$on("focus",s[7]),{c(){e=M("div"),b(t.$$.fragment),$(e,"class","c-user-guidelines-category__input svelte-10borzo")},m(o,c){w(o,e,c),S(t,e,null),r=!0},p(o,[c]){const l={};!n&&1&c&&(n=!0,l.value=o[0],Ye(()=>n=!1)),t.$set(l)},i(o){r||(p(t.$$.fragment,o),r=!0)},o(o){m(t.$$.fragment,o),r=!1},d(o){o&&y(e),k(t)}}}function Qp(s,e,t){let n;const r=Si();let{userGuidelines:a=""}=e,{userGuidelinesLengthLimit:i}=e,{updateUserGuideline:o=()=>!1}=e;const c=Le(void 0);function l(){const d=a.trim();if(n!==d){if(!o(d))throw i&&d.length>i?`The user guideline must be less than ${i} character long`:"An error occurred updating the user";sr(c,n=d,n)}}return bt(s,c,d=>t(8,n=d)),Ia(()=>{sr(c,n=a.trim(),n)}),Pa(()=>{l()}),s.$$set=d=>{"userGuidelines"in d&&t(0,a=d.userGuidelines),"userGuidelinesLengthLimit"in d&&t(4,i=d.userGuidelinesLengthLimit),"updateUserGuideline"in d&&t(5,o=d.updateUserGuideline)},[a,r,c,l,i,o,function(d){a=d,t(0,a)},d=>{r("focus",d)}]}class vi extends fe{constructor(e){super(),ge(this,e,Qp,Xp,he,{userGuidelines:0,userGuidelinesLengthLimit:4,updateUserGuideline:5})}}class em{constructor(e,t){ve(this,"_rulesFiles",Le([]));ve(this,"_loading",Le(!0));ve(this,"_extensionClient");this._host=e,this._msgBroker=t,this.requestRules();const n=new lo;this._extensionClient=new uo(e,t,n)}handleMessageFromExtension(e){if(e.data&&e.data.type===ae.getRulesListResponse)return this._rulesFiles.set(e.data.data),this._loading.set(!1),!0;if(e.data&&e.data.type===ae.createRuleResponse&&this._extensionClient.reportAgentSessionEvent({eventName:Ts.rulesImported,conversationId:"",eventData:{rulesImportedData:{type:Wn.manuallyCreated,numFiles:1}}}),e.data&&e.data.type===ae.autoImportRulesResponse&&e.data.data&&this._extensionClient.reportAgentSessionEvent({eventName:Ts.rulesImported,conversationId:"",eventData:{rulesImportedData:{type:Wn.auto,numFiles:e.data.data.importedRulesCount,source:e.data.data.source}}}),e.data&&e.data.type===ae.triggerImportDialogResponse&&e.data.data){this.requestRules();const t=e.data.data.directoryOrFile==="directory"?Wn.selectedDirectory:Wn.selectedFile;return this._extensionClient.reportAgentSessionEvent({eventName:Ts.rulesImported,conversationId:"",eventData:{rulesImportedData:{type:t,numFiles:e.data.data.importedRulesCount}}}),!0}return!1}requestRules(){this._loading.set(!0),this._host.postMessage({type:ae.getRulesListRequest})}createRule(){this._host.postMessage({type:ae.createRule})}openRule(e){this._host.postMessage({type:ae.openRule,data:{path:e}})}updateRuleType(e,t,n){const r=Ki.updateAlwaysApplyFrontmatterKey(t,n);this._extensionClient.saveFile({repoRoot:"",pathName:e,content:r})}deleteRule(e){this._host.postMessage({type:ae.deleteRule,data:{path:e}})}autoImportRules(){this._host.postMessage({type:ae.autoImportRules})}selectFileToImport(){this._host.postMessage({type:ae.triggerImportDialog})}importRule(e){this._host.postMessage({type:ae.importFileRequest,data:{filename:e,autoImport:!0}})}importDirectoryContents(e){this._host.postMessage({type:ae.importDirectoryRequest,data:{directoryPath:e}})}getRulesFiles(){return this._rulesFiles}getLoading(){return this._loading}}function Sa(s,e,t){const n=s.slice();return n[20]=e[t],n}function ka(s,e,t){const n=s.slice();return n[23]=e[t],n}function tm(s){let e;return{c(){e=U("Rules")},m(t,n){w(t,e,n)},d(t){t&&y(e)}}}function nm(s){let e;return{c(){e=U("Learn more")},m(t,n){w(t,e,n)},d(t){t&&y(e)}}}function sm(s){let e,t,n=[],r=new Map,a=xe(s[5]);const i=o=>o[23].path;for(let o=0;o<a.length;o+=1){let c=ka(s,a,o),l=i(c);r.set(l,n[o]=Ca(l,c))}return{c(){for(let o=0;o<n.length;o+=1)n[o].c();e=Te()},m(o,c){for(let l=0;l<n.length;l+=1)n[l]&&n[l].m(o,c);w(o,e,c),t=!0},p(o,c){288&c&&(a=xe(o[5]),K(),n=Ot(n,c,i,1,o,a,r,e.parentNode,Et,Ca,e,ka),W())},i(o){if(!t){for(let c=0;c<a.length;c+=1)p(n[c]);t=!0}},o(o){for(let c=0;c<n.length;c+=1)m(n[c]);t=!1},d(o){o&&y(e);for(let c=0;c<n.length;c+=1)n[c].d(o)}}}function rm(s){let e,t,n;return t=new de({props:{size:1,color:"neutral",$$slots:{default:[dm]},$$scope:{ctx:s}}}),{c(){e=M("div"),b(t.$$.fragment),$(e,"class","c-rules-list-empty svelte-11i1bw2")},m(r,a){w(r,e,a),S(t,e,null),n=!0},p(r,a){const i={};67108864&a&&(i.$$scope={dirty:a,ctx:r}),t.$set(i)},i(r){n||(p(t.$$.fragment,r),n=!0)},o(r){m(t.$$.fragment,r),n=!1},d(r){r&&y(e),k(t)}}}function am(s){let e,t=s[23].path+"";return{c(){e=U(t)},m(n,r){w(n,e,r)},p(n,r){32&r&&t!==(t=n[23].path+"")&&we(e,t)},d(n){n&&y(e)}}}function im(s){let e,t,n,r,a,i,o;return t=new Ti({}),a=new de({props:{size:1,color:"neutral",$$slots:{default:[am]},$$scope:{ctx:s}}}),{c(){e=M("div"),b(t.$$.fragment),n=E(),r=M("div"),b(a.$$.fragment),i=E(),$(r,"class","c-rule-item-path svelte-11i1bw2"),$(e,"class","c-rule-item-info svelte-11i1bw2"),$(e,"slot","header-left")},m(c,l){w(c,e,l),S(t,e,null),x(e,n),x(e,r),S(a,r,null),x(e,i),o=!0},p(c,l){const d={};67108896&l&&(d.$$scope={dirty:l,ctx:c}),a.$set(d)},i(c){o||(p(t.$$.fragment,c),p(a.$$.fragment,c),o=!0)},o(c){m(t.$$.fragment,c),m(a.$$.fragment,c),o=!1},d(c){c&&y(e),k(t),k(a)}}}function om(s){let e,t;return e=new co({props:{slot:"iconRight"}}),{c(){b(e.$$.fragment)},m(n,r){S(e,n,r),t=!0},p:H,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function cm(s){let e,t;return e=new Gi({props:{slot:"iconRight"}}),{c(){b(e.$$.fragment)},m(n,r){S(e,n,r),t=!0},p:H,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function lm(s){let e,t,n,r,a,i,o,c,l;function d(...u){return s[12](s[23],...u)}return n=new po({props:{onSave:d,alwaysApply:s[23].type===ar.ALWAYS_ATTACHED}}),a=new Xe({props:{size:1,variant:"ghost-block",color:"neutral",class:"c-rule-item-button",$$slots:{iconRight:[om]},$$scope:{ctx:s}}}),a.$on("click",function(...u){return s[13](s[23],...u)}),o=new Xe({props:{size:1,variant:"ghost-block",color:"neutral",class:"c-rule-item-button",$$slots:{iconRight:[cm]},$$scope:{ctx:s}}}),o.$on("click",function(...u){return s[14](s[23],...u)}),{c(){e=M("div"),t=M("div"),b(n.$$.fragment),r=E(),b(a.$$.fragment),i=E(),b(o.$$.fragment),c=E(),$(t,"class","status-controls svelte-11i1bw2"),$(e,"class","server-actions"),$(e,"slot","header-right")},m(u,h){w(u,e,h),x(e,t),S(n,t,null),x(t,r),S(a,t,null),x(t,i),S(o,t,null),x(e,c),l=!0},p(u,h){s=u;const g={};32&h&&(g.onSave=d),32&h&&(g.alwaysApply=s[23].type===ar.ALWAYS_ATTACHED),n.$set(g);const v={};67108864&h&&(v.$$scope={dirty:h,ctx:s}),a.$set(v);const f={};67108864&h&&(f.$$scope={dirty:h,ctx:s}),o.$set(f)},i(u){l||(p(n.$$.fragment,u),p(a.$$.fragment,u),p(o.$$.fragment,u),l=!0)},o(u){m(n.$$.fragment,u),m(a.$$.fragment,u),m(o.$$.fragment,u),l=!1},d(u){u&&y(e),k(n),k(a),k(o)}}}function Ca(s,e){let t,n,r;return n=new ni({props:{isClickable:!0,$$slots:{"header-right":[lm],"header-left":[im]},$$scope:{ctx:e}}}),n.$on("click",function(){return e[15](e[23])}),{key:s,first:null,c(){t=Te(),b(n.$$.fragment),this.first=t},m(a,i){w(a,t,i),S(n,a,i),r=!0},p(a,i){e=a;const o={};67108896&i&&(o.$$scope={dirty:i,ctx:e}),n.$set(o)},i(a){r||(p(n.$$.fragment,a),r=!0)},o(a){m(n.$$.fragment,a),r=!1},d(a){a&&y(t),k(n,a)}}}function dm(s){let e;return{c(){e=U("No rules files found")},m(t,n){w(t,e,n)},d(t){t&&y(e)}}}function um(s){let e,t,n,r,a;return t=new _s({}),{c(){e=M("div"),b(t.$$.fragment),n=E(),r=M("span"),r.textContent="Create new rule file",$(e,"class","c-rules-actions-button-content svelte-11i1bw2")},m(i,o){w(i,e,o),S(t,e,null),x(e,n),x(e,r),a=!0},p:H,i(i){a||(p(t.$$.fragment,i),a=!0)},o(i){m(t.$$.fragment,i),a=!1},d(i){i&&y(e),k(t)}}}function pm(s){let e,t,n,r,a,i,o;return t=new gi({}),i=new mo({}),{c(){e=M("div"),b(t.$$.fragment),n=E(),r=M("span"),r.textContent="Import rules",a=E(),b(i.$$.fragment),$(e,"class","c-rules-actions-button-content svelte-11i1bw2")},m(c,l){w(c,e,l),S(t,e,null),x(e,n),x(e,r),x(e,a),S(i,e,null),o=!0},p:H,i(c){o||(p(t.$$.fragment,c),p(i.$$.fragment,c),o=!0)},o(c){m(t.$$.fragment,c),m(i.$$.fragment,c),o=!1},d(c){c&&y(e),k(t),k(i)}}}function mm(s){let e,t;return e=new Xe({props:{size:1,variant:"soft",color:"neutral",class:"c-rules-action-button",$$slots:{default:[pm]},$$scope:{ctx:s}}}),{c(){b(e.$$.fragment)},m(n,r){S(e,n,r),t=!0},p(n,r){const a={};67108864&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function hm(s){let e,t=s[20].label+"";return{c(){e=U(t)},m(n,r){w(n,e,r)},p:H,d(n){n&&y(e)}}}function Ta(s,e){let t,n,r;return n=new Ce.Item({props:{onSelect:function(){return e[17](e[20])},$$slots:{default:[hm]},$$scope:{ctx:e}}}),{key:s,first:null,c(){t=Te(),b(n.$$.fragment),this.first=t},m(a,i){w(a,t,i),S(n,a,i),r=!0},p(a,i){e=a;const o={};67108864&i&&(o.$$scope={dirty:i,ctx:e}),n.$set(o)},i(a){r||(p(n.$$.fragment,a),r=!0)},o(a){m(n.$$.fragment,a),r=!1},d(a){a&&y(t),k(n,a)}}}function Ma(s){let e,t,n,r;return e=new Ce.Separator({}),n=new Ce.Label({props:{$$slots:{default:[fm]},$$scope:{ctx:s}}}),{c(){b(e.$$.fragment),t=E(),b(n.$$.fragment)},m(a,i){S(e,a,i),w(a,t,i),S(n,a,i),r=!0},p(a,i){const o={};67108928&i&&(o.$$scope={dirty:i,ctx:a}),n.$set(o)},i(a){r||(p(e.$$.fragment,a),p(n.$$.fragment,a),r=!0)},o(a){m(e.$$.fragment,a),m(n.$$.fragment,a),r=!1},d(a){a&&y(t),k(e,a),k(n,a)}}}function fm(s){let e,t=(s[6]!==void 0?s[10][s[6]].description:s[10][0])+"";return{c(){e=U(t)},m(n,r){w(n,e,r)},p(n,r){64&r&&t!==(t=(n[6]!==void 0?n[10][n[6]].description:n[10][0])+"")&&we(e,t)},d(n){n&&y(e)}}}function gm(s){let e,t,n,r=[],a=new Map,i=xe(s[10]);const o=l=>l[20].id;for(let l=0;l<i.length;l+=1){let d=Sa(s,i,l),u=o(d);a.set(u,r[l]=Ta(u,d))}let c=s[6]!==void 0&&Ma(s);return{c(){for(let l=0;l<r.length;l+=1)r[l].c();e=E(),c&&c.c(),t=Te()},m(l,d){for(let u=0;u<r.length;u+=1)r[u]&&r[u].m(l,d);w(l,e,d),c&&c.m(l,d),w(l,t,d),n=!0},p(l,d){3072&d&&(i=xe(l[10]),K(),r=Ot(r,d,o,1,l,i,a,e.parentNode,Et,Ta,e,Sa),W()),l[6]!==void 0?c?(c.p(l,d),64&d&&p(c,1)):(c=Ma(l),c.c(),p(c,1),c.m(t.parentNode,t)):c&&(K(),m(c,1,1,()=>{c=null}),W())},i(l){if(!n){for(let d=0;d<i.length;d+=1)p(r[d]);p(c),n=!0}},o(l){for(let d=0;d<r.length;d+=1)m(r[d]);m(c),n=!1},d(l){l&&(y(e),y(t));for(let d=0;d<r.length;d+=1)r[d].d(l);c&&c.d(l)}}}function vm(s){let e,t,n,r;return e=new Ce.Trigger({props:{$$slots:{default:[mm]},$$scope:{ctx:s}}}),n=new Ce.Content({props:{align:"start",side:"bottom",$$slots:{default:[gm]},$$scope:{ctx:s}}}),{c(){b(e.$$.fragment),t=E(),b(n.$$.fragment)},m(a,i){S(e,a,i),w(a,t,i),S(n,a,i),r=!0},p(a,i){const o={};67108864&i&&(o.$$scope={dirty:i,ctx:a}),e.$set(o);const c={};67108928&i&&(c.$$scope={dirty:i,ctx:a}),n.$set(c)},i(a){r||(p(e.$$.fragment,a),p(n.$$.fragment,a),r=!0)},o(a){m(e.$$.fragment,a),m(n.$$.fragment,a),r=!1},d(a){a&&y(t),k(e,a),k(n,a)}}}function $m(s){let e;return{c(){e=U("User Guidelines")},m(t,n){w(t,e,n)},d(t){t&&y(e)}}}function ym(s){let e;return{c(){e=U("Learn more")},m(t,n){w(t,e,n)},d(t){t&&y(e)}}}function _m(s){let e,t,n,r,a,i,o,c,l,d,u,h,g,v,f,_,C,P,I,A,T,N,j,B,F,Y,ce,G,le,J,me,_t;n=new de({props:{class:"c-section-header",size:3,color:"primary",$$slots:{default:[tm]},$$scope:{ctx:s}}}),c=new de({props:{size:1,weight:"regular",$$slots:{default:[nm]},$$scope:{ctx:s}}});const Ut=[rm,sm],Qe=[];function tn(re,ke){return re[5].length===0?0:1}function Vt(re){s[18](re)}function $i(re){s[19](re)}u=tn(s),h=Qe[u]=Ut[u](s),f=new Xe({props:{size:1,variant:"soft",color:"neutral",class:"c-rules-action-button",$$slots:{default:[um]},$$scope:{ctx:s}}}),f.$on("click",s[16]);let Ss={$$slots:{default:[vm]},$$scope:{ctx:s}};return s[4]!==void 0&&(Ss.requestClose=s[4]),s[3]!==void 0&&(Ss.focusedIndex=s[3]),C=new Ce.Root({props:Ss}),Ke.push(()=>We(C,"requestClose",Vt)),Ke.push(()=>We(C,"focusedIndex",$i)),N=new de({props:{class:"c-section-header",size:3,color:"primary",$$slots:{default:[$m]},$$scope:{ctx:s}}}),ce=new de({props:{size:1,weight:"regular",$$slots:{default:[ym]},$$scope:{ctx:s}}}),le=new vi({props:{userGuidelines:s[0],userGuidelinesLengthLimit:s[1],updateUserGuideline:s[2]}}),{c(){e=M("div"),t=M("div"),b(n.$$.fragment),r=E(),a=M("div"),i=U(`Rules are instructions for Augment Chat and Agent that can be applied automatically across all
      conversations or referenced in specific conversations using @mentions (e.g., @rule-file.md) `),o=M("a"),b(c.$$.fragment),l=E(),d=M("div"),h.c(),g=E(),v=M("div"),b(f.$$.fragment),_=E(),b(C.$$.fragment),A=E(),T=M("div"),b(N.$$.fragment),j=E(),B=M("div"),F=U(`User Guidelines allow you to control Augment's behavior through natural language instructions.
      These guidelines are applied globally to all Chat and Agent interactions. `),Y=M("a"),b(ce.$$.fragment),G=E(),b(le.$$.fragment),$(o,"href","https://docs.augmentcode.com/setup-augment/guidelines#workspace-guidelines"),$(o,"target","_blank"),$(d,"class","c-rules-list svelte-11i1bw2"),$(v,"class","c-rules-actions-container svelte-11i1bw2"),$(t,"class","c-rules-section svelte-11i1bw2"),$(Y,"href","https://docs.augmentcode.com/setup-augment/guidelines#workspace-guidelines"),$(Y,"target","_blank"),$(T,"class","c-user-guidelines-section svelte-11i1bw2"),$(e,"class","c-rules-category svelte-11i1bw2")},m(re,ke){w(re,e,ke),x(e,t),S(n,t,null),x(t,r),x(t,a),x(a,i),x(a,o),S(c,o,null),x(t,l),x(t,d),Qe[u].m(d,null),x(t,g),x(t,v),S(f,v,null),x(v,_),S(C,v,null),x(e,A),x(e,T),S(N,T,null),x(T,j),x(T,B),x(B,F),x(B,Y),S(ce,Y,null),x(T,G),S(le,T,null),J=!0,me||(_t=ze(window,"message",s[7].onMessageFromExtension),me=!0)},p(re,[ke]){const Ws={};67108864&ke&&(Ws.$$scope={dirty:ke,ctx:re}),n.$set(Ws);const Ys={};67108864&ke&&(Ys.$$scope={dirty:ke,ctx:re}),c.$set(Ys);let ks=u;u=tn(re),u===ks?Qe[u].p(re,ke):(K(),m(Qe[ks],1,1,()=>{Qe[ks]=null}),W(),h=Qe[u],h?h.p(re,ke):(h=Qe[u]=Ut[u](re),h.c()),p(h,1),h.m(d,null));const Xs={};67108864&ke&&(Xs.$$scope={dirty:ke,ctx:re}),f.$set(Xs);const Gn={};67108928&ke&&(Gn.$$scope={dirty:ke,ctx:re}),!P&&16&ke&&(P=!0,Gn.requestClose=re[4],Ye(()=>P=!1)),!I&&8&ke&&(I=!0,Gn.focusedIndex=re[3],Ye(()=>I=!1)),C.$set(Gn);const Qs={};67108864&ke&&(Qs.$$scope={dirty:ke,ctx:re}),N.$set(Qs);const er={};67108864&ke&&(er.$$scope={dirty:ke,ctx:re}),ce.$set(er);const Hn={};1&ke&&(Hn.userGuidelines=re[0]),2&ke&&(Hn.userGuidelinesLengthLimit=re[1]),4&ke&&(Hn.updateUserGuideline=re[2]),le.$set(Hn)},i(re){J||(p(n.$$.fragment,re),p(c.$$.fragment,re),p(h),p(f.$$.fragment,re),p(C.$$.fragment,re),p(N.$$.fragment,re),p(ce.$$.fragment,re),p(le.$$.fragment,re),J=!0)},o(re){m(n.$$.fragment,re),m(c.$$.fragment,re),m(h),m(f.$$.fragment,re),m(C.$$.fragment,re),m(N.$$.fragment,re),m(ce.$$.fragment,re),m(le.$$.fragment,re),J=!1},d(re){re&&y(e),k(n),k(c),Qe[u].d(),k(f),k(C),k(N),k(ce),k(le),me=!1,_t()}}}function wm(s,e,t){let n,r,a=H,i=()=>(a(),a=Bs(g,_=>t(6,r=_)),g);s.$$.on_destroy.push(()=>a());let{userGuidelines:o=""}=e,{userGuidelinesLengthLimit:c}=e,{updateUserGuideline:l=()=>!1}=e;const d=new ja(Ae),u=new em(Ae,d);d.registerConsumer(u);const h=u.getRulesFiles();bt(s,h,_=>t(5,n=_));let g;i();let v=()=>{};async function f(_){try{_.id==="select_file_or_directory"?u.selectFileToImport():_.id==="auto_import"&&u.autoImportRules()}catch(C){console.error("Failed to handle import select:",C)}v&&v()}return Ia(()=>{u.requestRules()}),s.$$set=_=>{"userGuidelines"in _&&t(0,o=_.userGuidelines),"userGuidelinesLengthLimit"in _&&t(1,c=_.userGuidelinesLengthLimit),"updateUserGuideline"in _&&t(2,l=_.updateUserGuideline)},[o,c,l,g,v,n,r,d,u,h,[{label:"Choose existing rules to import",id:"auto_import",description:"Choose existing rules in your workspace to import to Augment."},{label:"Select file(s) or directory to import",id:"select_file_or_directory",description:"Select an existing directory or list of markdown files to import to Augment."}],f,(_,C)=>{u.updateRuleType(`${Wi}/${Yi}/${_.path}`,_.content,C)},(_,C)=>{C.stopPropagation(),u.openRule(_.path)},(_,C)=>{C.stopPropagation(),u.deleteRule(_.path)},_=>u.openRule(_.path),()=>u.createRule(),_=>f(_),function(_){v=_,t(4,v)},function(_){g=_,i(t(3,g))}]}class xm extends fe{constructor(e){super(),ge(this,e,wm,_m,he,{userGuidelines:0,userGuidelinesLengthLimit:1,updateUserGuideline:2})}}function bm(s){let e,t,n=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},s[0]],r={};for(let a=0;a<n.length;a+=1)r=Se(r,n[a]);return{c(){e=Fe("svg"),t=new zn(!0),this.h()},l(a){e=Dn(a,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var i=Un(e);t=Vn(i,!0),i.forEach(y),this.h()},h(){t.a=null,lt(e,r)},m(a,i){qn(a,e,i),t.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M352 146.2 462 256 352 365.8V312c0-13.3-10.7-24-24-24H208v-64h120c13.3 0 24-10.7 24-24zM512 256c0-11.5-4.6-22.5-12.7-30.6L383.2 109.6c-8.7-8.7-20.5-13.6-32.8-13.6-25.6 0-46.4 20.8-46.4 46.4V176h-96c-26.5 0-48 21.5-48 48v64c0 26.5 21.5 48 48 48h96v33.6c0 25.6 20.8 46.4 46.4 46.4 12.3 0 24.1-4.9 32.8-13.6l116.1-115.8c8.1-8.1 12.7-19.1 12.7-30.6M168 80c13.3 0 24-10.7 24-24s-10.7-24-24-24H88C39.4 32 0 71.4 0 120v272c0 48.6 39.4 88 88 88h80c13.3 0 24-10.7 24-24s-10.7-24-24-24H88c-22.1 0-40-17.9-40-40V120c0-22.1 17.9-40 40-40z"/>',e)},p(a,[i]){lt(e,r=yt(n,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&i&&a[0]]))},i:H,o:H,d(a){a&&y(e)}}}function Sm(s,e,t){return s.$$set=n=>{t(0,e=Se(Se({},e),He(n)))},[e=He(e)]}class km extends fe{constructor(e){super(),ge(this,e,Sm,bm,he,{})}}function Cm(s){let e;return{c(){e=U("Sign Out")},m(t,n){w(t,e,n)},d(t){t&&y(e)}}}function Tm(s){let e,t;return e=new km({props:{slot:"iconLeft"}}),{c(){b(e.$$.fragment)},m(n,r){S(e,n,r),t=!0},p:H,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Mm(s){let e,t;return e=new Xe({props:{loading:s[0],variant:"soft","data-testid":"sign-out-button",$$slots:{iconLeft:[Tm],default:[Cm]},$$scope:{ctx:s}}}),e.$on("click",s[1]),{c(){b(e.$$.fragment)},m(n,r){S(e,n,r),t=!0},p(n,[r]){const a={};1&r&&(a.loading=n[0]),8&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Am(s,e,t){let{onSignOut:n}=e,r=!1;return s.$$set=a=>{"onSignOut"in a&&t(2,n=a.onSignOut)},[r,function(){n(),t(0,r=!0)},n]}class Nm extends fe{constructor(e){super(),ge(this,e,Am,Mm,he,{onSignOut:2})}}function Zm(s){let e,t;return e=new Hp({props:{tools:s[6],onAuthenticate:s[18],onRevokeAccess:s[19],servers:s[7],onMCPServerAdd:s[24],onMCPServerSave:s[25],onMCPServerDelete:s[26],onMCPServerToggleDisable:s[27],onMCPServerJSONImport:s[28],isMCPEnabled:s[8]&&s[2].mcpServerList,isMCPImportEnabled:s[8]&&s[2].mcpServerImport,supportedShells:s[9].supportedShells,selectedShell:s[9].selectedShell,startupScript:s[9].startupScript,onShellSelect:s[20],onStartupScriptChange:s[21],isTerminalEnabled:s[2].terminal}}),{c(){b(e.$$.fragment)},m(n,r){S(e,n,r),t=!0},p(n,r){const a={};64&r[0]&&(a.tools=n[6]),128&r[0]&&(a.servers=n[7]),260&r[0]&&(a.isMCPEnabled=n[8]&&n[2].mcpServerList),260&r[0]&&(a.isMCPImportEnabled=n[8]&&n[2].mcpServerImport),512&r[0]&&(a.supportedShells=n[9].supportedShells),512&r[0]&&(a.selectedShell=n[9].selectedShell),512&r[0]&&(a.startupScript=n[9].startupScript),4&r[0]&&(a.isTerminalEnabled=n[2].terminal),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Om(s){let e,t;return e=new Nm({props:{onSignOut:s[22]}}),{c(){b(e.$$.fragment)},m(n,r){S(e,n,r),t=!0},p:H,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Em(s){let e,t,n,r;const a=[Rm,jm],i=[];function o(c,l){return c[2].rules?0:1}return e=o(s),t=i[e]=a[e](s),{c(){t.c(),n=Te()},m(c,l){i[e].m(c,l),w(c,n,l),r=!0},p(c,l){let d=e;e=o(c),e===d?i[e].p(c,l):(K(),m(i[d],1,1,()=>{i[d]=null}),W(),t=i[e],t?t.p(c,l):(t=i[e]=a[e](c),t.c()),p(t,1),t.m(n.parentNode,n))},i(c){r||(p(t),r=!0)},o(c){m(t),r=!1},d(c){c&&y(n),i[e].d(c)}}}function Im(s){let e,t;return e=new Qc({}),{c(){b(e.$$.fragment)},m(n,r){S(e,n,r),t=!0},p:H,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Pm(s){return{c:H,m:H,p:H,i:H,o:H,d:H}}function jm(s){let e,t;return e=new vi({props:{userGuidelines:s[5],userGuidelinesLengthLimit:s[4],updateUserGuideline:s[17]}}),{c(){b(e.$$.fragment)},m(n,r){S(e,n,r),t=!0},p(n,r){const a={};32&r[0]&&(a.userGuidelines=n[5]),16&r[0]&&(a.userGuidelinesLengthLimit=n[4]),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Rm(s){let e,t;return e=new xm({props:{userGuidelines:s[5],userGuidelinesLengthLimit:s[4],updateUserGuideline:s[17]}}),{c(){b(e.$$.fragment)},m(n,r){S(e,n,r),t=!0},p(n,r){const a={};32&r[0]&&(a.userGuidelines=n[5]),16&r[0]&&(a.userGuidelinesLengthLimit=n[4]),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Lm(s){let e,t,n,r,a;const i=[Pm,Im,Em,Om,Zm],o=[];function c(l,d){var u,h,g;return 2&d[1]&&(t=null),t==null&&(t=!si(l[32])),t?0:((u=l[32])==null?void 0:u.id)==="context"?1:((h=l[32])==null?void 0:h.id)==="guidelines"?2:((g=l[32])==null?void 0:g.id)==="account"?3:4}return n=c(s,[-1,-1]),r=o[n]=i[n](s),{c(){e=M("span"),r.c(),$(e,"slot","content")},m(l,d){w(l,e,d),o[n].m(e,null),a=!0},p(l,d){let u=n;n=c(l,d),n===u?o[n].p(l,d):(K(),m(o[u],1,1,()=>{o[u]=null}),W(),r=o[n],r?r.p(l,d):(r=o[n]=i[n](l),r.c()),p(r,1),r.m(e,null))},i(l){a||(p(r),a=!0)},o(l){m(r),a=!1},d(l){l&&y(e),o[n].d()}}}function Fm(s){let e,t;return e=new Cl({props:{items:s[1],mode:"tree",class:"c-settings-navigation",selectedId:s[0],$$slots:{content:[Lm,({item:n})=>({32:n}),({item:n})=>[0,n?2:0]]},$$scope:{ctx:s}}}),{c(){b(e.$$.fragment)},m(n,r){S(e,n,r),t=!0},p(n,r){const a={};2&r[0]&&(a.items=n[1]),1&r[0]&&(a.selectedId=n[0]),1012&r[0]|6&r[1]&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function zm(s){let e,t,n,r;return e=new io.Root({props:{$$slots:{default:[Fm]},$$scope:{ctx:s}}}),{c(){b(e.$$.fragment)},m(a,i){S(e,a,i),t=!0,n||(r=ze(window,"message",s[11].onMessageFromExtension),n=!0)},p(a,i){const o={};1015&i[0]|4&i[1]&&(o.$$scope={dirty:i,ctx:a}),e.$set(o)},i(a){t||(p(e.$$.fragment,a),t=!0)},o(a){m(e.$$.fragment,a),t=!1},d(a){k(e,a),n=!1,r()}}}function Dm(s,e,t){let n,r,a,i,o,c,l,d,u,h,g=H;s.$$.on_destroy.push(()=>g());const v=new fo(Ae),f=new xc(Ae),_=new bc(Ae),C=new ja(Ae),P=v.getSettingsComponentSupported();bt(s,P,F=>t(2,o=F));const I=v.getEnableAgentMode();bt(s,I,F=>t(8,u=F)),C.registerConsumer(v),C.registerConsumer(f),C.registerConsumer(_);const A=_.getTerminalSettings();let T;bt(s,A,F=>t(9,h=F));const N={handleMessageFromExtension:F=>!(!F.data||F.data.type!==ae.navigateToSettingsSection)&&(F.data.data&&typeof F.data.data=="string"&&t(0,T=F.data.data),!0)};C.registerConsumer(N);const j=v.getDisplayableTools();bt(s,j,F=>t(6,l=F));const B=v.getGuidelines();return bt(s,B,F=>t(23,c=F)),Pa(()=>{v.dispose()}),v.notifyLoaded(),Ae.postMessage({type:ae.getOrientationStatus}),Ae.postMessage({type:ae.settingsPanelLoaded}),s.$$.update=()=>{var F,Y,ce;8388608&s.$$.dirty[0]&&t(5,n=(F=c.userGuidelines)==null?void 0:F.contents),8388608&s.$$.dirty[0]&&t(4,r=(Y=c.userGuidelines)==null?void 0:Y.lengthLimit),4&s.$$.dirty[0]&&t(1,i=[o.remoteTools?Yn("Tools","",Ml,"section-tools"):void 0,o.userGuidelines&&!o.rules?Yn("User Guidelines","Guidelines for Augment Chat to follow.",Mi,"guidelines"):void 0,o.rules?Yn("Rules and User Guidelines","",Yp,"guidelines"):void 0,o.workspaceContext?{name:"Context",description:"",icon:Nl,id:"context"}:void 0,Yn("Account","Manage your Augment account settings.",Ai,"account")].filter(Boolean)),3&s.$$.dirty[0]&&i.length>1&&!T&&t(0,T=(ce=i[0])==null?void 0:ce.id)},t(3,a=f.getServers()),g(),g=Bs(a,F=>t(7,d=F)),[T,i,o,a,r,n,l,d,u,h,f,C,P,I,A,j,B,function(F){const Y=F.trim();return!(r&&Y.length>r)&&(v.updateLocalUserGuidelines(Y),Ae.postMessage({type:ae.updateUserGuidelines,data:F}),!0)},function(F){Ae.postMessage({type:ae.toolConfigStartOAuth,data:{authUrl:F}}),v.startPolling()},function(F){Ae.postMessage({type:ae.toolConfigRevokeAccess,data:{toolId:F.identifier}})},function(F){_.updateSelectedShell(F)},function(F){_.updateStartupScript(F)},function(){Ae.postMessage({type:ae.signOut})},c,F=>f.addServer(F),F=>f.updateServer(F),F=>f.deleteServer(F),F=>f.toggleDisabledServer(F),F=>f.importServersFromJSON(F)]}class Um extends fe{constructor(e){super(),ge(this,e,Dm,zm,he,{},null,[-1,-1])}}(async function(){Ae&&Ae.initialize&&await Ae.initialize(),new Um({target:document.getElementById("app")})})();
