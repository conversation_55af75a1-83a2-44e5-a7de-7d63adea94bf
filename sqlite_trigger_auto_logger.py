#!/usr/bin/env python3
"""
SYSTÈME DE TRIGGER SQLITE POUR LOGGING AUTOMATIQUE
==================================================

Ce script installe un trigger directement dans la base SQLite pour
capturer automatiquement toutes les modifications de conversation
et les écrire dans un fichier parallèle.

ATTENTION: Modifie directement la base de données VSCode !
"""

import sqlite3
import json
import datetime
import os
from pathlib import Path

class SQLiteTriggerLogger:
    def __init__(self):
        self.state_file = Path("C:/Users/<USER>/AppData/Roaming/Code/User/workspaceStorage/a35ba43ef26792e61fe787a234121806/state.vscdb")
        self.backup_file = Path("C:/Users/<USER>/Desktop/Travail/Projet7/backup_state.vscdb")
        self.log_file = Path("C:/Users/<USER>/Desktop/Travail/Projet7/conversation_auto_trigger.txt")
        
    def create_backup(self):
        """Crée une sauvegarde de la base de données"""
        try:
            import shutil
            shutil.copy2(self.state_file, self.backup_file)
            print(f"✅ Sauvegarde créée: {self.backup_file}")
            return True
        except Exception as e:
            print(f"❌ Erreur sauvegarde: {e}")
            return False
    
    def install_trigger(self):
        """Installe un trigger dans la base SQLite"""
        try:
            conn = sqlite3.connect(str(self.state_file))
            cursor = conn.cursor()
            
            # Créer une table pour les logs si elle n'existe pas
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS conversation_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT,
                    key_name TEXT,
                    old_value TEXT,
                    new_value TEXT,
                    change_type TEXT
                )
            """)
            
            # Créer le trigger pour capturer les modifications
            trigger_sql = """
                CREATE TRIGGER IF NOT EXISTS conversation_change_logger
                AFTER UPDATE ON ItemTable
                FOR EACH ROW
                WHEN NEW.key = 'memento/webviewView.augment-chat'
                BEGIN
                    INSERT INTO conversation_log (timestamp, key_name, old_value, new_value, change_type)
                    VALUES (datetime('now'), NEW.key, OLD.value, NEW.value, 'UPDATE');
                END
            """
            
            cursor.execute(trigger_sql)
            
            # Trigger pour les insertions
            insert_trigger_sql = """
                CREATE TRIGGER IF NOT EXISTS conversation_insert_logger
                AFTER INSERT ON ItemTable
                FOR EACH ROW
                WHEN NEW.key = 'memento/webviewView.augment-chat'
                BEGIN
                    INSERT INTO conversation_log (timestamp, key_name, old_value, new_value, change_type)
                    VALUES (datetime('now'), NEW.key, '', NEW.value, 'INSERT');
                END
            """
            
            cursor.execute(insert_trigger_sql)
            
            conn.commit()
            conn.close()
            
            print(f"✅ Triggers installés dans la base de données")
            return True
            
        except Exception as e:
            print(f"❌ Erreur installation trigger: {e}")
            return False
    
    def check_and_process_logs(self):
        """Vérifie et traite les nouveaux logs"""
        try:
            conn = sqlite3.connect(str(self.state_file))
            cursor = conn.cursor()
            
            # Récupérer les nouveaux logs
            cursor.execute("""
                SELECT id, timestamp, new_value, change_type 
                FROM conversation_log 
                ORDER BY id DESC 
                LIMIT 10
            """)
            
            logs = cursor.fetchall()
            
            if logs:
                print(f"📝 {len(logs)} entrées de log trouvées")
                
                for log_id, timestamp, new_value, change_type in logs:
                    self.process_conversation_change(timestamp, new_value, change_type)
            
            conn.close()
            
        except Exception as e:
            print(f"❌ Erreur traitement logs: {e}")
    
    def process_conversation_change(self, timestamp, new_value, change_type):
        """Traite un changement de conversation"""
        try:
            if not new_value:
                return
            
            # Parser les données de conversation
            main_data = json.loads(new_value)
            webview_data = json.loads(main_data['webviewState'])
            
            conversations = webview_data.get('conversations', {})
            current_conv_id = webview_data.get('currentConversationId')
            
            if current_conv_id and current_conv_id in conversations:
                chat_history = conversations[current_conv_id].get('chatHistory', [])
                
                # Écrire dans le fichier de log
                with open(self.log_file, 'a', encoding='utf-8') as f:
                    f.write(f"\n[{timestamp}] CHANGEMENT DÉTECTÉ ({change_type})\n")
                    f.write(f"Messages dans l'historique: {len(chat_history)}\n")
                    
                    # Écrire le dernier message s'il existe
                    if chat_history:
                        last_msg = chat_history[-1]
                        
                        if 'request_message' in last_msg and last_msg['request_message']:
                            f.write(f"👤 DERNIER MESSAGE UTILISATEUR:\n")
                            f.write(f"{last_msg['request_message']}\n\n")
                        
                        if 'structured_output_nodes' in last_msg:
                            for node in last_msg['structured_output_nodes']:
                                if node.get('type') == 0 and node.get('content'):
                                    f.write(f"🤖 DERNIÈRE RÉPONSE ASSISTANT:\n")
                                    f.write(f"{node['content']}\n\n")
                                    break
                    
                    f.write("-" * 80 + "\n")
                
                print(f"✅ Changement loggé: {len(chat_history)} messages")
            
        except Exception as e:
            print(f"❌ Erreur traitement changement: {e}")
    
    def remove_triggers(self):
        """Supprime les triggers (pour nettoyage)"""
        try:
            conn = sqlite3.connect(str(self.state_file))
            cursor = conn.cursor()
            
            cursor.execute("DROP TRIGGER IF EXISTS conversation_change_logger")
            cursor.execute("DROP TRIGGER IF EXISTS conversation_insert_logger")
            cursor.execute("DROP TABLE IF EXISTS conversation_log")
            
            conn.commit()
            conn.close()
            
            print(f"✅ Triggers supprimés")
            return True
            
        except Exception as e:
            print(f"❌ Erreur suppression triggers: {e}")
            return False
    
    def setup_auto_logging(self):
        """Configure le système de logging automatique"""
        print(f"🔧 INSTALLATION SYSTÈME DE LOGGING AUTOMATIQUE")
        print(f"=" * 60)
        
        # 1. Créer sauvegarde
        if not self.create_backup():
            return False
        
        # 2. Installer triggers
        if not self.install_trigger():
            return False
        
        # 3. Initialiser fichier de log
        with open(self.log_file, 'w', encoding='utf-8') as f:
            f.write("CONVERSATION AUGMENT - LOG AUTOMATIQUE PAR TRIGGER\n")
            f.write(f"Installé: {datetime.datetime.now().isoformat()}\n")
            f.write("=" * 80 + "\n")
        
        print(f"✅ Système installé avec succès!")
        print(f"📝 Log automatique: {self.log_file}")
        print(f"💾 Sauvegarde: {self.backup_file}")
        
        return True

def install_auto_logging():
    """Installe le système de logging automatique"""
    logger = SQLiteTriggerLogger()
    return logger.setup_auto_logging()

def remove_auto_logging():
    """Supprime le système de logging automatique"""
    logger = SQLiteTriggerLogger()
    return logger.remove_triggers()

def check_logs():
    """Vérifie les logs récents"""
    logger = SQLiteTriggerLogger()
    logger.check_and_process_logs()

if __name__ == "__main__":
    print("🎯 SYSTÈME DE LOGGING AUTOMATIQUE AUGMENT")
    print("=" * 50)
    print("1. Installer le logging automatique")
    print("2. Vérifier les logs")
    print("3. Supprimer le système")
    
    choice = input("\nChoix (1-3): ").strip()
    
    if choice == "1":
        install_auto_logging()
    elif choice == "2":
        check_logs()
    elif choice == "3":
        remove_auto_logging()
    else:
        print("❌ Choix invalide")
