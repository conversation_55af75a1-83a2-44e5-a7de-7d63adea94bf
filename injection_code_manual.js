
// ===== AUGMENT AUTO LOGGING SYSTEM =====
const fs = require('fs');
const path = require('path');

class AugmentAutoLogger {
    constructor() {
        this.initializeLogging();
    }
    
    initializeLogging() {
        try {
            const workspaceRoot = vscode?.workspace?.workspaceFolders?.[0]?.uri?.fsPath || process.cwd();
            this.logFile = path.join(workspaceRoot, 'augment_conversation_auto.txt');
            
            const timestamp = new Date().toISOString();
            const header = `AUGMENT CONVERSATION - LOG AUTOMATIQUE\nDémarré: ${timestamp}\nWorkspace: ${workspaceRoot}\n${'='.repeat(80)}\n\n`;
            
            if (!fs.existsSync(this.logFile)) {
                fs.writeFileSync(this.logFile, header, 'utf8');
                console.log('[AUGMENT AUTO LOGGER] Fichier de log créé:', this.logFile);
            }
            
        } catch (error) {
            console.error('[AUGMENT AUTO LOGGER] Erreur:', error);
        }
    }
}

// Initialiser automatiquement
setTimeout(() => {
    try {
        if (typeof vscode !== 'undefined') {
            new AugmentAutoLogger();
        }
    } catch (e) {
        console.log('[AUGMENT AUTO LOGGER] Initialisation différée');
    }
}, 3000);
// ===== FIN AUTO LOGGING =====

