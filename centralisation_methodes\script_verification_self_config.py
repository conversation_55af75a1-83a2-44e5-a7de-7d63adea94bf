#!/usr/bin/env python3
"""
SCRIPT DE VÉRIFICATION SELF.CONFIG DANS LES 30 MÉTHODES
=======================================================

Objectif : Vérifier si chacune des 30 méthodes "sans paramètres" 
utilise bien self.config (paramètres centralisés)
"""

import re

def extract_method_content(file_content, method_name, start_line):
    """Extrait le contenu complet d'une méthode"""
    lines = file_content.split('\n')
    method_lines = []
    
    # Commencer à partir de la ligne de définition
    in_method = False
    indent_level = None
    
    for i, line in enumerate(lines):
        if i + 1 >= start_line:  # À partir de la ligne de définition
            if not in_method and f"def {method_name}(" in line:
                in_method = True
                indent_level = len(line) - len(line.lstrip())
                method_lines.append(line)
            elif in_method:
                # Si on trouve une nouvelle méthode au même niveau d'indentation, on s'arrête
                if line.strip() and not line.startswith(' ' * (indent_level + 1)) and line.strip() != '':
                    if line.strip().startswith('def ') or line.strip().startswith('class '):
                        break
                method_lines.append(line)
    
    return '\n'.join(method_lines)

def check_self_config_usage():
    """Vérifie l'usage de self.config dans les 30 méthodes"""
    
    # Liste des 30 méthodes à vérifier
    methods_to_check = [
        ("__init__", 1),
        ("execute_cluster_pipeline", 29),
        ("_calculate_cluster_confidence", 3788),
        ("_calculate_confidence_risk_factors", 3916),
        ("_calculate_epistemic_uncertainty", 3975),
        ("_calculate_rollout_consensus", 4013),
        ("_get_last_historical_pb_result", 4146),
        ("calculate_rollout2_diversity_score", 4288),
        ("calculate_rollout3_reward", 4326),
        ("calculate_rollout3_risk_factor", 4414),
        ("_generate_sequence_from_signal", 4620),
        ("_classify_confidence_level", 4700),
        ("_generate_so_based_sequence", 4719),
        ("_generate_impair_sync_sequence", 5089),
        ("_analyze_complete_pbt_index", 5346),
        ("_analyze_complete_so_index", 5394),
        ("_analyze_desync_sync_to_pbt_impact", 5620),
        ("_analyze_tri_dimensional_impacts", 5786),
        ("_analyze_variations_impact_on_outcomes", 5828),
        ("_analyze_consecutive_length_impact", 5893),
        ("_calculate_asymmetric_impair_alert_level", 6023),
        ("_calculate_asymmetric_pair_alert_level", 6041),
        ("_generate_exploitation_recommendation", 8125),
        ("_identify_best_prediction_context", 8143),
        ("_calculate_statistical_significance", 8229),
        ("get_max_sequence_length", 10328),
        ("get_max_so_conversions", 10343),
        ("is_game_complete", 10358),
        ("_calculate_rupture_probability", 10738),
        ("_calculate_cluster_confidence", 10906)
    ]
    
    try:
        with open('centralisation_methodes/class.txt', 'r', encoding='utf-8') as f:
            file_content = f.read()
        
        results = []
        
        for method_name, start_line in methods_to_check:
            print(f"Vérification de {method_name}...")
            
            # Extraire le contenu de la méthode
            method_content = extract_method_content(file_content, method_name, start_line)
            
            # Compter les occurrences de self.config
            self_config_count = len(re.findall(r'self\.config\.[a-zA-Z_][a-zA-Z0-9_]*', method_content))
            
            # Rechercher des valeurs numériques codées en dur
            hardcoded_numbers = re.findall(r'[^a-zA-Z_]([0-9]+\.?[0-9]*)[^a-zA-Z_]', method_content)
            # Filtrer les numéros de ligne et indices évidents
            filtered_numbers = [n for n in hardcoded_numbers if n not in ['0', '1', '2', '3']]
            
            result = {
                'method_name': method_name,
                'line_number': start_line,
                'self_config_count': self_config_count,
                'uses_self_config': self_config_count > 0,
                'hardcoded_numbers_found': filtered_numbers,
                'has_hardcoded_numbers': len(filtered_numbers) > 0,
                'method_length': len(method_content.split('\n')),
                'status': 'CENTRALISÉ' if self_config_count > 0 else 'NON CENTRALISÉ'
            }
            
            results.append(result)
        
        return results
        
    except Exception as e:
        print(f"Erreur: {e}")
        return []

def generate_report(results):
    """Génère un rapport détaillé"""
    
    centralized_methods = [r for r in results if r['uses_self_config']]
    non_centralized_methods = [r for r in results if not r['uses_self_config']]
    methods_with_hardcoded = [r for r in results if r['has_hardcoded_numbers']]
    
    report = f"""RAPPORT DE VÉRIFICATION SELF.CONFIG - 30 MÉTHODES
=====================================================

RÉSULTATS GLOBAUX :
==================
Total méthodes vérifiées : {len(results)}
Méthodes utilisant self.config : {len(centralized_methods)}
Méthodes sans self.config : {len(non_centralized_methods)}
Méthodes avec valeurs codées : {len(methods_with_hardcoded)}

MÉTHODES CENTRALISÉES ({len(centralized_methods)}) :
{'=' * 40}
"""
    
    for result in centralized_methods:
        report += f"✅ {result['method_name']} - Ligne {result['line_number']}\n"
        report += f"   self.config utilisé : {result['self_config_count']} fois\n"
        if result['has_hardcoded_numbers']:
            report += f"   ⚠️ Valeurs codées trouvées : {result['hardcoded_numbers_found']}\n"
        report += "\n"
    
    report += f"\nMÉTHODES NON CENTRALISÉES ({len(non_centralized_methods)}) :\n"
    report += "=" * 45 + "\n"
    
    for result in non_centralized_methods:
        report += f"❌ {result['method_name']} - Ligne {result['line_number']}\n"
        report += f"   self.config utilisé : {result['self_config_count']} fois\n"
        if result['has_hardcoded_numbers']:
            report += f"   ⚠️ Valeurs codées trouvées : {result['hardcoded_numbers_found']}\n"
        report += "\n"
    
    report += f"\nMÉTHODES AVEC VALEURS CODÉES ({len(methods_with_hardcoded)}) :\n"
    report += "=" * 45 + "\n"
    
    for result in methods_with_hardcoded:
        report += f"⚠️ {result['method_name']} - Ligne {result['line_number']}\n"
        report += f"   Valeurs trouvées : {result['hardcoded_numbers_found']}\n"
        report += f"   self.config : {'OUI' if result['uses_self_config'] else 'NON'}\n\n"
    
    report += "\nCONCLUSION :\n"
    report += "=" * 12 + "\n"
    
    if len(centralized_methods) == len(results):
        report += "🎉 TOUTES LES 30 MÉTHODES UTILISENT SELF.CONFIG !\n"
        report += "✅ Paramètres bien centralisés dans AZRConfig\n"
    else:
        report += f"⚠️ {len(non_centralized_methods)} méthodes ne sont pas centralisées\n"
        report += "❌ Centralisation incomplète\n"
    
    if len(methods_with_hardcoded) > 0:
        report += f"⚠️ {len(methods_with_hardcoded)} méthodes ont encore des valeurs codées\n"
        report += "🔧 Centralisation supplémentaire nécessaire\n"
    else:
        report += "✅ Aucune valeur codée en dur détectée\n"
    
    return report

def main():
    """Fonction principale"""
    print("Vérification de l'usage de self.config dans les 30 méthodes...")
    
    results = check_self_config_usage()
    
    if not results:
        print("Erreur lors de la vérification")
        return
    
    # Génération du rapport
    report = generate_report(results)
    
    # Sauvegarde
    output_file = 'centralisation_methodes/verification_self_config_30_methodes.txt'
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"Rapport sauvé dans {output_file}")
    
    # Résumé rapide
    centralized_count = sum(1 for r in results if r['uses_self_config'])
    print(f"\nRÉSUMÉ : {centralized_count}/30 méthodes utilisent self.config")

if __name__ == "__main__":
    main()
