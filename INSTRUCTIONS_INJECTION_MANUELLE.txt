
INSTRUCTIONS POUR INJECTION MANUELLE:

1. <PERSON><PERSON><PERSON><PERSON> le fichier extension.js dans un éditeur de texte (pas VSCode)
2. Ajoutez le contenu de C:\Users\<USER>\Desktop\Travail\Projet7\injection_code_manual.js au DÉBUT du fichier
3. <PERSON>uvegardez le fichier
4. Redémarrez VSCode

Fichier à modifier:
C:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.470.1\out\extension.js
