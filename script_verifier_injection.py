#!/usr/bin/env python3
"""
VÉRIFICATION DE L'INJECTION DANS L'EXTENSION AUGMENT
====================================================

Ce script vérifie si notre code de logging automatique a été correctement
injecté dans l'extension Augment.
"""

from pathlib import Path

def verifier_injection():
    """Vérifie si l'injection a réussi"""
    extension_js = Path("C:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.470.1/out/extension.js")
    
    print(f"🔍 VÉRIFICATION DE L'INJECTION")
    print(f"=" * 50)
    print(f"Fichier: {extension_js}")
    
    if not extension_js.exists():
        print(f"❌ Fichier extension.js non trouvé")
        return False
    
    try:
        with open(extension_js, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"📊 Taille du fichier: {len(content)} caractères")
        
        # Chercher nos marqueurs d'injection
        markers = [
            "AUGMENT AUTO LOGGING SYSTEM",
            "AugmentAutoLogger",
            "augment_conversation_auto.txt",
            "console.log('[AUGMENT AUTO LOGGER]"
        ]
        
        injection_found = False
        for marker in markers:
            if marker in content:
                print(f"✅ Marqueur trouvé: {marker}")
                injection_found = True
            else:
                print(f"❌ Marqueur manquant: {marker}")
        
        if injection_found:
            print(f"\n✅ INJECTION DÉTECTÉE!")
            
            # Compter les occurrences de notre code
            auto_logger_count = content.count("AugmentAutoLogger")
            logging_system_count = content.count("AUGMENT AUTO LOGGING SYSTEM")
            
            print(f"📊 Occurrences 'AugmentAutoLogger': {auto_logger_count}")
            print(f"📊 Occurrences 'AUGMENT AUTO LOGGING SYSTEM': {logging_system_count}")
            
            # Chercher le début de notre injection
            start_marker = "// ===== AUGMENT AUTO LOGGING SYSTEM ====="
            if start_marker in content:
                start_pos = content.find(start_marker)
                print(f"📍 Position d'injection: {start_pos}")
                
                # Afficher un extrait autour de l'injection
                extract_start = max(0, start_pos - 100)
                extract_end = min(len(content), start_pos + 500)
                extract = content[extract_start:extract_end]
                
                print(f"\n📝 Extrait autour de l'injection:")
                print(f"{'='*60}")
                print(extract)
                print(f"{'='*60}")
            
            return True
        else:
            print(f"\n❌ INJECTION NON DÉTECTÉE")
            
            # Afficher le début du fichier pour diagnostic
            print(f"\n📝 Début du fichier (premiers 500 caractères):")
            print(f"{'='*60}")
            print(content[:500])
            print(f"{'='*60}")
            
            return False
            
    except Exception as e:
        print(f"❌ Erreur lecture fichier: {e}")
        return False

def creer_injection_manuelle():
    """Crée un script pour injection manuelle si nécessaire"""
    print(f"\n🔧 CRÉATION SCRIPT D'INJECTION MANUELLE")
    print(f"=" * 50)
    
    # Code d'injection simplifié
    injection_code = '''
// ===== AUGMENT AUTO LOGGING SYSTEM =====
const fs = require('fs');
const path = require('path');

class AugmentAutoLogger {
    constructor() {
        this.initializeLogging();
    }
    
    initializeLogging() {
        try {
            const workspaceRoot = vscode?.workspace?.workspaceFolders?.[0]?.uri?.fsPath || process.cwd();
            this.logFile = path.join(workspaceRoot, 'augment_conversation_auto.txt');
            
            const timestamp = new Date().toISOString();
            const header = `AUGMENT CONVERSATION - LOG AUTOMATIQUE\\nDémarré: ${timestamp}\\nWorkspace: ${workspaceRoot}\\n${'='.repeat(80)}\\n\\n`;
            
            if (!fs.existsSync(this.logFile)) {
                fs.writeFileSync(this.logFile, header, 'utf8');
                console.log('[AUGMENT AUTO LOGGER] Fichier de log créé:', this.logFile);
            }
            
        } catch (error) {
            console.error('[AUGMENT AUTO LOGGER] Erreur:', error);
        }
    }
}

// Initialiser automatiquement
setTimeout(() => {
    try {
        if (typeof vscode !== 'undefined') {
            new AugmentAutoLogger();
        }
    } catch (e) {
        console.log('[AUGMENT AUTO LOGGER] Initialisation différée');
    }
}, 3000);
// ===== FIN AUTO LOGGING =====

'''
    
    # Sauvegarder le code d'injection
    injection_file = Path("C:/Users/<USER>/Desktop/Travail/Projet7/injection_code_manual.js")
    with open(injection_file, 'w', encoding='utf-8') as f:
        f.write(injection_code)
    
    print(f"✅ Code d'injection sauvegardé: {injection_file}")
    
    # Instructions pour injection manuelle
    instructions = f"""
INSTRUCTIONS POUR INJECTION MANUELLE:

1. Ouvrez le fichier extension.js dans un éditeur de texte (pas VSCode)
2. Ajoutez le contenu de {injection_file} au DÉBUT du fichier
3. Sauvegardez le fichier
4. Redémarrez VSCode

Fichier à modifier:
C:\\Users\\<USER>\\.vscode\\extensions\\augment.vscode-augment-0.470.1\\out\\extension.js
"""
    
    instructions_file = Path("C:/Users/<USER>/Desktop/Travail/Projet7/INSTRUCTIONS_INJECTION_MANUELLE.txt")
    with open(instructions_file, 'w', encoding='utf-8') as f:
        f.write(instructions)
    
    print(f"✅ Instructions sauvegardées: {instructions_file}")

def main():
    """Fonction principale"""
    if verifier_injection():
        print(f"\n🎉 L'INJECTION A RÉUSSI!")
        print(f"✅ L'extension Augment contient maintenant le code de logging automatique")
        print(f"✅ Tous les nouveaux projets auront automatiquement le logging")
        print(f"\n💡 REDÉMARREZ VSCODE pour activer complètement les modifications")
    else:
        print(f"\n⚠️ L'injection n'a pas été détectée")
        print(f"Création des fichiers pour injection manuelle...")
        creer_injection_manuelle()

if __name__ == "__main__":
    main()
