#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour rechercher et reconstituer les conversations par ordre chronologique
"""

import os
import json
import re
from pathlib import Path
from datetime import datetime

def extract_timestamp_from_filename(filename):
    """Extrait le timestamp du nom de fichier"""
    # Pattern: document-c__...-TIMESTAMP-UUID.json
    match = re.search(r'-(\d+)-[a-f0-9\-]+\.json$', filename)
    if match:
        timestamp = int(match.group(1))
        return datetime.fromtimestamp(timestamp / 1000)  # Convertir ms en secondes
    return None

def search_conversations_chronological():
    """Recherche et trie les conversations par ordre chronologique"""
    
    base_path = Path("C:/Users/<USER>/AppData/Roaming/Code/User/workspaceStorage")
    
    print("🔍 RECHERCHE CHRONOLOGIQUE DES CONVERSATIONS")
    print("=" * 60)
    
    all_files = []
    
    # Collecter TOUS les fichiers avec timestamps
    for workspace_dir in base_path.iterdir():
        if workspace_dir.is_dir():
            conversations_path = workspace_dir / "Augment.vscode-augment" / "augment-user-assets" / "checkpoint-documents"
            
            if conversations_path.exists():
                for conversation_dir in conversations_path.iterdir():
                    if conversation_dir.is_dir():
                        for file_path in conversation_dir.iterdir():
                            if file_path.is_file() and file_path.name.startswith("document-c__"):
                                timestamp = extract_timestamp_from_filename(file_path.name)
                                if timestamp:
                                    all_files.append({
                                        'workspace_id': workspace_dir.name,
                                        'conversation_id': conversation_dir.name,
                                        'file_name': file_path.name,
                                        'file_path': str(file_path),
                                        'timestamp': timestamp,
                                        'timestamp_ms': int(re.search(r'-(\d+)-', file_path.name).group(1))
                                    })
    
    # Trier par timestamp (plus récent en premier)
    all_files.sort(key=lambda x: x['timestamp'], reverse=True)
    
    print(f"📊 Total fichiers trouvés: {len(all_files)}")
    print(f"📅 Période: {all_files[-1]['timestamp']} → {all_files[0]['timestamp']}")
    
    # Afficher les 20 fichiers les plus récents
    print(f"\n📅 20 FICHIERS LES PLUS RÉCENTS:")
    print("-" * 80)
    
    for i, file_info in enumerate(all_files[:20], 1):
        print(f"{i:2d}. {file_info['timestamp']} - {file_info['file_name'][:60]}...")
        
        # Lire et analyser le contenu
        try:
            with open(file_info['file_path'], 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Parser JSON et extraire contenu
            try:
                data = json.loads(content)
                original_code = data.get('originalCode', '')
                modified_code = data.get('modifiedCode', '')
                full_content = original_code + "\n" + modified_code
                
                # Chercher "tu avais raison"
                if "tu avais raison" in full_content.lower():
                    print(f"    🎉 CONTIENT 'TU AVAIS RAISON'!")
                    
                    # Extraire contexte
                    content_lower = full_content.lower()
                    start_pos = content_lower.find("tu avais raison")
                    context_start = max(0, start_pos - 150)
                    context_end = min(len(full_content), start_pos + 200)
                    context = full_content[context_start:context_end]
                    
                    print(f"    📝 Contexte: {context.replace(chr(10), ' ')[:100]}...")
                    
                    # Vérifier "finalement"
                    if "finalement" in content_lower:
                        print(f"    🎯 CONTIENT AUSSI 'FINALEMENT'!")
                        
                        # Chercher "tu avais raison finalement"
                        if "tu avais raison finalement" in content_lower:
                            print(f"    🏆 PHRASE COMPLÈTE TROUVÉE!")
                            
                            # Extraire contexte complet
                            start_pos = content_lower.find("tu avais raison finalement")
                            context_start = max(0, start_pos - 300)
                            context_end = min(len(full_content), start_pos + 400)
                            full_context = full_content[context_start:context_end]
                            
                            print(f"\n" + "="*60)
                            print(f"🎯 CONVERSATION TROUVÉE!")
                            print(f"📅 Date: {file_info['timestamp']}")
                            print(f"📁 Workspace: {file_info['workspace_id']}")
                            print(f"💬 Conversation: {file_info['conversation_id']}")
                            print(f"📄 Fichier: {file_info['file_name']}")
                            print(f"📝 Contexte complet:")
                            print("-" * 60)
                            print(full_context)
                            print("="*60)
                            
                            return file_info, full_context
                
                # Afficher aperçu du contenu
                preview = full_content[:100].replace('\n', ' ').strip()
                if preview:
                    print(f"    📝 Aperçu: {preview}...")
                    
            except json.JSONDecodeError:
                print(f"    ⚠️ Erreur JSON")
                
        except Exception as e:
            print(f"    ❌ Erreur: {e}")
        
        print()
    
    print(f"\n❌ 'TU AVAIS RAISON FINALEMENT' non trouvé dans les 20 fichiers les plus récents")
    
    # Recherche étendue dans TOUS les fichiers
    print(f"\n🔍 RECHERCHE ÉTENDUE DANS TOUS LES {len(all_files)} FICHIERS...")
    
    for file_info in all_files:
        try:
            with open(file_info['file_path'], 'r', encoding='utf-8') as f:
                content = f.read()
            
            try:
                data = json.loads(content)
                original_code = data.get('originalCode', '')
                modified_code = data.get('modifiedCode', '')
                full_content = original_code + "\n" + modified_code
                
                if "tu avais raison finalement" in full_content.lower():
                    print(f"\n🎯 TROUVÉ!")
                    print(f"📅 Date: {file_info['timestamp']}")
                    print(f"📄 Fichier: {file_info['file_name']}")
                    
                    # Extraire contexte
                    content_lower = full_content.lower()
                    start_pos = content_lower.find("tu avais raison finalement")
                    context_start = max(0, start_pos - 300)
                    context_end = min(len(full_content), start_pos + 400)
                    context = full_content[context_start:context_end]
                    
                    print(f"📝 Contexte:")
                    print("-" * 60)
                    print(context)
                    print("-" * 60)
                    
                    return file_info, context
                    
            except json.JSONDecodeError:
                pass
                
        except Exception:
            pass
    
    print(f"\n❌ 'TU AVAIS RAISON FINALEMENT' non trouvé dans aucun fichier")
    return None, None

if __name__ == "__main__":
    print("🚀 RECHERCHE CHRONOLOGIQUE DES CONVERSATIONS")
    print("=" * 60)

    result = search_conversations_chronological()

    if result[0]:
        print(f"\n🎉 MISSION ACCOMPLIE!")
    else:
        print(f"\n❌ PHRASE NON TROUVÉE")
        print("🤔 La conversation pourrait avoir été supprimée ou être dans un autre format")
