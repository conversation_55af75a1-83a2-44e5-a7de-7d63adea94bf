import{S as P,i as Q,s as V,E as W,e as x,q as X,t as $,r as Z,u as p,h as B,Z as y,a as h,j as A,a8 as D,a7 as f,P as w,Q as j,a1 as m,a2 as g,V as q,W as S,X as E,g as N,a4 as v,a6 as F}from"./SpinnerAugment-BJ4-L7QR.js";import"./BaseButton-C6Dhmpxa.js";function G(e){let n,a;const u=e[9].default,t=w(u,e,e[8],null);let r=[e[1]],c={};for(let i=0;i<r.length;i+=1)c=h(c,r[i]);return{c(){n=j("div"),t&&t.c(),m(n,c),g(n,"svelte-149ttoo",!0)},m(i,o){x(i,n,o),t&&t.m(n,null),a=!0},p(i,o){t&&t.p&&(!a||256&o)&&q(t,u,i,i[8],a?E(u,i[8],o,null):S(i[8]),null),m(n,c=N(r,[2&o&&i[1]])),g(n,"svelte-149ttoo",!0)},i(i){a||(p(t,i),a=!0)},o(i){$(t,i),a=!1},d(i){i&&B(n),t&&t.d(i)}}}function H(e){let n,a,u,t;const r=e[9].default,c=w(r,e,e[8],null);let i=[e[1],{role:"button"},{tabindex:"0"}],o={};for(let l=0;l<i.length;l+=1)o=h(o,i[l]);return{c(){n=j("div"),c&&c.c(),m(n,o),g(n,"svelte-149ttoo",!0)},m(l,d){x(l,n,d),c&&c.m(n,null),a=!0,u||(t=[v(n,"click",e[10]),v(n,"keyup",e[11]),v(n,"keydown",e[12]),v(n,"mousedown",e[13]),v(n,"mouseover",e[14]),v(n,"focus",e[15]),v(n,"mouseleave",e[16]),v(n,"blur",e[17]),v(n,"contextmenu",e[18])],u=!0)},p(l,d){c&&c.p&&(!a||256&d)&&q(c,r,l,l[8],a?E(r,l[8],d,null):S(l[8]),null),m(n,o=N(i,[2&d&&l[1],{role:"button"},{tabindex:"0"}])),g(n,"svelte-149ttoo",!0)},i(l){a||(p(c,l),a=!0)},o(l){$(c,l),a=!1},d(l){l&&B(n),c&&c.d(l),u=!1,F(t)}}}function I(e){let n,a,u,t;const r=[H,G],c=[];function i(o,l){return o[0]?0:1}return n=i(e),a=c[n]=r[n](e),{c(){a.c(),u=W()},m(o,l){c[n].m(o,l),x(o,u,l),t=!0},p(o,[l]){let d=n;n=i(o),n===d?c[n].p(o,l):(X(),$(c[d],1,1,()=>{c[d]=null}),Z(),a=c[n],a?a.p(o,l):(a=c[n]=r[n](o),a.c()),p(a,1),a.m(u.parentNode,u))},i(o){t||(p(a),t=!0)},o(o){$(a),t=!1},d(o){o&&B(u),c[n].d(o)}}}function J(e,n,a){let u,t,r;const c=["size","insetContent","variant","interactive","includeBackground"];let i=y(n,c),{$$slots:o={},$$scope:l}=n,{size:d=1}=n,{insetContent:k=!1}=n,{variant:C="surface"}=n,{interactive:b=!1}=n,{includeBackground:z=!0}=n;return e.$$set=s=>{n=h(h({},n),A(s)),a(19,i=y(n,c)),"size"in s&&a(2,d=s.size),"insetContent"in s&&a(3,k=s.insetContent),"variant"in s&&a(4,C=s.variant),"interactive"in s&&a(0,b=s.interactive),"includeBackground"in s&&a(5,z=s.includeBackground),"$$scope"in s&&a(8,l=s.$$scope)},e.$$.update=()=>{a(7,{class:u}=i,u),189&e.$$.dirty&&a(6,t=["c-card",`c-card--size-${d}`,`c-card--${C}`,k?"c-card--insetContent":"",b?"c-card--interactive":"",u,z?"c-card--with-background":""]),64&e.$$.dirty&&a(1,r={...D("accent"),class:t.join(" ")})},[b,r,d,k,C,z,t,u,l,o,function(s){f.call(this,e,s)},function(s){f.call(this,e,s)},function(s){f.call(this,e,s)},function(s){f.call(this,e,s)},function(s){f.call(this,e,s)},function(s){f.call(this,e,s)},function(s){f.call(this,e,s)},function(s){f.call(this,e,s)},function(s){f.call(this,e,s)}]}class M extends P{constructor(n){super(),Q(this,n,J,I,V,{size:2,insetContent:3,variant:4,interactive:0,includeBackground:5})}}export{M as C};
