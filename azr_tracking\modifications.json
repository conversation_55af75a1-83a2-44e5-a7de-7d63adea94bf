{"version": "1.0", "created": "2025-06-03T21:19:39.005154", "modifications": [{"id": "mod_20250603_211939_0", "timestamp": "2025-06-03T21:19:39.016159", "file": "azr_baccarat_predictor.py", "old_hash": null, "new_hash": "0fe1a29fd36f6224ea7ef8177bbd42b8", "snapshot_path": null, "changes_analysis": {"type": "new_file", "parameters_changed": [], "sections_affected": [], "risk_level": "medium", "validation_required": []}, "validation_status": "pending"}, {"id": "mod_20250603_215259_1", "timestamp": "2025-06-03T21:52:59.946785", "file": "azr_baccarat_predictor.py", "old_hash": "0fe1a29fd36f6224ea7ef8177bbd42b8", "new_hash": "91e1ce9fe3af4414f708ea5f71825b37", "snapshot_path": "azr_tracking\\snapshots\\mod_20250603_215259_1_azr_baccarat_predictor.py", "changes_analysis": {"type": "modification", "parameters_changed": [{"parameter": "rollout1_analysis_time_ms", "line": 498, "content": "rollout1_analysis_time_ms: int = 60          # Temps alloué analyse complète"}, {"parameter": "rollout1_index_time_ms", "line": 499, "content": "rollout1_index_time_ms: int = 10             # Temps par index (2,3,5)"}, {"parameter": "rollout1_index4_time_ms", "line": 500, "content": "rollout1_index4_time_ms: int = 15            # Temps index 4 (P/B/T)"}, {"parameter": "rollout1_synthesis_time_ms", "line": 501, "content": "rollout1_synthesis_time_ms: int = 15         # Temps synthèse finale"}, {"parameter": "rollout1_min_hands_quality", "line": 504, "content": "rollout1_min_hands_quality: int = 50         # Minimum mains pour qualité optimale"}, {"parameter": "rollout1_data_richness_factor", "line": 505, "content": "rollout1_data_richness_factor: float = 50.0    # Minimum mains pour données riches (50)"}, {"parameter": "rollout1_coherence_neutral", "line": 506, "content": "rollout1_coherence_neutral: float = 0.5        # Valeur neutre cohérence (50%)"}, {"parameter": "rollout1_quality_threshold", "line": 507, "content": "rollout1_quality_threshold: float = 0.7      # Seuil qualité analyse acceptable"}, {"parameter": "rollout1_so_start_offset", "line": 510, "content": "rollout1_so_start_offset: int = 1              # Offset début séquences S/O (1)"}, {"parameter": "rollout1_max_divisor_safety", "line": 511, "content": "rollout1_max_divisor_safety: int = 1           # Diviseur sécurité minimum (1)"}, {"parameter": "rollout1_impact_strength_threshold", "line": 512, "content": "rollout1_impact_strength_threshold: float = 0.1  # Seuil force impact significatif"}, {"parameter": "rollout1_desync_period_min", "line": 515, "content": "rollout1_desync_period_min: int = 1          # Longueur minimum période DESYNC"}, {"parameter": "rollout1_desync_period_start_init", "line": 516, "content": "rollout1_desync_period_start_init: int = 0   # Initialisation start période"}, {"parameter": "rollout1_desync_period_length_init", "line": 517, "content": "rollout1_desync_period_length_init: int = 1  # Initialisation longueur période"}, {"parameter": "rollout1_combined_pair_sync_influence", "line": 520, "content": "rollout1_combined_pair_sync_influence: float = 0.12      # PAIR_SYNC → O (61.2% - 50% = 11.2%)"}, {"parameter": "rollout1_combined_impair_sync_influence", "line": 521, "content": "rollout1_combined_impair_sync_influence: float = 0.011   # IMPAIR_SYNC → S (51.1% - 50% = 1.1%)"}, {"parameter": "rollout1_combined_pair_desync_influence", "line": 522, "content": "rollout1_combined_pair_desync_influence: float = 0.032   # PAIR_DESYNC → O (53.2% - 50% = 3.2%)"}, {"parameter": "rollout1_combined_impair_desync_influence", "line": 523, "content": "rollout1_combined_impair_desync_influence: float = 0.004 # IMPAIR_DESYNC → O (50.4% - 50% = 0.4%)"}, {"parameter": "rollout1_step_increment", "line": 526, "content": "rollout1_step_increment: float = 0.05        # Incrément pour transitions"}, {"parameter": "rollout1_small_increment", "line": 527, "content": "rollout1_small_increment: float = 0.02       # Petit incrément pour ajustements"}, {"parameter": "rollout1_low_performance_threshold", "line": 530, "content": "rollout1_low_performance_threshold: float = -0.01     # Seuil performance en baisse"}, {"parameter": "rollout1_high_performance_threshold", "line": 531, "content": "rollout1_high_performance_threshold: float = 0.01     # Seuil performance en hausse"}, {"parameter": "rollout1_min_accuracy_trend", "line": 532, "content": "rollout1_min_accuracy_trend: int = 10                 # Minimum points pour tendance"}, {"parameter": "rollout1_bias_threshold_low", "line": 535, "content": "rollout1_bias_threshold_low: float = 0.3              # Seuil biais faible"}, {"parameter": "rollout1_bias_threshold_medium", "line": 536, "content": "rollout1_bias_threshold_medium: float = 0.5           # <PERSON><PERSON> bi<PERSON> moyen"}, {"parameter": "rollout1_bias_threshold_high", "line": 537, "content": "rollout1_bias_threshold_high: float = 0.6             # Seuil biais élevé"}, {"parameter": "rollout1_bias_threshold_critical", "line": 538, "content": "rollout1_bias_threshold_critical: float = 0.8         # <PERSON><PERSON> critique"}, {"parameter": "rollout1_bias_threshold_extreme", "line": 539, "content": "rollout1_bias_threshold_extreme: float = 0.9          # Seuil biais extrême"}, {"parameter": "rollout1_significance_threshold_low", "line": 542, "content": "rollout1_significance_threshold_low: float = 0.05     # Seuil significativité faible (5%)"}, {"parameter": "rollout1_significance_threshold_medium", "line": 543, "content": "rollout1_significance_threshold_medium: float = 0.1   # Seuil significativité moyenne (10%)"}, {"parameter": "rollout1_significance_threshold_high", "line": 544, "content": "rollout1_significance_threshold_high: float = 0.15    # Seuil significativité élevée (15%)"}, {"parameter": "rollout1_significance_threshold_critical", "line": 545, "content": "rollout1_significance_threshold_critical: float = 0.2 # Seuil significativité critique (20%)"}, {"parameter": "rollout1_default_return_value", "line": 548, "content": "rollout1_default_return_value: float = 0.0            # Valeur retour par défaut"}, {"parameter": "rollout1_neutral_bias_value", "line": 549, "content": "rollout1_neutral_bias_value: float = 0.5              # Valeur neutre pour biais"}, {"parameter": "rollout1_minimum_confidence_value", "line": 550, "content": "rollout1_minimum_confidence_value: float = 0.1        # Confiance minimum"}, {"parameter": "rollout1_maximum_confidence_value", "line": 551, "content": "rollout1_maximum_confidence_value: float = 0.95         # Confiance maximum autorisée (95%)"}, {"parameter": "rollout1_coherence_threshold_weak", "line": 554, "content": "rollout1_coherence_threshold_weak: float = 0.2        # Cohérence faible"}, {"parameter": "rollout1_coherence_threshold_acceptable", "line": 555, "content": "rollout1_coherence_threshold_acceptable: float = 0.4  # Cohérence acceptable"}, {"parameter": "rollout1_coherence_threshold_good", "line": 556, "content": "rollout1_coherence_threshold_good: float = 0.6        # Cohérence bonne"}, {"parameter": "rollout1_coherence_threshold_excellent", "line": 557, "content": "rollout1_coherence_threshold_excellent: float = 0.8     # Seuil cohérence excellente (40%)"}, {"parameter": "rollout1_impair_consecutive_common", "line": 560, "content": "rollout1_impair_consecutive_common: float = 0.3          # Seuil IMPAIR consécutif commun (30%)"}, {"parameter": "rollout1_impair_consecutive_rare", "line": 561, "content": "rollout1_impair_consecutive_rare: float = 0.6            # Seuil IMPAIR consécutif rare (60%)"}, {"parameter": "rollout1_impair_consecutive_very_rare", "line": 562, "content": "rollout1_impair_consecutive_very_rare: float = 0.8       # Seuil IMPAIR consécutif très rare (80%)"}, {"parameter": "rollout1_pair_consecutive_very_common", "line": 565, "content": "rollout1_pair_consecutive_very_common: float = 0.1       # Seuil PAIR consécutif très commun (10%)"}, {"parameter": "rollout1_pair_consecutive_common", "line": 566, "content": "rollout1_pair_consecutive_common: float = 0.3            # Seuil PAIR consécutif commun (20%)"}, {"parameter": "rollout1_pair_consecutive_fairly_common", "line": 567, "content": "rollout1_pair_consecutive_fairly_common: float = 0.5     # Seuil PAIR consécutif assez commun (30%)"}, {"parameter": "rollout1_pair_consecutive_less_common", "line": 568, "content": "rollout1_pair_consecutive_less_common: float = 0.7       # Seuil PAIR consécutif moins commun (40%)"}, {"parameter": "rollout1_global_strength_threshold", "line": 571, "content": "rollout1_global_strength_threshold: float = 0.3         # Seuil force globale minimum (15%)"}, {"parameter": "rollout1_exploitability_threshold", "line": 572, "content": "rollout1_exploitability_threshold: float = 0.35       # Seuil exploitabilité élevée"}, {"parameter": "rollout1_tri_dimensional_threshold", "line": 573, "content": "rollout1_tri_dimensional_threshold: float = 0.3       # Seuil tri-dimensionnel"}, {"parameter": "rollout1_sync_high_threshold", "line": 576, "content": "rollout1_sync_high_threshold: float = 0.7             # Seuil synchronisation élevée"}, {"parameter": "rollout1_desync_high_threshold", "line": 577, "content": "rollout1_desync_high_threshold: float = 0.3           # Seuil désynchronisation élevée (inverse)"}, {"parameter": "rollout1_phase_early_ratio", "line": 580, "content": "rollout1_phase_early_ratio: float = 0.33                # Ratio phase précoce (30%)"}, {"parameter": "rollout1_phase_middle_ratio", "line": 581, "content": "rollout1_phase_middle_ratio: float = 0.66               # Ratio phase moyenne (70%)"}, {"parameter": "rollout1_phase_late_start", "line": 582, "content": "rollout1_phase_late_start: float = 0.3                # Début phase tardive (30%)"}, {"parameter": "rollout2_generation_time_ms", "line": 594, "content": "rollout2_generation_time_ms: int = 50        # Temps alloué génération complète"}, {"parameter": "rollout2_sequence_time_ms", "line": 595, "content": "rollout2_sequence_time_ms: int = 10          # Temps par séquence candidate"}, {"parameter": "rollout2_evaluation_time_ms", "line": 596, "content": "rollout2_evaluation_time_ms: int = 10        # Temps évaluation finale"}, {"parameter": "rollout2_candidates_count", "line": 599, "content": "rollout2_candidates_count: int = 4           # Nombre séquences candidates à générer"}, {"parameter": "rollout2_strategy_count", "line": 600, "content": "rollout2_strategy_count: int = 4             # Nombre stratégies distinctes"}, {"parameter": "rollout2_max_probability", "line": 603, "content": "rollout2_max_probability: float = 0.85         # Probabilité maximum (85%)"}, {"parameter": "rollout2_alternative_probability", "line": 604, "content": "rollout2_alternative_probability: float = 0.70    # Probabilité alternative (70%)"}, {"parameter": "rollout2_rupture_probability", "line": 605, "content": "rollout2_rupture_probability: float = 0.60     # Probabilité rupture (60%)"}, {"parameter": "rollout2_conservative_probability", "line": 606, "content": "rollout2_conservative_probability: float = 0.45    # Probabilité conservative (45%)"}, {"parameter": "rollout2_fixed_length", "line": 609, "content": "rollout2_fixed_length: int = 3                 # Longueur fixe séquences P/B (4)"}, {"parameter": "rollout2_sequences_count", "line": 610, "content": "rollout2_sequences_count: int = 4              # Nombre séquences générées (8)"}, {"parameter": "rollout2_total_possibilities", "line": 611, "content": "rollout2_total_possibilities: int = 8        # Total théorique : 2^3 = 8 possibilités"}, {"parameter": "rollout2_confidence_threshold", "line": 614, "content": "rollout2_confidence_threshold: float = 0.6   # Seuil confiance génération"}, {"parameter": "rollout2_diversity_threshold", "line": 615, "content": "rollout2_diversity_threshold: float = 0.3    # Seuil diversité séquences"}, {"parameter": "rollout2_quality_threshold", "line": 616, "content": "rollout2_quality_threshold: float = 0.5      # Seuil qualité minimum"}, {"parameter": "rollout2_signal_confidence_default", "line": 619, "content": "rollout2_signal_confidence_default: float = 0.5       # Confiance signal par défaut (50%)"}, {"parameter": "rollout2_signal_confidence_high", "line": 620, "content": "rollout2_signal_confidence_high: float = 0.8          # Seuil signal confiance élevée (80%)"}, {"parameter": "rollout2_signal_confidence_medium", "line": 621, "content": "rollout2_signal_confidence_medium: float = 0.6        # Seuil signal confiance moyenne (60%)"}, {"parameter": "rollout2_signal_confidence_low", "line": 622, "content": "rollout2_signal_confidence_low: float = 0.4           # Seuil signal confiance faible (40%)"}, {"parameter": "rollout2_confidence_level_excellent", "line": 625, "content": "rollout2_confidence_level_excellent: str = \"EXCELLENT\"    # Label confiance excellente"}, {"parameter": "rollout2_confidence_level_good", "line": 626, "content": "rollout2_confidence_level_good: str = \"GOOD\"              # Label bonne confiance"}, {"parameter": "rollout2_confidence_level_acceptable", "line": 627, "content": "rollout2_confidence_level_acceptable: str = \"ACCEPTABLE\"   # Label confiance acceptable"}, {"parameter": "rollout2_confidence_level_poor", "line": 628, "content": "rollout2_confidence_level_poor: str = \"POOR\"              # Label confiance faible"}, {"parameter": "rollout2_default_signal_type", "line": 631, "content": "rollout2_default_signal_type: str = \"unknown\"             # Type signal par défaut"}, {"parameter": "rollout2_default_signal_name", "line": 632, "content": "rollout2_default_signal_name: str = \"\"                    # Nom signal par défaut"}, {"parameter": "rollout2_default_strategy_prefix", "line": 633, "content": "rollout2_default_strategy_prefix: str = \"signal_based\"    # Préfixe stratégie par défaut"}, {"parameter": "rollout2_default_justification", "line": 634, "content": "rollout2_default_justification: str = \"Signal exploitation\"   # Justification par défaut"}, {"parameter": "rollout2_so_prediction_keyword", "line": 637, "content": "rollout2_so_prediction_keyword: str = \"so_prediction\"     # Mot-clé prédictions S/O"}, {"parameter": "rollout2_pb_prediction_keyword", "line": 638, "content": "rollout2_pb_prediction_keyword: str = \"pb_prediction\"     # Mot-clé prédictions P/B"}, {"parameter": "rollout2_player_keyword", "line": 639, "content": "rollout2_player_keyword: str = \"PLAYER\"                   # Mot-clé patterns Player"}, {"parameter": "rollout2_banker_keyword", "line": 640, "content": "rollout2_banker_keyword: str = \"BANKER\"                   # Mot-clé patterns Banker"}, {"parameter": "rollout2_pair_sync_keyword", "line": 641, "content": "rollout2_pair_sync_keyword: str = \"PAIR_SYNC\"             # Mot-clé PAIR synchronisés"}, {"parameter": "rollout2_impair_sync_keyword", "line": 642, "content": "rollout2_impair_sync_keyword: str = \"IMPAIR_SYNC\"         # Mot-clé IMPAIR synchronisés"}, {"parameter": "rollout2_player_result", "line": 645, "content": "rollout2_player_result: str = \"P\"                         # Code résultat Player (P)"}, {"parameter": "rollout2_banker_result", "line": 646, "content": "rollout2_banker_result: str = \"B\"                         # Code résultat Banker (B)"}, {"parameter": "rollout2_fallback_strategy_1", "line": 649, "content": "rollout2_fallback_strategy_1: str = \"impair_pair_optimized\"    # Stratégie fallback priorité 1"}, {"parameter": "rollout2_fallback_strategy_2", "line": 650, "content": "rollout2_fallback_strategy_2: str = \"sync_based\"               # Stratégie fallback priorité 2"}, {"parameter": "rollout2_fallback_strategy_3", "line": 651, "content": "rollout2_fallback_strategy_3: str = \"combined_index\"           # Stratégie fallback priorité 3"}, {"parameter": "rollout2_fallback_strategy_4", "line": 652, "content": "rollout2_fallback_strategy_4: str = \"so_pattern\"               # Stratégie fallback priorité 4"}, {"parameter": "rollout2_fallback_justification_1", "line": 655, "content": "rollout2_fallback_justification_1: str = \"Exploitation corrélations IMPAIR/PAIR → P/B/T (fallback)\"  # Justification stratégie haute confiance"}, {"parameter": "rollout2_fallback_justification_2", "line": 656, "content": "rollout2_fallback_justification_2: str = \"Exploitation patterns synchronisation (fallback)\"  # Justification approche alternative"}, {"parameter": "rollout2_fallback_justification_3", "line": 657, "content": "rollout2_fallback_justification_3: str = \"Exploitation index combiné dominant (fallback)\"  # Justification scénario rupture"}, {"parameter": "rollout2_fallback_justification_4", "line": 658, "content": "rollout2_fallback_justification_4: str = \"Exploitation patterns S/O (Same/Opposite) (fallback)\"  # Justification approche conservative"}, {"parameter": "rollout2_optimal_difficulty", "line": 659, "content": "rollout2_optimal_difficulty: float = 0.6             # Difficulté optimale rollout2"}, {"parameter": "rollout2_min_difficulty", "line": 660, "content": "rollout2_min_difficulty: float = 0.1                 # Difficulté minimum rollout2"}, {"parameter": "rollout2_max_difficulty", "line": 661, "content": "rollout2_max_difficulty: float = 0.9                 # Difficulté maximum rollout2"}, {"parameter": "rollout2_diversity_bonus", "line": 662, "content": "rollout2_diversity_bonus: float = 1.0                # Bonus diversité rollout2"}, {"parameter": "rollout2_diversity_malus", "line": 663, "content": "rollout2_diversity_malus: float = 0.5                # Malus diversité rollout2"}, {"parameter": "rollout2_excellence_threshold", "line": 664, "content": "rollout2_excellence_threshold: float = 0.8           # Seuil excellence rollout2"}, {"parameter": "rollout2_acceptable_threshold", "line": 665, "content": "rollout2_acceptable_threshold: float = 0.6           # Seuil acceptable rollout2"}, {"parameter": "rollout2_weak_threshold", "line": 666, "content": "rollout2_weak_threshold: float = 0.4                 # Seuil faible rollout2"}, {"parameter": "rollout2_poor_threshold", "line": 667, "content": "rollout2_poor_threshold: float = 0.0                 # Seuil pauvre rollout2"}, {"parameter": "rollout2_excellence_bonus", "line": 668, "content": "rollout2_excellence_bonus: float = 1.15              # Bonus excellence (+15%)"}, {"parameter": "rollout2_neutral_multiplier", "line": 669, "content": "rollout2_neutral_multiplier: float = 1.0             # Multiplicateur neutre"}, {"parameter": "rollout2_weak_malus", "line": 670, "content": "rollout2_weak_malus: float = 0.85                    # Malus faible (-15%)"}, {"parameter": "rollout2_poor_malus", "line": 671, "content": "rollout2_poor_malus: float = 0.7                     # <PERSON><PERSON> pauvre (-30%)"}, {"parameter": "rollout2_exploitation_threshold_high", "line": 674, "content": "rollout2_exploitation_threshold_high: float = 0.7        # Seuil exploitation élevé (25%)"}, {"parameter": "rollout2_exploitation_threshold_medium", "line": 675, "content": "rollout2_exploitation_threshold_medium: float = 0.5      # Seuil <PERSON> moyen (20%)"}, {"parameter": "rollout2_exploitation_threshold_low", "line": 676, "content": "rollout2_exploitation_threshold_low: float = 0.3     # Seuil exploitation faible (≥30%)"}, {"parameter": "rollout2_bias_detection_threshold", "line": 677, "content": "rollout2_bias_detection_threshold: float = 0.1       # Seuil détection biais (>10%)"}, {"parameter": "rollout2_default_return_value", "line": 680, "content": "rollout2_default_return_value: float = 0.0           # Valeur retour par défaut"}, {"parameter": "rollout2_fallback_confidence", "line": 681, "content": "rollout2_fallback_confidence: float = 0.5            # Confiance fallback"}, {"parameter": "rollout2_minimum_signal_strength", "line": 682, "content": "rollout2_minimum_signal_strength: float = 0.1        # Force signal minimum"}, {"parameter": "rollout2_maximum_signal_strength", "line": 683, "content": "rollout2_maximum_signal_strength: float = 0.95       # Force signal maximum"}, {"parameter": "rollout2_impair_pair_threshold_high", "line": 686, "content": "rollout2_impair_pair_threshold_high: float = 0.95    # Seuil IMPAIR/PAIR élevé"}, {"parameter": "rollout2_impair_pair_threshold_medium", "line": 687, "content": "rollout2_impair_pair_threshold_medium: float = 0.85  # Seuil IMPAIR/PAIR moyen"}, {"parameter": "rollout2_impair_pair_threshold_low", "line": 688, "content": "rollout2_impair_pair_threshold_low: float = 0.55     # Seuil IMPAIR/PAIR faible"}, {"parameter": "rollout2_impair_pair_threshold_weak", "line": 689, "content": "rollout2_impair_pair_threshold_weak: float = 0.2     # Seuil IMPAIR/PAIR faible"}, {"parameter": "rollout2_sync_threshold_high", "line": 692, "content": "rollout2_sync_threshold_high: float = 0.9            # Seuil sync élevé"}, {"parameter": "rollout2_sync_threshold_medium", "line": 693, "content": "rollout2_sync_threshold_medium: float = 0.7          # Seuil sync moyen"}, {"parameter": "rollout2_sync_threshold_low", "line": 694, "content": "rollout2_sync_threshold_low: float = 0.65            # Seuil sync faible"}, {"parameter": "rollout2_sync_detection_threshold", "line": 695, "content": "rollout2_sync_detection_threshold: float = 0.15      # Seuil détection sync"}, {"parameter": "rollout2_combined_index_threshold_high", "line": 698, "content": "rollout2_combined_index_threshold_high: float = 0.8  # Seuil index combiné élevé"}, {"parameter": "rollout2_combined_index_threshold_medium", "line": 699, "content": "rollout2_combined_index_threshold_medium: float = 0.7 # Seuil index combiné moyen"}, {"parameter": "rollout2_combined_index_threshold_low", "line": 700, "content": "rollout2_combined_index_threshold_low: float = 0.6   # Seuil index combiné faible"}, {"parameter": "rollout2_combined_index_threshold_weak", "line": 701, "content": "rollout2_combined_index_threshold_weak: float = 0.75 # Seuil index combiné faible"}, {"parameter": "rollout2_so_pattern_threshold_high", "line": 704, "content": "rollout2_so_pattern_threshold_high: float = 0.8      # Seuil pattern S/O élevé"}, {"parameter": "rollout2_so_pattern_threshold_medium", "line": 705, "content": "rollout2_so_pattern_threshold_medium: float = 0.7    # Seuil pattern S/O moyen"}, {"parameter": "rollout2_so_pattern_threshold_low", "line": 706, "content": "rollout2_so_pattern_threshold_low: float = 0.6       # Seuil pattern S/O faible"}, {"parameter": "rollout2_so_pattern_detection_threshold", "line": 707, "content": "rollout2_so_pattern_detection_threshold: float = 0.2 # Seuil détection pattern S/O"}, {"parameter": "rollout2_veto_sync_threshold", "line": 710, "content": "rollout2_veto_sync_threshold: float = 0.85               # <PERSON><PERSON> veto SYNC rollout2 (70%)"}, {"parameter": "rollout2_veto_combined_threshold", "line": 711, "content": "rollout2_veto_combined_threshold: float = 0.90           # <PERSON><PERSON> veto COMBINÉ rollout2 (80%)"}, {"parameter": "rollout2_veto_impair_threshold", "line": 712, "content": "rollout2_veto_impair_threshold: float = 0.95         # Seuil veto signal IMPAIR extrême"}, {"parameter": "rollout2_cross_impact_ratio_threshold", "line": 715, "content": "rollout2_cross_impact_ratio_threshold: float = 0.6       # Seuil ratio impact croisé (60%)"}, {"parameter": "rollout2_cross_impact_confidence_boost", "line": 716, "content": "rollout2_cross_impact_confidence_boost: float = 0.15     # Boost confiance impact croisé (20%)"}, {"parameter": "rollout2_cross_impact_max_confidence", "line": 717, "content": "rollout2_cross_impact_max_confidence: float = 0.95       # Confiance max impact croisé (90%)"}, {"parameter": "rollout2_transition_signal_threshold", "line": 720, "content": "rollout2_transition_signal_threshold: float = 0.55       # Seuil signal transition (60%)"}, {"parameter": "rollout2_sync_rate_enriched_threshold", "line": 721, "content": "rollout2_sync_rate_enriched_threshold: float = 0.6       # Seuil taux sync enrichi (70%)"}, {"parameter": "rollout2_so_bias_strong_threshold", "line": 724, "content": "rollout2_so_bias_strong_threshold: float = 0.6           # Seuil biais S/O fort (70%)"}, {"parameter": "rollout2_so_bias_detection_threshold", "line": 725, "content": "rollout2_so_bias_detection_threshold: float = 0.1    # Seuil détection biais S/O"}, {"parameter": "rollout2_statistical_significance_threshold", "line": 728, "content": "rollout2_statistical_significance_threshold: float = 0.6      # Seuil significativité statistique (15%)"}, {"parameter": "rollout2_global_strength_recommendation", "line": 729, "content": "rollout2_global_strength_recommendation: float = 0.4         # Seuil force globale recommandé (20%)"}, {"parameter": "rollout2_global_strength_high_threshold", "line": 732, "content": "rollout2_global_strength_high_threshold: float = 0.7         # Seuil force globale élevée (30%)"}, {"parameter": "rollout2_global_strength_boost_factor", "line": 733, "content": "rollout2_global_strength_boost_factor: float = 0.3           # Facteur boost force globale (×2.0)"}, {"parameter": "rollout2_signal_detection_threshold", "line": 736, "content": "rollout2_signal_detection_threshold: float = 0.05            # Seuil détection signal (10%)"}, {"parameter": "rollout2_signal_strength_threshold", "line": 737, "content": "rollout2_signal_strength_threshold: float = 0.1              # Seuil force signal (15%)"}, {"parameter": "rollout2_exploitation_strength_threshold", "line": 738, "content": "rollout2_exploitation_strength_threshold: float = 0.25   # Seuil force exploitation"}, {"parameter": "rollout2_priority_weight_1", "line": 741, "content": "rollout2_priority_weight_1: float = 0.4                      # Poids priorité 1 IMPAIR (40%)"}, {"parameter": "rollout2_priority_weight_2", "line": 742, "content": "rollout2_priority_weight_2: float = 0.3                      # Poids priorité 2 PAIR (30%)"}, {"parameter": "rollout2_priority_weight_3", "line": 743, "content": "rollout2_priority_weight_3: float = 0.2                      # Poids priorité 3 SYNC (20%)"}, {"parameter": "rollout2_priority_weight_4", "line": 744, "content": "rollout2_priority_weight_4: float = 0.1                      # Poids priorité 4 COMBINÉ (10%)"}, {"parameter": "rollout2_evaluation_weight_standard", "line": 747, "content": "rollout2_evaluation_weight_standard: float = 0.15            # Poids évaluation standard (15%)"}, {"parameter": "rollout2_evaluation_weight_medium", "line": 748, "content": "rollout2_evaluation_weight_medium: float = 0.25          # Poids évaluation moyenne"}, {"parameter": "rollout2_adjustment_small", "line": 751, "content": "rollout2_adjustment_small: float = 0.1                       # Petit ajustement (5%)"}, {"parameter": "rollout2_adjustment_medium", "line": 752, "content": "rollout2_adjustment_medium: float = 0.15                     # Ajustement moyen (15%)"}, {"parameter": "rollout2_adjustment_large", "line": 753, "content": "rollout2_adjustment_large: float = 0.2                       # Grand ajustement (20%)"}, {"parameter": "rollout2_bonus_evolution", "line": 754, "content": "rollout2_bonus_evolution: float = 0.15                   # Bonus évolution temporelle"}, {"parameter": "rollout2_bonus_correlation", "line": 755, "content": "rollout2_bonus_correlation: float = 0.1                  # Bonus corrélation détectée"}, {"parameter": "rollout2_bonus_force_bias", "line": 756, "content": "rollout2_bonus_force_bias: float = 0.2                   # Bonus force de biais"}, {"parameter": "rollout2_bonus_sync_probability", "line": 757, "content": "rollout2_bonus_sync_probability: float = 0.1             # Bonus probabilité sync"}, {"parameter": "rollout2_exploitation_readiness_threshold", "line": 760, "content": "rollout2_exploitation_readiness_threshold: float = 0.1       # Seuil préparation exploitation (70%)"}, {"parameter": "rollout2_consistency_weight", "line": 761, "content": "rollout2_consistency_weight: float = 0.2                     # Poids cohérence (25%)"}, {"parameter": "rollout2_correlation_weight", "line": 762, "content": "rollout2_correlation_weight: float = 0.25                    # Poids corrélations (30%)"}, {"parameter": "rollout2_base_confidence_high", "line": 765, "content": "rollout2_base_confidence_high: float = 0.8                 # Confiance base élevée (80%)"}, {"parameter": "rollout2_base_confidence_medium", "line": 766, "content": "rollout2_base_confidence_medium: float = 0.6               # Confiance base moyenne (60%)"}, {"parameter": "rollout2_base_confidence_low", "line": 767, "content": "rollout2_base_confidence_low: float = 0.4                  # Confiance base faible (40%)"}, {"parameter": "rollout2_base_confidence_default", "line": 768, "content": "rollout2_base_confidence_default: float = 0.5            # Confiance de base par défaut"}, {"parameter": "rollout2_zone_confidence_threshold", "line": 771, "content": "rollout2_zone_confidence_threshold: float = 0.2              # Seuil confiance zone (20%)"}, {"parameter": "rollout2_zone_strength_threshold", "line": 772, "content": "rollout2_zone_strength_threshold: float = 0.35           # Seuil force zone"}, {"parameter": "rollout2_exploitation_priority_threshold", "line": 773, "content": "rollout2_exploitation_priority_threshold: float = 0.35   # Seuil priorité exploitation"}, {"parameter": "rollout2_confidence_limit_max", "line": 776, "content": "rollout2_confidence_limit_max: float = 0.95              # Limite confiance maximum"}, {"parameter": "rollout2_confidence_limit_min", "line": 777, "content": "rollout2_confidence_limit_min: float = 0.55              # Limite confiance minimum"}, {"parameter": "rollout2_confidence_adjustment", "line": 778, "content": "rollout2_confidence_adjustment: float = 0.85             # Ajustement confiance"}, {"parameter": "rollout2_confidence_value_high", "line": 781, "content": "rollout2_confidence_value_high: float = 0.7                # Valeur confiance élevée (70%)"}, {"parameter": "rollout2_confidence_value_medium_high", "line": 782, "content": "rollout2_confidence_value_medium_high: float = 0.65        # Valeur confiance moyenne-élev<PERSON> (65%)"}, {"parameter": "rollout2_confidence_value_standard", "line": 783, "content": "rollout2_confidence_value_standard: float = 0.6            # Valeur confiance standard (60%)"}, {"parameter": "rollout2_confidence_derived_factor", "line": 784, "content": "rollout2_confidence_derived_factor: float = 0.8          # Facteur confiance dérivée (80%)"}, {"parameter": "rollout2_so_confidence_limit", "line": 787, "content": "rollout2_so_confidence_limit: float = 0.9                    # Limite confiance S/O (90%)"}, {"parameter": "rollout2_pb_confidence_limit", "line": 788, "content": "rollout2_pb_confidence_limit: float = 0.95               # Limite confiance P/B"}, {"parameter": "rollout2_confidence_minimum_safe", "line": 789, "content": "rollout2_confidence_minimum_safe: float = 0.55           # Minimum sécurisé"}, {"parameter": "rollout2_confidence_maximum_safe", "line": 790, "content": "rollout2_confidence_maximum_safe: float = 0.85           # Maximum sécurisé"}, {"parameter": "rollout3_prediction_time_ms", "line": 802, "content": "rollout3_prediction_time_ms: int = 60        # Temps alloué prédiction complète"}, {"parameter": "rollout3_evaluation_time_ms", "line": 803, "content": "rollout3_evaluation_time_ms: int = 15        # Temps évaluation par séquence"}, {"parameter": "rollout3_selection_time_ms", "line": 804, "content": "rollout3_selection_time_ms: int = 15         # Temps sélection finale"}, {"parameter": "rollout3_fixed_length", "line": 807, "content": "rollout3_fixed_length: int = 3                   # Longueur fixe S/O (3)"}, {"parameter": "rollout3_default_confidence", "line": 810, "content": "rollout3_default_confidence: float = 0.0         # Confiance par défaut (50%)"}, {"parameter": "rollout3_fallback_probability", "line": 811, "content": "rollout3_fallback_probability: float = 0.7       # Probabilité fallback (45%)"}, {"parameter": "rollout3_min_confidence", "line": 812, "content": "rollout3_min_confidence: float = 0.1         # Confiance minimum"}, {"parameter": "rollout3_max_confidence", "line": 813, "content": "rollout3_max_confidence: float = 0.95        # Confiance maximum"}, {"parameter": "rollout3_so_priority_weight", "line": 816, "content": "rollout3_so_priority_weight: float = 0.7     # Poids priorité S/O"}, {"parameter": "rollout3_pb_priority_weight", "line": 817, "content": "rollout3_pb_priority_weight: float = 0.3     # Poids priorité P/B"}, {"parameter": "rollout3_coherence_weight", "line": 818, "content": "rollout3_coherence_weight: float = 0.4       # Poids cohérence analyseur"}, {"parameter": "rollout3_quality_weight", "line": 819, "content": "rollout3_quality_weight: float = 0.6         # Poids qualité séquence"}, {"parameter": "rollout3_excellent_threshold", "line": 822, "content": "rollout3_excellent_threshold: float = 0.8    # Seuil évaluation excellente"}, {"parameter": "rollout3_good_threshold", "line": 823, "content": "rollout3_good_threshold: float = 0.6         # Seuil évaluation bonne"}, {"parameter": "rollout3_acceptable_threshold", "line": 824, "content": "rollout3_acceptable_threshold: float = 0.4   # Seuil évaluation acceptable"}, {"parameter": "rollout3_poor_threshold", "line": 825, "content": "rollout3_poor_threshold: float = 0.2         # Seuil évaluation faible"}, {"parameter": "rollout3_minimum_quality_threshold", "line": 828, "content": "rollout3_minimum_quality_threshold: float = 0.3      # Seuil qualité minimum (30%)"}, {"parameter": "rollout3_conservative_probability", "line": 829, "content": "rollout3_conservative_probability: float = 0.5       # Probabilité conservative (40%)"}, {"parameter": "rollout3_confidence_bonus_correct", "line": 832, "content": "rollout3_confidence_bonus_correct: float = 0.2       # Bonus confiance correct (+20%)"}, {"parameter": "rollout3_confidence_bonus_incorrect", "line": 833, "content": "rollout3_confidence_bonus_incorrect: float = 0.1     # Bonus confiance humble (+10%)"}, {"parameter": "rollout3_optimal_risk", "line": 834, "content": "rollout3_optimal_risk: float = 0.55                  # Risque optimal"}, {"parameter": "rollout3_min_risk", "line": 835, "content": "rollout3_min_risk: float = 0.3                       # Risque minimum"}, {"parameter": "rollout3_max_risk", "line": 836, "content": "rollout3_max_risk: float = 0.8                       # Risque maximum"}, {"parameter": "rollout3_difficulty_bonus_max", "line": 837, "content": "rollout3_difficulty_bonus_max: float = 0.1           # Bonus difficulté max (+10%)"}, {"parameter": "rollout3_overconfidence_threshold", "line": 838, "content": "rollout3_overconfidence_threshold: float = 0.8       # Seuil sur-confiance"}, {"parameter": "rollout3_overconfidence_penalty_max", "line": 839, "content": "rollout3_overconfidence_penalty_max: float = 0.1     # Pénalité sur-confiance max (-10%)"}, {"parameter": "rollout3_min_reward", "line": 840, "content": "rollout3_min_reward: float = 0.0                     # Récompense minimum"}, {"parameter": "rollout3_max_reward", "line": 841, "content": "rollout3_max_reward: float = 1.3                     # Récompense maximum"}, {"parameter": "rollout3_evaluation_threshold_high", "line": 844, "content": "rollout3_evaluation_threshold_high: float = 0.8      # Seuil évaluation élevée"}, {"parameter": "rollout3_evaluation_threshold_medium", "line": 845, "content": "rollout3_evaluation_threshold_medium: float = 0.5    # Seuil évaluation moyenne"}, {"parameter": "rollout3_evaluation_threshold_low", "line": 846, "content": "rollout3_evaluation_threshold_low: float = 0.3       # Seuil évaluation faible"}, {"parameter": "rollout3_alignment_threshold", "line": 847, "content": "rollout3_alignment_threshold: float = 0.3            # Seuil alignement signal"}, {"parameter": "rollout3_default_return_value", "line": 850, "content": "rollout3_default_return_value: float = 0.0           # Valeur retour par défaut"}, {"parameter": "rollout3_fallback_alignment_value", "line": 851, "content": "rollout3_fallback_alignment_value: float = 0.3       # Valeur alignement fallback"}, {"parameter": "rollout3_neutral_evaluation_value", "line": 852, "content": "rollout3_neutral_evaluation_value: float = 0.5           # Valeur évaluation neutre (50%)"}, {"parameter": "rollout3_cluster_confidence_threshold_high", "line": 855, "content": "rollout3_cluster_confidence_threshold_high: float = 0.95  # Seuil confiance cluster élevée"}, {"parameter": "rollout3_cluster_confidence_threshold_medium", "line": 856, "content": "rollout3_cluster_confidence_threshold_medium: float = 0.5 # Seuil confiance cluster moyenne"}, {"parameter": "rollout3_cluster_confidence_threshold_low", "line": 857, "content": "rollout3_cluster_confidence_threshold_low: float = 0.3    # Seuil confiance cluster faible"}, {"parameter": "rollout3_cluster_confidence_bonus", "line": 858, "content": "rollout3_cluster_confidence_bonus: float = 0.15          # Bonus confiance cluster"}, {"parameter": "rollout3_cluster_confidence_default", "line": 859, "content": "rollout3_cluster_confidence_default: float = 0.5             # Confiance cluster par défaut (50%)"}, {"parameter": "rollout3_risk_factor_threshold", "line": 862, "content": "rollout3_risk_factor_threshold: float = 0.5          # Seuil facteur de risque"}, {"parameter": "rollout3_risk_factor_default", "line": 863, "content": "rollout3_risk_factor_default: float = 0.5            # Facteur de risque par défaut"}, {"parameter": "rollout3_quality_bonus_small", "line": 866, "content": "rollout3_quality_bonus_small: float = 0.3                # Petit bonus qualité (30%)"}, {"parameter": "rollout3_quality_bonus_medium", "line": 867, "content": "rollout3_quality_bonus_medium: float = 0.4               # Bonus qualité moyen (40%)"}, {"parameter": "rollout3_quality_bonus_large", "line": 868, "content": "rollout3_quality_bonus_large: float = 0.5                # Grand bonus qualité (50%)"}, {"parameter": "rollout3_quality_weight_so", "line": 869, "content": "rollout3_quality_weight_so: float = 0.7              # Poids priorité S/O (70%)"}, {"parameter": "rollout3_quality_weight_coherence", "line": 870, "content": "rollout3_quality_weight_coherence: float = 0.3       # Poids cohérence (30%)"}, {"parameter": "rollout3_correlation_threshold_low", "line": 873, "content": "rollout3_correlation_threshold_low: float = 0.15     # Seuil corrélation faible"}, {"parameter": "rollout3_correlation_threshold_high", "line": 874, "content": "rollout3_correlation_threshold_high: float = 0.2     # Seuil corrélation élevée"}, {"parameter": "rollout1_impair_consecutive_common", "line": 889, "content": "rollout1_impair_consecutive_common: float = 0.3          # Seuil IMPAIR consécutif commun (30%)"}, {"parameter": "rollout1_impair_consecutive_rare", "line": 890, "content": "rollout1_impair_consecutive_rare: float = 0.6            # Seuil IMPAIR consécutif rare (60%)"}, {"parameter": "rollout1_impair_consecutive_very_rare", "line": 891, "content": "rollout1_impair_consecutive_very_rare: float = 0.8       # Seuil IMPAIR consécutif très rare (80%)"}, {"parameter": "rollout1_impair_consecutive_ultra_rare", "line": 892, "content": "rollout1_impair_consecutive_ultra_rare: float = 0.95     # Seuil IMPAIR consécutif ultra-rare (95%)"}, {"parameter": "rollout1_pair_consecutive_very_common", "line": 896, "content": "rollout1_pair_consecutive_very_common: float = 0.1       # Seuil PAIR consécutif très commun (10%)"}, {"parameter": "rollout1_pair_consecutive_common", "line": 897, "content": "rollout1_pair_consecutive_common: float = 0.2            # Seuil PAIR consécutif commun (20%)"}, {"parameter": "rollout1_pair_consecutive_fairly_common", "line": 898, "content": "rollout1_pair_consecutive_fairly_common: float = 0.3     # Seuil PAIR consécutif assez commun (30%)"}, {"parameter": "rollout1_pair_consecutive_less_common", "line": 899, "content": "rollout1_pair_consecutive_less_common: float = 0.4       # Seuil PAIR consécutif moins commun (40%)"}, {"parameter": "rollout1_global_strength_threshold", "line": 902, "content": "rollout1_global_strength_threshold: float = 0.15        # Seuil force globale minimum (15%)"}, {"parameter": "rollout1_data_richness_factor", "line": 908, "content": "rollout1_data_richness_factor: int = 50                 # Minimum mains pour données riches (50)"}, {"parameter": "rollout1_coherence_neutral", "line": 909, "content": "rollout1_coherence_neutral: float = 0.5                 # Valeur neutre cohérence (50%)"}, {"parameter": "rollout1_coherence_threshold_excellent", "line": 910, "content": "rollout1_coherence_threshold_excellent: float = 0.4     # Seuil cohérence excellente (40%)"}, {"parameter": "rollout1_maximum_confidence_value", "line": 911, "content": "rollout1_maximum_confidence_value: float = 0.95         # Confiance maximum autorisée (95%)"}, {"parameter": "rollout1_so_start_offset", "line": 917, "content": "rollout1_so_start_offset: int = 1                       # Offset début séquences S/O (1)"}, {"parameter": "rollout1_max_divisor_safety", "line": 918, "content": "rollout1_max_divisor_safety: int = 1                    # Diviseur sécurité minimum (1)"}, {"parameter": "rollout1_phase_early_ratio", "line": 919, "content": "rollout1_phase_early_ratio: float = 0.3                 # Ratio phase précoce (30%)"}, {"parameter": "rollout1_phase_middle_ratio", "line": 920, "content": "rollout1_phase_middle_ratio: float = 0.7                # Ratio phase moyenne (70%)"}, {"parameter": "rollout2_fixed_length", "line": 934, "content": "rollout2_fixed_length: int = 4                         # Longueur fixe séquences P/B (4)"}, {"parameter": "rollout2_sequences_count", "line": 935, "content": "rollout2_sequences_count: int = 8                       # Nombre séquences générées (8)"}, {"parameter": "rollout2_default_strategy_prefix", "line": 936, "content": "rollout2_default_strategy_prefix: str = \"strategy\"      # Préfixe stratégie par défaut"}, {"parameter": "rollout2_default_signal_type", "line": 937, "content": "rollout2_default_signal_type: str = \"generic\"           # Type signal par défaut"}, {"parameter": "rollout2_default_signal_name", "line": 938, "content": "rollout2_default_signal_name: str = \"unknown\"           # Nom signal par défaut"}, {"parameter": "rollout2_default_justification", "line": 939, "content": "rollout2_default_justification: str = \"Generated\"       # Justification par défaut"}, {"parameter": "rollout2_so_prediction_keyword", "line": 942, "content": "rollout2_so_prediction_keyword: str = \"so_prediction\"   # Mot-clé prédictions S/O"}, {"parameter": "rollout2_pb_prediction_keyword", "line": 943, "content": "rollout2_pb_prediction_keyword: str = \"pb_prediction\"   # Mot-clé prédictions P/B"}, {"parameter": "rollout2_player_keyword", "line": 944, "content": "rollout2_player_keyword: str = \"player\"                 # Mot-clé patterns Player"}, {"parameter": "rollout2_banker_keyword", "line": 945, "content": "rollout2_banker_keyword: str = \"banker\"                 # Mot-clé patterns Banker"}, {"parameter": "rollout2_pair_sync_keyword", "line": 946, "content": "rollout2_pair_sync_keyword: str = \"pair_sync\"           # Mot-clé PAIR synchronisés"}, {"parameter": "rollout2_impair_sync_keyword", "line": 947, "content": "rollout2_impair_sync_keyword: str = \"impair_sync\"       # Mot-clé IMPAIR synchronisés"}, {"parameter": "rollout2_player_result", "line": 950, "content": "rollout2_player_result: str = \"P\"                       # Code résultat Player (P)"}, {"parameter": "rollout2_banker_result", "line": 951, "content": "rollout2_banker_result: str = \"B\"                       # Code résultat Banker (B)"}, {"parameter": "rollout2_so_same_result", "line": 952, "content": "rollout2_so_same_result: str = \"S\"                      # Code résultat Same (S)"}, {"parameter": "rollout2_base_confidence_high", "line": 958, "content": "rollout2_base_confidence_high: float = 0.8              # Confiance base élevée (80%)"}, {"parameter": "rollout2_base_confidence_medium", "line": 959, "content": "rollout2_base_confidence_medium: float = 0.6            # Confiance base moyenne (60%)"}, {"parameter": "rollout2_base_confidence_low", "line": 960, "content": "rollout2_base_confidence_low: float = 0.4               # Confiance base faible (40%)"}, {"parameter": "rollout2_confidence_value_high", "line": 963, "content": "rollout2_confidence_value_high: float = 0.7             # Valeur confiance élevée (70%)"}, {"parameter": "rollout2_confidence_value_medium_high", "line": 964, "content": "rollout2_confidence_value_medium_high: float = 0.65     # Valeur confiance moyenne-élev<PERSON> (65%)"}, {"parameter": "rollout2_confidence_value_standard", "line": 965, "content": "rollout2_confidence_value_standard: float = 0.6         # Valeur confiance standard (60%)"}, {"parameter": "rollout2_confidence_value_medium", "line": 966, "content": "rollout2_confidence_value_medium: float = 0.55          # Valeur confiance moyenne (55%)"}, {"parameter": "rollout2_signal_confidence_high", "line": 969, "content": "rollout2_signal_confidence_high: float = 0.8            # Seuil signal confiance élevée (80%)"}, {"parameter": "rollout2_signal_confidence_medium", "line": 970, "content": "rollout2_signal_confidence_medium: float = 0.6          # Seuil signal confiance moyenne (60%)"}, {"parameter": "rollout2_signal_confidence_low", "line": 971, "content": "rollout2_signal_confidence_low: float = 0.4             # Seuil signal confiance faible (40%)"}, {"parameter": "rollout2_signal_confidence_default", "line": 972, "content": "rollout2_signal_confidence_default: float = 0.5         # Confiance signal par défaut (50%)"}, {"parameter": "rollout2_confidence_level_excellent", "line": 975, "content": "rollout2_confidence_level_excellent: str = \"excellent\"   # Label confiance excellente"}, {"parameter": "rollout2_confidence_level_good", "line": 976, "content": "rollout2_confidence_level_good: str = \"good\"            # Label bonne confiance"}, {"parameter": "rollout2_confidence_level_acceptable", "line": 977, "content": "rollout2_confidence_level_acceptable: str = \"acceptable\"   # Label confiance acceptable"}, {"parameter": "rollout2_confidence_level_poor", "line": 978, "content": "rollout2_confidence_level_poor: str = \"poor\"            # Label confiance faible"}, {"parameter": "rollout2_max_probability", "line": 984, "content": "rollout2_max_probability: float = 0.85                 # Probabilité maximum (85%)"}, {"parameter": "rollout2_alternative_probability", "line": 985, "content": "rollout2_alternative_probability: float = 0.70         # Probabilité alternative (70%)"}, {"parameter": "rollout2_rupture_probability", "line": 986, "content": "rollout2_rupture_probability: float = 0.60             # Probabilité rupture (60%)"}, {"parameter": "rollout2_conservative_probability", "line": 987, "content": "rollout2_conservative_probability: float = 0.45        # Probabilité conservative (45%)"}, {"parameter": "rollout2_fallback_strategy_1", "line": 990, "content": "rollout2_fallback_strategy_1: str = \"high_confidence\"   # Stratégie fallback priorité 1"}, {"parameter": "rollout2_fallback_strategy_2", "line": 991, "content": "rollout2_fallback_strategy_2: str = \"alternative\"       # Stratégie fallback priorité 2"}, {"parameter": "rollout2_fallback_strategy_3", "line": 992, "content": "rollout2_fallback_strategy_3: str = \"rupture\"           # Stratégie fallback priorité 3"}, {"parameter": "rollout2_fallback_strategy_4", "line": 993, "content": "rollout2_fallback_strategy_4: str = \"conservative\"      # Stratégie fallback priorité 4"}, {"parameter": "rollout2_fallback_justification_1", "line": 996, "content": "rollout2_fallback_justification_1: str = \"High confidence pattern\"    # Justification stratégie haute confiance"}, {"parameter": "rollout2_fallback_justification_2", "line": 997, "content": "rollout2_fallback_justification_2: str = \"Alternative approach\"       # Justification approche alternative"}, {"parameter": "rollout2_fallback_justification_3", "line": 998, "content": "rollout2_fallback_justification_3: str = \"Rupture scenario\"           # Justification scénario rupture"}, {"parameter": "rollout2_fallback_justification_4", "line": 999, "content": "rollout2_fallback_justification_4: str = \"Conservative approach\"      # Justification approche conservative"}, {"parameter": "rollout2_adjustment_small", "line": 1005, "content": "rollout2_adjustment_small: float = 0.05                  # Petit ajustement (5%)"}, {"parameter": "rollout2_adjustment_medium", "line": 1006, "content": "rollout2_adjustment_medium: float = 0.15                 # Ajustement moyen (15%)"}, {"parameter": "rollout2_adjustment_large", "line": 1007, "content": "rollout2_adjustment_large: float = 0.2                   # Grand ajustement (20%)"}, {"parameter": "rollout2_signal_strength_threshold", "line": 1010, "content": "rollout2_signal_strength_threshold: float = 0.15         # Seuil force signal (15%)"}, {"parameter": "rollout2_signal_detection_threshold", "line": 1011, "content": "rollout2_signal_detection_threshold: float = 0.1         # Seuil détection signal (10%)"}, {"parameter": "rollout2_transition_signal_threshold", "line": 1012, "content": "rollout2_transition_signal_threshold: float = 0.6        # Seuil signal transition (60%)"}, {"parameter": "rollout2_exploitation_threshold_high", "line": 1015, "content": "rollout2_exploitation_threshold_high: float = 0.25       # Seuil exploitation élevé (25%)"}, {"parameter": "rollout2_exploitation_threshold_medium", "line": 1016, "content": "rollout2_exploitation_threshold_medium: float = 0.2      # Seuil <PERSON> moyen (20%)"}, {"parameter": "rollout2_exploitation_readiness_threshold", "line": 1017, "content": "rollout2_exploitation_readiness_threshold: float = 0.7     # Seuil préparation exploitation (70%)"}, {"parameter": "rollout2_global_strength_recommendation", "line": 1020, "content": "rollout2_global_strength_recommendation: float = 0.2      # Seuil force globale recommandé (20%)"}, {"parameter": "rollout2_statistical_significance_threshold", "line": 1021, "content": "rollout2_statistical_significance_threshold: float = 0.15     # Seuil significativité statistique (15%)"}, {"parameter": "rollout2_global_strength_high_threshold", "line": 1022, "content": "rollout2_global_strength_high_threshold: float = 0.3      # Seuil force globale élevée (30%)"}, {"parameter": "rollout2_global_strength_boost_factor", "line": 1023, "content": "rollout2_global_strength_boost_factor: float = 2.0        # Facteur boost force globale (×2.0)"}, {"parameter": "rollout2_priority_weight_1", "line": 1029, "content": "rollout2_priority_weight_1: float = 0.4                  # Poids priorité 1 IMPAIR (40%)"}, {"parameter": "rollout2_priority_weight_2", "line": 1030, "content": "rollout2_priority_weight_2: float = 0.3                  # Poids priorité 2 PAIR (30%)"}, {"parameter": "rollout2_priority_weight_3", "line": 1031, "content": "rollout2_priority_weight_3: float = 0.2                  # Poids priorité 3 SYNC (20%)"}, {"parameter": "rollout2_priority_weight_4", "line": 1032, "content": "rollout2_priority_weight_4: float = 0.1                  # Poids priorité 4 COMBINÉ (10%)"}, {"parameter": "rollout2_correlation_weight", "line": 1035, "content": "rollout2_correlation_weight: float = 0.3                 # Poids corrélations (30%)"}, {"parameter": "rollout2_consistency_weight", "line": 1036, "content": "rollout2_consistency_weight: float = 0.25                # Poids cohérence (25%)"}, {"parameter": "rollout2_evaluation_weight_standard", "line": 1037, "content": "rollout2_evaluation_weight_standard: float = 0.15        # Poids évaluation standard (15%)"}, {"parameter": "rollout2_sync_rate_enriched_threshold", "line": 1040, "content": "rollout2_sync_rate_enriched_threshold: float = 0.7       # Seuil taux sync enrichi (70%)"}, {"parameter": "rollout2_zone_confidence_threshold", "line": 1041, "content": "rollout2_zone_confidence_threshold: float = 0.2          # Seuil confiance zone (20%)"}, {"parameter": "rollout2_so_bias_strong_threshold", "line": 1042, "content": "rollout2_so_bias_strong_threshold: float = 0.7           # Seuil biais S/O fort (70%)"}, {"parameter": "rollout2_so_confidence_limit", "line": 1043, "content": "rollout2_so_confidence_limit: float = 0.9                # Limite confiance S/O (90%)"}, {"parameter": "rollout2_cross_impact_ratio_threshold", "line": 1046, "content": "rollout2_cross_impact_ratio_threshold: float = 0.6       # Seuil ratio impact croisé (60%)"}, {"parameter": "rollout2_cross_impact_confidence_boost", "line": 1047, "content": "rollout2_cross_impact_confidence_boost: float = 0.2      # Boost confiance impact croisé (20%)"}, {"parameter": "rollout2_cross_impact_max_confidence", "line": 1048, "content": "rollout2_cross_impact_max_confidence: float = 0.9        # Confiance max impact croisé (90%)"}, {"parameter": "rollout2_rewards", "line": 1051, "content": "rollout2_rewards: Dict[str, float] = field(default_factory=lambda: {  # Système de récompenses pour rollout2 (zone développement proximal)"}, {"parameter": "rollout3_quality_bonus_small", "line": 1079, "content": "rollout3_quality_bonus_small: float = 0.3                # Petit bonus qualité (30%)"}, {"parameter": "rollout3_quality_bonus_medium", "line": 1080, "content": "rollout3_quality_bonus_medium: float = 0.4               # Bonus qualité moyen (40%)"}, {"parameter": "rollout3_quality_bonus_large", "line": 1081, "content": "rollout3_quality_bonus_large: float = 0.5                # Grand bonus qualité (50%)"}, {"parameter": "rollout3_neutral_evaluation_value", "line": 1082, "content": "rollout3_neutral_evaluation_value: float = 0.5           # Valeur évaluation neutre (50%)"}, {"parameter": "rollout3_minimum_quality_threshold", "line": 1088, "content": "rollout3_minimum_quality_threshold: float = 0.3          # Seuil qualité minimum (30%)"}, {"parameter": "rollout3_default_confidence", "line": 1089, "content": "rollout3_default_confidence: float = 0.5                 # Confiance par défaut (50%)"}, {"parameter": "rollout3_conservative_probability", "line": 1090, "content": "rollout3_conservative_probability: float = 0.4           # Probabilité conservative (40%)"}, {"parameter": "rollout3_cluster_confidence_default", "line": 1096, "content": "rollout3_cluster_confidence_default: float = 0.5         # Confiance cluster par défaut (50%)"}, {"parameter": "rollout3_fallback_probability", "line": 1097, "content": "rollout3_fallback_probability: float = 0.45              # Probabilité fallback (45%)"}, {"parameter": "rollout3_fixed_length", "line": 1098, "content": "rollout3_fixed_length: int = 3                           # Longueur fixe S/O (3)"}, {"parameter": "rollout3_rewards", "line": 1104, "content": "rollout3_rewards: Dict[str, float] = field(default_factory=lambda: {  # Système de récompenses pour rollout3 (évaluation qualité)"}, {"parameter": "veto_sync_strength_threshold", "line": 1129, "content": "veto_sync_strength_threshold: float = 0.7                # Seuil force SYNC pour veto (70%)"}, {"parameter": "veto_combined_strength_threshold", "line": 1130, "content": "veto_combined_strength_threshold: float = 0.8            # Seuil force COMBINÉ pour veto (80%)"}, {"parameter": "veto_pair_strength_threshold", "line": 1131, "content": "veto_pair_strength_threshold: float = 0.6                # Seuil force PAIR pour veto (60%)"}, {"parameter": "rollout2_veto_sync_threshold", "line": 1134, "content": "rollout2_veto_sync_threshold: float = 0.7                # <PERSON><PERSON> veto SYNC rollout2 (70%)"}, {"parameter": "rollout2_veto_combined_threshold", "line": 1135, "content": "rollout2_veto_combined_threshold: float = 0.8            # <PERSON><PERSON> veto COMBINÉ rollout2 (80%)"}, {"parameter": "veto_impairs_weak_threshold", "line": 1136, "content": "veto_impairs_weak_threshold: float = 0.3                 # Seuil IMPAIRS faibles (30%)"}, {"parameter": "veto_sync_impair_weakness", "line": 1142, "content": "veto_sync_impair_weakness: float = 0.3                   # Seuil faiblesse IMPAIR vs SYNC (30%)"}, {"parameter": "veto_combined_impair_weakness", "line": 1143, "content": "veto_combined_impair_weakness: float = 0.4               # Seuil faiblesse IMPAIR vs COMBINÉ (40%)"}, {"parameter": "veto_pair_impair_weakness", "line": 1144, "content": "veto_pair_impair_weakness: float = 0.2                   # Seuil faiblesse IMPAIR vs PAIR (20%)"}, {"parameter": "rollout_base_performance", "line": 1158, "content": "rollout_base_performance: int = 100                       # Performance base (100 rollouts/sec)"}, {"parameter": "rollout_parallel_efficiency", "line": 1159, "content": "rollout_parallel_efficiency: float = 0.8                 # Efficacité parallélisation (80%)"}, {"parameter": "rollout_base_memory_per_hand", "line": 1165, "content": "rollout_base_memory_per_hand: float = 0.5                # Mémoire base par manche (0.5 KB)"}, {"parameter": "rollout_average_hands_per_game", "line": 1166, "content": "rollout_average_hands_per_game: int = 60                 # Manches moyennes par partie (60)"}, {"parameter": "rollout_kb_to_mb_conversion", "line": 1167, "content": "rollout_kb_to_mb_conversion: int = 1024                  # Facteur conversion KB→MB (1024)"}, {"parameter": "rollout_memory_high_threshold", "line": 1170, "content": "rollout_memory_high_threshold: int = 80                  # Seuil mémoire élevée (80%)"}, {"parameter": "rollout_memory_low_threshold", "line": 1171, "content": "rollout_memory_low_threshold: int = 50                   # Seuil mémoire faible (50%)"}, {"parameter": "rollout_memory_limit_gb", "line": 1172, "content": "rollout_memory_limit_gb: int = 8                         # Limite mémoire mode efficace (8 GB)"}, {"parameter": "rollout_cpu_low_threshold", "line": 1178, "content": "rollout_cpu_low_threshold: int = 50                      # Seuil CPU sous-utilisé (50%)"}, {"parameter": "rollout_cpu_high_threshold", "line": 1179, "content": "rollout_cpu_high_threshold: int = 90                     # Seuil CPU surchargé (90%)"}, {"parameter": "rollout_accuracy_min_samples", "line": 1182, "content": "rollout_accuracy_min_samples: int = 5                    # Minimum échantillons tendance (5)"}, {"parameter": "rollout_accuracy_recent_window", "line": 1183, "content": "rollout_accuracy_recent_window: int = 10                 # Fenêtre analyse récente (10)"}, {"parameter": "rollout_accuracy_min_regression", "line": 1184, "content": "rollout_accuracy_min_regression: int = 2                 # Minimum points régression (2)"}, {"parameter": "rollout_analyzer_normality_threshold", "line": 1198, "content": "rollout_analyzer_normality_threshold: float = 0.5        # Seuil écart normalité (50%)"}, {"parameter": "rollout_analyzer_amplification_factor", "line": 1199, "content": "rollout_analyzer_amplification_factor: float = 2.0       # Facteur amplification rareté (×2.0)"}, {"parameter": "rollout_analyzer_context_min_value", "line": 1205, "content": "rollout_analyzer_context_min_value: float = 0.1          # Valeur contextuelle minimum (10%)"}, {"parameter": "rollout_analyzer_normalcy_divisor", "line": 1206, "content": "rollout_analyzer_normalcy_divisor: float = 10.0          # Diviseur normalisation (10.0)"}, {"parameter": "rollout_analyzer_support_min_weight", "line": 1207, "content": "rollout_analyzer_support_min_weight: float = 0.1         # Poids support minimum (10%)"}, {"parameter": "rollout_analyzer_support_max_reduction", "line": 1208, "content": "rollout_analyzer_support_max_reduction: float = 20.0     # Réduction max support (÷20.0)"}, {"parameter": "rollout_analyzer_complementarity_divisor", "line": 1209, "content": "rollout_analyzer_complementarity_divisor: float = 50.0     # Diviseur bonus complémentarité (50.0)"}, {"parameter": "rollout_analyzer_position_offset", "line": 1215, "content": "rollout_analyzer_position_offset: int = 1                # Offset position index (1)"}, {"parameter": "rollout_analyzer_sequence_window", "line": 1216, "content": "rollout_analyzer_sequence_window: int = 3                # Fenêtre séquences récentes (3)"}, {"parameter": "rollout_analyzer_min_regression_points", "line": 1217, "content": "rollout_analyzer_min_regression_points: int = 2          # Minimum points régression (2)"}, {"parameter": "rollout_analyzer_recent_window_size", "line": 1218, "content": "rollout_analyzer_recent_window_size: int = 3             # <PERSON><PERSON> fenêtre r<PERSON> (3)"}, {"parameter": "rollout_analyzer_sequence_increment", "line": 1219, "content": "rollout_analyzer_sequence_increment: int = 1             # Incrément position séquence (1)"}, {"parameter": "rollout_analyzer_min_sequence_length", "line": 1220, "content": "rollout_analyzer_min_sequence_length: int = 1            # Longueur minimum séquence (1)"}, {"parameter": "rollout_analyzer_signal_increment", "line": 1221, "content": "rollout_analyzer_signal_increment: int = 1               # Incrément ajout signaux (1)"}, {"parameter": "rollout_reward_valid_sequence_increment", "line": 1224, "content": "rollout_reward_valid_sequence_increment: int = 1         # Incrément séquences valides (1)"}, {"parameter": "cluster_analysis_time_ms", "line": 1238, "content": "cluster_analysis_time_ms: int = 60              # Temps phase analyse (60ms)"}, {"parameter": "cluster_generation_time_ms", "line": 1239, "content": "cluster_generation_time_ms: int = 50            # Temps phase génération (50ms)"}, {"parameter": "cluster_prediction_time_ms", "line": 1240, "content": "cluster_prediction_time_ms: int = 60            # Temps phase prédiction (60ms)"}, {"parameter": "cluster_total_time_ms", "line": 1241, "content": "cluster_total_time_ms: int = 170                # Temps total optimal (170ms)"}, {"parameter": "cluster_rollout1_weight", "line": 1247, "content": "cluster_rollout1_weight: float = 0.0                     # Poids rollout1 (0%)"}, {"parameter": "cluster_rollout2_weight", "line": 1248, "content": "cluster_rollout2_weight: float = 0.4                     # Poids rollout2 (40%)"}, {"parameter": "cluster_rollout3_weight", "line": 1249, "content": "cluster_rollout3_weight: float = 0.6                     # Poids rollout3 (60%)"}, {"parameter": "cluster_count_increment", "line": 1255, "content": "cluster_count_increment: float = 1.0                     # Incrément comptage (1.0)"}, {"parameter": "cluster_same_increment", "line": 1256, "content": "cluster_same_increment: float = 1.0                      # Incrément même résultat (1.0)"}, {"parameter": "cluster_probability_neutral", "line": 1257, "content": "cluster_probability_neutral: float = 0.5                 # Probabilité neutre (50%)"}, {"parameter": "cluster_confidence_multiplier", "line": 1258, "content": "cluster_confidence_multiplier: float = 2.0               # Multiplicateur confiance (×2.0)"}, {"parameter": "cluster_confidence_sequence_weight", "line": 1289, "content": "cluster_confidence_sequence_weight: float = 0.3          # Poids probabilité séquence (30%)"}, {"parameter": "cluster_confidence_evaluation_weight", "line": 1290, "content": "cluster_confidence_evaluation_weight: float = 0.3        # Poids évaluation (30%)"}, {"parameter": "cluster_confidence_analysis_weight", "line": 1291, "content": "cluster_confidence_analysis_weight: float = 0.25         # Poids analyse rollout1 (25%)"}, {"parameter": "cluster_confidence_signals_weight", "line": 1292, "content": "cluster_confidence_signals_weight: float = 0.15          # Poids confiance signaux (15%)"}, {"parameter": "cluster_base_confidence_high", "line": 1295, "content": "cluster_base_confidence_high: float = 0.8                # Confiance base élevée (80%)"}, {"parameter": "cluster_base_confidence_medium", "line": 1296, "content": "cluster_base_confidence_medium: float = 0.65             # Confiance base moyenne (65%)"}, {"parameter": "cluster_base_confidence_low", "line": 1297, "content": "cluster_base_confidence_low: float = 0.6                 # Confiance base faible (60%)"}, {"parameter": "cluster_base_confidence_default", "line": 1298, "content": "cluster_base_confidence_default: float = 0.5             # Confiance base par défaut (50%)"}, {"parameter": "cluster_confidence_adjustment_small", "line": 1304, "content": "cluster_confidence_adjustment_small: float = 0.1         # Petit ajustement confiance (10%)"}, {"parameter": "cluster_confidence_adjustment_medium", "line": 1305, "content": "cluster_confidence_adjustment_medium: float = 0.15       # Ajustement confiance moyen (15%)"}, {"parameter": "cluster_confidence_adjustment_large", "line": 1306, "content": "cluster_confidence_adjustment_large: float = 0.2         # Grand ajustement confiance (20%)"}, {"parameter": "cluster_confidence_limit_max", "line": 1309, "content": "cluster_confidence_limit_max: float = 0.95               # Limite confiance maximum (95%)"}, {"parameter": "cluster_confidence_limit_medium", "line": 1310, "content": "cluster_confidence_limit_medium: float = 0.9             # Limite confiance moyenne (90%)"}, {"parameter": "cluster_confidence_threshold_adjustment", "line": 1313, "content": "cluster_confidence_threshold_adjustment: float = 0.05     # Ajustement seuils confiance (5%)"}, {"parameter": "cluster_bias_significance_threshold", "line": 1314, "content": "cluster_bias_significance_threshold: float = 0.1         # Seuil significativité biais (10%)"}, {"parameter": "cluster_reward_threshold_high", "line": 1328, "content": "cluster_reward_threshold_high: float = 0.8               # Seuil récompense élevée (80%)"}, {"parameter": "cluster_reward_threshold_medium", "line": 1329, "content": "cluster_reward_threshold_medium: float = 0.6             # Seuil récompense moyenne (60%)"}, {"parameter": "cluster_reward_threshold_low", "line": 1330, "content": "cluster_reward_threshold_low: float = 0.4                # Seuil récompense faible (40%)"}, {"parameter": "cluster_quality_score_bonus_high", "line": 1333, "content": "cluster_quality_score_bonus_high: float = 0.2            # Bonus score qualité élevé (20%)"}, {"parameter": "cluster_quality_score_bonus_medium", "line": 1334, "content": "cluster_quality_score_bonus_medium: float = 0.15         # Bonus score qualité moyen (15%)"}, {"parameter": "cluster_quality_score_bonus_low", "line": 1335, "content": "cluster_quality_score_bonus_low: float = 0.1             # Bonus score qualité faible (10%)"}, {"parameter": "cluster_consensus_threshold_high", "line": 1341, "content": "cluster_consensus_threshold_high: float = 0.8            # Seuil consensus élevé (80%)"}, {"parameter": "cluster_consensus_threshold_medium", "line": 1342, "content": "cluster_consensus_threshold_medium: float = 0.6          # <PERSON><PERSON> consensus moyen (60%)"}, {"parameter": "cluster_consensus_weight_factor", "line": 1343, "content": "cluster_consensus_weight_factor: float = 0.3             # Facteur poids consensus (30%)"}, {"parameter": "cluster_accuracy_threshold_excellent", "line": 1346, "content": "cluster_accuracy_threshold_excellent: float = 0.85       # Seuil précision excellente (85%)"}, {"parameter": "cluster_accuracy_threshold_good", "line": 1347, "content": "cluster_accuracy_threshold_good: float = 0.75            # Seuil précision bonne (75%)"}, {"parameter": "cluster_accuracy_threshold_acceptable", "line": 1348, "content": "cluster_accuracy_threshold_acceptable: float = 0.65      # Seuil précision acceptable (65%)"}, {"parameter": "cluster_metrics_update_frequency", "line": 1354, "content": "cluster_metrics_update_frequency: int = 10               # Fréquence mise à jour métriques (10)"}, {"parameter": "cluster_metrics_window_size", "line": 1355, "content": "cluster_metrics_window_size: int = 100                   # Taille fenêtre métriques (100)"}, {"parameter": "cluster_test_accuracy_threshold", "line": 1358, "content": "cluster_test_accuracy_threshold: float = 0.7             # Seuil précision test (70%)"}, {"parameter": "cluster_test_confidence_threshold", "line": 1359, "content": "cluster_test_confidence_threshold: float = 0.6           # Seuil confiance test (60%)"}, {"parameter": "cluster_boost_factor", "line": 1365, "content": "cluster_boost_factor: float = 1.2                        # Facteur boost général (×1.2)"}, {"parameter": "cluster_weight_factor_high", "line": 1366, "content": "cluster_weight_factor_high: float = 1.5                  # Facteur poids élevé (×1.5)"}, {"parameter": "cluster_weight_factor_medium", "line": 1367, "content": "cluster_weight_factor_medium: float = 1.2                # Facteur poids moyen (×1.2)"}, {"parameter": "cluster_weight_factor_low", "line": 1368, "content": "cluster_weight_factor_low: float = 1.0                   # Facteur poids faible (×1.0)"}, {"parameter": "cluster_pattern_specializations", "line": 1371, "content": "cluster_pattern_specializations: Dict[int, Dict] = None  # Spécialisations patterns par cluster (rollout1)"}, {"parameter": "cluster_high_signal_risk", "line": 1403, "content": "cluster_high_signal_risk: float = 0.8               # Risque élevé si peu de signaux"}, {"parameter": "cluster_default_window_size", "line": 1404, "content": "cluster_default_window_size: int = 5                # Taille fenêtre par défaut cluster"}, {"parameter": "cluster_default_burn_parity", "line": 1405, "content": "cluster_default_burn_parity: str = 'IMPAIR'         # Parité brûlage par défaut"}, {"parameter": "cluster_default_sync_state", "line": 1406, "content": "cluster_default_sync_state: str = 'DESYNC'          # État sync par défaut"}, {"parameter": "n_rollouts", "line": 1419, "content": "n_rollouts: int = 16                        # Nombre rollouts (2 par cœur)"}, {"parameter": "rollout_temperature", "line": 1420, "content": "rollout_temperature: float = 0.7            # Température des rollouts"}, {"parameter": "rollout_step_size", "line": 1421, "content": "rollout_step_size: float = 0.15             # Taille de pas rollout"}, {"parameter": "rollout_random_factor", "line": 1422, "content": "rollout_random_factor: float = 0.1          # Facteur aléatoire rollout"}, {"parameter": "parallel_rollouts", "line": 1425, "content": "parallel_rollouts: bool = True              # Activer rollouts parallèles"}, {"parameter": "veto_sync_exceptional_threshold", "line": 2000, "content": "veto_sync_exceptional_threshold: float = 0.85        # <PERSON><PERSON> veto SYNC exceptionnel"}, {"parameter": "veto_combined_ultra_rare_threshold", "line": 2001, "content": "veto_combined_ultra_rare_threshold: float = 0.90     # <PERSON><PERSON> veto COMBINÉ ultra-rare"}, {"parameter": "veto_pairs_dominant_threshold", "line": 2002, "content": "veto_pairs_dominant_threshold: float = 0.70          # <PERSON><PERSON> veto PAIRS dominant"}, {"parameter": "veto_impairs_weak_threshold", "line": 2003, "content": "veto_impairs_weak_threshold: float = 0.20                # Seuil IMPAIRS faibles (30%)"}, {"parameter": "veto_coalition_threshold", "line": 2004, "content": "veto_coalition_threshold: float = 0.75               # Seuil coalition SYNC+COMBINÉ"}, {"parameter": "veto_impairs_coalition_weak_threshold", "line": 2005, "content": "veto_impairs_coalition_weak_threshold: float = 0.25  # Seuil IMPAIRS faibles coalition"}, {"parameter": "estimation_conservative_rare", "line": 2023, "content": "estimation_conservative_rare: float = 0.075          # Estimation conservative rare (0.25 * self.config.rollout3_quality_bonus_small)"}, {"parameter": "def __init__(self, cluster_id", "line": 2707, "content": "def __init__(self, cluster_id: int, config: AZRConfig, predictor_instance=None):"}, {"parameter": "recent_lengths = sequence_lengths[-self.config.rollout_analyzer_recent_window_size", "line": 3135, "content": "recent_lengths = sequence_lengths[-self.config.rollout_analyzer_recent_window_size:] if len(sequence_lengths) >= self.config.rollout_analyzer_recent_window_size else sequence_lengths"}, {"parameter": "if len(so_sequence) != self.config.rollout3_fixed_length", "line": 5302, "content": "if len(so_sequence) != self.config.rollout3_fixed_length:"}, {"parameter": "so_sequence = so_sequence[", "line": 5310, "content": "so_sequence = so_sequence[:self.config.rollout3_fixed_length]"}, {"parameter": "def calculate_cluster_total_reward(self, rollout1_result", "line": 5623, "content": "def calculate_cluster_total_reward(self, rollout1_result: Dict, rollout2_result: Dict, rollout3_result: Dict, actual_outcome: str = None) -> Dict:"}, {"parameter": "if len(exhaustive_sequences) == self.config.rollout2_sequences_count", "line": 5756, "content": "if len(exhaustive_sequences) == self.config.rollout2_sequences_count:"}, {"parameter": "if signal_type == self.config.rollout2_so_prediction_keyword and target_outcome", "line": 5801, "content": "if signal_type == self.config.rollout2_so_prediction_keyword and target_outcome:"}, {"parameter": "elif signal_type == self.config.rollout2_pb_prediction_keyword", "line": 5804, "content": "elif signal_type == self.config.rollout2_pb_prediction_keyword:"}, {"parameter": "for sequence_tuple in itertools.product(['P', 'B'], repeat=self.config.rollout2_fixed_length)", "line": 5954, "content": "for sequence_tuple in itertools.product(['P', 'B'], repeat=self.config.rollout2_fixed_length):"}, {"parameter": "top_4_sequences = all_so_possibilities[", "line": 5989, "content": "top_4_sequences = all_so_possibilities[:self.config.rollout2_sequences_count]"}, {"parameter": "aligned_impair_pair = impair_pair_seq[self.config.rollout1_so_start_offset", "line": 6766, "content": "aligned_impair_pair = impair_pair_seq[self.config.rollout1_so_start_offset:len(so_seq)+self.config.rollout1_so_start_offset] if len(impair_pair_seq) > len(so_seq) else impair_pair_seq[:len(so_seq)]"}, {"parameter": "if global_strength >= self.config.rollout2_exploitation_threshold_high", "line": 9297, "content": "if global_strength >= self.config.rollout2_exploitation_threshold_high:"}, {"parameter": "elif global_strength >= self.config.rollout2_exploitation_threshold_medium", "line": 9299, "content": "elif global_strength >= self.config.rollout2_exploitation_threshold_medium:"}, {"parameter": "elif global_strength >= self.config.rollout1_global_strength_threshold", "line": 9306, "content": "elif global_strength >= self.config.rollout1_global_strength_threshold:"}, {"parameter": "if optimal_phase == 'late_game' and i >= sequence_length * self.config.rollout2_confidence_value_high", "line": 10671, "content": "if optimal_phase == 'late_game' and i >= sequence_length * self.config.rollout2_confidence_value_high:"}, {"parameter": "elif optimal_phase == 'early_game' and i < sequence_length * self.config.rollout3_quality_bonus_small", "line": 10675, "content": "elif optimal_phase == 'early_game' and i < sequence_length * self.config.rollout3_quality_bonus_small:"}, {"parameter": "if predicted_pb == 'P' and expected_p_ratio > self.config.rollout2_cross_impact_ratio_threshold", "line": 11321, "content": "if predicted_pb == 'P' and expected_p_ratio > self.config.rollout2_cross_impact_ratio_threshold:"}, {"parameter": "if predicted_pb == 'P' and expected_p_ratio > self.config.rollout2_cross_impact_ratio_threshold", "line": 11335, "content": "if predicted_pb == 'P' and expected_p_ratio > self.config.rollout2_cross_impact_ratio_threshold:"}, {"parameter": "if predicted_so == 'S' and sync_s_ratio > self.config.rollout2_so_bias_strong_threshold", "line": 11348, "content": "if predicted_so == 'S' and sync_s_ratio > self.config.rollout2_so_bias_strong_threshold:"}, {"parameter": "if predicted_so == 'S' and desync_s_ratio > self.config.rollout2_so_bias_strong_threshold", "line": 11357, "content": "if predicted_so == 'S' and desync_s_ratio > self.config.rollout2_so_bias_strong_threshold:"}, {"parameter": "if not self.config.parallel_rollouts or self.config.n_rollouts <= 4", "line": 14231, "content": "if not self.config.parallel_rollouts or self.config.n_rollouts <= 4:"}, {"parameter": "AVANT", "line": 14243, "content": "AVANT : 16 rollouts × reconstruction complète = 16x duplication !"}, {"parameter": "APRÈS", "line": 14244, "content": "APRÈS : 1 construction + 16 rollouts = Performance optimale !"}, {"parameter": "def benchmark_cpu_performance(self, test_rollouts", "line": 15775, "content": "def benchmark_cpu_performance(self, test_rollouts: int = 100, test_generation: int = 1000) -> Dict[str, Any]:"}, {"parameter": "recent_accuracy = self.accuracy_history[-self.config.rollout_accuracy_recent_window", "line": 16049, "content": "recent_accuracy = self.accuracy_history[-self.config.rollout_accuracy_recent_window:]"}, {"parameter": "if sync_states[pos - self.config.rollout_analyzer_position_offset] == 'DESYNC'", "line": 16085, "content": "if sync_states[pos - self.config.rollout_analyzer_position_offset] == 'DESYNC':"}, {"parameter": "if sync_states[pos - self.config.rollout_analyzer_position_offset] == 'DESYNC'", "line": 16093, "content": "if sync_states[pos - self.config.rollout_analyzer_position_offset] == 'DESYNC':"}, {"parameter": "veto_result = {'veto_applied'", "line": 16601, "content": "veto_result = {'veto_applied': False}"}, {"parameter": "test_rollouts = int(input(\"Nombre de rollouts à tester", "line": 19191, "content": "test_rollouts = int(input(\"Nombre de rollouts à tester: \") or \"500\")"}], "sections_affected": ["C2", "A", "R1", "C3", "R3", "C1", "P", "V", "R2"], "risk_level": "high", "validation_required": ["validation_commentaires_finale.py", "test_rollout_diversity.py", "benchmark_cpu_performance()", "test_veto_system.py", "test_predictions_sample.py"]}, "validation_status": "pending"}], "file_hashes": {"azr_baccarat_predictor.py": "91e1ce9fe3af4414f708ea5f71825b37"}, "last_check": "2025-06-03T21:52:59.946885"}