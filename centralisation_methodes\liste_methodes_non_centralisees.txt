LISTE DES MÉTHODES NON CENTRALISÉES DANS CLASS.TXT
==================================================

Total des méthodes dans class.txt : 162 méthodes
Méthodes universelles centralisées : 31 méthodes
Méthodes restantes non centralisées : 131 méthodes

MÉTHODES RETIRÉES (31 méthodes universelles centralisées) :
==========================================================

ROLLOUT 1 ANALYSEUR (15 méthodes retirées) :
1. _rollout_analyzer() - Ligne 108 ❌ CENTRALISÉE
5. _analyze_impair_consecutive_bias() - Ligne 413 ❌ CENTRALISÉE
6. _analyze_pair_priority_2_autonomous() - Ligne 641 ❌ CENTRALISÉE
7. _analyze_sync_alternation_bias() - Ligne 753 ❌ CENTRALISÉE
20. _correlate_impair_with_sync() - Ligne 1787 ❌ CENTRALISÉE
21. _correlate_impair_with_combined() - Ligne 1828 ❌ CENTRALISÉE
22. _correlate_impair_with_pb() - Ligne 1870 ❌ CENTRALISÉE
23. _correlate_impair_with_so() - Ligne 1910 ❌ CENTRALISÉE
24. _correlate_bias_to_pb_variations() - Ligne 1950 ❌ CENTRALISÉE
25. _correlate_bias_to_so_variations() - Ligne 2001 ❌ CENTRALISÉE
29. _analyze_combined_structural_bias() - Ligne 2250 ❌ CENTRALISÉE
32. _generate_priority_based_synthesis_autonomous() - Ligne 2512 ❌ CENTRALISÉE
34. _generate_bias_signals_summary() - Ligne 2835 ❌ CENTRALISÉE
35. _generate_bias_generation_guidance() - Ligne 2851 ❌ CENTRALISÉE
36. _generate_bias_quick_access() - Ligne 2864 ❌ CENTRALISÉE

ROLLOUT 2 GÉNÉRATEUR (5 méthodes retirées) :
41. _rollout_generator() - Ligne 3201 ❌ CENTRALISÉE
65. _define_optimized_generation_space() - Ligne 4531 ❌ CENTRALISÉE
66. _generate_sequences_from_signals() - Ligne 4568 ❌ CENTRALISÉE
68. _generate_fallback_sequences() - Ligne 4654 ❌ CENTRALISÉE
146. _enrich_sequences_with_complete_indexes() - Ligne 10034 ❌ CENTRALISÉE

ROLLOUT 3 PRÉDICTEUR (11 méthodes retirées) :
42. _rollout_predictor() - Ligne 3308 ❌ CENTRALISÉE
43. _evaluate_sequence_quality() - Ligne 3437 ❌ CENTRALISÉE
44. _evaluate_signal_alignment() - Ligne 3473 ❌ CENTRALISÉE
45. _evaluate_fallback_alignment() - Ligne 3529 ❌ CENTRALISÉE
46. _analyze_sequence_consistency() - Ligne 3553 ❌ CENTRALISÉE
47. _assess_risk_reward_ratio() - Ligne 3613 ❌ CENTRALISÉE
48. _validate_sequence_logic() - Ligne 3659 ❌ CENTRALISÉE
49. _calculate_sequence_score() - Ligne 3720 ❌ CENTRALISÉE
50. _select_best_sequence() - Ligne 3749 ❌ CENTRALISÉE
52. _calculate_cluster_confidence_azr_calibrated() - Ligne 3833 ❌ CENTRALISÉE
57. _convert_pb_sequence_to_so() - Ligne 4096 ❌ CENTRALISÉE

MÉTHODES RESTANTES NON CENTRALISÉES (131 méthodes) :
===================================================

MÉTHODES PRINCIPALES ET UTILITAIRES :
1. __init__(self, cluster_id: int, config: AZRConfig, predictor_instance=None) - Ligne 1
2. execute_cluster_pipeline(self, standardized_sequence: Dict) -> Dict - Ligne 29

MÉTHODES SPÉCIALISÉES PAR CLUSTER :
3. _rollout_analyzer_c3_patterns_moyens(self, standardized_sequence: Dict) -> Dict - Ligne 222
4. _rollout_analyzer_c2_patterns_courts(self, standardized_sequence: Dict) -> Dict - Ligne 909
8. _analyze_impair_consecutive_bias_c2_specialized(self, hands_data: List, position_types: List, ...) - Ligne 1096
9. _analyze_sync_alternation_bias_c2_specialized(self, hands_data: List) -> Dict - Ligne 1207
10. _apply_c2_short_patterns_specialization(self, base_analysis: Dict) -> Dict - Ligne 1287
11. _generate_bias_signals_summary_c2(self, bias_synthesis: Dict, c2_specialization: Dict) -> Dict - Ligne 1328
12. _generate_bias_generation_guidance_c2(self, bias_synthesis: Dict, c2_specialization: Dict) -> Dict - Ligne 1351
13. _generate_bias_quick_access_c2(self, bias_synthesis: Dict, c2_specialization: Dict) -> Dict - Ligne 1374
14. _get_cluster_specialization_params(self, cluster_id: int) -> Dict - Ligne 1402
15. _create_generic_cluster_analyzer(self, cluster_id: int, standardized_sequence: Dict) -> Dict - Ligne 1468
16. _analyze_impair_bias_specialized(self, hands_data: List, position_types: List, ...) - Ligne 1652
17. _analyze_sync_bias_specialized(self, hands_data: List, cluster_id: int, spec_params: Dict) -> Dict - Ligne 1700
18. _apply_cluster_specialization(self, base_analysis: Dict, cluster_id: int, spec_params: Dict) -> Dict - Ligne 1745
19. _analyze_impair_consecutive_bias_c3_specialized(self, hands_data: List, position_types: List, ...) - Ligne 2043
26. _analyze_sync_alternation_bias_c3_specialized(self, hands_data: List) -> Dict - Ligne 2128
27. _apply_c3_medium_patterns_specialization(self, base_analysis: Dict) -> Dict - Ligne 2209

MÉTHODES CORRÉLATION DUPLIQUÉES (versions non universelles) :
28. _correlate_bias_to_pb_variations(self, impair_bias: Dict, sync_bias: Dict, combined_bias: Dict, hands_data: List) -> Dict - Ligne 2374
30. _correlate_bias_to_so_variations(self, pb_correlation: Dict, hands_data: List) -> Dict - Ligne 2442

MÉTHODES SYNTHÈSE ET ANALYSE COMPLÈTE :
31. _generate_bias_exploitation_synthesis(self, bias_analyses: Dict, hands_data: List) -> Dict - Ligne 2670
33. _generate_complete_synthesis(self, all_indices: Dict, hands_data: List) -> Dict - Ligne 2877
37. _calculate_cross_index_impacts(self, all_indices: Dict) -> Dict - Ligne 2967
38. _calculate_variations_impact(self, all_indices: Dict) -> Dict - Ligne 3067
39. _calculate_global_strength_metrics(self, all_indices: Dict) -> Dict - Ligne 3121

MÉTHODES CONFIANCE ET PRÉDICTION ALTERNATIVES :
40. _calculate_cluster_confidence(self, best_sequence: Dict, analyzer_report: Dict) -> float - Ligne 3788
51. _calculate_confidence_risk_factors(self, best_sequence: Dict, analyzer_report: Dict) -> float - Ligne 3916
53. _calculate_epistemic_uncertainty(self, analyzer_report: Dict) -> float - Ligne 3975
54. _calculate_rollout_consensus(self, best_sequence: Dict, analyzer_report: Dict) -> float - Ligne 4013
55. _extract_next_hand_prediction(self, best_sequence: Dict) -> str - Ligne 4062
56. _get_last_historical_pb_result(self, analyzer_report: Dict) -> str - Ligne 4146

MÉTHODES CALCUL RÉCOMPENSES :
58. calculate_rollout2_reward(self, sequence_quality: float, diversity_score: float, difficulty_factor: float) -> Dict - Ligne 4178
59. calculate_rollout2_sequence_quality(self, sequences: List[Dict]) -> float - Ligne 4247
60. calculate_rollout2_diversity_score(self, sequences: List[Dict]) -> float - Ligne 4288
61. calculate_rollout3_reward(self, prediction: str, actual_outcome: str, confidence: float, risk_factor: float) -> Dict - Ligne 4326
62. calculate_rollout3_risk_factor(self, prediction_data: Dict, analyzer_report: Dict) -> float - Ligne 4414
63. calculate_cluster_total_reward(self, rollout1_result: Dict, rollout2_result: Dict, rollout3_result: Dict, actual_outcome: str = None) -> Dict - Ligne 4455

MÉTHODES GÉNÉRATION SÉQUENCES SPÉCIALISÉES :
64. _generate_sequence_from_signal(self, signal: Dict, generation_space: Dict) -> List[str] - Ligne 4620
67. _classify_confidence_level(self, signal_confidence: float) -> str - Ligne 4700
69. _generate_so_based_sequence(self, target_outcome: str, sequence_length: int, generation_space: Dict) -> List[str] - Ligne 4719
70. _generate_all_possible_sequences(self, generation_space: Dict) -> List[Dict] - Ligne 4760
71. _convert_pb_sequence_to_so_with_history(self, pb_sequence: List[str], last_historical_pb: str) -> List[str] - Ligne 4833
72. _calculate_sequence_probability(self, sequence: List[str], generation_space: Dict) -> float - Ligne 4861
73. _calculate_sequence_quality_metrics(self, sequence: List[str], generation_space: Dict) -> Dict - Ligne 4953
74. _generate_pb_sequence(self, target_pb: str, sequence_length: int, generation_space: Dict) -> List[str] - Ligne 5000
75. _generate_pair_sync_sequence(self, sequence_length: int, generation_space: Dict) -> List[str] - Ligne 5047
76. _generate_impair_sync_sequence(self, sequence_length: int, generation_space: Dict) -> List[str] - Ligne 5089
77. _generate_generic_signal_sequence(self, signal: Dict, sequence_length: int, generation_space: Dict) -> List[str] - Ligne 5134

MÉTHODES ANALYSE INDICES COMPLETS :
78. _analyze_complete_impair_pair_index(self, hands_data: List) -> Dict - Ligne 5189
79. _analyze_complete_desync_sync_index(self, hands_data: List) -> Dict - Ligne 5259
80. _analyze_complete_combined_index(self, impair_pair_data: Dict, desync_sync_data: Dict, hands_data: List) -> Dict - Ligne 5309
81. _analyze_complete_pbt_index(self, hands_data: List) -> Dict - Ligne 5346
82. _analyze_complete_so_index(self, hands_data: List) -> Dict - Ligne 5394
83. _synthesize_complete_analysis(self, all_indices: Dict) -> Dict - Ligne 5450
84. _analyze_complete_cross_impacts(self, all_indices: Dict) -> Dict - Ligne 5507

MÉTHODES ANALYSE IMPACTS CROISÉS :
85. _analyze_impair_pair_to_so_impact(self, impair_pair_seq: List[str], so_seq: List[str]) -> Dict - Ligne 5592
86. _analyze_desync_sync_to_pbt_impact(self, desync_sync_seq: List[str], pbt_seq: List[str]) -> Dict - Ligne 5620
87. _identify_desync_periods(self, sync_sequence: List[str]) -> List[Dict] - Ligne 5654
88. _analyze_desync_sync_to_so_impact(self, desync_sync_seq: List[str], so_seq: List[str]) -> Dict - Ligne 5678
89. _analyze_combined_to_pbt_impact(self, combined_seq: List[str], pbt_seq: List[str]) -> Dict - Ligne 5706
90. _analyze_combined_to_so_impact(self, combined_seq: List[str], so_seq: List[str]) -> Dict - Ligne 5750
91. _analyze_tri_dimensional_impacts(self, impair_pair_seq: List[str], desync_sync_seq: List[str], ...) - Ligne 5786
92. _analyze_variations_impact_on_outcomes(self, all_indices: Dict) -> Dict - Ligne 5828

MÉTHODES ANALYSE SÉQUENCES CONSÉCUTIVES :
93. _analyze_consecutive_length_impact(self, impair_pair_seq: List[str], pbt_seq: List[str], so_seq: List[str]) -> Dict - Ligne 5893
94. _find_consecutive_sequences_with_positions(self, sequence: List[str], pattern: str) -> List[Dict] - Ligne 5965
95. _find_consecutive_sequences(self, sequence: List[str], pattern: str) -> List[int] - Ligne 5996

MÉTHODES CALCUL ALERTES ET SCORES :
96. _calculate_asymmetric_impair_alert_level(self, impair_consecutive: int) -> int - Ligne 6023
97. _calculate_asymmetric_pair_alert_level(self, pair_consecutive: int) -> int - Ligne 6041
98. _calculate_impair_rarity_score(self, impair_consecutive: int) -> float - Ligne 6059
99. _calculate_pair_commonality_score(self, pair_consecutive: int) -> float - Ligne 6078
100. _calculate_asymmetric_significance(self, impair_consecutive: int, pair_consecutive: int) -> Dict - Ligne 6095

MÉTHODES IDENTIFICATION PATTERNS DOMINANTS :
101. _identify_dominant_desync_sync_so_pattern(self, sync_s_count: int, sync_o_count: int, ...) - Ligne 6123
102. _calculate_combined_so_impact_strength(self, impact_analysis: Dict) -> float - Ligne 6179
103. _calculate_combined_pbt_impact_strength(self, impact_analysis: Dict) -> float - Ligne 6243
104. _identify_dominant_impair_pair_so_pattern(self, impair_s_count: int, impair_o_count: int, ...) - Ligne 6314
105. _calculate_overall_impact_strength(self, cross_impacts: Dict) -> float - Ligne 6370

MÉTHODES ANALYSE TRANSITIONS ET MOMENTS :
106. _analyze_transition_moments_impact(self, impair_pair_seq: List[str], desync_sync_seq: List[str], ...) - Ligne 6467
107. _calculate_distribution(self, sequence: List[str], possible_values: List[str]) -> Dict - Ligne 6632
108. _analyze_desync_periods_impact(self, desync_periods: List[Dict], pbt_seq: List[str], so_seq: List[str]) -> Dict - Ligne 6684
109. _analyze_combined_state_changes_impact(self, combined_seq: List[str], pbt_seq: List[str], so_seq: List[str]) -> Dict - Ligne 6940

MÉTHODES ANALYSE TEMPORELLE ET CORRÉLATIONS :
110. _analyze_temporal_correlation_evolution(self, impair_pair_seq: List[str], desync_sync_seq: List[str], ...) - Ligne 7223
111. _calculate_phase_impair_pair_pb_correlation(self, impair_pair_seq: List[str], pbt_seq: List[str]) -> Dict - Ligne 7424
112. _calculate_phase_impair_pair_so_correlation(self, impair_pair_seq: List[str], so_seq: List[str]) -> Dict - Ligne 7481
113. _calculate_phase_sync_desync_pb_correlation(self, desync_sync_seq: List[str], pbt_seq: List[str]) -> Dict - Ligne 7535
114. _calculate_phase_sync_desync_so_correlation(self, desync_sync_seq: List[str], so_seq: List[str]) -> Dict - Ligne 7592
115. _calculate_phase_correlation_strength(self, impair_pb_corr: Dict, impair_so_corr: Dict, ...) - Ligne 7646
116. _analyze_correlation_trend(self, correlation_values: List[float], trend_name: str) -> Dict - Ligne 7672
117. _calculate_correlation_stability(self, phase_strengths: Dict) -> float - Ligne 7707
118. _calculate_variance(self, values: List[float]) -> float - Ligne 7720
119. _generate_temporal_recommendation(self, best_phase: str, stability: float, trends: Dict) -> str - Ligne 7731
120. _calculate_evolution_strength(self, trends: Dict) -> float - Ligne 7748
121. _calculate_temporal_consistency(self, phase_strengths: Dict) -> float - Ligne 7764
122. _calculate_temporal_predictability(self, trends: Dict) -> float - Ligne 7781

MÉTHODES ANALYSE VARIATIONS ET FORCES :
123. _calculate_variation_strength_analysis(self, variations_impact: Dict) -> Dict - Ligne 7802
124. _extract_consecutive_length_strength(self, consecutive_impacts: Dict) -> float - Ligne 7974
125. _extract_transition_moments_strength(self, transition_impacts: Dict) -> float - Ligne 8011
126. _extract_desync_periods_strength(self, desync_impacts: Dict) -> float - Ligne 8042
127. _extract_combined_state_changes_strength(self, combined_impacts: Dict) -> float - Ligne 8072
128. _extract_temporal_evolution_strength(self, temporal_impacts: Dict) -> float - Ligne 8093
129. _calculate_confidence_level(self, global_strength: float, valid_strengths: int) -> str - Ligne 8111
130. _generate_exploitation_recommendation(self, global_strength: float, dominant_type: str, ...) - Ligne 8125
131. _identify_best_prediction_context(self, variations_impact: Dict, dominant_type: str, ...) - Ligne 8143

MÉTHODES CALCUL STATISTIQUES AVANCÉES :
132. _calculate_strength_distribution(self, individual_strengths: Dict, weights: Dict) -> Dict - Ligne 8170
133. _calculate_variation_consistency(self, individual_strengths: Dict) -> float - Ligne 8187
134. _assess_sample_size_adequacy(self, variations_impact: Dict) -> float - Ligne 8205
135. _calculate_statistical_significance(self, individual_strengths: Dict, variations_impact: Dict) -> float - Ligne 8229
136. _calculate_pattern_stability(self, individual_strengths: Dict) -> float - Ligne 8239
137. _assess_overall_quality(self, consistency: float, sample_adequacy: float, ...) - Ligne 8251

MÉTHODES IDENTIFICATION CORRÉLATIONS ET ZONES :
138. _identify_enhanced_dominant_correlations(self, all_indices: Dict, cross_index_impacts: Dict) -> List[Dict] - Ligne 8268
139. _identify_enhanced_high_confidence_zones(self, all_indices: Dict, cross_index_impacts: Dict) -> List[Dict] - Ligne 8566

MÉTHODES DÉPRÉCIÉES :
140. _define_complete_generation_space_DEPRECATED(self, indices_analysis: Dict, synthesis: Dict, sequence_metadata: Dict) -> Dict - Ligne 8880

MÉTHODES GÉNÉRATION SÉQUENCES OPTIMISÉES :
141. _generate_impair_pair_optimized_sequence(self, generation_space: Dict) -> List[Dict] - Ligne 9167
142. _generate_sync_based_sequence(self, generation_space: Dict) -> List[Dict] - Ligne 9346
143. _generate_combined_index_sequence(self, generation_space: Dict) -> List[Dict] - Ligne 9550
144. _generate_so_pattern_sequence(self, generation_space: Dict) -> List[Dict] - Ligne 9787

MÉTHODES CLASSIFICATION ET UTILITAIRES :
145. _classify_combined_transition_type(self, from_state: str, to_state: str) -> str - Ligne 10291
146. get_max_sequence_length(self, mode: str = "real") -> int - Ligne 10328
147. get_max_so_conversions(self, mode: str = "real") -> int - Ligne 10343
148. is_game_complete(self, pb_hands: int, so_conversions: int, mode: str = "real") -> bool - Ligne 10358

MÉTHODES GÉNÉRATION SIGNAUX ET GUIDANCE :
149. _generate_signals_summary(self, all_indices: Dict, synthesis: Dict) -> Dict - Ligne 10379
150. _generate_generation_guidance(self, all_indices: Dict, synthesis: Dict, signals_summary: Dict) -> Dict - Ligne 10496
151. _generate_quick_access(self, all_indices: Dict, synthesis: Dict, signals_summary: Dict) -> Dict - Ligne 10601

MÉTHODES PERFORMANCE ET CALCULS SPÉCIALISÉS :
152. _update_performance_metrics(self, prediction: Dict, total_time: float) - Ligne 10702
153. _count_consecutive_pattern(self, sequence: List[str], pattern: str) -> int - Ligne 10721
154. _calculate_rupture_probability(self, impair_count: int, pair_count: int) -> float - Ligne 10738
155. _analyze_correlations_std_dev(self, pair_impair_seq: List[str], ...) - Ligne 10768
156. _identify_improbability_zones(self, impair_count: int, pair_count: int, ...) - Ligne 10802

MÉTHODES DUPLIQUÉES (versions non universelles) :
157. _evaluate_sequence_quality(self, sequence, analyzer_report: Dict) -> Dict - Ligne 10849
158. _select_best_sequence(self, evaluated_sequences: List[Dict]) -> Dict - Ligne 10892
159. _calculate_cluster_confidence(self, best_sequence: Dict, analyzer_report: Dict) -> float - Ligne 10906
160. _extract_next_hand_prediction(self, best_sequence) -> Dict - Ligne 10927

ANALYSE STATISTIQUE DES MÉTHODES NON CENTRALISÉES :
==================================================

TOTAL MÉTHODES NON CENTRALISÉES : 131 méthodes

RÉPARTITION PAR CATÉGORIE :
- Méthodes spécialisées par cluster (C2, C3) : 12 méthodes
- Méthodes analyse indices complets : 7 méthodes
- Méthodes analyse impacts croisés : 8 méthodes
- Méthodes analyse séquences consécutives : 3 méthodes
- Méthodes calcul alertes et scores : 5 méthodes
- Méthodes identification patterns : 5 méthodes
- Méthodes analyse transitions : 4 méthodes
- Méthodes analyse temporelle : 13 méthodes
- Méthodes analyse variations : 9 méthodes
- Méthodes calcul statistiques : 6 méthodes
- Méthodes identification corrélations : 2 méthodes
- Méthodes génération optimisées : 4 méthodes
- Méthodes utilitaires et classification : 4 méthodes
- Méthodes génération signaux : 3 méthodes
- Méthodes performance : 5 méthodes
- Méthodes synthèse et analyse : 5 méthodes
- Méthodes confiance alternatives : 6 méthodes
- Méthodes calcul récompenses : 6 méthodes
- Méthodes génération spécialisées : 14 méthodes
- Méthodes corrélation dupliquées : 2 méthodes
- Méthodes dupliquées (non universelles) : 4 méthodes
- Méthodes dépréciées : 1 méthode
- Méthodes principales : 2 méthodes

STATUT :
=======
✅ 31 méthodes essentielles centralisées et universalisées
✅ 131 méthodes restantes disponibles pour centralisation future
✅ Système fonctionnel avec méthodes universelles prioritaires
✅ Méthodes spécialisées et avancées conservées pour développements futurs

RECOMMANDATIONS :
================
1. Les 131 méthodes restantes peuvent être centralisées si nécessaire
2. Priorité aux méthodes spécialisées cluster (C2, C3) si extension requise
3. Méthodes dupliquées peuvent être supprimées après validation
4. Méthodes dépréciées peuvent être supprimées
5. Focus sur méthodes analyse avancée pour optimisations futures
