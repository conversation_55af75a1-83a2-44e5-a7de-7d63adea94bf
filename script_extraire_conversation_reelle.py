#!/usr/bin/env python3
"""
Script pour extraire la vraie conversation d'Augment depuis la clé memento/webviewView.augment-chat
"""

import sqlite3
import json
from pathlib import Path
import re

def extraire_conversation_reelle():
    """Extrait la vraie conversation depuis la base de données"""
    
    state_file = Path("C:/Users/<USER>/AppData/Roaming/Code/User/workspaceStorage/a35ba43ef26792e61fe787a234121806/state.vscdb")
    
    print("=== EXTRACTION CONVERSATION RÉELLE ===")
    
    try:
        conn = sqlite3.connect(str(state_file))
        cursor = conn.cursor()
        
        # Récupérer la clé avec les conversations
        cursor.execute("SELECT value FROM ItemTable WHERE key = 'memento/webviewView.augment-chat';")
        result = cursor.fetchone()
        
        if result:
            value = result[0]
            if isinstance(value, bytes):
                value_str = value.decode('utf-8')
            else:
                value_str = str(value)
            
            print(f"Données trouvées: {len(value_str)} caractères")
            
            # Sauvegarder les données brutes pour analyse
            with open("conversation_raw_data.json", 'w', encoding='utf-8') as f:
                f.write(value_str)
            print("Données brutes sauvegardées dans: conversation_raw_data.json")
            
            # Parser le JSON principal
            main_data = json.loads(value_str)
            webview_state_str = main_data['webviewState']
            
            # Sauvegarder le webviewState pour analyse
            with open("webview_state_raw.json", 'w', encoding='utf-8') as f:
                f.write(webview_state_str)
            print("WebviewState brut sauvegardé dans: webview_state_raw.json")
            
            # Parser le webviewState
            webview_data = json.loads(webview_state_str)
            
            # Sauvegarder le webviewState parsé
            with open("webview_state_parsed.json", 'w', encoding='utf-8') as f:
                json.dump(webview_data, f, indent=2, ensure_ascii=False)
            print("WebviewState parsé sauvegardé dans: webview_state_parsed.json")
            
            # Analyser la structure
            print(f"\nStructure du webviewState:")
            for key in webview_data.keys():
                print(f"  - {key}: {type(webview_data[key])}")
                if isinstance(webview_data[key], dict):
                    print(f"    Sous-clés: {list(webview_data[key].keys())[:5]}...")
                elif isinstance(webview_data[key], list):
                    print(f"    Éléments: {len(webview_data[key])}")
            
            # Rechercher les messages dans toute la structure
            def rechercher_messages(obj, path=""):
                """Recherche récursive des messages"""
                messages_found = []
                
                if isinstance(obj, dict):
                    for key, value in obj.items():
                        new_path = f"{path}.{key}" if path else key
                        
                        # Vérifier si c'est un message
                        if key in ['messages', 'chatHistory', 'content'] or 'message' in key.lower():
                            print(f"Trouvé clé potentielle: {new_path}")
                            if isinstance(value, list) and len(value) > 0:
                                print(f"  Liste de {len(value)} éléments")
                                messages_found.extend(value)
                            elif isinstance(value, str) and len(value) > 50:
                                print(f"  Texte de {len(value)} caractères")
                                messages_found.append({"content": value, "source": new_path})
                        
                        # Recherche récursive
                        messages_found.extend(rechercher_messages(value, new_path))
                
                elif isinstance(obj, list):
                    for i, item in enumerate(obj):
                        new_path = f"{path}[{i}]"
                        messages_found.extend(rechercher_messages(item, new_path))
                
                return messages_found
            
            print(f"\n=== RECHERCHE DE MESSAGES ===")
            all_messages = rechercher_messages(webview_data)
            
            print(f"Messages trouvés: {len(all_messages)}")
            
            # Sauvegarder tous les messages trouvés
            if all_messages:
                with open("messages_extraits.json", 'w', encoding='utf-8') as f:
                    json.dump(all_messages, f, indent=2, ensure_ascii=False)
                print("Messages sauvegardés dans: messages_extraits.json")
                
                # Créer une version lisible
                with open("conversation_lisible.txt", 'w', encoding='utf-8') as f:
                    f.write("CONVERSATION AUGMENT EXTRAITE\n")
                    f.write("="*80 + "\n\n")
                    
                    for i, msg in enumerate(all_messages):
                        f.write(f"MESSAGE {i+1}\n")
                        f.write("-" * 40 + "\n")
                        
                        if isinstance(msg, dict):
                            for key, value in msg.items():
                                if isinstance(value, str) and len(value) > 20:
                                    f.write(f"{key.upper()}:\n{value}\n\n")
                                else:
                                    f.write(f"{key}: {value}\n")
                        else:
                            f.write(f"CONTENU: {msg}\n")
                        
                        f.write("="*80 + "\n\n")
                
                print("Version lisible sauvegardée dans: conversation_lisible.txt")
            
            # Rechercher spécifiquement "prends connaissances"
            print(f"\n=== RECHERCHE 'PRENDS CONNAISSANCES' ===")
            pattern = r'prends\s+connaissances'
            matches = re.findall(pattern, value_str, re.IGNORECASE)
            print(f"Occurrences trouvées: {len(matches)}")
            
            # Extraire le contexte autour de chaque occurrence
            contexts = []
            for match in re.finditer(pattern, value_str, re.IGNORECASE):
                start = max(0, match.start() - 200)
                end = min(len(value_str), match.end() + 200)
                context = value_str[start:end]
                contexts.append(context)
            
            if contexts:
                with open("contextes_prends_connaissances.txt", 'w', encoding='utf-8') as f:
                    f.write("CONTEXTES 'PRENDS CONNAISSANCES'\n")
                    f.write("="*80 + "\n\n")
                    
                    for i, context in enumerate(contexts):
                        f.write(f"CONTEXTE {i+1}\n")
                        f.write("-" * 40 + "\n")
                        f.write(context)
                        f.write("\n" + "="*80 + "\n\n")
                
                print("Contextes sauvegardés dans: contextes_prends_connaissances.txt")
        
        conn.close()
        
    except Exception as e:
        print(f"Erreur: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    extraire_conversation_reelle()
