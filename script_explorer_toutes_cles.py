#!/usr/bin/env python3
"""
Script pour explorer toutes les clés de la base de données et trouver les messages
"""

import sqlite3
import json
from pathlib import Path
import re

def explorer_toutes_cles():
    """Explore toutes les clés pour trouver où sont stockés les messages"""
    
    state_file = Path("C:/Users/<USER>/AppData/Roaming/Code/User/workspaceStorage/a35ba43ef26792e61fe787a234121806/state.vscdb")
    
    print("=== EXPLORATION COMPLÈTE DE TOUTES LES CLÉS ===")
    
    try:
        conn = sqlite3.connect(str(state_file))
        cursor = conn.cursor()
        
        cursor.execute("SELECT key, value FROM ItemTable ORDER BY key;")
        all_data = cursor.fetchall()
        
        print(f"Total d'entrées: {len(all_data)}")
        
        # Mots-clés à rechercher dans les valeurs
        keywords = ['message', 'content', 'user', 'assistant', 'conversation', 'prends', 'connaissances', 'mémoires']
        
        for i, (key, value) in enumerate(all_data):
            print(f"\n--- ENTRÉE {i+1}: {key} ---")
            
            try:
                if isinstance(value, bytes):
                    value_str = value.decode('utf-8')
                else:
                    value_str = str(value)
                
                print(f"Taille: {len(value_str)} caractères")
                
                # Vérifier si contient des mots-clés
                found_keywords = []
                for keyword in keywords:
                    if keyword.lower() in value_str.lower():
                        found_keywords.append(keyword)
                
                if found_keywords:
                    print(f"Mots-clés trouvés: {', '.join(found_keywords)}")
                    
                    # Afficher plus de détails pour les entrées intéressantes
                    if len(value_str) > 1000:  # Entrées volumineuses
                        print(f"CONTENU VOLUMINEUX - Premiers 500 caractères:")
                        print(value_str[:500])
                        print("...")
                        print(f"Derniers 200 caractères:")
                        print(value_str[-200:])
                    else:
                        print(f"CONTENU COMPLET:")
                        print(value_str)
                
                # Essayer de parser comme JSON pour les gros objets
                if len(value_str) > 1000:
                    try:
                        json_data = json.loads(value_str)
                        print(f"JSON parsé - Type: {type(json_data)}")
                        if isinstance(json_data, dict):
                            print(f"Clés JSON: {list(json_data.keys())}")
                    except json.JSONDecodeError:
                        pass
                
                # Rechercher des patterns spécifiques
                patterns = [
                    r'"role":\s*"(user|assistant)"',
                    r'"content":\s*"[^"]{50,}',
                    r'prends\s+connaissances',
                    r'Augment-Memories'
                ]
                
                for pattern in patterns:
                    matches = re.findall(pattern, value_str, re.IGNORECASE)
                    if matches:
                        print(f"Pattern '{pattern}' trouvé: {len(matches)} occurrences")
                        if len(matches) <= 3:
                            for match in matches:
                                print(f"  -> {match}")
                
            except Exception as e:
                print(f"Erreur traitement: {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"Erreur: {e}")

def rechercher_dans_autres_fichiers():
    """Recherche dans d'autres fichiers du workspace"""
    
    workspace_dir = Path("C:/Users/<USER>/AppData/Roaming/Code/User/workspaceStorage/a35ba43ef26792e61fe787a234121806")
    
    print(f"\n=== EXPLORATION DU WORKSPACE ===")
    print(f"Répertoire: {workspace_dir}")
    
    if workspace_dir.exists():
        for item in workspace_dir.rglob("*"):
            if item.is_file() and item.name != "state.vscdb":
                print(f"\nFichier trouvé: {item.name}")
                print(f"  Chemin: {item}")
                print(f"  Taille: {item.stat().st_size} bytes")
                
                # Lire les petits fichiers
                if item.stat().st_size < 10000:
                    try:
                        with open(item, 'r', encoding='utf-8') as f:
                            content = f.read()
                            if any(keyword in content.lower() for keyword in ['message', 'conversation', 'prends']):
                                print(f"  Contenu intéressant trouvé:")
                                print(f"  {content[:300]}...")
                    except Exception as e:
                        print(f"  Erreur lecture: {e}")

if __name__ == "__main__":
    explorer_toutes_cles()
    rechercher_dans_autres_fichiers()
